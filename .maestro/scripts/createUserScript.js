// userStatus is an env variable

const USER_CONFIG = {
  VERIFIED_GR: {
    status: "VERIFIED",
    props: {
      residencyCountry: "GR",
    },
  },
  VERIFIED_WITH_REFERRAL_GR: {
    status: "VERIFIED",
    props: {
      residencyCountry: "GR",
      referredByEmail: "<EMAIL>",
    },
  },
  VERIFIED: {
    status: "VERIFIED",
    props: {
      residencyCountry: "GB",
    },
  },
  VERIFIED_WITH_REFERRAL_UK: {
    status: "VERIFIED",
    props: {
      residencyCountry: "GB",
      referredByEmail: "<EMAIL>",
    },
  },
  VERIFIED_SKIPPED_PORTFOLIO: {
    status: "VERIFIED_WITH_SUBSCRIPTION",
    props: {
      skippedPortfolioCreation: true,
    },
  },
  VERIFIED_SKIPPED_PORTFOLIO_GR: {
    status: "VERIFIED_WITH_SUBSCRIPTION",
    props: {
      skippedPortfolioCreation: true,
      residencyCountry: "GR",
    },
  },
  VERIFIED_WITH_TARGET: {
    status: "VERIFIED_WITH_TARGET",
  },
  VERIFIED_WITH_BANK: {
    status: "VERIFIED_WITH_BANK",
  },
  VERIFIED_WITH_BANK_MANDATE: {
    status: "VERIFIED_WITH_BANK_MANDATE",
  },
  VERIFIED_WITH_AUTOMATIONS: {
    status: "VERIFIED_WITH_AUTOMATIONS",
  },
  INVESTED: {
    status: "INVESTED",
  },
  INVESTED_WITH_CASH: {
    status: "INVESTED_WITH_CASH",
  },
  VERIFIED_WITH_CASH: {
    status: "VERIFIED_WITH_CASH",
  },
  INVESTED_WITH_CASH_GR: {
    status: "INVESTED_WITH_CASH",
    props: {
      residencyCountry: "GR",
    },
  },
  VERIFIED_WITH_AUTOMATIONS_GR: {
    status: "VERIFIED_WITH_AUTOMATIONS",
    props: {
      residencyCountry: "GR",
    },
  },
  VERIFIED_WITH_BANK_MANDATE_GR: {
    status: "VERIFIED_WITH_BANK_MANDATE",
    props: {
      residencyCountry: "GR",
    },
  },
  VERIFIED_WITH_TARGET_GR: {
    status: "VERIFIED_WITH_TARGET",
    props: {
      residencyCountry: "GR",
    },
  },
  INVESTED_WITH_CASH_NO_TARGET_GR: {
    status: "INVESTED_WITH_CASH_NO_TARGET",
    props: {
      residencyCountry: "GR",
    },
  },
  VERIFIED_WITH_GIFT: {
    status: "VERIFIED_WITH_GIFT",
    props: {
      residencyCountry: "GB",
      skippedPortfolioCreation: true,
    },
  },
  VERIFIED_WITH_GIFT_GR: {
    status: "VERIFIED_WITH_GIFT",
    props: {
      residencyCountry: "GR",
      skippedPortfolioCreation: true,
    },
  },
  INVESTED_WITH_CASH_NO_BANK_ACCOUNT: {
    status: "INVESTED_WITH_CASH_NO_BANK_ACCOUNT",
    props: {
      residencyCountry: "GB",
    },
  },
  INVESTED_WITH_REWARD_GR: {
    status: "INVESTED_WITH_REWARD",
    props: {
      residencyCountry: "GR",
    },
  },
  INVESTED_WITH_REWARD: {
    status: "INVESTED_WITH_REWARD",
    props: {
      residencyCountry: "GB",
    },
  },
  INVESTED_WITH_SAVINGS_GR: {
    status: "INVESTED_WITH_SAVINGS",
    props: {
      residencyCountry: "GR",
    },
  },
  INVESTED_WITH_REWARD_NO_APP_RATING_GR: {
    status: "INVESTED_WITH_REWARD_NO_APP_RATING",
    props: {
      residencyCountry: "GR",
    },
  },
};

const userConfigData = USER_CONFIG[userStatus];
const status = userConfigData.status;
const props = userConfigData.props || {};

const response = http.post(
  "https://wealthyhood-app-api-sandbox.onrender.com/test/users",
  {
    headers: { "Content-Type": "application/json" },
    body: JSON.stringify({
      email: "<EMAIL>",
      status,
      props,
    }),
  }
);
