appId: com.wealthyhood.wealthyhood
jsEngine: graaljs
---

# Open AssetDetails

- waitForAnimationToEnd
- tapOn: "Invest"
- waitForAnimationToEnd

- scrollUntilVisible:
    element:
      text: "Individual Stocks"
    centerElement: true

- tapOn: "Sony"

# Open dividend transaction

- waitForAnimationToEnd

- scrollUntilVisible:
    element:
      text: "Recent Activity"
    centerElement: true

- tapOn: "Reward"

# Make assertions

- waitForAnimationToEnd

- assertVisible: "Reward"
- assertVisible: ".*SONY.*"
- assertVisible: ".*Sony"
- assertVisible: "Market Order"

# Check date

- copyTextFrom:
    id: "date_text_view"

- assertTrue: ${ maestro.copiedText.length > 0 }

# Check time

- copyTextFrom:
    id: "time_text_view"

- assertTrue: ${ maestro.copiedText.length > 0 }

# Check Amount

- assertVisible: "Amount"

- copyTextFrom:
    id: "first_column_value_text_view"

- assertTrue: ${ maestro.copiedText.length > 0 }

# Check Shares

- assertVisible: "Shares"

- copyTextFrom:
    id: "second_column_value_text_view"

- assertTrue: ${ maestro.copiedText.length > 0 }

# Check Per share

- assertVisible: "Per share"

- copyTextFrom:
    id: "third_column_value_text_view"

- assertTrue: ${ maestro.copiedText.length > 0 }

# Check Client field

- copyTextFrom:
    id: "label_text_view"
    index: 0

- assertTrue: ${ maestro.copiedText == "Client" }

- copyTextFrom:
    id: "value_text_view"
    index: 0

- assertTrue: ${ maestro.copiedText.length > 0 }

# Check Order ID field

- copyTextFrom:
    id: "label_text_view"
    index: 1

- assertTrue: ${ maestro.copiedText == "Order ID" }

- copyTextFrom:
    id: "value_text_view"
    index: 1

- assertTrue: ${ maestro.copiedText.length > 0 }

# Check ISIN field

- copyTextFrom:
    id: "label_text_view"
    index: 2

- assertTrue: ${ maestro.copiedText == "ISIN" }

- copyTextFrom:
    id: "value_text_view"
    index: 2

- assertTrue: ${ maestro.copiedText.length > 0 }

# Check Reporting firm field

- copyTextFrom:
    id: "label_text_view"
    index: 3

- assertTrue: ${ maestro.copiedText == "Reporting firm" }

- copyTextFrom:
    id: "value_text_view"
    index: 3

- assertTrue: ${ maestro.copiedText.trim().length > 0 }

# Check there are no other info rows

- assertNotVisible:
    id: "label_text_view"
    index: 4

- runFlow:
    when:
      true: ${RESIDENCY == "GR"}
    commands:
      # Check trade confirmation

      - scrollUntilVisible:
          element:
            text: "Trade Confirmation"
          centerElement: true

      # Check trade confirmation URL

      - tapOn: "Trade Confirmation"
      - waitForAnimationToEnd

      - extendedWaitUntil:
          visible:
            text: ".*docs.google.com.*"

- runFlow:
    when:
      true: ${RESIDENCY == "UK"}
    commands:
      # Check there is no trade confirmation

      - scrollUntilVisible:
          element:
            id: "wealthyhood_logo_image_view"
          centerElement: true

      - assertNotVisible: "Trade Confirmation"
