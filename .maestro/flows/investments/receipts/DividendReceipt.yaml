appId: com.wealthyhood.wealthyhood
jsEngine: graaljs
---
- runFlow:
    file: ../../common/LoginFlow.yaml
    env:
      userStatus: "INVESTED_WITH_REWARD_GR"

# Open AssetDetails

- waitForAnimationToEnd
- tapOn: "Invest"
- waitForAnimationToEnd

- scrollUntilVisible:
    element:
      text: "Individual Stocks"
    centerElement: true

- tapOn: "Apple"

# Open dividend transaction

- waitForAnimationToEnd

- scrollUntilVisible:
    element:
      text: "Recent Activity"
    centerElement: true

- tapOn: "Dividend"

# Make assertions

- waitForAnimationToEnd

- assertVisible: "Dividend"
- assertVisible: ".*AAPL.*"
- assertVisible: ".*Apple"
- assertNotVisible: "Market Order"

# Check date

- copyTextFrom:
    id: "date_text_view"

- assertTrue: ${ maestro.copiedText.length > 0 }

# Check time

- copyTextFrom:
    id: "time_text_view"

- assertTrue: ${ maestro.copiedText.length > 0 }

# Check amount

- assertVisible: "Amount"

- copyTextFrom:
    id: "first_column_value_text_view"

- assertTrue: ${ maestro.copiedText.length > 0 }

# Check there is no other column

- assertNotVisible: "Shares"
- assertNotVisible: "Per share"

# Check Client field

- copyTextFrom:
    id: "label_text_view"
    index: 0

- assertTrue: ${ maestro.copiedText == "Client" }

- copyTextFrom:
    id: "value_text_view"
    index: 0

- assertTrue: ${ maestro.copiedText.length > 0 }

# Check ISIN field

- copyTextFrom:
    id: "label_text_view"
    index: 1

- assertTrue: ${ maestro.copiedText == "ISIN" }

- copyTextFrom:
    id: "value_text_view"
    index: 1

- assertTrue: ${ maestro.copiedText.length > 0 }

# Check Reporting firm field

- copyTextFrom:
    id: "label_text_view"
    index: 2

- assertTrue: ${ maestro.copiedText == "Reporting firm" }

- copyTextFrom:
    id: "value_text_view"
    index: 2

- assertTrue: ${ maestro.copiedText.trim().length > 0 }

# Check there are no other info rows

- assertNotVisible:
    id: "label_text_view"
    index: 3

# Check there is no trade confirmation

- scrollUntilVisible:
    element:
      id: "wealthyhood_logo_image_view"
    centerElement: true

- assertNotVisible: "Trade Confirmation"
