appId: com.wealthyhood.wealthyhood
jsEngine: graaljs
---
- runFlow:
    file: ../../common/LoginFlow.yaml
    env:
      userStatus: "INVESTED_WITH_CASH"

# SCREEN: Home
- waitForAnimationToEnd
- tapOn: "Invest"
- waitForAnimationToEnd
- scrollUntilVisible:
    element:
      text: "Individual Stocks"
    centerElement: true
- tapOn: "ETFs"
- waitForAnimationToEnd

- scrollUntilVisible:
    element:
      text: "Vanguard S&P 500 UCITS ETF"
    centerElement: true

- tapOn: "Vanguard S&P 500 UCITS ETF"

## SCREEN: Asset Details
- waitForAnimationToEnd
- extendedWaitUntil:
    visible:
      id: "buy_button"
- tapOn:
    id: "buy_button"

## SCREEN: ETF Buy
- waitForAnimationToEnd
- assertVisible:
    id: "selected_payment_method_component"
- tapOn:
    id: "selected_payment_method_component"
- waitForAnimationToEnd
- tapOn:
    text: "Cash balance"
- waitForAnimationToEnd
- assertVisible:
    text: "Cash balance .*"
- tapOn:
    id: "digit_1_button"
- tapOn:
    id: "digit_2_button"
- tapOn:
    id: "digit_3_button"
- tapOn:
    id: "digit_1_button"
- tapOn:
    id: "action_button"

## SCREEN: Warning is visible
- waitForAnimationToEnd
- assertVisible:
    id: "validation_message_text_view"
