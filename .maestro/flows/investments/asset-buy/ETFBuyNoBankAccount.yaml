appId: com.wealthyhood.wealthyhood
jsEngine: graaljs
---
- runFlow:
    file: ../../common/LoginFlow.yaml
    env:
      userStatus: "VERIFIED_WITH_TARGET"

# SCREEN: Home
- waitForAnimationToEnd
- tapOn: "Invest"
- waitForAnimationToEnd
- extendedWaitUntil:
    visible:
      id: "discover_button"
- tapOn:
    id: "discover_button"

## SCREEN: Discover
- waitForAnimationToEnd
- assertVisible: "Discover"
- scrollUntilVisible:
    element:
      text: "Vanguard S&P 500 UCITS ETF"
- tapOn:
    text: "Vanguard S&P 500 UCITS ETF"

## SCREEN: Etf Details
- waitForAnimationToEnd
- extendedWaitUntil:
    visible:
      id: "buy_button"
- tapOn:
    id: "buy_button"

## SCREEN: Etf Buy
- assertNotVisible:
    text: "Redeem my .* gift"
- assertNotVisible:
    text: "Cash balance .*"
- assertVisible: "Proceed"
- tapOn:
    id: "digit_1_button"
- tapOn:
    id: "digit_2_button"
- tapOn:
    id: "digit_3_button"
- tapOn: "Proceed"

## Flow: Bank Account Linking
- tapOn: "Mock UK Payments - Redirect Flow"
- waitForAnimationToEnd
- tapOn: "Allow"
- waitForAnimationToEnd
- runFlow:
    file: ../../common/TruelayerBankAccountLinkingFlow.yaml

## SCREEN: Etf Buy
- extendedWaitUntil:
    visible:
      id: "selected_payment_method_component"
