appId: com.wealthyhood.wealthyhood
jsEngine: graaljs
---
- runFlow:
    file: ../../common/LoginFlow.yaml
    env:
      userStatus: "INVESTED_WITH_CASH"

# SCREEN: Home
- waitForAnimationToEnd
- tapOn: "Invest"
- waitForAnimationToEnd
- scrollUntilVisible:
    element:
      text: "Individual Stocks"
    centerElement: true
- tapOn: "ETFs"
- waitForAnimationToEnd

- scrollUntilVisible:
    element:
      text: "Vanguard S&P 500 UCITS ETF"
    centerElement: true

- tapOn: "Vanguard S&P 500 UCITS ETF"

## SCREEN: Asset Details
- waitForAnimationToEnd
- extendedWaitUntil:
    visible:
      id: "buy_button"
- tapOn:
    id: "buy_button"

## SCREEN: ETF Buy
- waitForAnimationToEnd
- assertNotVisible:
    id: "asset_tag_component"
- assertVisible:
    id: "selected_payment_method_component"
- tapOn:
    id: "selected_payment_method_component"
- waitForAnimationToEnd
- tapOn:
    text: "Mock UK Payments .*"
- waitForAnimationToEnd
- assertVisible:
    text: "Mock UK Payments .*"
- tapOn:
    id: "digit_1_button"
- tapOn:
    id: "digit_2_button"
- tapOn:
    id: "digit_3_button"
- tapOn:
    id: "action_button"

## SCREEN: ETF Buy Review Dialog
- waitForAnimationToEnd
## Check commission, trading windows and orders when the switch is ON
- assertNotVisible: "Smart execution"
- assertVisible: "Commission"
- assertNotVisible: "Stocks execution time"
- assertVisible: "ETFs execution time"
- tapOn:
    id: "button"

## SCREEN: Bank loading screen
- waitForAnimationToEnd
- runFlow:
    file: ../../common/TruelayerPaymentFlow.yaml

## SCREEN: Order placed
- extendedWaitUntil:
    visible: "Order placed!"
- scrollUntilVisible:
    element:
      text: "ETFs execution time"
    centerElement: true
- assertNotVisible: "Smart execution"
- assertVisible: "Commission"
- assertNotVisible: "Stocks execution time"
