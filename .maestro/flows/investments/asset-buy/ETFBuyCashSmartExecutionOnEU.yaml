appId: com.wealthyhood.wealthyhood
jsEngine: graaljs
---
- runFlow:
    file: ../../common/LoginFlow.yaml
    env:
      userStatus: "INVESTED_WITH_CASH_GR"

# SCREEN: Home

- waitForAnimationToEnd
- tapOn: "Invest"
- waitForAnimationToEnd
- scrollUntilVisible:
    element:
      text: "Individual Stocks"
    centerElement: true
- tapOn: "ETFs"
- waitForAnimationToEnd

- scrollUntilVisible:
    element:
      text: "Vanguard S&P 500 UCITS ETF"
    centerElement: true

- tapOn: "Vanguard S&P 500 UCITS ETF"

## SCREEN: Asset Details
- waitForAnimationToEnd
- extendedWaitUntil:
    visible:
      id: "buy_button"
- tapOn:
    id: "buy_button"

## SCREEN: ETF Buy
- waitForAnimationToEnd
- assertVisible:
    id: "asset_tag_component"
- assertVisible:
    text: "Cash balance .*"
- tapOn:
    id: "digit_1_button"
- tapOn:
    id: "digit_2_button"
- tapOn:
    id: "digit_3_button"
- tapOn:
    id: "action_button"

## SCREEN: ETF Buy Review Dialog

- waitForAnimationToEnd
## Check commission, trading windows and orders when the switch is ON
- assertVisible:
    id: "switch_material"
    rightOf:
      text: "Smart execution"
    checked: true
- assertVisible: "Commission"
- assertNotVisible: "Stocks execution time"
- assertVisible: "ETFs execution time"
## Get the current Commission and ETFs execution time values
- copyTextFrom:
    id: "value_text_view"
    index: 3
- evalScript: ${output.commissionTextWithoutFormat = maestro.copiedText}
- copyTextFrom:
    id: "value_text_view"
    index: 5
- evalScript: ${output.etfExecutionTime = maestro.copiedText}
## Check commission, trading windows and orders when the switch is OFF
- tapOn:
    id: "switch_material"
- assertVisible: "Commission"
- assertNotVisible: "Stocks execution time"
- assertVisible: "ETFs execution time"
## Check that the Commission and ETFs execution time changed
- copyTextFrom:
    id: "value_text_view"
    index: 3
- assertTrue: ${output.commissionTextWithoutFormat != maestro.copiedText}
- copyTextFrom:
    id: "value_text_view"
    index: 5
- assertTrue: ${output.etfExecutionTime != maestro.copiedText}

## SCREEN: ETF Review
- waitForAnimationToEnd
- tapOn:
    id: "switch_material"
- assertVisible:
    id: "switch_material"
    checked: true
    rightOf: "Smart execution"
- tapOn:
    id: "button"

## SCREEN: Order placed
- waitForAnimationToEnd
- assertVisible: "Order placed!"
- scrollUntilVisible:
    element:
      text: "ETFs execution time"
    centerElement: true
- assertNotVisible: "Smart execution"
- copyTextFrom:
    rightOf: "Commission"
- evalScript: |
    // Check if the copied text matches our pattern for zero (0.00 or 0,00)
    const text = maestro.copiedText;
    const isZero = /^0[.,]00$/.test(text.trim());
    maestro.assertTrue(isZero, "Commission should be zero (0.00 or 0,00), but was: " + text);
- assertNotVisible: "Stocks execution time"
