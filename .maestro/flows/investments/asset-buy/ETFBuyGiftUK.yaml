appId: com.wealthyhood.wealthyhood
jsEngine: graaljs
---
- runFlow:
    file: ../../common/LoginFlow.yaml
    env:
      userStatus: "VERIFIED_WITH_GIFT"

# SCREEN: Home
- waitForAnimationToEnd
- runFlow:
    when:
      visible:
        id: "redeem_gift_constraint_layout"
    commands:
      - swipe:
          start: 50%, 20%
          end: 50%, 80%
          duration: 300
- tapOn: "Invest"
- waitForAnimationToEnd
- extendedWaitUntil:
    visible:
      id: "discover_button"
- tapOn:
    id: "discover_button"

## SCREEN: Discover
- waitForAnimationToEnd
- assertVisible: "Discover"
- scrollUntilVisible:
    element:
      text: "Vanguard S&P 500 UCITS ETF"
- tapOn:
    text: "Vanguard S&P 500 UCITS ETF"

## SCREEN: Asset Details
- waitForAnimationToEnd
- extendedWaitUntil:
    visible:
      id: "buy_button"
- tapOn:
    id: "buy_button"

## SCREEN: Stock Buy
- waitForAnimationToEnd
- assertNotVisible:
    id: "asset_tag_component"
- assertVisible:
    id: "selected_payment_method_component"
- tapOn:
    id: "selected_payment_method_component"
- waitForAnimationToEnd
- tapOn:
    text: "Redeem my .* gift"
- waitForAnimationToEnd
- tapOn:
    id: "digit_1_button"
- tapOn:
    id: "digit_2_button"
- tapOn:
    id: "digit_3_button"
- assertVisible:
    id: "edit_text"
    text: "0"
- assertVisible:
    text: "Redeem my .* gift"
- tapOn:
    id: "action_button"

## SCREEN: Stock Buy Review Dialog
- waitForAnimationToEnd
- assertNotVisible: "Smart execution"
- assertVisible: "Commission"
- assertNotVisible: "Stocks execution time"
- assertVisible: "ETFs execution time"
- tapOn:
    id: "button"

## SCREEN: Order placed
- waitForAnimationToEnd
- scrollUntilVisible:
    element:
      text: "ETFs execution time"
    centerElement: true
- assertVisible: "Order placed!"
- assertNotVisible: "Smart execution"
- assertNotVisible: "Stocks execution time"
