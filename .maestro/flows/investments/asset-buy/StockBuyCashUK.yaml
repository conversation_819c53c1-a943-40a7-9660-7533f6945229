appId: com.wealthyhood.wealthyhood
jsEngine: graaljs
---
- runFlow:
    file: ../../common/LoginFlow.yaml
    env:
      userStatus: "INVESTED_WITH_CASH"

# SCREEN: Home
- waitForAnimationToEnd
- tapOn: "Invest"
- waitForAnimationToEnd
- scrollUntilVisible:
    element:
      text: "Apple"
    centerElement: true
- tapOn: "Apple"

## SCREEN: Asset Details
- waitForAnimationToEnd
- extendedWaitUntil:
    visible:
      id: "buy_button"
- tapOn:
    id: "buy_button"

## SCREEN: Stock Buy
- waitForAnimationToEnd
- assertVisible:
    id: "asset_tag_component"
- assertVisible:
    id: "selected_payment_method_component"
- tapOn:
    id: "selected_payment_method_component"
- waitForAnimationToEnd
- tapOn:
    text: "Cash balance"
- waitForAnimationToEnd
- assertVisible:
    text: "Cash balance .*"
- tapOn:
    id: "digit_1_button"
- tapOn:
    id: "digit_2_button"
- tapOn:
    id: "digit_3_button"
- tapOn:
    id: "action_button"

## SCREEN: Stock Buy Review Dialog

- waitForAnimationToEnd
- assertNotVisible: "Smart execution"
- assertVisible: "Commission"
- assertVisible: "Stocks execution time"
- assertNotVisible: "ETFs execution time"
- tapOn:
    id: "button"

## SCREEN: Order placed
- waitForAnimationToEnd
- assertVisible: "Order placed!"

- scrollUntilVisible:
    element:
      text: "Commission"
    centerElement: true

- scrollUntilVisible:
    element:
      text: "Stocks execution time"
    centerElement: true

- assertNotVisible: "ETFs execution time"
