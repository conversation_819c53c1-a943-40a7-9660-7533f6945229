appId: com.wealthyhood.wealthyhood
jsEngine: graaljs
---
- runFlow:
    file: ../common/LoginFlow.yaml
    env:
      userStatus: "INVESTED_WITH_CASH_GR"

# Navigate to Invest screen

- waitForAnimationToEnd
- tapOn: "Invest"
- waitForAnimationToEnd

- scrollUntilVisible:
    element:
      text: "Apple"
    centerElement: true

- assertVisible: ".*AAPL.*,.*%"

- assertVisible:
    id: "percentage_text_view"
    text: ".*,.*%"
