appId: com.wealthyhood.wealthyhood
jsEngine: graaljs
---

- runFlow:
    file: ../../../flows/common/LoginFlow.yaml
    env:
      userStatus: "INVESTED_WITH_REWARD_GR"

- waitForAnimationToEnd

# Open stock sell

- tapOn: "Invest"
- waitForAnimationToEnd

- scrollUntilVisible:
    element:
      text: "Individual Stocks"
    centerElement: true

- scrollUntilVisible:
    element:
      text: "Sony"
    centerElement: true

- tapOn: "Sony"

- extendedWaitUntil:
    visible:
      id: "sell_button"

- tapOn:
    id: "sell_button"

- waitForAnimationToEnd

# Check available shares

- assertVisible: "0 shares available"

# Check info button

- assertVisible:
    id: "info_button"

- tapOn:
    id: "info_button"

- extendedWaitUntil:
    visible: "Number of shares available"
