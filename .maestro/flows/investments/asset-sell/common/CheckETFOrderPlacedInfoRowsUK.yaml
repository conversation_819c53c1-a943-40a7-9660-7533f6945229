appId: com.wealthyhood.wealthyhood
jsEngine: graaljs
---

# No. of shares

- scrollUntilVisible:
    element:
      id: "label_text_view"
      index: 0
    centerElement: true

- copyTextFrom:
    id: "label_text_view"
    index: 0

- assertTrue: ${maestro.copiedText == "No. of shares"}

- copyTextFrom:
    id: "value_text_view"
    index: 0

- assertTrue: ${maestro.copiedText == NUMBER_OF_SHARES}

# Amount (est.)

- scrollUntilVisible:
    element:
      id: "label_text_view"
      index: 1
    centerElement: true

- copyTextFrom:
    id: "label_text_view"
    index: 1

- assertTrue: ${maestro.copiedText == "Amount (est.)"}

# Cashback

- scrollUntilVisible:
    element:
      id: "label_text_view"
      index: 2
    centerElement: true

- copyTextFrom:
    id: "label_text_view"
    index: 2

- assertTrue: ${maestro.copiedText == "Cashback"}

- copyTextFrom:
    id: "value_text_view"
    index: 2

- assertTrue: ${maestro.copiedText == "£0.00"}

- tapOn:
    id: "info_button"
    index: 0

- extendedWaitUntil:
    visible: "Earn cashback with Plus!"

- swipe:
    direction: DOWN

# Commission

- scrollUntilVisible:
    element:
      id: "label_text_view"
      index: 3
    centerElement: true

- copyTextFrom:
    id: "label_text_view"
    index: 3

- assertTrue: ${maestro.copiedText == "Commission"}

- copyTextFrom:
    id: "value_text_view"
    index: 3

- assertTrue: ${maestro.copiedText == "£0.00"}

- tapOn:
    id: "info_button"
    index: 1

- extendedWaitUntil:
    visible: "Wealthyhood is commission-free, which means we charge ZERO COMMISSIONS .*"

- swipe:
    direction: DOWN

# Latest price

- scrollUntilVisible:
    element:
      id: "label_text_view"
      index: 4
    centerElement: true

- copyTextFrom:
    id: "label_text_view"
    index: 4

- assertTrue: ${maestro.copiedText == "Latest price"}

# ETFs execution time

- scrollUntilVisible:
    element:
      id: "label_text_view"
      index: 5
    centerElement: true

- copyTextFrom:
    id: "label_text_view"
    index: 5

- assertTrue: ${maestro.copiedText == "ETFs execution time"}

- tapOn:
    id: "info_button"
    index: 2

- extendedWaitUntil:
    visible: "ETF orders are executed within a dedicated trading window at.*"

- swipe:
    direction: DOWN

# Status

- scrollUntilVisible:
    element:
      id: "label_text_view"
      index: 6
    centerElement: true

- copyTextFrom:
    id: "label_text_view"
    index: 6

- assertTrue: ${maestro.copiedText == "Status"}

- copyTextFrom:
    id: "value_text_view"
    index: 6

- assertTrue: ${maestro.copiedText == "Pending"}

# Check there is no other info row

- assertNotVisible:
    id: "label_text_view"
    index: 7
