appId: com.wealthyhood.wealthyhood
jsEngine: graaljs
---

# No. of shares

- scrollUntilVisible:
    element:
      id: "label_text_view"
      index: 0
    centerElement: true

- copyTextFrom:
    id: "label_text_view"
    index: 0

- assertTrue: ${maestro.copiedText == "No. of shares"}

- copyTextFrom:
    id: "value_text_view"
    index: 0

- assertTrue: ${maestro.copiedText == NUMBER_OF_SHARES}

# Amount (est.)

- scrollUntilVisible:
    element:
      id: "label_text_view"
      index: 1
    centerElement: true

- copyTextFrom:
    id: "label_text_view"
    index: 1

- assertTrue: ${maestro.copiedText == "Amount (est.)"}

# Latest price

- scrollUntilVisible:
    element:
      id: "label_text_view"
      index: 2
    centerElement: true

- copyTextFrom:
    id: "label_text_view"
    index: 2

- assertTrue: ${maestro.copiedText == "Latest price"}

# Commission

- scrollUntilVisible:
    element:
      id: "label_text_view"
      index: 3
    centerElement: true

- copyTextFrom:
    id: "label_text_view"
    index: 3

- assertTrue: ${maestro.copiedText == "Commission"}

- copyTextFrom:
    id: "value_text_view"
    index: 3

- assertTrue: ${maestro.copiedText.slice(0, -2) == "1,00"}

- tapOn:
    id: "info_button"
    index: 0

- extendedWaitUntil:
    visible: "Only express execution is available for ETF sell orders .*"

- swipe:
    direction: DOWN

# FX rate

- scrollUntilVisible:
    element:
      id: "label_text_view"
      index: 4
    centerElement: true

- copyTextFrom:
    id: "label_text_view"
    index: 4

- assertTrue: ${maestro.copiedText == "FX rate"}

- tapOn:
    id: "info_button"
    index: 1

- extendedWaitUntil:
    visible: "About our FX rate"

- swipe:
    direction: DOWN

# ETFs execution time

- scrollUntilVisible:
    element:
      id: "label_text_view"
      index: 5
    centerElement: true

- copyTextFrom:
    id: "label_text_view"
    index: 5

- assertTrue: ${maestro.copiedText == "ETFs execution time"}

- tapOn:
    id: "info_button"
    index: 2

- extendedWaitUntil:
    visible: "ETFs execution time depends on the smart execution mode.*"

- swipe:
    direction: DOWN

# Check there is no other info row

- assertNotVisible:
    id: "label_text_view"
    index: 6
