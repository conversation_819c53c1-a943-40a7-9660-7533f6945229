appId: com.wealthyhood.wealthyhood
jsEngine: graaljs
---

# Check pending transaction

- scrollUntilVisible:
    element:
      id: "status_text_view"
      index: 0
    centerElement: true

- copyTextFrom:
    id: "status_text_view"
    index: 0

- assertTrue: ${maestro.copiedText == "Pending"}

- copyTextFrom:
    id: "secondary_status_text_view"
    index: 0

- assertTrue: ${maestro.copiedText == (NUMBER_OF_SHARES + " shares")}
