appId: com.wealthyhood.wealthyhood
jsEngine: graaljs
---

- runFlow:
    file: ../../../flows/common/LoginFlow.yaml
    env:
      userStatus: "INVESTED_WITH_CASH_GR"

- waitForAnimationToEnd

# Open stock sell

- tapOn: "Invest"
- waitForAnimationToEnd

- scrollUntilVisible:
    element:
      text: "Individual Stocks"
    centerElement: true

- tapOn: "ETFs"

- scrollUntilVisible:
    element:
      text: "iShares FTSE 100 UCITS ETF"
    centerElement: true

- tapOn: "iShares FTSE 100 UCITS ETF"

- extendedWaitUntil:
    visible:
      id: "sell_button"

- tapOn:
    id: "sell_button"

- waitForAnimationToEnd

# Change amount

- tapOn:
    id: "digit_0_button"
- tapOn:
    id: "decimal_separator_button"
- tapOn:
    id: "digit_0_button"
- tapOn:
    id: "digit_0_button"
- tapOn:
    id: "digit_0_button"
- tapOn:
    id: "digit_2_button"

- assertVisible: "You cannot sell ETF shares worth less than .*."

- assertVisible:
    id: "review_button"
    enabled: false
