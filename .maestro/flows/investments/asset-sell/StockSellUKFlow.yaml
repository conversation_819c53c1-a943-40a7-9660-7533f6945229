appId: com.wealthyhood.wealthyhood
jsEngine: graaljs
---

- runFlow:
    file: ../../../flows/common/LoginFlow.yaml
    env:
      userStatus: "INVESTED_WITH_CASH"

- waitForAnimationToEnd

# Open stock sell

- tapOn: "Invest"
- waitForAnimationToEnd

- scrollUntilVisible:
    element:
      text: "Individual Stocks"
    centerElement: true

- tapOn: "Apple"

- extendedWaitUntil:
    visible:
      id: "sell_button"

- tapOn:
    id: "sell_button"

- waitForAnimationToEnd

# Change amount

- tapOn:
    id: "digit_0_button"

- tapOn:
    id: "decimal_separator_button"

- tapOn:
    id: "digit_1_button"

- tapOn:
    id: "digit_5_button"

- tapOn:
    id: "digit_8_button"

- tapOn:
    id: "digit_9_button"

# Check market hours

- assertVisible:
    id: "asset_tag_component"

- tapOn:
    id: "asset_tag_component"

- extendedWaitUntil:
    visible: "Market hours"

- swipe:
    direction: DOWN

# Review order

- tapOn:
    id: "review_button"

- waitForAnimationToEnd

# Check info rows

- runFlow:
    file: common/CheckStockReviewInfoRows.yaml
    env:
      RESIDENCY: "UK"
      NUMBER_OF_SHARES: "0.1589"

# Confirm order

- tapOn:
    id: "button"

- extendedWaitUntil:
    visible: "Order placed!"

- assertVisible: "Sell Apple"

# Check info rows

- runFlow:
    file: common/CheckStockOrderPlacedInfoRowsUK.yaml

# Return to main screen

- tapOn:
    id: "done_button"

- waitForAnimationToEnd

# Check pending transaction

- tapOn:
    id: "fifth_tab_bar_item_image_view"

- extendedWaitUntil:
    visible: "Activity"

- runFlow:
    file: common/CheckPendingTransaction.yaml
    env:
      NUMBER_OF_SHARES: "0.1589"
