appId: com.wealthyhood.wealthyhood
jsEngine: graaljs
---

- runFlow:
    file: ../../../../flows/common/LoginFlow.yaml
    env:
      userStatus: "VERIFIED_WITH_GIFT_GR"

- waitForAnimationToEnd

# Dismiss gift dialog

- extendedWaitUntil:
    visible: "Has sent you a.*"

- swipe:
    direction: DOWN

- extendedWaitUntil:
    notVisible: "Has sent you a.*"

# Open Investments

- tapOn: "Invest"
- waitForAnimationToEnd

- assertVisible: "Investments"

# Open Portfolio Buy

- scrollUntilVisible:
    element:
      text: "Buy"
    centerElement: true

- tapOn: "Buy"
- waitForAnimationToEnd

- tapOn: "One-off"
- waitForAnimationToEnd

# Select gift

- tapOn:
    id: "selected_payment_method_component"

- extendedWaitUntil:
    visible:
      id: "radio_button_image_view"
      index: 0

- tapOn: "Redeem my.*"

- extendedWaitUntil:
    notVisible: "Add bank account"

# Make sure keyboard is disabled

- tapOn:
    id: "digit_1_button"
- tapOn:
    id: "digit_0_button"
- tapOn:
    id: "digit_0_button"

# Select Target portfolio option

- tapOn:
    id: "selected_allocation_component"
- tapOn: "Target portfolio"
- assertVisible: "Target portfolio"

# Review the order

- tapOn: "Review"
- waitForAnimationToEnd

- assertVisible: "Buy Portfolio"

- assertVisible:
    id: "switch_material"
    index: 0
    checked: true

- runFlow:
    when:
      true: ${SHOULD_ENABLE_SMART_EXECUTION == "false"}
    commands:
      - tapOn:
          id: "switch_material"
      - assertVisible:
          id: "switch_material"
          index: 0
          checked: false

# Problem with € symbol. Slice it.

- copyTextFrom:
    id: "value_text_view"
    index: 0
- assertTrue: ${maestro.copiedText.slice(0, -2) == '20,00'}

# Confirm buy

- tapOn: "Confirm Buy"

- extendedWaitUntil:
    visible: "Order placed!"

# Check the order summary
# Problem with € symbol. Slice it.

- copyTextFrom:
    id: "value_text_view"
    index: 0
- assertTrue: ${maestro.copiedText.slice(0, -2) == '20,00'}

# Return to main screen

- tapOn: "Done"
- waitForAnimationToEnd

- assertVisible: "Buy"
- assertVisible: "Activity"
- assertVisible: "Target"

# Open Activity

- tapOn: "Activity"
- waitForAnimationToEnd

# Find the new pending portfolio

- runFlow:
    file: FindNewPendingPortfolioBuyWithGiftFlow.yaml
    env:
      RESIDENCY: "GR"
