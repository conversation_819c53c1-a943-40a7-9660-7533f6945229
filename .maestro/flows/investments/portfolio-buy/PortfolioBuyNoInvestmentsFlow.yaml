appId: com.wealthyhood.wealthyhood
jsEngine: graaljs
---

- runFlow:
    file: ../../../flows/common/LoginFlow.yaml
    env:
      userStatus: "VERIFIED_WITH_TARGET"

- waitForAnimationToEnd

# Open Investments

- tapOn: "Invest"
- waitForAnimationToEnd

- assertVisible: "Investments"

# Open Portfolio Buy

- scrollUntilVisible:
    element:
      text: "Buy"
    centerElement: true

- tapOn: "Buy"
- waitForAnimationToEnd

- tapOn: "Monthly"
- waitForAnimationToEnd

# Check enabled status of allocations

- tapOn:
    id: "selected_allocation_component"

- extendedWaitUntil:
    visible: "Select the allocation to buy"

- assertVisible:
    text: "My investments"
    enabled: false

- tapOn: "My investments"

- extendedWaitUntil:
    visible: "Looks like you don’t have any investments at the moment.*"

- assertVisible:
    text: "Target portfolio"
    enabled: true

# Check info button is clickable

- tapOn:
    id: "info_button"
    index: 0

- extendedWaitUntil:
    visible: "Buy your investments"
