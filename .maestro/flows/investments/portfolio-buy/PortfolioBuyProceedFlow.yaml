appId: com.wealthyhood.wealthyhood
jsEngine: graaljs
---

- runFlow:
    file: ../../../flows/common/LoginFlow.yaml
    env:
      userStatus: "VERIFIED_WITH_TARGET"

- waitForAnimationToEnd

# Open Investments

- tapOn: "Invest"
- waitForAnimationToEnd

- assertVisible: "Investments"

# Open Portfolio Buy

- scrollUntilVisible:
    element:
      text: "Buy"
    centerElement: true

- tapOn: "Buy"
- waitForAnimationToEnd

- tapOn: "Monthly"
- waitForAnimationToEnd

# Enter amount

- tapOn:
    id: "digit_1_button"
- tapOn:
    id: "digit_0_button"
- tapOn:
    id: "digit_0_button"

# Check Proceed button

- assertVisible: "Proceed"
- tapOn: "Proceed"
- waitForAnimationToEnd
- assertVisible: "Choose your bank"
