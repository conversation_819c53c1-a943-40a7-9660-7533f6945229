appId: com.wealthyhood.wealthyhood
jsEngine: graaljs
---
- runFlow:
    file: ../../../flows/common/LoginFlow.yaml
    env:
      userStatus: "VERIFIED_WITH_CASH"

# SCREEN: Home

- waitForAnimationToEnd
- tapOn: "Invest"
- waitForAnimationToEnd
- scrollUntilVisible:
    element:
      text: "Buy"
    centerElement: true
- tapOn:
    text: "Buy"

# SCREEN: Portfolio buy

- waitForAnimationToEnd
- tapOn: "One-off"

# Make sure cash is selected

- tapOn:
    id: "selected_payment_method_component"
- waitForAnimationToEnd
- tapOn: "Cash balance"
- waitForAnimationToEnd

# Continue with portfolio buy

- tapOn:
    id: "digit_1_button"
- tapOn:
    id: "digit_2_button"
- tapOn:
    id: "digit_3_button"
- tapOn:
    id: "action_button"

# SCREEN: Portfolio buy review dialog

- waitForAnimationToEnd
- tapOn:
    id: "button"

# SCREEN: Order placed

- waitForAnimationToEnd
- assertVisible: "Order Placed!"
- tapOn:
    id: "cancel_button"
- waitForAnimationToEnd
- tapOn:
    id: "button1"
- waitForAnimationToEnd
- assertNotVisible: "Set up a monthly investment!"

# SCREEN: Investments

- extendedWaitUntil:
    visible:
      text: "Buy"

- assertVisible: "Activity"
- assertVisible: "Target"
