appId: com.wealthyhood.wealthyhood
jsEngine: graaljs
---

- runFlow:
    file: ../../../flows/common/LoginFlow.yaml
    env:
      userStatus: "INVESTED_WITH_CASH"

- waitForAnimationToEnd

# Open Investments

- tapOn: "Invest"
- waitForAnimationToEnd

- assertVisible: "Investments"

# Open Portfolio Buy

- scrollUntilVisible:
    element:
      text: "Buy"
    centerElement: true

- tapOn: "Buy"
- waitForAnimationToEnd

- tapOn: "One-off"
- waitForAnimationToEnd

# Check cash option is visible

- tapOn:
    id: "selected_payment_method_component"

- extendedWaitUntil:
    visible: "Cash balance"

- swipe:
    direction: DOWN

# Select monthly

- tapOn:
    id: "first_drop_down_component"

- extendedWaitUntil:
    visible: "How often?"

- tapOn: "Monthly"

- extendedWaitUntil:
    notVisible: "How often?"

# Check cash option is not visible

- tapOn:
    id: "selected_payment_method_component"

- extendedWaitUntil:
    visible: "Add bank account"

- assertNotVisible: "Cash balance"
