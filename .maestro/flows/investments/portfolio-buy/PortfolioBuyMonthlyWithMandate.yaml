appId: com.wealthyhood.wealthyhood
jsEngine: graaljs
---

- runFlow:
    file: ../../../flows/common/LoginFlow.yaml
    env:
      userStatus: "VERIFIED_WITH_BANK_MANDATE"

- waitForAnimationToEnd

# Open Investments

- tapOn: "Invest"
- waitForAnimationToEnd

- assertVisible: "Investments"

# Open Portfolio Buy

- scrollUntilVisible:
    element:
      text: "Buy"
    centerElement: true

- tapOn: "Buy"
- waitForAnimationToEnd

- tapOn: "Monthly"
- waitForAnimationToEnd

# Enter amount

- tapOn:
    id: "digit_1_button"
- tapOn:
    id: "digit_0_button"
- tapOn:
    id: "digit_0_button"

# Select date

- tapOn:
    id: "second_drop_down_component"

- extendedWaitUntil:
    visible: "Every month on the:"

- tapOn: "18th"

- extendedWaitUntil:
    notVisible: "Every month on the"

- assertVisible: "18th"

# Review the order

- tapOn: "Review"
- waitForAnimationToEnd

- assertVisible: "Buy Portfolio"

# Problem with € symbol. Slice it.

- copyTextFrom:
    id: "value_text_view"
    index: 0
- assertTrue: ${maestro.copiedText == '£100.00'}

- assertVisible: "Repeating"
- assertVisible: "Every month on the 18th"

# Confirm buy

- tapOn: "Confirm Buy"

# Check Direct Debit setup was successful

- extendedWaitUntil:
    visible: "Your Direct Debit was set up successfully."

# Return to main screen

- extendedWaitUntil:
    visible: "Buy"

- assertVisible: "Activity"
- assertVisible: "Target"

# Open Autopilot

- tapOn: "Autopilot"
- waitForAnimationToEnd

- assertVisible: ".*monthly on the 18th"
