appId: com.wealthyhood.wealthyhood
jsEngine: graaljs
---

- runFlow:
    file: ../../../flows/common/LoginFlow.yaml
    env:
      userStatus: "INVESTED_WITH_CASH"

- waitForAnimationToEnd

# Open Investments

- tapOn: "Invest"
- waitForAnimationToEnd

- assertVisible: "Investments"

# Open Portfolio Buy

- scrollUntilVisible:
    element:
      text: "Buy"
    centerElement: true

- tapOn: "Buy"
- waitForAnimationToEnd

- tapOn: "One-off"
- waitForAnimationToEnd

# Enter amount

- tapOn:
    id: "digit_5_button"
- tapOn:
    id: "digit_0_button"
- tapOn:
    id: "digit_0_button"
- tapOn:
    id: "digit_0_button"

# Review the order

- tapOn: "Review"

# Check the error

- assertVisible: "Your available balance is .*"
