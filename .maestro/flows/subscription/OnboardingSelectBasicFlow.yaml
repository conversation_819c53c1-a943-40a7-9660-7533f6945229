appId: com.wealthyhood.wealthyhood
---
- launchApp:
    appId: "com.wealthyhood.wealthyhood"
    clearState: true
    stopApp: true # optional (true by default): stop the app before launching it
- runScript: ../../scripts/createVerifiedUserScript.js

# SCREEN: Main Austronaut
- tapOn: "Sign in"

# SCREEN: Sign in options
- tapOn: "Use your email"

# SCREEN: Sign in with email
- tapOn: "Your e-mail"
- inputText: "<EMAIL>"
- tapOn: "Send verification code"

# SCREEN: Device passcode
- tapOn:
    id: "com.wealthyhood.wealthyhood:id/first_digit_text_view"
- inputText: 1111
- inputText: 1111

# SCREEN: Notifications
- assertVisible: "Don't miss important updates"
- tapOn: "Not now"

# SCREEN: Plan selection
- swipe:
    from: "Plus"
    direction: "RIGHT"
- assertVisible: "Basic"
- tapOn: "Continue with Basic"

# SCREEN: Portfolio creation
- assertVisible: "Create my portfolio!"

