appId: com.wealthyhood.wealthyhood
jsEngine: graaljs
---
- runFlow:
    file: ../common/LoginFlow.yaml
    env:
      userStatus: "INVESTED_WITH_CASH_GR"

# Open Asset Details

- waitForAnimationToEnd
- tapOn: "Invest"
- waitForAnimationToEnd

- scrollUntilVisible:
    element:
      text: "Individual Stocks"
    centerElement: true

- waitForAnimationToEnd
- tapOn: "ETFs"
- tapOn: "US Stocks (S&P 500)"
- waitForAnimationToEnd

## Click on metric

- scrollUntilVisible:
    element:
      text: "Portfolio allocation"
    centerElement: true

- tapOn: "Portfolio allocation"
- waitForAnimationToEnd

## Check bottom sheet is not visible

- assertVisible: "Portfolio allocation"

- assertNotVisible:
    id: "drag_view"
