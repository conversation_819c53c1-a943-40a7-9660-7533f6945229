appId: com.wealthyhood.wealthyhood
jsEngine: graaljs
---

- runFlow:
    file: ../../flows/common/LoginFlow.yaml
    env:
      userStatus: "INVESTED_WITH_REWARD_GR"

# SCREEN: Home
- waitForAnimationToEnd

- extendedWaitUntil:
    visible: "You’ve unlocked a free share!"

# SCREEN: Reward

- tapOn: "Reveal my reward!"
- waitForAnimationToEnd
- tapOn: "Done!"
- waitForAnimationToEnd
- assertNotVisible: "You’ve unlocked a free share!"
