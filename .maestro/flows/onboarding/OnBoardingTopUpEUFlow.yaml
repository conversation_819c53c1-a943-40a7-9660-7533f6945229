appId: com.wealthyhood.wealthyhood
jsEngine: graaljs
---
- runFlow:
    file: ../common/LoginFlow.yaml
    env:
      userStatus: "VERIFIED_GR"

# Select basic

- swipe:
    from:
      id: "recycler_view"
      index: 1
    direction: RIGHT

- tapOn: "Continue with Basic"

# Make assertions

- extendedWaitUntil:
    visible: "You’re all set!"

- assertVisible: "You’re now ready to fund your account and start investing!"

# Top up account

- tapOn: "Top up your account"

- extendedWaitUntil:
    visible: "Regular bank transfer"

- tapOn: "Regular bank transfer"
- waitForAnimationToEnd

# Go to home

- tapOn: "Done"

- extendedWaitUntil:
    visible: "You’re now ready to start investing!"

# Make assertions

- assertVisible: "Discover stocks & ETFs"
- assertVisible: "Get started with portfolio builder"

# Go to investments

- tapOn: "Invest"

- extendedWaitUntil:
    visible: "You’re now ready to start investing!"

# Make assertions

- assertVisible: "Discover stocks & ETFs"
- assertVisible: "Start with our portfolio builder"
