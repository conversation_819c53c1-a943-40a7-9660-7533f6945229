appId: com.wealthyhood.wealthyhood
jsEngine: graaljs
---
- runFlow:
    file: ../common/LoginFlow.yaml
    env:
      userStatus: "VERIFIED"

# Select basic

- swipe:
    from:
      id: "recycler_view"
      index: 1
    direction: RIGHT

- tapOn: "Continue with Basic"

# Make assertions

- extendedWaitUntil:
    visible: "You’re all set!"

- assertVisible: "You’re now ready to fund your account and start investing!"

# Top up account

- tapOn: "Top up your account"

- extendedWaitUntil:
    visible: "Add money"

# Enter amount

- tapOn:
    id: "digit_1_button"

- tapOn:
    id: "digit_5_button"

- tapOn:
    id: "digit_2_button"

- tapOn: "Review"

# Add bank account

- extendedWaitUntil:
    visible: "Mock UK Payments.*"

- tapOn: "Mock UK Payments.*"
- waitForAnimationToEnd
- tapOn: "Allow"
- waitForAnimationToEnd

- runFlow:
    file: ../common/TruelayerBankAccountLinkingFlow.yaml

- extendedWaitUntil:
    visible: "Review"

# Make deposit

- tapOn: "Review"
- waitForAnimationToEnd
- tapOn: "Confirm"
- waitForAnimationToEnd

- runFlow:
    file: ../common/TruelayerPaymentFlow.yaml

# Check deposit made

- extendedWaitUntil:
    visible: "+£152.00"
