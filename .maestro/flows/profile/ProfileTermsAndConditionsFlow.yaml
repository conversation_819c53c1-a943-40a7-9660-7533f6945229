appId: com.wealthyhood.wealthyhood
jsEngine: graaljs
---
- runFlow:
    file: ../common/LoginFlow.yaml
    env:
      userStatus: "INVESTED_WITH_CASH_GR"

# Open Profile

- waitForAnimationToEnd

- tapOn:
    id: "profile_button"

- extendedWaitUntil:
    visible: "Hi,.*"

# Tap on Terms and conditions

- scrollUntilVisible:
    element:
      text: "App version:.*"
    centerElement: true

- tapOn:
    id: "dummy_terms_view"

# Make assertions

- extendedWaitUntil:
    visible: "TERMS & CONDITIONS"

- extendedWaitUntil:
    visible: "Last updated .*"
