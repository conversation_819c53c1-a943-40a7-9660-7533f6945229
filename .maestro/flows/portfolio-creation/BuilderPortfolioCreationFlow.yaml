appId: com.wealthyhood.wealthyhood
jsEngine: graaljs
---
- runFlow:
    file: ../common/LoginFlow.yaml
    env:
      userStatus: "INVESTED_WITH_CASH_NO_TARGET_GR"

# Open portfolio creation choices

- runFlow:
    file: common/OpenPortfolioCreationChoices.yaml

# Open builder

- tapOn: "Use our portfolio builder"

# SCREEN: Portfolio builder intro

- extendedWaitUntil:
    visible: "Pick your portfolio template"

- tapOn: "Let's go"

- extendedWaitUntil:
    visible: "Asset classes"

# Check asset classes info

- tapOn:
    id: "com.wealthyhood.wealthyhood:id/info_image_button"
    index: 0

- extendedWaitUntil:
    visible: "What is a stock?"

- tapOn:
    point: "50%,10%"

- waitForAnimationToEnd

- tapOn:
    id: "com.wealthyhood.wealthyhood:id/info_image_button"
    index: 1

- extendedWaitUntil:
    visible: "What is a bond?"

- tapOn:
    point: "50%,10%"

- waitForAnimationToEnd

- tapOn: "Next"

- extendedWaitUntil:
    visible: "Geography"

# Select geography

- tapOn: "Global"
- tapOn: "Next"

- extendedWaitUntil:
    visible: "Sectors & industries"

# Check sectors info

- tapOn:
    id: "com.wealthyhood.wealthyhood:id/info_image_button"
    index: 0

- extendedWaitUntil:
    visible: "Investing in tech"

- tapOn:
    point: "50%,7%"

- waitForAnimationToEnd

- tapOn:
    id: "com.wealthyhood.wealthyhood:id/info_image_button"
    index: 1

- extendedWaitUntil:
    visible: "Investing in healthcare"

- tapOn:
    point: "50%,8%"

- waitForAnimationToEnd

#Select sectors

- tapOn: "Technology"
- tapOn: "Healthcare"
- tapOn: "Next"

- extendedWaitUntil:
    visible: "Portfolio weighting"

# Check weighting types

- assertVisible: "Portfolio weighting"
- tapOn: "Equal"
- assertVisible: "Equal weighting"
- tapOn: "Custom"
- tapOn: "About Custom weighting"
- assertVisible: "About ‘Custom’ portfolio weighting"

# Select weighting

- tapOn:
    point: "50%,8%"

- tapOn: "Custom weighting"

# Open portfolio scratchpad

- extendedWaitUntil:
    visible: "Your portfolio template is ready!"

- tapOn: "Let's have a look"

- extendedWaitUntil:
    visible: "Customise your template"

# Add asset

- tapOn: "Add assets"
- waitForAnimationToEnd

- tapOn:
    id: "com.wealthyhood.wealthyhood:id/selection_state_image_view"
    index: 1

- tapOn: "Done"
- waitForAnimationToEnd

# Fix weights

- assertVisible: "Total 101%"

- scrollUntilVisible:
    element:
      text: "AMD"
    direction: "DOWN"
    speed: 40
    visibilityPercentage: 100
    centerElement: true

- assertVisible: "1%"

- tapOn: "Equal weights"
- assertVisible: "Individual Stocks"
- assertVisible: "ETFs"

# Create portfolio

- tapOn: "Create your portfolio!"

- extendedWaitUntil:
    visible: "You’re all set!"

- tapOn: "Done"
- waitForAnimationToEnd

# Check that portfolio was created and investments reloaded

- tapOn: "My Portfolio"

- extendedWaitUntil:
    visible: "My target portfolio"
