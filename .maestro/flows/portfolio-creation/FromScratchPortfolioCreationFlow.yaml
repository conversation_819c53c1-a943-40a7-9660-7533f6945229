appId: com.wealthyhood.wealthyhood
jsEngine: graaljs
---
- runFlow:
    file: ../common/LoginFlow.yaml
    env:
      userStatus: "INVESTED_WITH_CASH_NO_TARGET_GR"

# Open portfolio creation choices

- runFlow:
    file: common/OpenPortfolioCreationChoices.yaml

# Open scratchpad

- tapOn: "Start from scratch"

- extendedWaitUntil:
    visible: "Create your portfolio"

# Open add assets

- tapOn: "Add assets to get started building your portfolio!"

- extendedWaitUntil:
    visible: "Add assets"

# Open asset details

- tapOn: "AMD"

- extendedWaitUntil:
    visible: "$AMD • Chipmaker"

- tapOn: "Close"

- extendedWaitUntil:
    visible: "Add assets"

# Select assets

- tapOn:
    id: "com.wealthyhood.wealthyhood:id/selection_state_image_view"
    index: 1

- tapOn:
    id: "com.wealthyhood.wealthyhood:id/selection_state_image_view"
    index: 3

# Check ETFs

- tapOn: "ETFs"
- waitForAnimationToEnd
- assertVisible: "SPDR MSCI World UCITS ETF"

# Check stocks

- tapOn: "Individual Stocks"
- waitForAnimationToEnd
- assertVisible: "AMD"

# Add newly selected assets
- tapOn: "Done"

- extendedWaitUntil:
    visible: "Create your portfolio"

# Check allocation

- assertVisible: "Insights"
- assertVisible: "100%"
- assertVisible: "Stocks"
- assertVisible: "Create your portfolio!"

# Add more assets

- tapOn: "Add assets"

- extendedWaitUntil:
    visible: "Add assets"

- swipe:
    start: "57%,54%"
    end: "57%,45%"
    duration: 2684

- tapOn:
    id: "com.wealthyhood.wealthyhood:id/selection_state_image_view"
    index: 4

- tapOn: "Done"

- extendedWaitUntil:
    visible: "Create your portfolio"

# Check allocation

- tapOn: "Total 101%"
- assertVisible: "Your total allocation is 101%"

- tapOn:
    point: "50%,50%"

# Change weights

- tapOn: "Equal weights"

# Create portfolio

- tapOn: "Create your portfolio!"

- extendedWaitUntil:
    visible: "You’re all set!"

- tapOn: "Done"
- waitForAnimationToEnd

# Check that portfolio was created and investments reloaded

- tapOn: "My Portfolio"

- extendedWaitUntil:
    visible: "My target portfolio"
