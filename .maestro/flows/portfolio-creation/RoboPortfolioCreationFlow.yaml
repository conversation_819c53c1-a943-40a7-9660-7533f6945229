appId: com.wealthyhood.wealthyhood
jsEngine: graaljs
---
- runFlow:
    file: ../common/LoginFlow.yaml
    env:
      userStatus: "INVESTED_WITH_CASH_NO_TARGET_GR"

# Open portfolio creation choices

- runFlow:
    file: common/OpenPortfolioCreationChoices.yaml

# Open scratchpad

- tapOn: "Ready-made portfolio"

- extendedWaitUntil:
    visible: "Ready-made portfolios"

# Check tabs

- tapOn: "Defensive"
- assertVisible: "Low risk & reward"
- assertVisible: "Select Defensive portfolio"

- tapOn: "Cautious"
- assertVisible: "Low-moderate risk & reward"
- assertVisible: "Select Cautious portfolio"

- tapOn: "Confident"
- assertVisible: "Moderate risk & reward"
- assertVisible: "Select Confident portfolio"

- tapOn: "Growth"
- assertVisible: "High risk & reward"
- assertVisible: "Select Growth portfolio"

# Check expand button

- tapOn:
    id: "expand_button"

- waitForAnimationToEnd

# Check info buttons

- scrollUntilVisible:
    element:
      text: "Income"
    centerElement: true

- tapOn: "Income"

- extendedWaitUntil:
    visible: "Accumulating vs Distributing ETFs"

- swipe:
    direction: DOWN

- extendedWaitUntil:
    notVisible: "Accumulating vs Distributing ETFs"

- scrollUntilVisible:
    element:
      text: "Base currency"
    centerElement: true

- tapOn: "Base currency"

- extendedWaitUntil:
    visible: "What's the base currency of an ETF?"


- swipe:
    direction: DOWN

- extendedWaitUntil:
    notVisible: "What's the base currency of an ETF?"

# Check read more button

- scrollUntilVisible:
    element:
      text: "Read more"
    centerElement: true

- tapOn: "Read more"

- extendedWaitUntil:
    visible: "Smart execution"

- tapOn:
    id: "close_button"

- waitForAnimationToEnd

# Check ready-made portfolios button

- scrollUntilVisible:
    element:
      text: "Read about ready-made portfolios"
    centerElement: true

- tapOn: "Read about ready-made portfolios"

- extendedWaitUntil:
    visible: "About ready-made portfolios"

- swipe:
    direction: DOWN

- waitForAnimationToEnd

# Create portfolio

- tapOn: "Select Growth portfolio"

- extendedWaitUntil:
    visible: "You’re all set!"

- tapOn: "Done"
- waitForAnimationToEnd

# Check that portfolio was created and investments reloaded

- tapOn: "My Portfolio"

- extendedWaitUntil:
    visible: "My target portfolio"
