appId: com.wealthyhood.wealthyhood
jsEngine: graaljs
---
- runFlow:
    file: ../../flows/common/LoginFlow.yaml
    env:
      userStatus: "VERIFIED_SKIPPED_PORTFOLIO"

# SCREEN: Uninvested dashboard
- waitForAnimationToEnd
- assertVisible: "Discover stocks & ETFs"
- assertVisible: "Set up a target portfolio"
# FIXME: we have an unnecessary spinner on the uninvested dashboard
- waitForAnimationToEnd
- tapOn: "Discover stocks & ETFs"

# SCREEN: Discover
- assertVisible: "Discover"

# SCREEN: Discover - Asset classes
- scrollUntilVisible:
    element:
      text: "Asset classes.*"
    direction: "DOWN"
    speed: 40
    visibilityPercentage: 100
    centerElement: true
- assertVisible: "Asset classes.*"

# SCREEN: Discover - Tap on real estate
- swipe:
    start: 90%,70%
    end: 10%,70%
    duration: 1000
- assertVisible: "Real Estate.*"
- tapOn: "Real Estate"

# SCREEN: Asset class - Real Estate Traded Currencies
- assertVisible: "iShares Developed Markets Property UCITS ETF.*"
- assertVisible: "£IWDP • Developed markets real estate.*"
- assertVisible:
    text: "£[0-9.].+"
    rightOf: ".*Developed Markets.*"
    above: ".*US Property.*"

- assertVisible: "iShares US Property UCITS ETF.*"
- assertVisible: "\\$IDUP • US real estate companies.*"
- assertVisible:
    text: "\\$[0-9.].+"
    below: ".*IWDP.*"
    rightOf: ".*US Property.*"
