appId: com.wealthyhood.wealthyhood
jsEngine: graaljs
---
- runFlow:
    file: ../../flows/common/LoginFlow.yaml
    env:
      userStatus: "VERIFIED_SKIPPED_PORTFOLIO_GR"

# SCREEN: Uninvested dashboard
- waitForAnimationToEnd
- assertVisible: "Discover stocks & ETFs"
- assertVisible: "Set up a target portfolio"
# FIXME: we have an unnecessary spinner on the uninvested dashboard
- waitForAnimationToEnd
- tapOn: "Discover stocks & ETFs"

# SCREEN: Discover
- assertVisible: "Discover.*"

# SCREEN: Discover - Popular this week
- assertVisible: "Popular this week.*"

# SCREEN: Discover - Top Movers
- scrollUntilVisible:
    element:
      text: "Top Movers.*"
    direction: "DOWN"
    speed: 40
    visibilityPercentage: 100
    centerElement: true
- assertVisible: "Top Movers.*"

# SCREEN: Discover - Sectors
- scrollUntilVisible:
    element:
      text: "Sectors.*"
    direction: "DOWN"
    speed: 40
    visibilityPercentage: 100
    centerElement: true
- assertVisible: "Sectors.*"
- assertVisible: "Technology.*"
- assertVisible: "Healthcare.*"

# SCREEN: Discover - Popular Index ETFs
- scrollUntilVisible:
    element:
      text: "Popular Index ETFs.*"
    direction: "DOWN"
    speed: 40
    visibilityPercentage: 100
    centerElement: true
- assertVisible: "Popular Index ETFs.*"
- assertVisible: "Vanguard S&P 500 UCITS ETF.*"
- assertVisible: "iShares FTSE 100 UCITS ETF.*"
- assertVisible: "Invesco NASDAQ 100 UCITS ETF.*"

# SCREEN: Discover - Asset classes
- scrollUntilVisible:
    element:
      text: "Asset classes.*"
    direction: "DOWN"
    speed: 40
    visibilityPercentage: 100
    centerElement: true
- assertVisible: "Asset classes.*"
- assertVisible: "Stocks.*"
- assertVisible: "Bonds.*"

# SCREEN: Discover - ETFs
- scrollUntilVisible:
    element:
      text: "ETFs.*"
    direction: "DOWN"
    speed: 40
    visibilityPercentage: 100
    centerElement: true
- assertVisible: "ETFs.*"
- assertVisible: "Vanguard FTSE All-World UCITS ETF.*"

# SCREEN: Discover - Ready-Made
- scrollUntilVisible:
    element:
      text: "Ready-made portfolios.*"
    direction: "DOWN"
    speed: 40
    visibilityPercentage: 100
    centerElement: true
- assertVisible: "Ready-made portfolios.*"
- assertVisible: "Vanguard LifeStrategy 20% Equity UCITS ETF.*"

## SCREEN: Discover - Collections
- scrollUntilVisible:
    element:
      text: "Collections.*"
    direction: "DOWN"
    speed: 40
    visibilityPercentage: 100
    centerElement: true
- assertVisible: "Collections.*"
- assertVisible: "🇺🇸 US stocks.*"
