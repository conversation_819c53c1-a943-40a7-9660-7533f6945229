appId: com.wealthyhood.wealthyhood
jsEngine: graaljs
---

- runFlow:
    file: ../../../flows/common/LoginFlow.yaml
    env:
      userStatus: "INVESTED_WITH_CASH"

- waitForAnimationToEnd

# Open savings cash deposit

- runFlow:
    file: common/OpenSavingsDeposit.yaml

# Select One-off

- runFlow:
    file: common/SelectOneOff.yaml

# Select cash

- runFlow:
    file: common/SelectBankAccount.yaml

# Enter amount

- tapOn:
    id: "digit_1_button"
- tapOn:
    id: "digit_5_button"
- tapOn:
    id: "digit_0_button"

# Review the order

- tapOn: "Review"

- extendedWaitUntil:
    visible: "Add money"

- runFlow:
    file: common/CheckReviewRows.yaml

# Confirm order

- tapOn: "Confirm"
- waitForAnimationToEnd

# Run bank flow

- runFlow:
    file: ../../../flows/common/TruelayerPaymentFlow.yaml

- extendedWaitUntil:
    visible: "GBP interest"

- tapOn: "GBP interest"
- waitForAnimationToEnd

# Check new pending transaction

- runFlow:
    file: common/FindNewPendingSavingsDeposit.yaml
    env:
      TRANSACTION_TITLE: "Deposit"
      TRANSACTION_STATUS: "Pending"
