appId: com.wealthyhood.wealthyhood
jsEngine: graaljs
---

# Check total amount

- scrollUntilVisible:
    element:
      text: "Amount"
    centerElement: true

- copyTextFrom:
    id: "label_text_view"
    index: 0

- assertTrue: ${maestro.copiedText == "Amount"}

- copyTextFrom:
    id: "value_text_view"
    index: 0

- assertTrue: ${maestro.copiedText == "£150.00"}

# Check repeating

- scrollUntilVisible:
    element:
      text: "Repeating"
    centerElement: true

- copyTextFrom:
    id: "label_text_view"
    index: 1

- assertTrue: ${maestro.copiedText == "Repeating"}

- copyTextFrom:
    id: "value_text_view"
    index: 1

- assertTrue: ${maestro.copiedText == "Every month on the 18th"}

# Check there is no other row

- assertNotVisible:
    id: "label_text_view"
    index: 2
