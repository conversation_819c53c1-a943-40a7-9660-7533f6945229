appId: com.wealthyhood.wealthyhood
jsEngine: graaljs
---

- copyTextFrom:
    id: "description_text_view"
    index: 1

- assertTrue: ${maestro.copiedText == BANK_CODE}

- assertVisible:
    id: "radio_button_image_view"
    selected: true
    below:
      id: "radio_button_image_view"
      index: 0

# Check there is no other bank account

- assertNotVisible:
    id: "radio_button_image_view"
    index: 2
