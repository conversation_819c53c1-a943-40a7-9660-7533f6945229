appId: com.wealthyhood.wealthyhood
jsEngine: graaljs
---

# We should be on the top of the Cash screen with everything loaded

- scrollUntilVisible:
    element:
      id: "title_text_view"
      childOf:
        id: "transaction_container_constraint_layout"
      index: 0
    centerElement: true

- copyTextFrom:
    id: "title_text_view"
    childOf:
      id: "transaction_container_constraint_layout"
      index: 0

- assertTrue: ${maestro.copiedText == TRANSACTION_TITLE}

- copyTextFrom:
    id: "status_text_view"
    childOf:
      id: "transaction_container_constraint_layout"
      index: 0

- assertTrue: ${maestro.copiedText == TRANSACTION_STATUS}

- assertVisible: "£150.00"
