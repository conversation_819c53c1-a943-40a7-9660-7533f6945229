appId: com.wealthyhood.wealthyhood
jsEngine: graaljs
---

- runFlow:
    file: ../../../flows/common/LoginFlow.yaml
    env:
      userStatus: "VERIFIED_WITH_TARGET"

- waitForAnimationToEnd

# Open savings cash deposit

- runFlow:
    file: common/OpenSavingsDeposit.yaml

# Select One-off

- runFlow:
    file: common/SelectOneOff.yaml

# Enter amount

- tapOn:
    id: "digit_1_button"
- tapOn:
    id: "digit_5_button"
- tapOn:
    id: "digit_0_button"

# Check there is no payment methods drop down

- assertNotVisible:
    id: "selected_payment_method_component"

# Add new bank account

- tapOn: "Proceed"
- waitForAnimationToEnd

- tapOn: "Mock UK Payments - Redirect Flow"

- extendedWaitUntil:
    visible: "Link your Mock UK Payments - Redirect Flow account"

- scrollUntilVisible:
    element:
      text: "Allow"
    centerElement: true

- tapOn: "Allow"
- waitForAnimationToEnd

# Run bank flow

- runFlow:
    file: ../../../flows/common/TruelayerBankAccountLinkingFlow.yaml

- extendedWaitUntil:
    visible: "Mock UK Payments - Redirect Flow.*"

# Open payment methods

- tapOn:
    id: "selected_payment_method_component"

- extendedWaitUntil:
    visible: "Add bank account"

# Check selected bank account

- runFlow:
    file: common/CheckSingleSelectedBankAccount.yaml
    env:
      BANK_CODE: "01-21-31"

# Close payment methods dialog

- swipe:
    direction: DOWN

# Review the order

- tapOn: "Review"
- waitForAnimationToEnd

# Check info rows

- runFlow:
    file: common/CheckReviewRows.yaml

# Confirm deposit

- tapOn: "Confirm"
- waitForAnimationToEnd

# Run bank flow

- runFlow:
    file: ../../../flows/common/TruelayerPaymentFlow.yaml

- extendedWaitUntil:
    visible: "GBP interest"

- tapOn: "GBP interest"
- waitForAnimationToEnd

# Check pending transaction

- runFlow:
    file: common/FindNewPendingSavingsDeposit.yaml
    env:
      TRANSACTION_TITLE: "Deposit"
      TRANSACTION_STATUS: "Pending"
