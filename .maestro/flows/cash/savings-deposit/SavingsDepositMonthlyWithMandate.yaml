appId: com.wealthyhood.wealthyhood
jsEngine: graaljs
---

- runFlow:
    file: ../../../flows/common/LoginFlow.yaml
    env:
      userStatus: "VERIFIED_WITH_BANK_MANDATE"

- waitForAnimationToEnd

# Open savings cash deposit

- runFlow:
    file: common/OpenSavingsDeposit.yaml

# Select Monthly

- runFlow:
    file: common/SelectMonthly.yaml

# Enter amount

- tapOn:
    id: "digit_1_button"
- tapOn:
    id: "digit_5_button"
- tapOn:
    id: "digit_0_button"

# Select date

- tapOn:
    id: "second_drop_down_component"

- extendedWaitUntil:
    visible: "Every month on the:"

- tapOn: "18th"

- extendedWaitUntil:
    notVisible: "Every month on the"

- assertVisible: "18th"

# Review the order

- tapOn: "Review"
- waitForAnimationToEnd
- assertVisible: "Set up a monthly deposit"

# Check info rows

- runFlow:
    file: common/CheckMonthlyReviewRows.yaml

# Confirm deposit

- tapOn: "Confirm monthly deposit"

# Check Direct Debit setup was successful

- extendedWaitUntil:
    visible: "Your Direct Debit was set up successfully."

# Return to main screen

- extendedWaitUntil:
    visible: "GBP interest"

# Open Autopilot

- tapOn: "Autopilot"
- waitForAnimationToEnd

- scrollUntilVisible:
    element: "Earn .* interest"

- assertVisible: ".*monthly on the 18th"
