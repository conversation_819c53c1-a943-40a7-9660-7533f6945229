appId: com.wealthyhood.wealthyhood
jsEngine: graaljs
---

- runFlow:
    file: ../../../flows/common/LoginFlow.yaml
    env:
      userStatus: "INVESTED_WITH_CASH"

- waitForAnimationToEnd

# Open savings cash deposit

- runFlow:
    file: common/OpenSavingsDeposit.yaml

# Select One-off

- runFlow:
    file: common/SelectOneOff.yaml

# Check cash option is visible

- tapOn:
    id: "selected_payment_method_component"

- extendedWaitUntil:
    visible: "Cash balance"

- swipe:
    direction: DOWN

# Select monthly

- tapOn:
    id: "first_drop_down_component"

- runFlow:
    file: common/SelectMonthly.yaml

# Check cash option is not visible

- tapOn:
    id: "selected_payment_method_component"

- extendedWaitUntil:
    visible: "Add bank account"

- assertNotVisible: "Cash balance"
