appId: com.wealthyhood.wealthyhood
jsEngine: graaljs
---

- runFlow:
    file: ../../../flows/common/LoginFlow.yaml
    env:
      userStatus: "INVESTED_WITH_CASH"

- waitForAnimationToEnd

# Open savings cash deposit

- runFlow:
    file: common/OpenSavingsDeposit.yaml

# Select Monthly

- runFlow:
    file: common/SelectMonthly.yaml

# Enter amount

- tapOn:
    id: "digit_1_button"
- tapOn:
    id: "digit_5_button"
- tapOn:
    id: "digit_0_button"

# Select date

- tapOn:
    id: "second_drop_down_component"

- extendedWaitUntil:
    visible: "Every month on the:"

- tapOn: "18th"

- extendedWaitUntil:
    notVisible: "Every month on the"

- assertVisible: "18th"

# Review the order

- tapOn: "Review"
- waitForAnimationToEnd
- assertVisible: "Set up a monthly deposit"

# Check info rows

- runFlow:
    file: common/CheckMonthlyReviewRows.yaml

# Confirm deposit

- tapOn: "Confirm monthly deposit"

# Go through Direct Debit setup process

- extendedWaitUntil:
    visible: "Set up a Direct Debit"

- tapOn: "Set up payment method"

- extendedWaitUntil:
    visible: "Your Direct Debit was set up successfully."

- tapOn: "Next"

- extendedWaitUntil:
    visible: "Your Direct Debit schedule"

# Return to main screen

- tapOn: "Got it"

- extendedWaitUntil:
    visible: "GBP interest"

# Open Autopilot

- tapOn: "Autopilot"
- waitForAnimationToEnd

- scrollUntilVisible:
    element: "Earn .* interest"

- assertVisible: ".*monthly on the 18th"
