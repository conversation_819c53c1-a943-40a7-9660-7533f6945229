appId: com.wealthyhood.wealthyhood
jsEngine: graaljs
---

- runFlow:
    file: ../../../flows/common/LoginFlow.yaml
    env:
      userStatus: "INVESTED_WITH_CASH"

- waitForAnimationToEnd

# Open savings cash deposit

- runFlow:
    file: common/OpenSavingsDeposit.yaml

# Select One-off

- runFlow:
    file: common/SelectOneOff.yaml

# Select cash

- runFlow:
    file: common/SelectCash.yaml

# Enter amount

- tapOn:
    id: "digit_1_button"
- tapOn:
    id: "digit_5_button"
- tapOn:
    id: "digit_0_button"

# Review the order

- tapOn: "Review"

- extendedWaitUntil:
    visible: "Add money"

- runFlow:
    file: common/CheckReviewRows.yaml

# Confirm order

- tapOn: "Confirm"

- extendedWaitUntil:
    visible: "GBP interest"

- waitForAnimationToEnd

# Check new pending transaction

- runFlow:
    file: common/FindNewPendingSavingsDeposit.yaml
    env:
      TRANSACTION_TITLE: "Cash Balance -> Savings GBP"
      TRANSACTION_STATUS: "Processing"
