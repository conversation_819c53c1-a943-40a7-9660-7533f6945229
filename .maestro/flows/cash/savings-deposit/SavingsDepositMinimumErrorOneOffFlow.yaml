appId: com.wealthyhood.wealthyhood
jsEngine: graaljs
---

- runFlow:
    file: ../../../flows/common/LoginFlow.yaml
    env:
      userStatus: "INVESTED_WITH_CASH"

- waitForAnimationToEnd

# Open savings cash deposit

- runFlow:
    file: common/OpenSavingsDeposit.yaml

# Select One-off

- runFlow:
    file: common/SelectOneOff.yaml

# Enter amount

- tapOn:
    id: "digit_0_button"
- tapOn:
    id: "decimal_separator_button"
- tapOn:
    id: "digit_1_button"

# Review the order

- tapOn: "Review"

# Check error

- assertVisible: "You can only deposit £1 or more."
