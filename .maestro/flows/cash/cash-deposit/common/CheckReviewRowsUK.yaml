appId: com.wealthyhood.wealthyhood
jsEngine: graaljs
---

# Check total amount

- scrollUntilVisible:
    element:
      text: "Amount"
    centerElement: true

- copyTextFrom:
    id: "label_text_view"
    index: 0

- assertTrue: ${maestro.copiedText == "Amount"}

- copyTextFrom:
    id: "value_text_view"
    index: 0

- assertTrue: ${maestro.copiedText == "£250.00"}

# Check arriving

- scrollUntilVisible:
    element:
      text: "Arriving"
    centerElement: true

- copyTextFrom:
    id: "label_text_view"
    index: 1

- assertTrue: ${maestro.copiedText == "Arriving"}

- copyTextFrom:
    id: "value_text_view"
    index: 1

- assertTrue: ${maestro.copiedText == "Instantly"}

# Check there is no other row

- assertNotVisible:
    id: "label_text_view"
    index: 2
