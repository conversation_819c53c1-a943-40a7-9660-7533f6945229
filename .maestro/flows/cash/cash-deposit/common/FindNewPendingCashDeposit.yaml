appId: com.wealthyhood.wealthyhood
jsEngine: graaljs
---

# We should be on the top of the Cash screen with everything loaded

- scrollUntilVisible:
    element:
      id: "title_text_view"
      childOf:
        id: "transaction_container_constraint_layout"
      index: 0
    centerElement: true

- assertVisible:
    id: "title_text_view"
    childOf:
      id: "transaction_container_constraint_layout"
      index: 0
    text: "Deposit"

- assertVisible:
    id: "status_text_view"
    childOf:
      id: "transaction_container_constraint_layout"
      index: 0
    text: "Pending"

- assertVisible: "+£250.00"
