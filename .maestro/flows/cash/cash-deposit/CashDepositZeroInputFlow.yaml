appId: com.wealthyhood.wealthyhood
jsEngine: graaljs
---

- runFlow:
    file: ../../../flows/common/LoginFlow.yaml
    env:
      userStatus: "INVESTED_WITH_CASH"

- waitForAnimationToEnd

# Open Cash

- tapOn: "Cash"
- waitForAnimationToEnd

# Open cash deposit

- tapOn: "Add money"
- waitForAnimationToEnd

## Check button is disabled

- assertVisible:
    id: "review_button"
    enabled: false

# Change amount

- tapOn:
    id: "digit_0_button"

## Check button is still disabled

- assertVisible:
    id: "review_button"
    enabled: false

# Change amount

- tapOn:
    id: "decimal_separator_button"
- tapOn:
    id: "digit_0_button"

## Check button is still disabled

- assertVisible:
    id: "review_button"
    enabled: false

# Change amount

- tapOn:
    id: "backspace_button"
- tapOn:
    id: "backspace_button"
- tapOn:
    id: "backspace_button"
- tapOn:
    id: "digit_1_button"
- tapOn:
    id: "digit_2_button"
- tapOn:
    id: "digit_5_button"

## Check button is enabled

- assertVisible:
    id: "review_button"
    enabled: true

# Review the order

- tapOn: "Review"
- waitForAnimationToEnd
- assertVisible: "Confirm"
