appId: com.wealthyhood.wealthyhood
jsEngine: graaljs
---

- runFlow:
    file: ../../../flows/common/LoginFlow.yaml
    env:
      userStatus: "VERIFIED_SKIPPED_PORTFOLIO"

# SCREEN: Uninvested dashboard
- waitForAnimationToEnd
- tapOn: "Cash"
- waitForAnimationToEnd
- assertVisible: "Cash balance"
- copyTextFrom:
    id: "amount_text_view"
    index: 0
- evalScript: ${maestro.copiedText = "£0.00"}
- assertVisible: "Withdraw"
- tapOn: "Withdraw"
- waitForAnimationToEnd
- copyTextFrom:
    id: "amount_text_view"
    index: 0
- evalScript: ${maestro.copiedText = "£0.00"}
