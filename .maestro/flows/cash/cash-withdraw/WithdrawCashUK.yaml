appId: com.wealthyhood.wealthyhood
jsEngine: graaljs
---

- runFlow:
    file: ../../../flows/common/LoginFlow.yaml
    env:
      userStatus: "INVESTED_WITH_CASH"

# SCREEN: Uninvested dashboard
- waitForAnimationToEnd
- tapOn: "Cash"
- waitForAnimationToEnd
- assertVisible: "Cash balance"
- assertVisible: "Withdraw"
- tapOn: "Withdraw"

# SCREEN: Withdraw view
- assertVisible: "Available to withdraw .*"
- assertVisible:
    id: "selected_payment_method_component"
- tapOn:
    id: "selected_payment_method_component"
- waitForAnimationToEnd
- tapOn:
    text: "Mock UK Payments .*"
- waitForAnimationToEnd
- assertVisible:
    text: "Mock UK Payments .*"
- tapOn:
    id: "digit_1_button"
- tapOn:
    id: "digit_2_button"
- tapOn:
    id: "review_button"
    
# SCREEN: Review
- copyTextFrom:
    id: "value_text_view"
    index: 0
- evalScript: ${maestro.copiedText = "£12.00"}
- copyTextFrom:
    id: "value_text_view"
    index: 1
- evalScript: ${maestro.copiedText = "2 – 3 days"}
- tapOn:
    id: "button"
- extendedWaitUntil:
    visible: "Cash balance"
- copyTextFrom:
    id: "amount_text_view"
    index: 0
- evalScript: ${maestro.copiedText = "£988.00"}

