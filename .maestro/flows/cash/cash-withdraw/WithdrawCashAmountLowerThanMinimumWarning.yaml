appId: com.wealthyhood.wealthyhood
jsEngine: graaljs
---

- runFlow:
    file: ../../../flows/common/LoginFlow.yaml
    env:
      userStatus: "INVESTED_WITH_CASH"

# SCREEN: Uninvested dashboard
- waitForAnimationToEnd
- tapOn: "Cash"
- waitForAnimationToEnd
- assertVisible: "Cash balance"
- assertVisible: "Withdraw"
- tapOn: "Withdraw"

# SCREEN: Withdraw view
- assertVisible: "Available to withdraw .*"
- assertVisible:
    id: "selected_payment_method_component"
- tapOn:
    id: "selected_payment_method_component"
- waitForAnimationToEnd
- tapOn:
    text: "Mock UK Payments .*"
- waitForAnimationToEnd
- assertVisible:
    text: "Mock UK Payments .*"
- tapOn:
    id: "digit_0_button"
- tapOn:
    id: "decimal_separator_button"
- tapOn:
    id: "digit_0_button"
- tapOn:
    id: "digit_1_button"
    
# SCREEN: Review
- assertVisible: "You can only withdraw .*"
