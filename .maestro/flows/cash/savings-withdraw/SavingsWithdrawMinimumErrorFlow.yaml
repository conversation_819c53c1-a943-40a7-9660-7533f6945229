appId: com.wealthyhood.wealthyhood
jsEngine: graaljs
---

- runFlow:
    file: ../../../flows/common/LoginFlow.yaml
    env:
      userStatus: "INVESTED_WITH_SAVINGS_GR"

- waitForAnimationToEnd

# Try to open savings withdraw

- runFlow:
    file: common/OpenSavingsWithdraw.yaml

# Enter amount

- tapOn:
    id: "digit_0_button"
- tapOn:
    id: "decimal_separator_button"
- tapOn:
    id: "digit_0_button"
- tapOn:
    id: "digit_1_button"

# Check error

- assertVisible: "You can only withdraw 0,02.* or more."

- assertVisible:
    id: "primary_button"
    enabled: false
