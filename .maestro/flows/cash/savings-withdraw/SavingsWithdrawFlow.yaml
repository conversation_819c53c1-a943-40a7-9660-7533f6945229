appId: com.wealthyhood.wealthyhood
jsEngine: graaljs
---

- runFlow:
    file: ../../../flows/common/LoginFlow.yaml
    env:
      userStatus: "INVESTED_WITH_SAVINGS_GR"

- waitForAnimationToEnd

# Try to open savings withdraw

- runFlow:
    file: common/OpenSavingsWithdraw.yaml

# Enter amount

- tapOn:
    id: "digit_0_button"
- tapOn:
    id: "decimal_separator_button"
- tapOn:
    id: "digit_3_button"
- tapOn:
    id: "digit_4_button"

# Execute withdraw

- tapOn:
    id: "primary_button"
    text: "Withdraw"

# Make assertion

- extendedWaitUntil:
    visible:
      id: "amount_text_view"
      text: "0,34.*"

- copyTextFrom:
    id: "title_text_view"
    index: 3

- assertTrue: ${maestro.copiedText == "Savings EUR -> Cash Balance"}

- copyTextFrom:
    id: "status_text_view"
    index: 0

- assertTrue: ${maestro.copiedText == "Processing"}
