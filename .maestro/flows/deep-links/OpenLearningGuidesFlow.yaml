appId: com.wealthyhood.wealthyhood
jsEngine: graaljs
---
- stopApp

# Open Wealthyhub from deep link

- openLink: https://wealthyhood.onelink.me/TwZO/wealthyhubinsights
- waitForAnimationToEnd

# Enter PIN

- runFlow:
    when:
      visible: "Enter your passcode"
    commands:
      - tapOn: "1"
      - tapOn: "1"
      - tapOn: "1"
      - tapOn: "1"
- waitForAnimationToEnd

# Check Wealthyhub is open with insights tab selected

- assertVisible: "Learning"
- assertVisible:
    text: "Analyst insights"
    selected: true
- assertVisible:
    text: "Learning guides"
    selected: false
