appId: com.wealthyhood.wealthyhood
jsEngine: graaljs
---
- stopApp

# Open article from deep link

- openLink: https://wealthyhood.onelink.me/TwZO/wealthyhubinsights?documentId=6808f5ea8858bdfeea7e3307
- waitForAnimationToEnd

# Enter PIN

- runFlow:
    when:
      visible: "Enter your passcode"
    commands:
      - tapOn: "1"
      - tapOn: "1"
      - tapOn: "1"
      - tapOn: "1"
- waitForAnimationToEnd

# Check article is open, not Wealthyhub listing the article

- assertNotVisible: "Learning"
- assertNotVisible: "Analyst insights"
- assertNotVisible: "Learning guides"

- assertVisible: ".*Cold Water On Eurozone.*"

# Open another article from deep link

- openLink: https://wealthyhood.onelink.me/TwZO/wealthyhubinsights?documentId=6807c08a7c5ab675f2f75f83
- waitForAnimationToEnd

# Check second article is open, not Wealthyhub listing the article

- assertNotVisible: "Learning"
- assertNotVisible: "Analyst insights"
- assertNotVisible: "Learning guides"

- assertVisible: ".*Ways To Take The.*"

# Go back

- tapOn:
    id: "close_button"
- waitForAnimationToEnd

# Check first article is open, not Wealthyhub listing the article

- assertNotVisible: "Learning"
- assertNotVisible: "Analyst insights"
- assertNotVisible: "Learning guides"

- assertVisible: ".*Cold Water On Eurozone.*"

# Go back

- tapOn:
    id: "close_button"
- waitForAnimationToEnd

# Check main screen is open

- assertVisible: "Invest"
- assertVisible: "Learn"
- assertVisible: "Home"
- assertVisible: "Autopilot"
- assertVisible: "Cash"
