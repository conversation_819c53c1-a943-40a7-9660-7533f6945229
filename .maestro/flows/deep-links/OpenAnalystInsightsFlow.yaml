appId: com.wealthyhood.wealthyhood
jsEngine: graaljs
---
- stopApp

# Open Wealthyhub from deep link

- openLink: https://wealthyhood.onelink.me/TwZO/wealthyhubguides
- waitForAnimationToEnd

# Enter PIN

- runFlow:
    when:
      visible: "Enter your passcode"
    commands:
      - tapOn: "1"
      - tapOn: "1"
      - tapOn: "1"
      - tapOn: "1"
- waitForAnimationToEnd

# Check Wealthyhub is open with guides tab selected

- assertVisible: "Learning"
- assertVisible:
    text: "Analyst insights"
    selected: false
- assertVisible:
    text: "Learning guides"
    selected: true
