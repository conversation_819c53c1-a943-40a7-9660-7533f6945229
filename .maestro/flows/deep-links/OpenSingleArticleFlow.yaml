appId: com.wealthyhood.wealthyhood
jsEngine: graaljs
---
- stopApp

# Open article from deep link

- openLink: https://wealthyhood.onelink.me/TwZO/wealthyhubinsights?documentId=6808f5ea8858bdfeea7e3307
- waitForAnimationToEnd

# Enter PIN

- runFlow:
    when:
      visible: "Enter your passcode"
    commands:
      - tapOn: "1"
      - tapOn: "1"
      - tapOn: "1"
      - tapOn: "1"
- waitForAnimationToEnd

# Check article is open, not Wealthyhub listing the article

- assertNotVisible: "Learning"
- assertNotVisible: "Analyst insights"
- assertNotVisible: "Learning guides"

- assertVisible: ".*Cold Water On Eurozone.*"
