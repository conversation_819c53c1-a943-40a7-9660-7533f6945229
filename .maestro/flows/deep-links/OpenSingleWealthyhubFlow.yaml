appId: com.wealthyhood.wealthyhood
jsEngine: graaljs
---
- stopApp

# Open Wealthyhub from deep link

- openLink: https://wealthyhood.onelink.me/TwZO/wealthyhubinsights
- waitForAnimationToEnd

# Enter PIN

- runFlow:
    when:
      visible: "Enter your passcode"
    commands:
      - tapOn: "1"
      - tapOn: "1"
      - tapOn: "1"
      - tapOn: "1"
- waitForAnimationToEnd

# Check Wealthyhub is open

- assertVisible: "Learning"
- assertVisible: "Analyst insights"
- assertVisible: "Learning guides"

# Open article from deep link

- openLink: https://wealthyhood.onelink.me/TwZO/wealthyhubinsights?documentId=6808f5ea8858bdfeea7e3307
- waitForAnimationToEnd

# Check article is open, not Wealthyhub listing the article

- assertNotVisible: "Learning"
- assertNotVisible: "Analyst insights"
- assertNotVisible: "Learning guides"

- assertVisible: ".*Cold Water On Eurozone.*"

# Open Wealthyhub from deep link

- openLink: https://wealthyhood.onelink.me/TwZO/wealthyhubinsights
- waitForAnimationToEnd

# Check Wealthyhub is open

- assertVisible: "Learning"
- assertVisible: "Analyst insights"
- assertVisible: "Learning guides"

# Go back

- tapOn:
    id: "close_button"
- waitForAnimationToEnd

# Check main screen is open

- assertVisible: "Invest"
- assertVisible: "Learn"
- assertVisible: "Home"
- assertVisible: "Autopilot"
- assertVisible: "Cash"
