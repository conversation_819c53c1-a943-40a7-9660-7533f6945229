appId: com.wealthyhood.wealthyhood
jsEngine: graaljs
---
# SCREEN: Truelayer - Connect your account
- runFlow:
    when:
      platform: Android
    commands:
    - runFlow:
          when:
            visible: "Use without an account"
          commands:
            - tapOn: "Use without an account"
- runFlow:
    when:
      platform: Android
    commands:
      - extendedWaitUntil:
          visible: "Wealthkernel"

- runFlow:
    when:
      visible: "Select your bank"
    commands:
      - tapOn: "Mock UK Payments.*"

- extendedWaitUntil:
    visible: "Go to bank"
- tapOn: "Go to bank"

# SCREEN: Truelayer - Online Banking Portal
- tapOn: "Enter username"
- inputText: "test_executed"
- hideKeyboard
- scrollUntilVisible:
    element:
      text: "3rd"
    direction: "DOWN"
    speed: 40
    visibilityPercentage: 100
    centerElement: true
- tapOn: "3rd"
- inputText: 1
- tapOn: "4th"
- inputText: 1
- tapOn: "6th"
- inputText: 1

- hideKeyboard

- scrollUntilVisible:
    element:
      text: "Continue"
    direction: "DOWN"
    speed: 40
    visibilityPercentage: 100
- tapOn: "Continue"

# SCREEN: Select account & confirm payment
- assertVisible: "Select account and confirm payment"
- scrollUntilVisible:
    element:
      text: "Continue"
    direction: "DOWN"
    speed: 40
    visibilityPercentage: 100
- tapOn: "Continue"

# Enter PIN

- extendedWaitUntil:
    visible: "Enter your passcode"

- repeat:
    times: 4
    commands:
      - tapOn:
          id: "one_button"
