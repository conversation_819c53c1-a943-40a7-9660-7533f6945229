appId: com.wealthyhood.wealthyhood
jsEngine: graaljs
---
- launchApp:
    appId: "com.wealthyhood.wealthyhood"
    clearState: true
    stopApp: true # optional (true by default): stop the app before launching it
- runScript:
    file: ../../scripts/createUserScript.js

# SCREEN: Main Austronaut
- tapOn: "Sign in"

# SCREEN: Sign in options
- tapOn: "Use your email"

# SCREEN: Sign in with email
- tapOn: "Your e-mail"
- inputText: "<EMAIL>"
- tapOn: "Send verification code"
- waitForAnimationToEnd

# SCREEN: Device passcode
- tapOn:
    id: "com.wealthyhood.wealthyhood:id/first_digit_text_view"
- inputText: 1111
- inputText: 1111

# SCREEN: Notifications
- runFlow:
    when:
      visible: "Don't miss important updates"
    commands:
      - tapOn:
          text: "Not now"

- waitForAnimationToEnd
- runFlow:
    when:
      visible: "How would you rate your experience so far?"
    commands:
      - tapOn:
          point: "50%,10%"
