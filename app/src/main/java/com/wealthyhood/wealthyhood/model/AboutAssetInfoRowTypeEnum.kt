package com.wealthyhood.wealthyhood.model

sealed class AboutAssetInfoRowTypeEnum {
    data object Ticker : AboutAssetInfoRowTypeEnum()
    data object Exchange : AboutAssetInfoRowTypeEnum()
    data object IsIN : AboutAssetInfoRowTypeEnum()
    data object Sector : AboutAssetInfoRowTypeEnum()
    data object Industry : AboutAssetInfoRowTypeEnum()
    data object CEO : AboutAssetInfoRowTypeEnum()
    data object Headquarters : AboutAssetInfoRowTypeEnum()
    data object Employees : AboutAssetInfoRowTypeEnum()
    data object Website : AboutAssetInfoRowTypeEnum()
    class ETFName(val labelText: String?) : AboutAssetInfoRowTypeEnum()
    data object Provider : AboutAssetInfoRowTypeEnum()
    data object Index : AboutAssetInfoRowTypeEnum()
    data object Replication : AboutAssetInfoRowTypeEnum()
    data object AssetClass : AboutAssetInfoRowTypeEnum()
    data object Allocation : AboutAssetInfoRowTypeEnum()
}
