package com.wealthyhood.wealthyhood.model

import androidx.annotation.ColorInt
import androidx.annotation.DrawableRes

data class ConfirmationReceiptScreenArgs(
    val receiptType: ConfirmationReceiptTypeEnum?,
    val orderID: String?,
    val displayOrderID: String?,
    val title: String?,
    @DrawableRes val iconDrawableRes: Int?,
    val iconURI: String?,
    val shouldShowFirstInfoColumnOnly: Boolean,
    val amount: String?,
    val date: String?,
    val time: String?,
    val shares: String?,
    val perShare: String?,
    val commission: String?,
    val fxRate: String?,
    val sideDescription: String?,
    val isin: String?,
    val firm: String?,
    @ColorInt val color: Int?,
    val userFullName: String?
)
