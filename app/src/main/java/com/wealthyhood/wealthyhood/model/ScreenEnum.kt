package com.wealthyhood.wealthyhood.model

sealed class ScreenEnum {
    object ForceUpdate : ScreenEnum()
    object GetStarted : ScreenEnum()
    object Pin : ScreenEnum()
    object NotificationsPermission : ScreenEnum()
    object PinSetup : ScreenEnum()
    object BiometricsSetup : ScreenEnum()
    object Plans : ScreenEnum()
    object Welcome : ScreenEnum()
    object RedeemReferralCode : ScreenEnum()
    object WelcomeWithReferral : ScreenEnum()
    object Dashboard : ScreenEnum()
    object ClosingAccount : ScreenEnum()
    object Undefined : ScreenEnum()
    object VerificationCompleted : ScreenEnum()
    class KYCIncomplete(val args: KYCIncompleteScreenArgs) : ScreenEnum()
    object VerificationPending : ScreenEnum()
    object WealthyBitesSignUp : ScreenEnum()
    object PreFetchWaiting : ScreenEnum()
    object WaitingList : ScreenEnum()
}
