package com.wealthyhood.wealthyhood.model

data class SetupDirectDebitScreenArgs(
    val savingsProductID: String?,
    val subtitle: String?,
    val portfolioBuyAmount: Double?,
    val subscriptionID: String?,
    val subscriptionPriceAPIKey: String?,
    val bankAccountID: String?,
    val bankSortCode: String?,
    val bankAccountNumber: String?,
    val repeatingText: String?,
    val shouldShowPlanActivationCompletedWithOptionsScreen: Boolean,
    val shouldShowPaymentMethodUpdatedScreen: Boolean,
    val allocationMethod: String?,
    val dayOfMonth: Int?
)
