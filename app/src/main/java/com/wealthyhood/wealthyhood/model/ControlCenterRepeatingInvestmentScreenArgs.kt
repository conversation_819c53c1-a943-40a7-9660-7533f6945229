package com.wealthyhood.wealthyhood.model

data class ControlCenterRepeatingInvestmentScreenArgs(
    val screenTitle: String?,
    val primaryActionButtonText: String?,
    val savingsProductID: String?,
    val automationAmount: Double?,
    val automationDayOfMonth: Int?,
    val automationBankAccountID: String?,
    val automationAllocationMethod: String?,
    val shouldShowBuyTargetPortfolioSwitch: Boolean
)
