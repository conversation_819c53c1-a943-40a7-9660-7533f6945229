package com.wealthyhood.wealthyhood.model.dataitems

import androidx.annotation.DrawableRes
import androidx.annotation.StyleRes

sealed class CountryDataItem {

    abstract val id: String

    data class SearchItem(
        override val id: String,
        val paddingStart: Int?,
        val paddingTop: Int?,
        val paddingEnd: Int?,
        val paddingBottom: Int?,
        val hint: String?,
        val searchText: String?
    ) : CountryDataItem()

    data class CountryItem(
        override val id: String,
        @DrawableRes
        val flagImageDrawableRes: Int,
        val flagImageAlpha: Float,
        val abbreviation: String,
        val name: String,
        val shouldShowTag: Boolean?,
        val tagText: String?,
        var isSelected: Boolean,
        var marginHorizontal: Int = 0,
        var marginVertical: Int = 0
    ) : CountryDataItem()

    data class DividerItem(
        override val id: String = "dividerItem",
        val paddingStart: Int?,
        val paddingTop: Int?,
        val paddingEnd: Int?,
        val paddingBottom: Int?,
        val lineHeight: Int?
    ) : CountryDataItem()

    data class HeaderItem(
        override val id: String = "headerItem",
        var editable: String? = null
    ) : CountryDataItem()

    object TitleItem : CountryDataItem() {
        override val id: String = "titleItem"
    }

    data class NoResultsItem(
        override val id: String,
        val paddingTop: Int?,
        val paddingBottom: Int?,
        @StyleRes val titleTextAppearanceRes: Int,
        val title: String?,
        @StyleRes val subtitleTextAppearanceRes: Int,
        val subtitle: String?
    ) : CountryDataItem()

    data class MarginItem(
        override val id: String = "marginItem",
        var height: Int = 0
    ) : CountryDataItem()
}
