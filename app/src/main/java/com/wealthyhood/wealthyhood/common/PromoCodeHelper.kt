package com.wealthyhood.wealthyhood.common

import android.content.Context
import androidx.core.net.toUri
import com.wealthyhood.wealthyhood.R
import java.util.regex.Pattern

object PromoCodeHelper {

    private const val VALIDATION_REGEX = """^[a-z0-9]+${'$'}"""

    private val validationPattern = Pattern.compile(
        VALIDATION_REGEX.toRegex().pattern,
        Pattern.CASE_INSENSITIVE
    )

    fun generateValidationError(context: Context, dirtyReferralCode: String?): String? {
        val resources = context.resources

        val validationError = if (dirtyReferralCode?.contains("wlthd=") == true) {
            resources.getString(R.string.referral_code_invalid_link_message)
        } else {
            resources.getString(R.string.referral_code_invalid_code_message)
        }

        if (dirtyReferralCode.isNullOrBlank()) return validationError

        val cleanReferralCode = generateCleanReferralCode(dirtyReferralCode)

        if (!validationPattern.matcher(cleanReferralCode).matches()) {
            return validationError
        }

        return null
    }

    fun generateCleanReferralCode(referralCode: String): String {
        return try {
            referralCode.toUri().getQueryParameter("wlthd") ?: referralCode
        } catch (e: Exception) {
            referralCode
        }.trim()
    }
}
