package com.wealthyhood.wealthyhood.common

import android.content.Context
import com.wealthyhood.wealthyhood.database.Asset
import com.wealthyhood.wealthyhood.database.WealthyhoodDatabase
import kotlinx.coroutines.sync.Mutex
import kotlinx.coroutines.sync.withLock

object UniverseSingleton {

    @Volatile
    private var _assets: List<Asset>? = null

    private val mutex = Mutex()

    suspend fun getAssets(context: Context): List<Asset>? {
        _assets?.let { return it }

        return mutex.withLock {
            _assets?.let { return@withLock it }

            try {
                val database = WealthyhoodDatabase.getInstance(context.applicationContext)
                _assets = database.assetDAO.getAll()
            } catch (e: Exception) {
                // _assets remains null for retry
            }

            _assets
        }
    }

    suspend fun resetAssets() {
        mutex.withLock {
            _assets = null
        }
    }
}
