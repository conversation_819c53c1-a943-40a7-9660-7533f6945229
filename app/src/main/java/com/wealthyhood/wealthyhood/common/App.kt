package com.wealthyhood.wealthyhood.common

import android.app.Activity
import android.app.Application
import android.content.Intent
import android.os.Bundle
import androidx.lifecycle.DefaultLifecycleObserver
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.ProcessLifecycleOwner
import coil.ImageLoader
import coil.ImageLoaderFactory
import coil.decode.SvgDecoder
import com.appsflyer.AppsFlyerConversionListener
import com.appsflyer.attribution.AppsFlyerRequestListener
import com.datadog.android.Datadog
import com.datadog.android.DatadogSite
import com.datadog.android.core.configuration.BatchSize
import com.datadog.android.core.configuration.Configuration
import com.datadog.android.core.configuration.Credentials
import com.datadog.android.core.configuration.UploadFrequency
import com.datadog.android.privacy.TrackingConsent
import com.datadog.android.rum.GlobalRum
import com.datadog.android.rum.RumMonitor
import com.datadog.android.rum.tracking.MixedViewTrackingStrategy
import com.onesignal.OneSignal
import com.onesignal.notifications.INotificationClickEvent
import com.onesignal.notifications.INotificationClickListener
import com.wealthyhood.wealthyhood.BuildConfig
import com.wealthyhood.wealthyhood.database.WealthyhoodDatabase
import com.wealthyhood.wealthyhood.domain.AppsFlyerHelper
import com.wealthyhood.wealthyhood.domain.CacheHelper
import com.wealthyhood.wealthyhood.domain.GetInvestmentProductsUseCase
import com.wealthyhood.wealthyhood.domain.GetPortfoliosCoroutinesUseCase
import com.wealthyhood.wealthyhood.domain.GetTrueLayerProvidersUseCase
import com.wealthyhood.wealthyhood.domain.GetUserCoroutinesUseCase
import com.wealthyhood.wealthyhood.domain.GetUserUseCase
import com.wealthyhood.wealthyhood.domain.GoogleAnalyticsHelper
import com.wealthyhood.wealthyhood.extensions.convertToUTF8String
import com.wealthyhood.wealthyhood.model.AppsFlyerConversionData
import com.wealthyhood.wealthyhood.model.FCMNotificationData
import com.wealthyhood.wealthyhood.model.IntercomAssets
import com.wealthyhood.wealthyhood.model.NetworkResource
import com.wealthyhood.wealthyhood.repository.AuthRepository
import com.wealthyhood.wealthyhood.repository.MyAccountRepository
import com.wealthyhood.wealthyhood.repository.PreferencesRepository
import com.wealthyhood.wealthyhood.repository.Repository
import com.wealthyhood.wealthyhood.service.GetUserQueryParams
import com.wealthyhood.wealthyhood.ui.accountdetails.CloseAccountLoadingActivity
import com.wealthyhood.wealthyhood.ui.authentication.getstarted.GetStartedActivity
import com.wealthyhood.wealthyhood.ui.authentication.login.LoginActivity
import com.wealthyhood.wealthyhood.ui.authentication.loginproviders.LoginProvidersActivity
import com.wealthyhood.wealthyhood.ui.authentication.pin.PinActivity
import com.wealthyhood.wealthyhood.ui.dailybrief.DailyBriefLoader
import com.wealthyhood.wealthyhood.ui.dashboard.investeddashboard.InvestedDashboardLoader
import com.wealthyhood.wealthyhood.ui.entry.EntryActivity
import com.wealthyhood.wealthyhood.ui.investments.InvestmentsLoader
import com.wealthyhood.wealthyhood.ui.myaccount.AccountsLoader
import com.wealthyhood.wealthyhood.ui.notificationspermission.NotificationsPermissionActivity
import com.wealthyhood.wealthyhood.ui.prefetchwaiting.PreFetchWaitingActivity
import com.wealthyhood.wealthyhood.ui.wealthyhub.WealthyHubLoader
import io.intercom.android.sdk.Intercom
import io.sentry.Breadcrumb
import io.sentry.Sentry
import io.sentry.SentryLevel
import io.sentry.SentryOptions
import io.sentry.TypeCheckHint.OKHTTP_REQUEST
import io.sentry.android.core.SentryAndroid
import io.sentry.android.replay.maskAllImages
import io.sentry.android.replay.maskAllText
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.MainScope
import kotlinx.coroutines.launch
import okhttp3.Request
import org.json.JSONException

class App : Application(), ImageLoaderFactory, AppsFlyerConversionListener,
    AppsFlyerRequestListener {

    companion object {

        private const val KEY_ANONYMOUS_ID = "anonymousId"
    }

    private var didSkipPINScreenBecauseOfOneSignalActivity = false

    private lateinit var authRepository: AuthRepository
    private lateinit var repository: Repository
    private lateinit var myAccountRepository: MyAccountRepository
    private lateinit var preferencesRepository: PreferencesRepository

    private lateinit var getUserCoroutinesUseCase: GetUserCoroutinesUseCase
    private lateinit var getPortfoliosCoroutinesUseCase: GetPortfoliosCoroutinesUseCase
    private lateinit var getInvestmentProductsUseCase: GetInvestmentProductsUseCase
    private lateinit var getTrueLayerProvidersUseCase: GetTrueLayerProvidersUseCase

    private lateinit var investedDashboardLoader: InvestedDashboardLoader
    private lateinit var investmentsLoader: InvestmentsLoader
    private lateinit var dailyBriefLoader: DailyBriefLoader
    private lateinit var accountsLoader: AccountsLoader
    private lateinit var wealthyHubLoader: WealthyHubLoader

    private var isRegisteringDeviceToken = false

    private var isPreFetchingInvestedDashboardData = false
    private var didFinishPreFetchingInvestedDashboardData = false

    private var isPreFetchingInvestmentsData = false
    private var didFinishPreFetchingInvestmentsData = false

    private var isPreFetchingDailyBriefData = false
    private var didFinishPreFetchingDailyBriefData = false

    private var isPreFetchingAccountsData = false
    private var isPreFetchingWealthyHubData = false

    private lateinit var coroutinesScope: CoroutineScope

    private val getUserUseCase = GetUserUseCase()

    private var currentActivity: Activity? = null

    override fun onCreate() {
        super.onCreate()

        val database = WealthyhoodDatabase.getInstance(this)

        authRepository = AuthRepository(this)
        myAccountRepository = MyAccountRepository()
        repository = Repository()
        preferencesRepository = PreferencesRepository(this)

        getUserCoroutinesUseCase = GetUserCoroutinesUseCase(
            preferencesRepository = preferencesRepository,
            repository = repository
        )

        getPortfoliosCoroutinesUseCase = GetPortfoliosCoroutinesUseCase(
            preferencesRepository = preferencesRepository,
            repository = repository
        )

        getInvestmentProductsUseCase = GetInvestmentProductsUseCase(
            preferencesRepository = preferencesRepository,
            repository = repository,
            database = database
        )

        getTrueLayerProvidersUseCase = GetTrueLayerProvidersUseCase(
            repository = repository
        )

        investedDashboardLoader = InvestedDashboardLoader(
            context = this,
            repository = repository,
            getUserUseCase = getUserCoroutinesUseCase,
            preferencesRepository = preferencesRepository
        )

        investmentsLoader = InvestmentsLoader(
            context = this,
            repository = repository,
            getUserUseCase = getUserCoroutinesUseCase,
            preferencesRepository = preferencesRepository
        )

        dailyBriefLoader = DailyBriefLoader(
            context = this,
            repository = repository,
            getUserUseCase = getUserCoroutinesUseCase,
            preferencesRepository = preferencesRepository
        )

        accountsLoader = AccountsLoader(
            context = this,
            repository = repository,
            getUserUseCase = getUserCoroutinesUseCase,
            getPortfoliosUseCase = getPortfoliosCoroutinesUseCase,
            getInvestmentProductsUseCase = getInvestmentProductsUseCase,
            getTrueLayerProvidersUseCase = getTrueLayerProvidersUseCase,
            preferencesRepository = preferencesRepository
        )

        wealthyHubLoader = WealthyHubLoader(
            context = this,
            repository = repository,
            getUserUseCase = getUserCoroutinesUseCase
        )

        coroutinesScope = MainScope()

        CacheHelper.clearCache(preferencesRepository)

        setupBackgroundStateTracking()
        setupCurrentActivityTracking()

        AppsFlyerHelper.setup(
            context = this,
            conversionListener = this,
            requestListener = this
        )

        setupSentry()
        setupDataDog()
        setupIntercom()
        setupOneSignal()

        getWhitelistedMails()

        OneSignal.Notifications.addClickListener(object : INotificationClickListener {

            override fun onClick(event: INotificationClickEvent) {
                handleNotificationClick(event)
            }
        })
    }

    override fun newImageLoader(): ImageLoader {
        return ImageLoader.Builder(this)
            .components {
                add(SvgDecoder.Factory())
            }
            .build()
    }

    override fun onConversionDataSuccess(p0: MutableMap<String, Any>?) {
        setAnonymousIDIfNotExists(p0)

        val anonymousID = p0?.get(KEY_ANONYMOUS_ID) as? String?
        setGoogleAnalyticsUserID(anonymousID)

        AppsFlyerConversionData.mapForSentry = p0

        AppsFlyerConversionData.grsf = p0?.get("grsf") as? String
        AppsFlyerConversionData.wlthd = p0?.get("wlthd") as? String
        AppsFlyerConversionData.sid = p0?.get("sid") as? String

        sendEvent(eventID = "appsflyerConversionSuccess")
    }

    override fun onConversionDataFail(p0: String?) {
        setGoogleAnalyticsUserID(null)

        sendEvent(eventID = "appsflyerConversionFailure")

        Sentry.captureMessage("AppsFlyerConversionListener onConversionDataFail: $p0")
    }

    private fun handleNotificationClick(event: INotificationClickEvent) {
        val notificationData = event.notification.additionalData ?: return

        try {
            val notificationType = notificationData.getString("notificationType")
            val documentID = notificationData.getString("documentId")

            val fcmNotificationData = FCMNotificationData(
                notificationType = notificationType,
                documentID = documentID
            )

            preferencesRepository.putFCMNotificationData(fcmNotificationData)

            val intent = Intent("com.wealthyhood.wealthyhood.PUSH_NOTIFICATION_CLICKED")
            intent.setPackage("com.wealthyhood.wealthyhood")

            sendBroadcast(intent)
        } catch (e: JSONException) {
            val breadcrumb = Breadcrumb().apply {
                category = "App"
                message = "handleNotificationClick failed: ${e.message}"
                level = SentryLevel.WARNING
            }

            Sentry.addBreadcrumb(breadcrumb)
        }
    }

    private fun setupSentry() {
        SentryAndroid.init(this) { options ->
            options.release = "${BuildConfig.APPLICATION_ID}@${BuildConfig.VERSION_NAME}"
            options.environment = BuildConfig.SENTRY_ENVIRONMENT
            options.dsn = BuildConfig.SENTRY_DSN

            options.tracesSampleRate = 1.0
            options.profilesSampleRate = 1.0

            val sessionReplayErrorSampleRate = if (BuildConfig.DEBUG) 0.0 else 1.0
            val sessionReplaySessionSampleRate = if (BuildConfig.DEBUG) 0.0 else 1.0

            options.sessionReplay.onErrorSampleRate = sessionReplayErrorSampleRate
            options.sessionReplay.sessionSampleRate = sessionReplaySessionSampleRate

            options.sessionReplay.maskAllText = false
            options.sessionReplay.maskAllImages = false

            // Send the body of the request to Sentry

            options.beforeSend = SentryOptions.BeforeSendCallback { event, hint ->
                hint.getAs(OKHTTP_REQUEST, Request::class.java)?.let { request ->
                    request.body?.let { body ->
                        val bodyString = body.convertToUTF8String()

                        event.contexts["Request body"] = mapOf(
                            "Content" to bodyString,
                            "Length" to body.contentLength(),
                            "Content type" to body.contentType()?.toString()
                        )
                    }
                }

                event
            }
        }
    }

    fun getDidFinishPreFetchingInvestedDashboardData(): Boolean {
        return didFinishPreFetchingInvestedDashboardData
    }

    fun getDidFinishPreFetchingInvestmentsData(): Boolean {
        return didFinishPreFetchingInvestmentsData
    }

    fun getDidFinishPreFetchingDailyBriefData(): Boolean {
        return didFinishPreFetchingDailyBriefData
    }

    private fun setAnonymousIDIfNotExists(conversionData: MutableMap<String, Any>?) {
        if (conversionData == null) return
        if (conversionData.containsKey(KEY_ANONYMOUS_ID)) return
        val appsFlyerID = AppsFlyerHelper.getUID(this) ?: return

        conversionData[KEY_ANONYMOUS_ID] = appsFlyerID
    }

    private fun setGoogleAnalyticsUserID(anonymousID: String?) {
        val savedGoogleUserID = preferencesRepository.getGoogleUserID()
        val finalGoogleUserID = savedGoogleUserID ?: anonymousID

        finalGoogleUserID?.let {
            GoogleAnalyticsHelper.setUserID(it)
        }
    }

    override fun onAppOpenAttribution(p0: MutableMap<String, String>?) {}

    override fun onAttributionFailure(p0: String?) {
        Sentry.captureMessage("AppsFlyerConversionListener onAttributionFailure: $p0")
    }

    private fun sendEvent(eventID: String) {
        myAccountRepository.sendEvent(
            context = this,
            eventID = eventID
        ) {
            when (it) {
                is NetworkResource.Success -> {
                    preferencesRepository.putDidSendInstallEvent(true)
                }

                is NetworkResource.Failure -> {}
            }
        }
    }

    private fun getCredentialsCall(callback: ((succeeded: Boolean, accessToken: String?, idToken: String?) -> Unit)?) {
        authRepository.loadAuth0Credentials {
            when (it) {
                is NetworkResource.Success -> {
                    val accessToken = "Bearer ${it.data?.accessToken}"
                    val idToken = "Bearer ${it.data?.idToken}"

                    callback?.invoke(true, accessToken, idToken)
                }

                is NetworkResource.Failure -> {
                    callback?.invoke(false, null, null)
                }
            }
        }
    }

    fun registerDeviceTokenIfNeeded(currentDeviceToken: String?) {
        val deviceToken = OneSignal.User.pushSubscription.token
        if (deviceToken.isBlank()) return
        if (deviceToken == currentDeviceToken) return

        if (isRegisteringDeviceToken) {
            return
        }

        isRegisteringDeviceToken = true

        getCredentialsCall { _, accessToken, idToken ->
            if (accessToken == null || idToken == null) {
                // TODO: Handle the failure

                isRegisteringDeviceToken = false

                return@getCredentialsCall
            }

            coroutinesScope.launch {
                repository.registerDeviceToken(
                    accessToken = accessToken,
                    idToken = idToken,
                    deviceToken = deviceToken
                )

                isRegisteringDeviceToken = false
            }
        }
    }

    fun preFetchInvestedDashboardDataIfNeeded() {
        if (isPreFetchingInvestedDashboardData) {
            return
        }

        isPreFetchingInvestedDashboardData = true

        preferencesRepository.putDashboardData(null)

        getCredentialsCall { _, accessToken, idToken ->
            if (accessToken == null || idToken == null) {
                // TODO: Handle the failure

                isPreFetchingInvestedDashboardData = false
                didFinishPreFetchingInvestedDashboardData = true

                return@getCredentialsCall
            }

            coroutinesScope.launch {
                val data = investedDashboardLoader.preFetchAllDataIfNeeded(
                    accessToken = accessToken,
                    idToken = idToken
                )

                preferencesRepository.putDashboardData(data)

                isPreFetchingInvestedDashboardData = false
                didFinishPreFetchingInvestedDashboardData = true
            }
        }
    }

    fun preFetchInvestmentsDataIfNeeded() {
        if (isPreFetchingInvestmentsData) {
            return
        }

        isPreFetchingInvestmentsData = true

        preferencesRepository.putInvestmentsData(null)

        getCredentialsCall { _, accessToken, idToken ->
            if (accessToken == null || idToken == null) {
                // TODO: Handle the failure

                isPreFetchingInvestmentsData = false
                didFinishPreFetchingInvestmentsData = true

                return@getCredentialsCall
            }

            coroutinesScope.launch {
                val data = investmentsLoader.preFetchAllData(
                    accessToken = accessToken,
                    idToken = idToken
                )

                preferencesRepository.putInvestmentsData(data)

                isPreFetchingInvestmentsData = false
                didFinishPreFetchingInvestmentsData = true
            }
        }
    }

    fun preFetchDailyBriefDataIfNeeded() {
        if (isPreFetchingDailyBriefData) {
            return
        }

        isPreFetchingDailyBriefData = true

        preferencesRepository.putDailyBriefData(null)

        getCredentialsCall { _, accessToken, idToken ->
            if (accessToken == null || idToken == null) {
                // TODO: Handle the failure

                isPreFetchingDailyBriefData = false
                didFinishPreFetchingDailyBriefData = true

                return@getCredentialsCall
            }

            coroutinesScope.launch {
                val data = dailyBriefLoader.preFetchAllDataIfNeeded(
                    accessToken = accessToken,
                    idToken = idToken
                )

                preferencesRepository.putDailyBriefData(data)

                isPreFetchingDailyBriefData = false
                didFinishPreFetchingDailyBriefData = true
            }
        }
    }

    fun preFetchAccountsDataIfNeeded() {
        if (isPreFetchingAccountsData) {
            return
        }

        isPreFetchingAccountsData = true

        preferencesRepository.putAccountsData(null)

        getCredentialsCall { _, accessToken, idToken ->
            if (accessToken == null || idToken == null) {
                // TODO: Handle the failure

                isPreFetchingAccountsData = false

                return@getCredentialsCall
            }

            coroutinesScope.launch {
                val data = accountsLoader.preFetchAllDataIfNeeded(
                    accessToken = accessToken,
                    idToken = idToken
                )

                preferencesRepository.putAccountsData(data)

                isPreFetchingAccountsData = false
            }
        }
    }

    fun preFetchWealthyHubDataIfNeeded(isUserVerified: Boolean) {
        if (isPreFetchingWealthyHubData) {
            return
        }

        isPreFetchingWealthyHubData = true

        preferencesRepository.putWealthyHubData(null)

        getCredentialsCall { _, accessToken, idToken ->
            if (accessToken == null || idToken == null) {
                // TODO: Handle the failure

                isPreFetchingWealthyHubData = false

                return@getCredentialsCall
            }

            coroutinesScope.launch {
                val data = wealthyHubLoader.preFetchAllDataIfNeeded(
                    accessToken = accessToken,
                    idToken = idToken,
                    isUserVerified = isUserVerified
                )

                preferencesRepository.putWealthyHubData(data)

                isPreFetchingWealthyHubData = false
            }
        }
    }

    private fun getWhitelistedMails() {
        coroutinesScope.launch {
            val result = repository.getWhitelistedMails()

            val response = parseSuccessResponse(result)
            preferencesRepository.putWhitelistedMails(response)
        }
    }

    private fun setupBackgroundStateTracking() {
        ProcessLifecycleOwner.get().lifecycle.addObserver(object : DefaultLifecycleObserver {

            override fun onStart(owner: LifecycleOwner) {
                super.onStart(owner)

                displayPINScreenIfNeeded()
                getIntercomAssets()
                getCredentialsAndDisplayClosedAccountIfNeeded()
            }
        })
    }

    private fun setupCurrentActivityTracking() {
        registerActivityLifecycleCallbacks(object : ActivityLifecycleCallbacks {

            override fun onActivityCreated(p0: Activity, p1: Bundle?) {}

            override fun onActivityStarted(p0: Activity) {
                currentActivity = p0

                if (didSkipPINScreenBecauseOfOneSignalActivity) {
                    didSkipPINScreenBecauseOfOneSignalActivity = false

                    displayPINScreenIfNeeded()
                }
            }

            override fun onActivityResumed(p0: Activity) {}

            override fun onActivityPaused(p0: Activity) {}

            override fun onActivityStopped(p0: Activity) {}

            override fun onActivitySaveInstanceState(p0: Activity, p1: Bundle) {}

            override fun onActivityDestroyed(p0: Activity) {}
        })
    }

    private fun setupDataDog() {
        val clientToken = BuildConfig.DATA_DOG_CLIENT_TOKEN
        val applicationID = BuildConfig.DATA_DOG_APPLICATION_ID

        val environmentName = BuildConfig.DATA_DOG_ENVIRONMENT_NAME
        val appVariantName = BuildConfig.DATA_DOG_APP_VARIANT_NAME

        val configuration = Configuration.Builder(
            logsEnabled = true,
            tracesEnabled = true,
            rumEnabled = true,
            crashReportsEnabled = true
        )
            .trackInteractions()
            .setBatchSize(BatchSize.SMALL)
            .useViewTrackingStrategy(MixedViewTrackingStrategy(true))
            .setUploadFrequency(UploadFrequency.FREQUENT)
            .useSite(DatadogSite.EU1)
            .build()

        val credentials = Credentials(clientToken, environmentName, appVariantName, applicationID)

        Datadog.initialize(
            context = this,
            credentials = credentials,
            configuration = configuration,
            trackingConsent = TrackingConsent.GRANTED
        )

        val monitor = RumMonitor.Builder().build()
        GlobalRum.registerIfAbsent(monitor)

        val userID = AppsFlyerHelper.getUID(this)

        Datadog.setUserInfo(userID, null, null)

        LogsHelper.getInstance().logMessageAndSync(
            context = this,
            message = "App.setupDataDog finished"
        )
    }

    private fun setupIntercom() {
        Intercom.initialize(
            this,
            BuildConfig.INTERCOM_API_KEY,
            BuildConfig.INTERCOM_APP_ID
        )

        Intercom.client().setInAppMessageVisibility(Intercom.Visibility.GONE)
    }

    private fun setupOneSignal() {
        //OneSignal.Debug.logLevel = LogLevel.VERBOSE

        val appID = BuildConfig.ONE_SIGNAL_APP_ID
        OneSignal.initWithContext(this, appID)
    }

    private fun displayPINScreenIfNeeded() {
        if (!shouldDisplayPINScreen()) return

        val currentActivity = currentActivity ?: return

        val intent = PinActivity.newIntent(
            context = this,
            shouldUseScreenChooser = false
        )

        // Έχουμε το εξής case:
        // 1. Βάζει το app background.
        // 2. Πατάει notification.
        // 3. Βάζει το app στο background.
        // 4. Πατάει notification.
        // Τώρα το stack έχει 2 PINActivity. Γι αυτό χρειάζεται το παρακάτω flag.

        intent.addFlags(Intent.FLAG_ACTIVITY_REORDER_TO_FRONT)

        currentActivity.startActivity(intent)
    }

    private fun getIntercomAssets() {
        myAccountRepository.getIntercomAssets {
            when (it) {
                is NetworkResource.Success -> {
                    IntercomAssets.article = it.data?.article
                    IntercomAssets.carousel = it.data?.carousel
                    IntercomAssets.helpCenterCollections = it.data?.helpCenterCollections
                    IntercomAssets.survey = it.data?.survey
                }

                is NetworkResource.Failure -> {}
            }
        }
    }

    private fun getCredentialsAndDisplayClosedAccountIfNeeded() {
        if (currentActivity is EntryActivity) return
        if (currentActivity is CloseAccountLoadingActivity) return

        authRepository.loadAuth0Credentials {
            when (it) {
                is NetworkResource.Success -> {
                    val accessToken = it.data?.accessToken
                    val idToken = it.data?.idToken

                    displayClosedAccountIfNeeded(
                        accessToken = accessToken,
                        idToken = idToken
                    )
                }

                is NetworkResource.Failure -> {
                    // TODO: Handle the failure
                }
            }
        }
    }

    private fun displayClosedAccountIfNeeded(accessToken: String?, idToken: String?) {
        val finalAccessToken = "Bearer $accessToken"
        val finalIDToken = "Bearer $idToken"

        // If we don't populate the accounts, the hasDisassociationRequest field
        // will have the wrong value.
        val queryParams = GetUserQueryParams(populate = "accounts")

        getUserUseCase(
            context = this,
            myAccountRepository = myAccountRepository,
            preferencesRepository = preferencesRepository,
            accessToken = finalAccessToken,
            idToken = finalIDToken,
            queryParams = queryParams
        ) {
            when (it) {
                is NetworkResource.Success -> {
                    val user = it.data

                    if (user?.hasDisassociationRequest == true) {
                        val state = ProcessLifecycleOwner.get().lifecycle.currentState

                        if (state != Lifecycle.State.RESUMED) {
                            // TODO: Test this
                            Sentry.captureMessage("Did not show ClosedAccount screen: $state")

                            return@getUserUseCase
                        }

                        currentActivity?.startActivity(
                            CloseAccountLoadingActivity.newIntent(this)
                        )
                    }
                }

                is NetworkResource.Failure -> {
                    // TODO: Handle the failure
                }
            }
        }
    }

    private fun shouldDisplayPINScreen(): Boolean {
        // Μόλις κάνουμε κλικ στο OneSignal notification, ανοίγει το NotificationOpenedActivity
        // το οποίο είναι invisible και μετά κλείνει αμέσως. Λογικά αυτό έχουν ρυθμίσει στο
        // PendingIntent. Σε αυτήν την περίπτωση δε θέλουμε να δείξουμε το PINActivity αλλιώς
        // θα ξεμείνουμε με ένα PINScreen στο bottom του stack
        // (αν κάνουμε συνέχεια back, το τελευταίο screen πριν το exit θα είναι το PINActivity)!

        if (currentActivity is com.onesignal.notifications.activities.NotificationOpenedActivity) {
            didSkipPINScreenBecauseOfOneSignalActivity = true

            return false
        }

        if (currentActivity is EntryActivity) return false
        if (currentActivity is LoginProvidersActivity) return false
        if (currentActivity is LoginActivity) return false
        if (currentActivity is GetStartedActivity) return false
        if (currentActivity is PinActivity) return false
        if (currentActivity is CloseAccountLoadingActivity) return false
        if (currentActivity is PreFetchWaitingActivity) return false

        // Εδώ δε θέλουμε να δείξουμε το PIN screen γιατί μπορεί να έχουμε το ακόλουθό issue:
        // 1. Από το NotificationsPermission screen ανοίγουμε τα app settings.
        // 2. Ο χρήστης επιστρέφει με back button.
        // 3. Δείχνουμε το PIN screen και επειδή χτυπάει και το onResume() του NotificationsPermission, τον πηγαίνουμε στο επόμενο screen.
        // Έτσι το PIN screen θα μείνει από πίσω.

        if (currentActivity is NotificationsPermissionActivity) return false

        if (preferencesRepository.getPin().isNullOrBlank()) return false

        return true
    }

    // AppsFlyerRequestListener Methods

    override fun onSuccess() {}

    override fun onError(p0: Int, p1: String) {
        Sentry.captureMessage("AppsFlyerRequestListener onError: $p0 - $p1")
    }
}
