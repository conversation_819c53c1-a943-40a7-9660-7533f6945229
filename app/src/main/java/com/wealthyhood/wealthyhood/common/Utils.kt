package com.wealthyhood.wealthyhood.common

import android.content.Context
import android.view.View
import coil.load
import com.wealthyhood.wealthyhood.R
import com.wealthyhood.wealthyhood.databinding.ComponentSelectedBankAccountBinding
import com.wealthyhood.wealthyhood.extensions.generateLogoURI
import com.wealthyhood.wealthyhood.extensions.generateTitle
import com.wealthyhood.wealthyhood.extensions.shouldShowBigImage
import com.wealthyhood.wealthyhood.model.DomainResult
import com.wealthyhood.wealthyhood.model.NetworkResource
import com.wealthyhood.wealthyhood.service.BankAccount

object Utils {

    fun refreshSelectedBankAccountComponent(
        component: ComponentSelectedBankAccountBinding,
        title: String?,
        description: String?
    ) {
        component.bigBankImageView.setImageDrawable(null)
        component.bigBankImageView.visibility = View.GONE

        component.bankImageView.visibility = View.VISIBLE
        component.bankImageView.setImageResource(R.drawable.ic_wealthyhood_logo_colored)

        component.bankNameTextView.text = title
        component.bankDescriptionTextView.text = description

        component.bankDescriptionDotTextView.visibility = View.GONE
        component.bankSecondaryDescriptionTextView.visibility = View.GONE
    }

    fun refreshSelectedBankAccountComponent(
        component: ComponentSelectedBankAccountBinding,
        bankAccount: BankAccount?
    ) {
        component.bankImageView.setImageDrawable(null)
        component.bigBankImageView.setImageDrawable(null)

        component.bankImageView.visibility = View.GONE
        component.bigBankImageView.visibility = View.GONE

        // Τα EU accounts δεν έχουν provider και είναι αυτά που έχουν το μεγάλο logo
        val shouldShowBigImage = bankAccount.shouldShowBigImage()

        val imageView = if (shouldShowBigImage) {
            component.bigBankImageView
        } else component.bankImageView

        imageView.visibility = View.VISIBLE
        imageView.load(bankAccount?.generateLogoURI())

        component.bankNameTextView.text = bankAccount?.generateTitle()

        component.bankDescriptionTextView.text =
            bankAccount?.sortCode ?: bankAccount?.displayAccountIdentifier

        if (bankAccount?.number == null) {
            component.bankDescriptionDotTextView.visibility = View.GONE
            component.bankSecondaryDescriptionTextView.visibility = View.GONE

            return
        }

        component.bankDescriptionDotTextView.visibility = View.VISIBLE
        component.bankSecondaryDescriptionTextView.visibility = View.VISIBLE

        component.bankSecondaryDescriptionTextView.text = bankAccount.number
    }

    fun generateScheduleStep3SubtitleText(context: Context, isUserEU: Boolean): String {
        val minNumberOfDays: String
        val maxNumberOfDays: String

        if (isUserEU) {
            minNumberOfDays = "4"
            maxNumberOfDays = "5"
        } else {
            minNumberOfDays = "3"
            maxNumberOfDays = "4"
        }

        return context.resources.getString(
            R.string.schedule_step_3_subtitle_text,
            minNumberOfDays,
            maxNumberOfDays
        )
    }

    fun generateErrorMessages(
        networkResources: List<NetworkResource<*>>?,
        domainResults: List<DomainResult<*>>?
    ): List<String> {
        val finalAnswer = mutableListOf<String>()

        networkResources?.forEach { networkResource ->
            if (networkResource !is NetworkResource.Failure) return@forEach

            val errorMessage = networkResource.message ?: "Unknown error"
            finalAnswer.add(errorMessage)
        }

        domainResults?.forEach { domainResult ->
            if (domainResult !is DomainResult.Error) return@forEach

            val errorMessage = domainResult.throwable?.message ?: "Unknown error"
            finalAnswer.add(errorMessage)
        }

        return finalAnswer
    }
}
