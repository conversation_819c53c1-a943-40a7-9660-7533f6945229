package com.wealthyhood.wealthyhood.common

import android.content.Context
import com.datadog.android.Datadog
import com.onesignal.OneSignal
import com.wealthyhood.wealthyhood.domain.AppsFlyerHelper
import com.wealthyhood.wealthyhood.domain.GetInvestmentUniverseUseCase
import com.wealthyhood.wealthyhood.domain.GetUserCoroutinesUseCase
import com.wealthyhood.wealthyhood.domain.ScreenChooserData
import com.wealthyhood.wealthyhood.extensions.saveEssentialPortfolioInfo
import com.wealthyhood.wealthyhood.model.DomainResult
import com.wealthyhood.wealthyhood.model.NetworkResource
import com.wealthyhood.wealthyhood.repository.PreferencesRepository
import com.wealthyhood.wealthyhood.repository.Repository
import com.wealthyhood.wealthyhood.service.ApiResponse
import com.wealthyhood.wealthyhood.service.GetForceUpdateDataResponse
import com.wealthyhood.wealthyhood.service.GetInvestmentUniverseResponse
import com.wealthyhood.wealthyhood.service.GetUserQueryParams
import com.wealthyhood.wealthyhood.service.Gift
import com.wealthyhood.wealthyhood.service.User
import io.intercom.android.sdk.Intercom
import io.intercom.android.sdk.IntercomError
import io.intercom.android.sdk.IntercomStatusCallback
import io.intercom.android.sdk.UserAttributes
import io.intercom.android.sdk.identity.Registration
import io.sentry.Breadcrumb
import io.sentry.Sentry
import io.sentry.SentryLevel

class ScreenChooserLoader(
    private val context: Context,
    private val repository: Repository,
    private val preferencesRepository: PreferencesRepository,
    private val getUserUseCase: GetUserCoroutinesUseCase,
    private val getInvestmentUniverseUseCase: GetInvestmentUniverseUseCase,
    private val shouldTriggerDataPreFetch: Boolean,
    private val didStartLoadingData: (() -> Unit)? = null
) {

    fun clearData() {
        preferencesRepository.putScreenChooserData(null)
    }

    suspend fun getOrFetchForceUpdateData(): Pair<Boolean, GetForceUpdateDataResponse?> {
        val savedData = preferencesRepository.getScreenChooserData()

        val savedForceUpdateData = savedData?.forceUpdateData
        if (savedForceUpdateData != null) return Pair(true, savedForceUpdateData)

        didStartLoadingData?.invoke()

        val result = getForceUpdateDataCall()

        if (result is DomainResult.Error) {
            val breadcrumb = Breadcrumb().apply {
                category = "ScreenChooserLoader"
                message = "getForceUpdateDataCall failed: ${result.throwable?.message}"
                level = SentryLevel.WARNING
            }

            Sentry.addBreadcrumb(breadcrumb)

            return Pair(false, null)
        }

        val forceUpdateData = parseSuccessResponse(result)

        val newData = ScreenChooserData(
            forceUpdateData = forceUpdateData,
            user = savedData?.user,
            universe = savedData?.universe,
            gifts = savedData?.gifts
        )

        preferencesRepository.putScreenChooserData(newData)

        return Pair(true, forceUpdateData)
    }

    suspend fun getOrFetchRequiredData(
        accessToken: String,
        idToken: String
    ): ScreenChooserData? {
        val savedData = preferencesRepository.getScreenChooserData()

        val userPair = getOrFetchUser(
            accessToken = accessToken,
            idToken = idToken,
            savedData = savedData
        )

        val didGetUserSuccessfully = userPair.first
        val user = userPair.second

        if (!didGetUserSuccessfully || user == null) {
            // Εάν υπήρξε κάποιο πρόβλημα δε θέλουμε να συνεχίσουμε γιατί θα διαγραφεί
            // και το portfolioID.

            val breadcrumb = Breadcrumb().apply {
                category = "ScreenChooserLoader"
                message = "getOrFetchRequiredData failed: got a null user."
                level = SentryLevel.ERROR
            }

            Sentry.addBreadcrumb(breadcrumb)

            return null
        }

        savePortfolioInfo(user)
        identifyUserForReportingServices(user)

        val referralCode = preferencesRepository.getUserReferralCode()

        val universePair = getOrFetchUniverse(
            companyEntity = user.companyEntity,
            country = user.residencyCountry,
            email = user.email,
            referralCode = referralCode,
            savedData = savedData
        )

        val didGetUniverseSuccessfully = universePair.first
        val universe = universePair.second

        if (!didGetUniverseSuccessfully) {
            return null
        }

        val isUserVerified = (user.isVerified == true)
        prefetchDataIfNeeded(isUserVerified)

        val newData = ScreenChooserData(
            forceUpdateData = savedData?.forceUpdateData,
            user = user,
            universe = universe,
            gifts = savedData?.gifts
        )

        preferencesRepository.putScreenChooserData(newData)

        return newData
    }

    suspend fun getOrFetchGifts(
        accessToken: String,
        idToken: String
    ): Pair<Boolean, List<Gift>?> {
        val savedData = preferencesRepository.getScreenChooserData()

        val savedGifts = savedData?.gifts
        if (savedGifts != null) return Pair(true, savedGifts)

        didStartLoadingData?.invoke()

        val result = getGiftsCall(
            accessToken = accessToken,
            idToken = idToken
        )

        if (result is DomainResult.Error) {
            val errorMessage = result.throwable?.message ?: "NetworkError: ${result.errorCode}"

            val breadcrumb = Breadcrumb().apply {
                category = "ScreenChooserLoader"
                message = "getOrFetchGifts failed: $errorMessage"
                level = SentryLevel.ERROR
            }

            Sentry.addBreadcrumb(breadcrumb)

            return Pair(false, null)
        }

        val gifts = parseSuccessResponse(result)?.data

        val newData = ScreenChooserData(
            forceUpdateData = savedData?.forceUpdateData,
            user = savedData?.user,
            universe = savedData?.universe,
            gifts = gifts
        )

        preferencesRepository.putScreenChooserData(newData)

        return Pair(true, gifts)
    }

    private fun prefetchDataIfNeeded(isUserVerified: Boolean) {
        // Πρέπει να γίνει μετά το getInvestmentUniverse γιατί ένα από τα calls,
        // το getSavingsProductFeeDetails, χρειάζεται info από το universe.
        // Επίσης θα πρέπει να γίνει εφόσον ο χρήστης έχει credentials.

        if (shouldTriggerDataPreFetch) {
            //(context.applicationContext as? App)?.preFetchInvestedDashboardDataIfNeeded()
            (context.applicationContext as? App)?.preFetchInvestmentsDataIfNeeded()
            (context.applicationContext as? App)?.preFetchDailyBriefDataIfNeeded()
            (context.applicationContext as? App)?.preFetchAccountsDataIfNeeded()
            (context.applicationContext as? App)?.preFetchWealthyHubDataIfNeeded(isUserVerified)
        }
    }

    private suspend fun getOrFetchUser(
        accessToken: String,
        idToken: String,
        savedData: ScreenChooserData?
    ): Pair<Boolean, User?> {
        val savedUser = savedData?.user
        if (savedUser != null) return Pair(true, savedUser)

        didStartLoadingData?.invoke()

        val result = getUserCall(
            accessToken = accessToken,
            idToken = idToken
        )

        if (result is NetworkResource.Failure) {
            // Εάν το call αποτύχει θέλουμε να το ξέρουμε ώστε να μην αποθηκεύσουμε τα data.

            val breadcrumb = Breadcrumb().apply {
                category = "ScreenChooserLoader"
                message = "getOrFetchUser failed: ${result.message}"
                level = SentryLevel.ERROR
            }

            Sentry.addBreadcrumb(breadcrumb)

            return Pair(false, null)
        }

        // Δεν αποθηκεύουμε εδώ τίποτα στα SharedPreferences για να μην έχουμε race conditions
        // αν για παράδειγμα τρέξουμε παράλληλα τις getOrFetchUser και getOrFetchGifts.

        val user = parseSuccessResponse(result)

        return Pair(true, user)
    }

    private suspend fun getOrFetchUniverse(
        companyEntity: String?,
        country: String?,
        email: String?,
        referralCode: String?,
        savedData: ScreenChooserData?
    ): Pair<Boolean, GetInvestmentUniverseResponse?> {
        val savedUniverse = savedData?.universe
        if (savedUniverse != null) return Pair(true, savedUniverse)

        didStartLoadingData?.invoke()

        val result = getInvestmentUniverseCall(
            companyEntity = companyEntity,
            country = country,
            email = email,
            referralCode = referralCode
        )

        if (result is DomainResult.Error) {
            val breadcrumb = Breadcrumb().apply {
                category = "ScreenChooserLoader"
                message = "getOrFetchUniverse failed: ${result.throwable?.message}"
                level = SentryLevel.ERROR
            }

            Sentry.addBreadcrumb(breadcrumb)

            return Pair(false, null)
        }

        val universe = parseSuccessResponse(result)

        return Pair(true, universe)
    }

    private suspend fun getForceUpdateDataCall(): DomainResult<GetForceUpdateDataResponse>? {
        return repository.getForceUpdateData()
    }

    private suspend fun getUserCall(
        accessToken: String,
        idToken: String
    ): NetworkResource<User> {
        // Ο ScreenChooser θα πρέπει οπωσδήποτε να καλέσει την getUser για να αποθηκεύσουμε τις
        // πληροφορίες χρήστη που χρειάζεται η υπόλοιπη εφαρμογή.

        // If we don't populate the subscription, the hasSubscription field
        // will have the wrong value.
        // If we don't populate the accounts, the hasSuspendedAccount field
        // will have the wrong value.
        // If we don't populate the portfolios, the isVerified and shouldShowKYCSuccessPage fields
        // will have the wrong value.

        val queryParams = GetUserQueryParams(
            populate = "subscription,addresses,accounts,userDataRequests,canUnlockFreeShare,portfolios,kycOperation"
        )

        return getUserUseCase(
            context = context,
            accessToken = accessToken,
            idToken = idToken,
            queryParams = queryParams
        )
    }

    private suspend fun getInvestmentUniverseCall(
        companyEntity: String?,
        country: String?,
        email: String?,
        referralCode: String?
    ): DomainResult<GetInvestmentUniverseResponse>? {
        return getInvestmentUniverseUseCase.invoke(
            companyEntity = companyEntity,
            country = country,
            email = email,
            referralCode = referralCode
        )
    }

    private suspend fun getGiftsCall(
        accessToken: String,
        idToken: String
    ): DomainResult<ApiResponse<List<Gift>>>? {
        return repository.getGifts(
            accessToken = accessToken,
            idToken = idToken,
            used = false,
            hasViewedAppModal = null
        )
    }

    private fun savePortfolioInfo(user: User) {
        // Κάναμε populate τα portfolios οπότε τα έχουμε πάνω στο user.

        val portfolios = user.getPortfoliosAsListOfObjects()

        val portfolio = portfolios?.find { portfolio ->
            portfolio.isReal == true
        }

        preferencesRepository.putPortfolioID(portfolio?.id)
        portfolio?.saveEssentialPortfolioInfo(preferencesRepository)
    }

    private fun identifyUserForReportingServices(user: User) {
        // TODO: Maybe it's better to do it in Entry activity and after successful login.
        //  ScreenChooser's job should be only to choose a screen.

        val userEmail = user.email

        val sentryUser = io.sentry.protocol.User().apply {
            email = userEmail
        }

        Sentry.setUser(sentryUser)

        val appsFlyerID = AppsFlyerHelper.getUID(context)
        Datadog.setUserInfo(appsFlyerID, null, userEmail)

        saveIDForGoogleAnalytics(user)
        identifyUserForIntercom(user)
        identifyUserForOneSignal(user)
    }

    private fun saveIDForGoogleAnalytics(user: User) {
        // We save the participant anonymous ID. This ID will be used in the next
        // AppsFlyer data conversion to set the Google Analytics User ID.

        user.participant?.anonymousID?.let {
            preferencesRepository.putGoogleUserID(it)
        }
    }

    private fun identifyUserForIntercom(user: User) {
        val userID = user.id ?: return
        val userIntercomHash = user.intercomUserIDHash ?: return

        Intercom.client().setUserHash(userIntercomHash)

        val registration = Registration.create().withUserId(userID)

        Intercom.client().loginIdentifiedUser(registration, object : IntercomStatusCallback {

            override fun onFailure(intercomError: IntercomError) {
                updateIntercomUser(user)
            }

            override fun onSuccess() {
                updateIntercomUser(user)
            }
        })
    }

    private fun identifyUserForOneSignal(user: User) {
        val userID = user.id ?: return

        OneSignal.login(userID)

        user.email?.let {
            OneSignal.User.addEmail(it)
        }
    }

    private fun updateIntercomUser(user: User) {
        val userAttributes = UserAttributes.Builder()
            //.withUserId(user.id)
            .withEmail(user.email)
            .withName(user.firstName)
            .build()

        Intercom.client().updateUser(userAttributes)
    }
}
