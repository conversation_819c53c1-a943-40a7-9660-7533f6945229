package com.wealthyhood.wealthyhood.common

import android.content.Context
import android.os.Build
import com.wealthyhood.wealthyhood.BuildConfig
import com.wealthyhood.wealthyhood.domain.GetInvestmentUniverseUseCase
import com.wealthyhood.wealthyhood.domain.GetUserCoroutinesUseCase
import com.wealthyhood.wealthyhood.model.KYCIncompleteScreenArgs
import com.wealthyhood.wealthyhood.model.NetworkResource
import com.wealthyhood.wealthyhood.model.ScreenEnum
import com.wealthyhood.wealthyhood.repository.AuthRepository
import com.wealthyhood.wealthyhood.repository.PreferencesRepository
import com.wealthyhood.wealthyhood.repository.Repository
import com.wealthyhood.wealthyhood.service.GetForceUpdateDataResponse
import com.wealthyhood.wealthyhood.service.User
import io.sentry.Breadcrumb
import io.sentry.Sentry
import io.sentry.SentryLevel
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.launch
import kotlin.math.max

class ScreenChooser(
    private val context: Context,
    private val authRepository: AuthRepository,
    private val preferencesRepository: PreferencesRepository,
    private val shouldSkipCheckPIN: <PERSON><PERSON><PERSON>,
    shouldTriggerInvestedDashboardDataPreFetch: Boolean,
    private val viewModelScope: CoroutineScope,
    private val didStartLoadingData: (() -> Unit)? = null,
    private val callback: (screen: ScreenEnum) -> Unit
) {

    private var accessToken: String? = null
    private var idToken: String? = null

    private var user: User? = null

    private val repository = Repository() // TODO: Use DI

    private val getInvestmentUniverseUseCase = GetInvestmentUniverseUseCase(
        context = context,
        repository = repository,
        preferencesRepository = preferencesRepository
    )

    private val getUserCoroutinesUseCase = GetUserCoroutinesUseCase(
        preferencesRepository = preferencesRepository,
        repository = repository
    )

    private val loader = ScreenChooserLoader(
        context = context,
        repository = repository,
        preferencesRepository = preferencesRepository,
        getUserUseCase = getUserCoroutinesUseCase,
        getInvestmentUniverseUseCase = getInvestmentUniverseUseCase,
        shouldTriggerDataPreFetch = shouldTriggerInvestedDashboardDataPreFetch
    ) {
        didStartLoadingData?.invoke()
    }

    fun chooseScreen(shouldInvalidateData: Boolean) {
        if (shouldInvalidateData) {
            loader.clearData()
        }

        checkForAppUpdate()
    }

    private fun checkForAppUpdate() {
        if (BuildConfig.DEBUG) {
            checkCredentials()

            return
        }

        viewModelScope.launch {
            val result = loader.getOrFetchForceUpdateData()

            val androidData = result.second?.androidData

            val shouldShowForceUpdate = shouldShowForceUpdate(androidData)

            if (shouldShowForceUpdate) {
                callback(ScreenEnum.ForceUpdate)

                return@launch
            }

            checkCredentials()
        }
    }

    private fun checkCredentials() {
        authRepository.loadAuth0Credentials {
            when (it) {
                is NetworkResource.Success -> {
                    accessToken = it.data?.accessToken
                    idToken = it.data?.idToken

                    getRequiredDataAndContinueChecks()
                }

                is NetworkResource.Failure -> {
                    val breadcrumb = Breadcrumb().apply {
                        category = "ScreenChooser"
                        message = "checkCredentials failed."
                        level = SentryLevel.ERROR
                    }

                    Sentry.addBreadcrumb(breadcrumb)
                    Sentry.captureMessage("Could not get credentials.", SentryLevel.ERROR)

                    callback(ScreenEnum.GetStarted)
                }
            }
        }
    }

    private fun getRequiredDataAndContinueChecks() {
        val accessToken = "Bearer $accessToken"
        val idToken = "Bearer $idToken"

        viewModelScope.launch {
            val data = loader.getOrFetchRequiredData(
                accessToken = accessToken,
                idToken = idToken
            )

            if (data == null) {
                val breadcrumb = Breadcrumb().apply {
                    category = "ScreenChooser"
                    message = "getRequiredDataAndContinueChecks failed."
                    level = SentryLevel.ERROR
                }

                Sentry.addBreadcrumb(breadcrumb)

                Sentry.captureMessage(
                    "ScreenChooser failed to choose a screen.",
                    SentryLevel.ERROR
                )

                callback(ScreenEnum.Undefined)

                return@launch
            }

            // We are saving the User because we will need it
            // in checkPlan() and checkWelcome() if we manage to get there.
            user = data.user

            checkAccountClosed()
        }
    }

    private fun checkAccountClosed() {
        if (user?.hasDisassociationRequest == true) {
            callback(ScreenEnum.ClosingAccount)

            return
        }

        checkPINSetup()
    }

    private fun checkPINSetup() {
        val savedPIN = preferencesRepository.getPin()

        if (savedPIN == null) {
            callback(ScreenEnum.PinSetup)

            return
        }

        checkNotificationPermissions()
    }

    private fun checkNotificationPermissions() {
        if (Build.VERSION.SDK_INT < Build.VERSION_CODES.TIRAMISU) {
            checkWealthyBitesSignUp()

            return
        }

        // FIXME: Δε θα πρέπει να ελέγξουμε και αν έχει ήδη δώσει τα σχετικά permissions;
        val didAskForNotifications = preferencesRepository.getDidAskForNotifications()

        if (didAskForNotifications) {
            checkWealthyBitesSignUp()

            return
        }

        callback(ScreenEnum.NotificationsPermission)
    }

    private fun checkWealthyBitesSignUp() {
        val shouldShowScreen = (user?.shouldDisplayWealthybitesScreen == true)

        if (shouldShowScreen) {
            callback(ScreenEnum.WealthyBitesSignUp)

            return
        }

        checkPIN()
    }

    private fun checkPIN() {
        if (shouldSkipCheckPIN) {
            checkRedeemReferralCodeScreen()

            return
        }

        val shouldDisplayPINScreen = !(preferencesRepository.getPin().isNullOrBlank())

        if (shouldDisplayPINScreen) {
            callback(ScreenEnum.Pin)

            return
        }

        checkRedeemReferralCodeScreen()
    }

    private fun checkRedeemReferralCodeScreen() {
        val shouldDisplayReferralCodeScreen = (user?.shouldDisplayReferralCodeScreen == true)

        if (shouldDisplayReferralCodeScreen) {
            callback(ScreenEnum.RedeemReferralCode)

            return
        }

        checkWaitingList()
    }

    private fun checkWaitingList() {
        val hasJoinedWaitingList = (user?.hasJoinedWaitingList == true)
        val isEUWhitelisted = (user?.isEUWhitelisted == true)

        if (hasJoinedWaitingList && !isEUWhitelisted) {
            callback(ScreenEnum.WaitingList)

            return
        }

        checkCountrySelection()
    }

    // Make sure you end up calling ScreenChooser again after selecting a country
    // in order to get the correct universe. We get the universe only from ScreenChooser.
    // SelectCountry screen navigates directly to Verification screen without
    // using the ScreenChooser but we currently have no issue because Verification is mandatory
    // and after it we call ScreenChooser again.
    private fun checkCountrySelection() {
        val hasSelectCountry = !(user?.residencyCountry.isNullOrEmpty())

        if (!hasSelectCountry) {
            checkWelcomeScreenType()

            return
        }

        checkVerificationStatus()
    }

    private fun checkVerificationStatus() {
        val hasSubmittedRequiredInfo = (user?.hasSubmittedRequiredInfo == true)
        val hasAcceptedTerms = (user?.hasAcceptedTerms == true)
        val isVerified = (user?.isVerified == true)
        val hasActiveSubscription = (user?.hasSubscription == true)
        val hasSelectedResidencyCountry = (user?.residencyCountry != null)

        val shouldShowVerificationCompletedScreen =
            (user?.shouldShowVerificationCompletedScreen == true)

        if (hasSubmittedRequiredInfo && hasAcceptedTerms && !isVerified) {
            callback(ScreenEnum.VerificationPending)

            return
        }

        if (!hasSubmittedRequiredInfo || !hasAcceptedTerms) {
            if (hasSelectedResidencyCountry) {
                val screenArgs = KYCIncompleteScreenArgs(
                    shouldShowCloseButton = true,
                    userHasCompletedKYCJourney = (user?.hasCompletedKYCJourney == true)
                )

                callback(ScreenEnum.KYCIncomplete(screenArgs))

                return
            }

            checkWelcomeScreenType()

            return
        }

        if (shouldShowVerificationCompletedScreen) {
            callback(ScreenEnum.VerificationCompleted)

            return
        }

        if (!hasActiveSubscription) {
            callback(ScreenEnum.Plans)

            return
        }

        moveToDashboardWhenReady()
    }

    private fun checkWelcomeScreenType() {
        if (user?.canUnlockFreeShare == true) {
            callback(ScreenEnum.WelcomeWithReferral)

            return
        }

        callback(ScreenEnum.Welcome)
    }

    private fun moveToDashboardWhenReady() {
        val app = (context.applicationContext as? App)

        val didFetchDailyBriefData = app?.getDidFinishPreFetchingDailyBriefData() ?: false

        val didFinishPreFetching = (didFetchDailyBriefData)

        if (didFinishPreFetching) {
            callback(ScreenEnum.Dashboard)

            return
        }

        callback(ScreenEnum.PreFetchWaiting)
    }

    private fun shouldShowForceUpdate(
        androidData: GetForceUpdateDataResponse.AndroidData?
    ): Boolean {
        val latest = BuildConfig.VERSION_NAME
        val forced = androidData?.forced

        return compareVersions(
            version1 = latest,
            version2 = forced
        ) < 0
    }

    private fun compareVersions(version1: String?, version2: String?): Int {
        // https://www.baeldung.com/java-comparing-versions

        val version1Splits = version1?.split(".") ?: emptyList()
        val version2Splits = version2?.split(".") ?: emptyList()

        val maxLengthOfSplits = max(version1Splits.size, version2Splits.size)

        for (i in 0..maxLengthOfSplits) {
            val version1Split = version1Splits.getOrNull(i)?.toIntOrNull() ?: 0
            val version2Split = version2Splits.getOrNull(i)?.toIntOrNull() ?: 0

            val compare = version1Split.compareTo(version2Split)

            if (compare != 0) {
                return compare
            }
        }

        return 0
    }
}
