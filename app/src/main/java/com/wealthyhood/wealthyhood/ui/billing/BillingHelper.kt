package com.wealthyhood.wealthyhood.ui.billing

import android.content.Context
import com.wealthyhood.wealthyhood.model.TransactionItem
import com.wealthyhood.wealthyhood.repository.PlansRepository
import com.wealthyhood.wealthyhood.service.Transaction
import com.wealthyhood.wealthyhood.service.TrueLayerProvider
import com.wealthyhood.wealthyhood.ui.myaccount.transactions.TransactionsHelper
import java.util.Locale

object BillingHelper {

    fun generateSubscriptionItems(
        context: Context,
        currencyISOCode: String?,
        userLocale: Locale,
        allTransactions: List<Transaction>?,
        plansRepository: PlansRepository,
        trueLayerProviders: List<TrueLayerProvider>?,
        limit: Int? = null
    ): List<BillingListAdapter.DataItem.TransactionDataItem> {
        // We don't need to pass the investmentProducts, onBoardingRepository and
        // trueLayerProviders because they are not needed in order to generate
        // the Charge Transaction Items.

        val allTransactionItems = TransactionsHelper.generateTransactionItems(
            context = context,
            currencyISOCode = currencyISOCode,
            userLocale = userLocale,
            transactions = allTransactions,
            investmentProducts = null,
            trueLayerProviders = trueLayerProviders,
            linkedBankAccounts = null,
            activityFilter = null,
            allPlans = plansRepository.getAllPlans(),
            allBasePlans = plansRepository.getAllBasePlans(context),
            allAssets = null,
            cashFlowSign = null,
            etfProviders = null
        )

        val transactionItems = mutableListOf<TransactionItem>()

        if (limit != null) {
            val limitedTransactionItems = allTransactionItems.take(limit)
            transactionItems.addAll(limitedTransactionItems)
        } else {
            transactionItems.addAll(allTransactionItems)
        }

        return transactionItems.filterIsInstance<TransactionItem.Transaction>()
            .map { transactionItem ->
                BillingListAdapter.DataItem.TransactionDataItem(
                    id = transactionItem.id,
                    paddingStart = transactionItem.paddingStart,
                    paddingTop = transactionItem.paddingTop,
                    paddingEnd = transactionItem.paddingEnd,
                    paddingBottom = transactionItem.paddingBottom,
                    shouldShowSmallImage = transactionItem.shouldShowSmallImage,
                    smallImageDrawableRes = transactionItem.smallImageDrawableRes,
                    smallImageURI = transactionItem.smallImageURI,
                    shouldShowBigImage = transactionItem.shouldShowBigImage,
                    bigImageDrawableRes = transactionItem.bigImageDrawableRes,
                    bigImageURI = transactionItem.bigImageURI,
                    titleText = transactionItem.titleText,
                    shouldShowBadge = transactionItem.shouldShowBadge,
                    badgeBackgroundColorRes = transactionItem.badgeBackgroundColorRes,
                    badgeText = transactionItem.badgeText,
                    badgeTextColorRes = transactionItem.badgeTextColorRes,
                    shouldShowStatusIcon = transactionItem.shouldShowStatusIcon,
                    statusIconDrawablesRes = transactionItem.statusIconDrawablesRes,
                    statusIconTintColorRes = transactionItem.statusIconTintColorRes,
                    statusText = transactionItem.statusText,
                    statusTextColorRes = transactionItem.statusTextColorRes,
                    shouldShowStatusDot = transactionItem.shouldShowStatusDot,
                    shouldShowSecondaryStatusText = transactionItem.shouldShowSecondaryStatusText,
                    secondaryStatusText = transactionItem.secondaryStatusText,
                    shouldShowPaymentViews = transactionItem.shouldShowPaymentViews,
                    paymentDrawableRes = transactionItem.paymentDrawableRes,
                    paymentText = transactionItem.paymentText,
                    shouldShowAmount = transactionItem.shouldShowAmount,
                    amountText = transactionItem.amountText,
                    shouldShowType = transactionItem.shouldShowType,
                    typeText = transactionItem.typeText,
                    typeTextColorRes = transactionItem.typeTextColorRes
                )
            }
    }
}
