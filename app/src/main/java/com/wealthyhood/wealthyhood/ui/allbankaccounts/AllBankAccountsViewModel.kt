package com.wealthyhood.wealthyhood.ui.allbankaccounts

import android.app.Activity
import android.app.Application
import android.text.SpannableString
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import androidx.recyclerview.widget.RecyclerView
import com.wealthyhood.wealthyhood.R
import com.wealthyhood.wealthyhood.common.parseSuccessResponse
import com.wealthyhood.wealthyhood.domain.EventsHelper
import com.wealthyhood.wealthyhood.extensions.generateDescription
import com.wealthyhood.wealthyhood.extensions.generateLogoURI
import com.wealthyhood.wealthyhood.extensions.generateTitle
import com.wealthyhood.wealthyhood.extensions.shouldShowBigImage
import com.wealthyhood.wealthyhood.model.DomainResult
import com.wealthyhood.wealthyhood.model.GenericMessageSheetScreenArgs
import com.wealthyhood.wealthyhood.model.NetworkResource
import com.wealthyhood.wealthyhood.repository.AuthRepository
import com.wealthyhood.wealthyhood.repository.PreferencesRepository
import com.wealthyhood.wealthyhood.repository.Repository
import com.wealthyhood.wealthyhood.service.BankAccount
import com.wealthyhood.wealthyhood.service.GetLinkedBankAccountsResponse
import com.wealthyhood.wealthyhood.ui.allbankaccounts.AllBankAccountsListAdapter.DataItem
import com.wealthyhood.wealthyhood.viewholders.HeaderViewHolder
import kotlinx.coroutines.Deferred
import kotlinx.coroutines.async
import kotlinx.coroutines.launch
import kotlin.math.abs

class AllBankAccountsViewModel(application: Application) : AndroidViewModel(application) {

    private val context = application

    private val preferencesRepository = PreferencesRepository(application) // TODO: Use DI
    private val authRepository = AuthRepository(application) // TODO: Use DI
    private val repository = Repository() // TODO: Use DI

    private var linkedBankAccounts: List<BankAccount>? = null

    private var shouldSetActivityResultOK = false

    private var bankAccountsDeferred: Deferred<DomainResult<GetLinkedBankAccountsResponse>?>? = null

    private val _navigationBarElevation = MutableLiveData<Float?>()
    val navigationBarElevation: LiveData<Float?>
        get() = _navigationBarElevation

    private val _isNavigationTitleVisible = MutableLiveData<Boolean?>()
    val isNavigationTitleVisible: LiveData<Boolean?>
        get() = _isNavigationTitleVisible

    private val _navigationTitleText = MutableLiveData<String?>()
    val navigationTitleText: LiveData<String?>
        get() = _navigationTitleText

    private val _dataItems = MutableLiveData<List<DataItem>?>()
    val dataItems: LiveData<List<DataItem>?>
        get() = _dataItems

    private val _eventShowOrHideLoadingDialog = MutableLiveData<Boolean?>()
    val eventShowOrHideLoadingDialog: LiveData<Boolean?>
        get() = _eventShowOrHideLoadingDialog

    private val _eventShowPaymentMethodOptionsDialog = MutableLiveData<String?>()
    val eventShowPaymentMethodOptionsDialog: LiveData<String?>
        get() = _eventShowPaymentMethodOptionsDialog

    private val _eventShowAddBankAccountScreen = MutableLiveData<Boolean?>()
    val eventShowAddBankAccountScreen: LiveData<Boolean?>
        get() = _eventShowAddBankAccountScreen

    private val _eventShowGenericMessageSheet = MutableLiveData<GenericMessageSheetScreenArgs?>()
    val eventShowGenericMessageSheet: LiveData<GenericMessageSheetScreenArgs?>
        get() = _eventShowGenericMessageSheet

    private val _eventCloseScreenWithResult = MutableLiveData<Int?>()
    val eventCloseScreenWithResult: LiveData<Int?>
        get() = _eventCloseScreenWithResult

    fun eventShowOrHideLoadingDialogCompleted() {
        _eventShowOrHideLoadingDialog.value = null
    }

    fun eventShowPaymentMethodOptionsDialogCompleted() {
        _eventShowPaymentMethodOptionsDialog.value = null
    }

    fun eventShowAddBankAccountScreenCompleted() {
        _eventShowAddBankAccountScreen.value = null
    }

    fun eventShowGenericMessageSheetCompleted() {
        _eventShowGenericMessageSheet.value = null
    }

    fun eventCloseScreenWithResultCompleted() {
        _eventCloseScreenWithResult.value = null
    }

    init {
        refreshNavigationTitle()
        refreshDataItems()

        reloadAndRefresh()
    }

    fun handleOnRecyclerViewScrolled(recyclerView: RecyclerView) {
        refreshNavigationBarElevation(recyclerView)
        refreshNavigationBarTitleVisibility(recyclerView)
    }

    private fun refreshNavigationBarElevation(recyclerView: RecyclerView) {
        val offset = recyclerView.computeVerticalScrollOffset()

        val elevationRes = if (offset > 0) R.dimen.spacing_2 else R.dimen.spacing_0
        val elevation = context.resources.getDimension(elevationRes)

        _navigationBarElevation.value = elevation
    }

    private fun refreshNavigationBarTitleVisibility(recyclerView: RecyclerView) {
        _isNavigationTitleVisible.value = shouldShowNavigationBarTitle(recyclerView)
    }

    private fun shouldShowNavigationBarTitle(recyclerView: RecyclerView): Boolean {
        val view =
            recyclerView.findChildViewUnder(0f, 0f) ?: return false
        val viewHolder = recyclerView.findContainingViewHolder(view) ?: return false
        val position = viewHolder.absoluteAdapterPosition

        // There is also the following useful property: viewHolder.itemView.top

        if (position > 0) return true

        (viewHolder as? HeaderViewHolder)?.let {
            val viewHolderTop = abs(viewHolder.itemView.top)
            val titleBottom = it.computeTitleBottom()

            return viewHolderTop >= titleBottom
        }

        return false
    }

    fun handleOnBackButtonPressed() {
        closeScreenWithResult()
    }

    fun handleOnBankAccountOptionsButtonClicked(bankAccountID: String?) {
        _eventShowPaymentMethodOptionsDialog.value = bankAccountID
    }

    fun handleOnActionClicked(itemID: String?) {
        _eventShowAddBankAccountScreen.value = true
    }

    fun handleOnNewBankAccountLinked() {
        shouldSetActivityResultOK = true

        linkedBankAccounts = null
        refreshDataItems()

        reloadAndRefresh()
    }

    fun handleOnDeactivateBankAccountRequest(bankAccountID: String?) {
        if (bankAccountID == null) return

        val bankAccount = linkedBankAccounts?.find { it.id == bankAccountID } ?: return

        if (bankAccount.getMandateAsObject()?.isActive == true) {
            showMandateError()

            return
        }

        deactivateBankAccount(bankAccountID)
    }

    private fun reloadAndRefresh() {
        bankAccountsDeferred?.cancel()

        getCredentialsCall { _, accessToken, idToken ->
            if (accessToken == null || idToken == null) {
                // TODO: Handle the failure

                return@getCredentialsCall
            }

            viewModelScope.launch {

                val userID = preferencesRepository.getUserID()

                bankAccountsDeferred = async {
                    repository.getLinkedBankAccounts(
                        accessToken = accessToken,
                        idToken = idToken,
                        userID = userID
                    )
                }

                val bankAccountsResult = bankAccountsDeferred?.await()

                if (bankAccountsResult is DomainResult.Error) {
                    // TODO: handle the error

                    return@launch
                }

                val bankAccounts = parseSuccessResponse(bankAccountsResult)?.data

                linkedBankAccounts = bankAccounts

                refreshDataItems()
            }
        }
    }

    private fun getCredentialsCall(callback: ((succeeded: Boolean, accessToken: String?, idToken: String?) -> Unit)?) {
        authRepository.loadAuth0Credentials {
            when (it) {
                is NetworkResource.Success -> {
                    val accessToken = "Bearer ${it.data?.accessToken}"
                    val idToken = "Bearer ${it.data?.idToken}"

                    callback?.invoke(true, accessToken, idToken)
                }

                is NetworkResource.Failure -> {
                    callback?.invoke(false, null, null)
                }
            }
        }
    }

    private fun deactivateBankAccount(bankAccountID: String?) {
        _eventShowOrHideLoadingDialog.value = true

        getCredentialsCall { _, accessToken, idToken ->
            if (accessToken == null || idToken == null) {
                // TODO: Handle the failure

                _eventShowOrHideLoadingDialog.value = false

                return@getCredentialsCall
            }

            viewModelScope.launch {
                val result = repository.deactivateBankAccount(
                    accessToken = accessToken,
                    idToken = idToken,
                    bankAccountID = bankAccountID
                )

                if (result is DomainResult.Success) {
                    EventsHelper.sendLinkBankAccountEventsIfNeeded(
                        context = context,
                        preferencesRepository = preferencesRepository,
                        didRequestToLinkBankAccount = false,
                        eventValues = emptyMap()
                    )
                }

                _eventShowOrHideLoadingDialog.value = false

                // Show the loading cell
                linkedBankAccounts = null
                refreshDataItems()

                reloadAndRefresh()
            }
        }
    }

    private fun refreshNavigationTitle() {
        val title = context.resources.getString(R.string.my_bank_accounts_label)
        _navigationTitleText.value = title
    }

    private fun refreshDataItems() {
        _dataItems.value = generateDataItems()
    }

    private fun generateDataItems(): List<DataItem> {
        val finalAnswer = mutableListOf<DataItem>()

        finalAnswer.add(generateHeaderItem())

        generateLoadingItem()?.let { finalAnswer.add(it) }

        generateBankAccountItems()?.let { finalAnswer.addAll(it) }

        finalAnswer.add(generateAddBankAccountItem())

        return finalAnswer
    }

    private fun generateHeaderItem(): DataItem.HeaderItem {
        val spacing12 = context.resources.getDimensionPixelSize(R.dimen.spacing_12)
        val spacing16 = context.resources.getDimensionPixelSize(R.dimen.spacing_16)
        val spacing32 = context.resources.getDimensionPixelSize(R.dimen.spacing_32)

        val title = context.resources.getString(R.string.my_bank_accounts_label)
        val spannableStringTitle = SpannableString(title)

        return DataItem.HeaderItem(
            id = "headerItem",
            headerType = null,
            paddingStart = spacing16,
            paddingTop = spacing12,
            paddingEnd = spacing16,
            paddingBottom = spacing32,
            textAppearanceRes = R.style.ScreenTitle,
            title = spannableStringTitle
        )
    }

    private fun generateLoadingItem(): DataItem.LoadingItem? {
        if (linkedBankAccounts != null) return null

        val spacing16 = context.resources.getDimensionPixelSize(R.dimen.spacing_16)

        return DataItem.LoadingItem(
            id = "loadingItem",
            paddingTop = spacing16,
            paddingBottom = spacing16
        )
    }

    private fun generateBankAccountItems(): List<DataItem.BankAccountItem>? {
        val spacing12 = context.resources.getDimensionPixelSize(R.dimen.spacing_12)
        val spacing16 = context.resources.getDimensionPixelSize(R.dimen.spacing_16)

        // Θα φτιάξουμε cells μόνο για όσα bank accounts έχουν ID.
        val numberOfBankAccountsWithID = linkedBankAccounts?.filter { it.id != null }?.size ?: 0
        val shouldShowOptionsButton = (numberOfBankAccountsWithID > 1)

        return linkedBankAccounts?.mapNotNull {
            val id = it.id ?: return@mapNotNull null

            DataItem.BankAccountItem(
                id = id,
                paddingStart = spacing16,
                paddingTop = spacing12,
                paddingEnd = spacing16,
                paddingBottom = spacing12,
                imageURL = it.generateLogoURI(),
                shouldUseBigImage = it.shouldShowBigImage(),
                title = it.generateTitle(),
                subtitle = it.generateDescription(),
                shouldShowOptionsButton = shouldShowOptionsButton
            )
        }
    }

    private fun generateAddBankAccountItem(): DataItem.ActionItem {
        val spacing12 = context.resources.getDimensionPixelSize(R.dimen.spacing_12)
        val spacing16 = context.resources.getDimensionPixelSize(R.dimen.spacing_16)

        val title = context.resources.getString(R.string.add_new_bank_account_label)

        return DataItem.ActionItem(
            id = "addBankAccountItem",
            paddingStart = spacing16,
            paddingTop = spacing12,
            paddingEnd = spacing16,
            paddingBottom = spacing12,
            hasBorder = false,
            imageDrawableRes = R.drawable.ic_account_balance,
            title = title
        )
    }

    private fun showMandateError() {
        val resources = context.resources

        val spacing16 = resources.getDimensionPixelSize(R.dimen.spacing_16)
        val spacing18 = resources.getDimensionPixelSize(R.dimen.spacing_18)
        val spacing24 = resources.getDimensionPixelSize(R.dimen.spacing_24)
        val spacing26 = resources.getDimensionPixelSize(R.dimen.spacing_26)
        val spacing40 = resources.getDimensionPixelSize(R.dimen.spacing_40)

        val primaryButtonElevation =
            resources.getDimensionPixelSize(R.dimen.primary_button_elevation)

        val titleText = resources.getString(R.string.delete_bank_account_mandate_error_title_text)

        val messageText =
            resources.getString(R.string.delete_bank_account_mandate_error_message_text)

        val buttonText = resources.getString(R.string.delete_bank_account_mandate_error_button_text)

        _eventShowGenericMessageSheet.value = GenericMessageSheetScreenArgs(
            titleText = titleText,
            titlePaddingStart = spacing16,
            titlePaddingTop = spacing24,
            titlePaddingEnd = spacing16,
            titlePaddingBottom = null,
            titleTextAppearanceRes = R.style.HeadingsH2Mobile_Primary,
            messageText = messageText,
            messagePaddingStart = spacing16,
            messagePaddingTop = spacing18,
            messagePaddingEnd = spacing16,
            messagePaddingBottom = null,
            messageTextAppearanceRes = R.style.BodySM_BaseTextColor,
            buttonText = buttonText,
            buttonPaddingStart = spacing16,
            buttonPaddingTop = spacing40,
            buttonPaddingEnd = spacing16,
            buttonPaddingBottom = spacing26,
            buttonElevation = primaryButtonElevation,
            extras = null
        )
    }

    private fun closeScreenWithResult() {
        val result = if (shouldSetActivityResultOK) Activity.RESULT_OK else Activity.RESULT_CANCELED
        _eventCloseScreenWithResult.value = result
    }
}
