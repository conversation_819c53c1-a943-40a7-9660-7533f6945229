package com.wealthyhood.wealthyhood.ui.confirmationreceipt.viewholder

import android.content.res.ColorStateList
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.annotation.ColorInt
import androidx.core.content.ContextCompat
import androidx.core.view.updatePadding
import androidx.recyclerview.widget.RecyclerView
import com.wealthyhood.wealthyhood.R
import com.wealthyhood.wealthyhood.databinding.ListItemConfirmationReceiptDividerBinding

class ConfirmationReceiptDividerViewHolder private constructor(
    private val binding: ListItemConfirmationReceiptDividerBinding
) : RecyclerView.ViewHolder(binding.root) {

    companion object {

        fun from(parent: ViewGroup): ConfirmationReceiptDividerViewHolder {
            val layoutInflater = LayoutInflater.from(parent.context)

            val binding =
                ListItemConfirmationReceiptDividerBinding.inflate(layoutInflater, parent, false)

            return ConfirmationReceiptDividerViewHolder(binding)
        }
    }

    fun bind(
        paddingStart: Int?,
        paddingTop: Int?,
        paddingEnd: Int?,
        paddingBottom: Int?,
        shouldShowTitle: Boolean,
        titleText: String?,
        @ColorInt titleSeparatorViewColor: Int?,
        @ColorInt dividerImageTintColor: Int?
    ) {
        refreshPadding(
            paddingStart = paddingStart,
            paddingTop = paddingTop,
            paddingEnd = paddingEnd,
            paddingBottom = paddingBottom
        )

        refreshTitle(shouldShowTitle, titleText, titleSeparatorViewColor)
        refreshDividerImage(dividerImageTintColor)
    }

    private fun refreshPadding(
        paddingStart: Int?,
        paddingTop: Int?,
        paddingEnd: Int?,
        paddingBottom: Int?
    ) {
        val spacing0 = itemView.context.resources.getDimensionPixelSize(R.dimen.spacing_0)

        val finalPaddingStart = paddingStart ?: spacing0
        val finalPaddingTop = paddingTop ?: spacing0
        val finalPaddingEnd = paddingEnd ?: spacing0
        val finalPaddingBottom = paddingBottom ?: spacing0

        binding.rootConstraintLayout.updatePadding(
            finalPaddingStart,
            finalPaddingTop,
            finalPaddingEnd,
            finalPaddingBottom
        )
    }

    private fun refreshTitle(
        isVisible: Boolean,
        text: String?,
        @ColorInt separatorViewColor: Int?
    ) {
        if (!isVisible) {
            binding.titleLinearLayout.visibility = View.GONE

            return
        }

        binding.titleLinearLayout.visibility = View.VISIBLE

        val defaultColor = ContextCompat.getColor(itemView.context, R.color.primary)
        val finalColor = separatorViewColor ?: defaultColor

        binding.titleTextView.text = text
        binding.titleSeparatorView.backgroundTintList = ColorStateList.valueOf(finalColor)
    }

    private fun refreshDividerImage(@ColorInt tintColor: Int?) {
        val defaultColor = ContextCompat.getColor(itemView.context, R.color.primary)
        val finalTintColor = tintColor ?: defaultColor

        binding.dividerImageView.imageTintList = ColorStateList.valueOf(finalTintColor)
    }
}
