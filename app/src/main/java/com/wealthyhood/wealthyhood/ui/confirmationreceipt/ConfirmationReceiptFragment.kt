package com.wealthyhood.wealthyhood.ui.confirmationreceipt

import android.app.Dialog
import android.graphics.drawable.Drawable
import android.net.Uri
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.browser.customtabs.CustomTabsIntent
import androidx.lifecycle.ViewModelProvider
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.SimpleItemAnimator
import com.google.android.material.bottomsheet.BottomSheetBehavior
import com.google.android.material.bottomsheet.BottomSheetDialog
import com.google.android.material.bottomsheet.BottomSheetDialogFragment
import com.google.gson.Gson
import com.wealthyhood.wealthyhood.R
import com.wealthyhood.wealthyhood.databinding.FragmentConfirmationReceiptBinding
import com.wealthyhood.wealthyhood.extensions.convertToConfirmationReceiptScreenArgs
import com.wealthyhood.wealthyhood.model.ConfirmationReceiptScreenArgs
import com.wealthyhood.wealthyhood.ui.confirmationreceipt.viewholder.ConfirmationReceiptActionViewHolder
import com.wealthyhood.wealthyhood.ui.loadingdialog.LoadingDialogFragment

class ConfirmationReceiptFragment : BottomSheetDialogFragment() {

    companion object {

        private const val ARG_SCREEN_ARGS = "ConfirmationReceiptFragment.screenArgs"

        fun newInstance(screenArgs: ConfirmationReceiptScreenArgs?): ConfirmationReceiptFragment {
            val arguments = Bundle()

            val screenArgsJSONString = screenArgs?.let {
                Gson().toJson(it)
            }

            arguments.putString(ARG_SCREEN_ARGS, screenArgsJSONString)

            val fragment = ConfirmationReceiptFragment()
            fragment.arguments = arguments

            return fragment
        }
    }

    private lateinit var _viewModel: ConfirmationReceiptViewModel
    private val viewModel get() = _viewModel

    private lateinit var _binding: FragmentConfirmationReceiptBinding
    private val binding get() = _binding

    override fun onCreateDialog(savedInstanceState: Bundle?): Dialog {
        val dialog = super.onCreateDialog(savedInstanceState)

        (dialog as? BottomSheetDialog)?.behavior?.let { behavior ->
            behavior.skipCollapsed = true
            behavior.isFitToContents = true

            behavior.isDraggable = true
            behavior.isHideable = true

            behavior.state = BottomSheetBehavior.STATE_EXPANDED
        }

        isCancelable = true

        return dialog
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        setupViewModel()
        setupBinding(inflater, container)

        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        setupRecyclerView()

        observeViewModel()
    }

    override fun getTheme(): Int {
        return R.style.CustomBottomSheetDialogTheme
    }

    private fun setupViewModel() {
        val screenArgsJSONString = arguments?.getString(ARG_SCREEN_ARGS)
        val screenArgs = screenArgsJSONString?.convertToConfirmationReceiptScreenArgs()

        val viewModelFactory = ConfirmationReceiptViewModelFactory(
            application = requireActivity().application,
            screenArgs = screenArgs
        )

        val viewModelClass = ConfirmationReceiptViewModel::class.java
        _viewModel = ViewModelProvider(this, viewModelFactory)[viewModelClass]
    }

    private fun setupBinding(inflater: LayoutInflater, container: ViewGroup?) {
        _binding = FragmentConfirmationReceiptBinding.inflate(inflater, container, false)
    }

    private fun setupRecyclerView() {
        (binding.recyclerView.itemAnimator as? SimpleItemAnimator)?.supportsChangeAnimations = false
        binding.recyclerView.layoutManager = LinearLayoutManager(requireContext())

        val actionListener = object :
            ConfirmationReceiptActionViewHolder.ConfirmationReceiptActionViewHolderListener {

            override fun onItemClicked() {
                viewModel.handleOnTradeConfirmationActionClicked()
            }
        }

        binding.recyclerView.adapter = ConfirmationReceiptListAdapter(
            actionListener = actionListener
        )
    }

    private fun observeViewModel() {
        viewModel.backgroundGradientDrawable.observe(viewLifecycleOwner) {
            refreshBackground(it)
        }

        viewModel.dataItems.observe(viewLifecycleOwner) {
            refreshDataItems(it)
        }

        viewModel.eventShowOrHideLoadingDialog.observe(viewLifecycleOwner) {
            it?.let {
                if (it) showLoading()
                else hideLoading()

                viewModel.eventShowOrHideLoadingDialogCompleted()
            }
        }

        viewModel.eventOpenBrowser.observe(viewLifecycleOwner) {
            it?.let {
                openBrowser(it)

                viewModel.eventOpenBrowserCompleted()
            }
        }
    }

    private fun refreshBackground(drawable: Drawable?) {
        if (drawable == null) return
        binding.rootLinearLayout.background = drawable
    }

    private fun refreshDataItems(dataItems: List<ConfirmationReceiptListAdapter.DataItem>?) {
        (binding.recyclerView.adapter as? ConfirmationReceiptListAdapter)?.submitList(dataItems)
    }

    private fun showLoading() {
        val isShowingLoadingDialog =
            childFragmentManager.findFragmentByTag(LoadingDialogFragment.TAG) != null

        if (isShowingLoadingDialog) return

        val fragment = LoadingDialogFragment.newInstance(
            isWithRoundedCorners = true
        )

        childFragmentManager.beginTransaction()
            .add(R.id.loading_dialog_fragment_container, fragment, LoadingDialogFragment.TAG)
            .commit()
    }

    private fun hideLoading() {
        val loadingDialog =
            childFragmentManager.findFragmentByTag(LoadingDialogFragment.TAG) ?: return

        childFragmentManager.beginTransaction()
            .remove(loadingDialog)
            .commit()
    }

    private fun openBrowser(uri: String) {
        // https://developer.chrome.com/docs/android/custom-tabs/guide-get-started

        val intent = CustomTabsIntent.Builder().build()
        intent.launchUrl(requireContext(), Uri.parse(uri))
    }
}
