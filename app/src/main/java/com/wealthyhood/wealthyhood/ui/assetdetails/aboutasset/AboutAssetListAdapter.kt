package com.wealthyhood.wealthyhood.ui.assetdetails.aboutasset

import android.view.ViewGroup
import androidx.annotation.ColorRes
import androidx.annotation.DrawableRes
import androidx.annotation.StyleRes
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.ListAdapter
import androidx.recyclerview.widget.RecyclerView
import com.wealthyhood.wealthyhood.ui.assetdetails.AssetDetailsSectionHeaderViewHolder

class AboutAssetListAdapter(
    private val infoRowListener: AboutAssetInfoRowViewHolder.AboutAssetInfoRowViewHolderListener?
) : ListAdapter<AboutAssetListAdapter.DataItem, RecyclerView.ViewHolder>(DataItemDiffCallback()) {

    companion object {

        const val ITEM_VIEW_TYPE_HEADER = 0
        const val ITEM_VIEW_TYPE_INFO_ROW = 1
        const val ITEM_VIEW_TYPE_SECTION_HEADER = 2
        const val ITEM_VIEW_TYPE_DESCRIPTION = 3
    }

    sealed class DataItem {

        abstract val id: String

        data class HeaderItem(
            override val id: String,
            val title: String?
        ) : DataItem()

        data class InfoRowItem(
            override val id: String,
            @DrawableRes var backgroundRes: Int?,
            val shouldShowSeparator: Boolean,
            val paddingTop: Int?,
            var paddingBottom: Int?,
            val labelText: String?,
            val shouldShowInfoIcon: Boolean,
            val valueText: String?,
            @ColorRes val valueTextColorRes: Int?
        ) : DataItem()

        data class SectionHeaderItem(
            override val id: String,
            val paddingStart: Int?,
            val paddingTop: Int?,
            val paddingEnd: Int?,
            val paddingBottom: Int?,
            @StyleRes val textAppearanceRes: Int,
            val title: String?
        ) : DataItem()

        data class DescriptionItem(
            override val id: String,
            val paddingTop: Int?,
            val paddingBottom: Int?,
            val description: String?
        ) : DataItem()
    }

    class DataItemDiffCallback : DiffUtil.ItemCallback<DataItem>() {

        override fun areItemsTheSame(oldItem: DataItem, newItem: DataItem): Boolean {
            return oldItem.id == newItem.id
        }

        override fun areContentsTheSame(oldItem: DataItem, newItem: DataItem): Boolean {
            return oldItem == newItem
        }
    }

    override fun getItemViewType(position: Int): Int {
        return when (getItem(position)) {
            is DataItem.HeaderItem -> ITEM_VIEW_TYPE_HEADER
            is DataItem.InfoRowItem -> ITEM_VIEW_TYPE_INFO_ROW
            is DataItem.SectionHeaderItem -> ITEM_VIEW_TYPE_SECTION_HEADER
            is DataItem.DescriptionItem -> ITEM_VIEW_TYPE_DESCRIPTION
        }
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): RecyclerView.ViewHolder {
        return when (viewType) {
            ITEM_VIEW_TYPE_HEADER -> AboutAssetHeaderViewHolder.from(
                parent = parent
            )

            ITEM_VIEW_TYPE_INFO_ROW -> AboutAssetInfoRowViewHolder.from(
                parent = parent,
                listener = infoRowListener
            )

            ITEM_VIEW_TYPE_SECTION_HEADER -> AssetDetailsSectionHeaderViewHolder.from(
                parent = parent,
                listener = null
            )

            else -> AboutAssetDescriptionViewHolder.from(parent = parent)
        }
    }

    override fun onBindViewHolder(holder: RecyclerView.ViewHolder, position: Int) {
        when (holder) {
            is AboutAssetHeaderViewHolder -> {
                val dataItem = getItem(position) as DataItem.HeaderItem

                holder.bind(
                    title = dataItem.title
                )
            }

            is AboutAssetInfoRowViewHolder -> {
                val dataItem = getItem(position) as DataItem.InfoRowItem

                holder.bind(
                    infoRowID = dataItem.id,
                    backgroundRes = dataItem.backgroundRes,
                    shouldShowSeparator = dataItem.shouldShowSeparator,
                    paddingTop = dataItem.paddingTop,
                    paddingBottom = dataItem.paddingBottom,
                    labelText = dataItem.labelText,
                    shouldShowInfoIcon = dataItem.shouldShowInfoIcon,
                    valueText = dataItem.valueText,
                    valueTextColorRes = dataItem.valueTextColorRes
                )
            }

            is AssetDetailsSectionHeaderViewHolder -> {
                val dataItem = getItem(position) as DataItem.SectionHeaderItem

                holder.bind(
                    sectionHeaderID = dataItem.id,
                    paddingStart = dataItem.paddingStart,
                    paddingTop = dataItem.paddingTop,
                    paddingEnd = dataItem.paddingEnd,
                    paddingBottom = dataItem.paddingBottom,
                    textAppearanceRes = dataItem.textAppearanceRes,
                    title = dataItem.title,
                    shouldShowInfoButton = false,
                    shouldShowSeeAllButton = false
                )
            }

            is AboutAssetDescriptionViewHolder -> {
                val dataItem = getItem(position) as DataItem.DescriptionItem

                holder.bind(
                    paddingTop = dataItem.paddingTop,
                    paddingBottom = dataItem.paddingBottom,
                    description = dataItem.description
                )
            }
        }
    }
}
