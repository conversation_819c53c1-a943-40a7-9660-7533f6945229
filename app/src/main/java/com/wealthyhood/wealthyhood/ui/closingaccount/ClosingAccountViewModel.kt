package com.wealthyhood.wealthyhood.ui.closingaccount

import android.app.Application
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import com.onesignal.OneSignal
import com.wealthyhood.wealthyhood.common.UniverseSingleton
import com.wealthyhood.wealthyhood.database.WealthyhoodDatabase
import com.wealthyhood.wealthyhood.repository.AuthRepository
import com.wealthyhood.wealthyhood.repository.PreferencesRepository
import io.intercom.android.sdk.Intercom
import kotlinx.coroutines.launch

class ClosingAccountViewModel(application: Application) : AndroidViewModel(application) {

    private val context = application

    private val authRepository = AuthRepository(application) // TODO: Use DI
    private val preferencesRepository = PreferencesRepository(application) // TODO: Use DI

    private val _eventNavigateToEntryScreen = MutableLiveData<Boolean?>()
    val eventNavigateToEntryScreen: LiveData<Boolean?>
        get() = _eventNavigateToEntryScreen

    fun eventNavigateToEntryScreenCompleted() {
        _eventNavigateToEntryScreen.value = null
    }

    fun handleOnSignOutButtonClicked() {
        Intercom.client().logout()
        OneSignal.logout()

        authRepository.logout()
        preferencesRepository.deleteLoginSessionPreferences()

        val database = WealthyhoodDatabase.getInstance(context.applicationContext)

        viewModelScope.launch {
            database.assetDAO.deleteAll()
            UniverseSingleton.resetAssets()

            _eventNavigateToEntryScreen.value = true
        }
    }
}
