package com.wealthyhood.wealthyhood.ui.assetdetails.aboutasset

import android.app.Dialog
import android.content.Intent
import android.net.Uri
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.lifecycle.ViewModelProvider
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.SimpleItemAnimator
import com.google.android.material.bottomsheet.BottomSheetBehavior
import com.google.android.material.bottomsheet.BottomSheetDialog
import com.google.android.material.bottomsheet.BottomSheetDialogFragment
import com.google.gson.Gson
import com.wealthyhood.wealthyhood.R
import com.wealthyhood.wealthyhood.databinding.FragmentAboutAssetBinding
import com.wealthyhood.wealthyhood.extensions.convertToAboutAssetScreenArgs
import com.wealthyhood.wealthyhood.model.AboutAssetScreenArgs
import io.sentry.Sentry

class AboutAssetFragment : BottomSheetDialogFragment() {

    companion object {

        private const val ARG_SCREEN_ARGS = "AboutAssetFragment.screenArgs"

        fun newInstance(screenArgs: AboutAssetScreenArgs?): AboutAssetFragment {
            val arguments = Bundle()

            screenArgs?.let {
                val jsonString = Gson().toJson(it)
                arguments.putString(ARG_SCREEN_ARGS, jsonString)
            }

            val fragment = AboutAssetFragment()
            fragment.arguments = arguments

            return fragment
        }
    }

    private lateinit var _viewModel: AboutAssetViewModel
    private val viewModel get() = _viewModel

    private lateinit var _binding: FragmentAboutAssetBinding
    private val binding get() = _binding

    override fun onCreateDialog(savedInstanceState: Bundle?): Dialog {
        val dialog = super.onCreateDialog(savedInstanceState)

        (dialog as? BottomSheetDialog)?.behavior?.let { behavior ->
            behavior.skipCollapsed = true
            behavior.isFitToContents = true

            behavior.isDraggable = true
            behavior.isHideable = true

            behavior.state = BottomSheetBehavior.STATE_EXPANDED
        }

        isCancelable = true

        return dialog
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        setupViewModel()
        setupBinding(inflater, container)

        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        setupRecyclerView()

        observeViewModel()
    }

    override fun getTheme(): Int {
        return R.style.CustomBottomSheetDialogTheme
    }

    private fun setupViewModel() {
        val screenArgsJSONString = arguments?.getString(ARG_SCREEN_ARGS)
        val screenArgs = screenArgsJSONString?.convertToAboutAssetScreenArgs()

        val viewModelFactory = AboutAssetViewModelFactory(
            application = requireActivity().application,
            screenArgs = screenArgs
        )

        val viewModelClass = AboutAssetViewModel::class.java
        _viewModel = ViewModelProvider(this, viewModelFactory)[viewModelClass]
    }

    private fun setupBinding(inflater: LayoutInflater, container: ViewGroup?) {
        _binding = FragmentAboutAssetBinding.inflate(inflater, container, false)
    }

    private fun setupRecyclerView() {
        (binding.recyclerView.itemAnimator as? SimpleItemAnimator)?.supportsChangeAnimations = false
        binding.recyclerView.layoutManager = LinearLayoutManager(requireContext())

        val infoRowListener =
            object : AboutAssetInfoRowViewHolder.AboutAssetInfoRowViewHolderListener {

                override fun onLabelClicked(infoRowID: String?) {
                    viewModel.handleOnInfoRowLabelClicked(infoRowID)
                }

                override fun onValueClicked(infoRowID: String?) {
                    viewModel.handleOnInfoRowValueClicked(infoRowID)
                }
            }

        binding.recyclerView.adapter = AboutAssetListAdapter(
            infoRowListener = infoRowListener
        )
    }

    private fun observeViewModel() {
        viewModel.dataItems.observe(viewLifecycleOwner) {
            refreshDataItems(it)
        }

        viewModel.eventOpenBrowser.observe(viewLifecycleOwner) {
            it?.let {
                openBrowser(it)

                viewModel.eventOpenBrowserCompleted()
            }
        }
    }

    private fun refreshDataItems(dataItems: List<AboutAssetListAdapter.DataItem>?) {
        (binding.recyclerView.adapter as? AboutAssetListAdapter)?.submitList(dataItems)
    }

    private fun openBrowser(url: String?) {
        val browserIntent = Intent(Intent.ACTION_VIEW, Uri.parse(url))

        try {
            startActivity(browserIntent)
        } catch (e: Exception) {
            // FIXME: Inform the user about the error.
            Sentry.captureMessage("Could not open the link in the Browser: ${e.message}")
        }
    }
}
