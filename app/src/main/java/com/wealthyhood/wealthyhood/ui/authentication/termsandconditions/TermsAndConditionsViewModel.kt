package com.wealthyhood.wealthyhood.ui.authentication.termsandconditions

import android.app.Application
import android.text.SpannableString
import android.text.TextPaint
import android.text.style.ClickableSpan
import android.view.View
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import com.wealthyhood.wealthyhood.R
import com.wealthyhood.wealthyhood.extensions.cleanSpecialFormattingCharacters
import com.wealthyhood.wealthyhood.extensions.findLinkSubstrings
import com.wealthyhood.wealthyhood.extensions.formatSubstringsAsBold
import com.wealthyhood.wealthyhood.extensions.formatSubstringsAsLinks
import com.wealthyhood.wealthyhood.model.DomainResult
import com.wealthyhood.wealthyhood.model.GenericDescriptionScreenArguments
import com.wealthyhood.wealthyhood.model.NetworkResource
import com.wealthyhood.wealthyhood.repository.AuthRepository
import com.wealthyhood.wealthyhood.repository.PreferencesRepository
import com.wealthyhood.wealthyhood.repository.Repository
import io.sentry.Sentry
import io.sentry.SentryLevel
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext

class TermsAndConditionsViewModel(application: Application) : AndroidViewModel(application) {

    val context = application

    private val authRepository = AuthRepository(application) // TODO: Use DI
    private val repository = Repository() // TODO: Use DI
    private val preferencesRepository = PreferencesRepository(application) // TODO: Use DI

    private val _termsAndConditionsText = MutableLiveData<SpannableString?>()
    val termsAndConditionsText: LiveData<SpannableString?>
        get() = _termsAndConditionsText

    private val _isTermsAndConditionsChecked = MutableLiveData<Boolean?>()
    val isTermsAndConditionsChecked: LiveData<Boolean?>
        get() = _isTermsAndConditionsChecked

    private val _usText = MutableLiveData<String?>()
    val usText: LiveData<String?>
        get() = _usText

    private val _isUSChecked = MutableLiveData<Boolean?>()
    val isUSChecked: LiveData<Boolean?>
        get() = _isUSChecked

    private val _w8BenFormText = MutableLiveData<SpannableString?>()
    val w8BenFormText: LiveData<SpannableString?>
        get() = _w8BenFormText

    private val _isW8BenFormChecked = MutableLiveData<Boolean?>()
    val isW8BenFormChecked: LiveData<Boolean?>
        get() = _isW8BenFormChecked

    private val _isNextButtonEnabled = MutableLiveData<Boolean?>()
    val isNextButtonEnabled: LiveData<Boolean?>
        get() = _isNextButtonEnabled

    private val _eventShowOrHideLoadingDialog = MutableLiveData<Boolean?>()
    val eventShowOrHideLoadingDialog: LiveData<Boolean?>
        get() = _eventShowOrHideLoadingDialog

    private val _eventShowUSDescription = MutableLiveData<GenericDescriptionScreenArguments?>()
    val eventShowUSDescription: LiveData<GenericDescriptionScreenArguments?>
        get() = _eventShowUSDescription

    private val _eventNavigateToWaitingScreen = MutableLiveData<Boolean?>()
    val eventNavigateToWaitingScreen: LiveData<Boolean?>
        get() = _eventNavigateToWaitingScreen

    private val _eventNavigateToBenFormScreen = MutableLiveData<Boolean?>()
    val eventNavigateToBenFormScreen: LiveData<Boolean?>
        get() = _eventNavigateToBenFormScreen

    private val _eventOpenBrowser = MutableLiveData<String?>()
    val eventOpenBrowser: LiveData<String?>
        get() = _eventOpenBrowser

    fun eventShowOrHideLoadingDialogCompleted() {
        _eventShowOrHideLoadingDialog.value = null
    }

    fun eventShowUSDescriptionCompleted() {
        _eventShowUSDescription.value = null
    }

    fun eventNavigateToWaitingScreenCompleted() {
        _eventNavigateToWaitingScreen.value = null
    }

    fun eventNavigateToBenFormScreenCompleted() {
        _eventNavigateToBenFormScreen.value = null
    }

    fun eventOpenBrowserCompleted() {
        _eventOpenBrowser.value = null
    }

    init {
        _isTermsAndConditionsChecked.value = false
        _isUSChecked.value = false
        _isW8BenFormChecked.value = false

        _termsAndConditionsText.value = generateTermsAndConditionsSpannableString()
        _usText.value = generateUSString()
        _w8BenFormText.value = generateW8BenFormSpannableString()

        refreshNextButton()
    }

    fun handleOnTermsAndConditionsCheckboxClicked() {
        val currentValue = _isTermsAndConditionsChecked.value ?: false
        _isTermsAndConditionsChecked.value = !currentValue

        refreshNextButton()
    }

    fun handleOnUSCheckboxClicked() {
        val currentValue = _isUSChecked.value ?: false
        _isUSChecked.value = !currentValue

        refreshNextButton()
    }

    fun handleOnUSInfoButtonClicked() {
        val title =
            context.resources.getString(R.string.terms_and_conditions_non_us_description_title)
        val description =
            context.resources.getString(R.string.terms_and_conditions_non_us_description_text)

        val spacing24 = context.resources.getDimensionPixelSize(R.dimen.spacing_24)
        val spacing40 = context.resources.getDimensionPixelSize(R.dimen.spacing_40)

        _eventShowUSDescription.value = GenericDescriptionScreenArguments(
            title = title,
            description = SpannableString(description),
            contentPaddingTop = spacing24,
            contentPaddingBottom = spacing40
        )
    }

    fun handleOnW8BenFormCheckboxClicked() {
        val currentValue = _isW8BenFormChecked.value ?: false
        _isW8BenFormChecked.value = !currentValue

        refreshNextButton()
    }

    fun handleOnNextButtonClicked() {
        submitDataAndContinue()
    }

    private fun getCredentialsCall(callback: ((succeeded: Boolean, accessToken: String?, idToken: String?) -> Unit)?) {
        authRepository.loadAuth0Credentials {
            when (it) {
                is NetworkResource.Success -> {
                    val accessToken = "Bearer ${it.data?.accessToken}"
                    val idToken = "Bearer ${it.data?.idToken}"

                    callback?.invoke(true, accessToken, idToken)
                }

                is NetworkResource.Failure -> {
                    callback?.invoke(false, null, null)
                }
            }
        }
    }

    private fun submitHasAcceptedTermsCall(
        accessToken: String,
        idToken: String,
        callback: ((succeeded: Boolean) -> Unit)?
    ) {
        viewModelScope.launch(Dispatchers.IO) {
            val result = repository.submitHasAcceptedTerms(
                accessToken = accessToken,
                idToken = idToken,
                hasAcceptedTerms = true
            )

            withContext(Dispatchers.Main) {
                when (result) {
                    is DomainResult.Success -> {
                        callback?.invoke(true)
                    }

                    else -> {
                        callback?.invoke(false)
                    }
                }
            }
        }
    }

    private fun submitDataAndContinue() {
        _eventShowOrHideLoadingDialog.value = true

        getCredentialsCall { _, accessToken, idToken ->
            if (accessToken == null || idToken == null) {
                // FIXME: Handle the error

                _eventShowOrHideLoadingDialog.value = false

                return@getCredentialsCall
            }

            submitHasAcceptedTermsCall(
                accessToken = accessToken,
                idToken = idToken
            ) { succeeded ->
                _eventShowOrHideLoadingDialog.value = false

                if (!succeeded) {
                    // FIXME: Handle the error
                    return@submitHasAcceptedTermsCall
                }

                _eventNavigateToWaitingScreen.value = true
            }
        }
    }

    private fun refreshNextButton() {
        val isTermsAndConditionsChecked = (_isTermsAndConditionsChecked.value == true)
        val isUSChecked = (_isUSChecked.value == true)
        val isW8BenFormChecked = (_isW8BenFormChecked.value == true)

        _isNextButtonEnabled.value =
            (isTermsAndConditionsChecked && isUSChecked && isW8BenFormChecked)
    }

    private fun handleOnInvestorTermsClicked() {
        val url = preferencesRepository.getLegalPages()?.platformInvestorTerms

        if (url == null) {
            Sentry.captureMessage("No platformInvestorTerms URL", SentryLevel.WARNING)

            return
        }

        _eventOpenBrowser.value = url
    }

    private fun handleOnW8BenFormClicked() {
        _eventNavigateToBenFormScreen.value = true
    }

    private fun generateTermsAndConditionsSpannableString(): SpannableString {
        val context = getApplication<Application>()
        val resources = context.resources

        val string = resources.getString(R.string.terms_and_conditions_checkbox_text)

        val linkSubstrings = string.findLinkSubstrings()

        val finalString = string.cleanSpecialFormattingCharacters()

        val spannableString = SpannableString(finalString)

        spannableString.formatSubstringsAsBold(
            context = context,
            substrings = linkSubstrings
        )

        val investorTermsClickableSpan = object : ClickableSpan() {

            override fun onClick(p0: View) {
                handleOnInvestorTermsClicked()
            }

            override fun updateDrawState(ds: TextPaint) {
                super.updateDrawState(ds)

                ds.isUnderlineText = false
            }
        }

        val clickableSpans = listOf(
            investorTermsClickableSpan
        )

        spannableString.formatSubstringsAsLinks(
            substrings = linkSubstrings,
            clickableSpans = clickableSpans
        )

        return spannableString
    }

    private fun generateUSString(): String {
        val resources = getApplication<Application>().resources
        return resources.getString(R.string.terms_and_conditions_us_checkbox_text)
    }

    private fun generateW8BenFormSpannableString(): SpannableString {
        val context = getApplication<Application>()
        val resources = context.resources

        val string = resources.getString(R.string.terms_and_conditions_w8_ben_form_checkbox_text)

        val linkSubstrings = string.findLinkSubstrings()

        val finalString = string.cleanSpecialFormattingCharacters()

        val spannableString = SpannableString(finalString)

        spannableString.formatSubstringsAsBold(
            context = context,
            substrings = linkSubstrings
        )

        val investorTermsClickableSpan = object : ClickableSpan() {

            override fun onClick(p0: View) {
                handleOnW8BenFormClicked()
            }

            override fun updateDrawState(ds: TextPaint) {
                super.updateDrawState(ds)

                ds.isUnderlineText = false
            }
        }

        val clickableSpans = listOf(
            investorTermsClickableSpan
        )

        spannableString.formatSubstringsAsLinks(
            substrings = linkSubstrings,
            clickableSpans = clickableSpans
        )

        return spannableString
    }
}
