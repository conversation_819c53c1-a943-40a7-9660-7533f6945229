package com.wealthyhood.wealthyhood.ui.assetdetails.recentactivity

import android.app.Activity
import android.app.Application
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import androidx.recyclerview.widget.RecyclerView
import com.wealthyhood.wealthyhood.R
import com.wealthyhood.wealthyhood.common.TargetDotSingleton
import com.wealthyhood.wealthyhood.database.Asset
import com.wealthyhood.wealthyhood.domain.InfoRowEnum
import com.wealthyhood.wealthyhood.extensions.findUserLocale
import com.wealthyhood.wealthyhood.extensions.formatAsPercentage
import com.wealthyhood.wealthyhood.extensions.generateETFOrderReceiptDialogArguments
import com.wealthyhood.wealthyhood.extensions.generateProviderLogoURI
import com.wealthyhood.wealthyhood.extensions.generateRewardReceiptDialogArguments
import com.wealthyhood.wealthyhood.extensions.getFXFee
import com.wealthyhood.wealthyhood.extensions.getIconDrawableRes
import com.wealthyhood.wealthyhood.model.ConfirmationReceiptScreenArgs
import com.wealthyhood.wealthyhood.model.DomainResult
import com.wealthyhood.wealthyhood.model.GenericErrorScreenArgs
import com.wealthyhood.wealthyhood.model.HandlePollingResultUseCase
import com.wealthyhood.wealthyhood.model.NetworkResource
import com.wealthyhood.wealthyhood.model.OrderReviewFragmentArguments
import com.wealthyhood.wealthyhood.model.PollingResult
import com.wealthyhood.wealthyhood.repository.AuthRepository
import com.wealthyhood.wealthyhood.repository.OnBoardingRepository
import com.wealthyhood.wealthyhood.repository.PlansRepository
import com.wealthyhood.wealthyhood.repository.PreferencesRepository
import com.wealthyhood.wealthyhood.repository.Repository
import com.wealthyhood.wealthyhood.service.AssetRecentActivityItem
import com.wealthyhood.wealthyhood.service.Order
import com.wealthyhood.wealthyhood.ui.assetdetails.AssetDetailsHelper
import com.wealthyhood.wealthyhood.ui.assetdetails.AssetDetailsListAdapter
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import java.util.Date

class RecentActivityViewModel(
    application: Application,
    private val assetID: String?,
    private val currentTickerPrice: Float?,
    private val tradedPrice: Float?,
    private val tradedCurrency: String?,
    private val userFullName: String?,
    private val planPriceAPIKey: String?
) : AndroidViewModel(application) {

    private val context = application

    private val authRepository = AuthRepository(application) // TODO: Use DI
    private val onBoardingRepository = OnBoardingRepository() // TODO: Use DI
    private val preferencesRepository = PreferencesRepository(application) // TODO: Use DI
    private val plansRepository = PlansRepository(preferencesRepository) // TODO: Use DI
    private val repository = Repository() // TODO: Use DI

    private val currencyISOCode = preferencesRepository.getUserCurrency()
    private val userLocale = preferencesRepository.findUserLocale()
    private val userCompanyEntity = preferencesRepository.getUserCompanyEntity()

    private val plans = plansRepository.getAllPlans()
    private val basePlans = plansRepository.getAllBasePlans(context)

    private val etfProviders = onBoardingRepository.getETFProviders(preferencesRepository)

    private var getAssetRecentActivityJob: Job? = null

    // Η τελευταία φορά που κάναμε update τα data εξαιτίας του polling
    private var latestPollingRefreshTimestamp = 0L

    private var asset: Asset? = null

    private val helper = AssetDetailsHelper(context)

    private val _navigationTitleText = MutableLiveData<String?>()
    val navigationTitleText: LiveData<String?>
        get() = _navigationTitleText

    private val _navigationBarElevation = MutableLiveData<Float?>()
    val navigationBarElevation: LiveData<Float?>
        get() = _navigationBarElevation

    private val _isLoadingIndicatorVisible = MutableLiveData<Boolean?>()
    val isLoadingIndicatorVisible: LiveData<Boolean?>
        get() = _isLoadingIndicatorVisible

    private val _dataItems = MutableLiveData<List<AssetDetailsListAdapter.DataItem>?>()
    val dataItems: LiveData<List<AssetDetailsListAdapter.DataItem>?>
        get() = _dataItems

    val eventRefreshTransactions = TargetDotSingleton.getInstance().pollingResult

    private val _eventShowOrHideLoadingDialog = MutableLiveData<Boolean?>()
    val eventShowOrHideLoadingDialog: LiveData<Boolean?>
        get() = _eventShowOrHideLoadingDialog

    private val _eventShowConfirmationReceiptDialog =
        MutableLiveData<ConfirmationReceiptScreenArgs?>()
    val eventShowConfirmationReceiptDialog: LiveData<ConfirmationReceiptScreenArgs?>
        get() = _eventShowConfirmationReceiptDialog

    private val _eventShowOrderReviewDialog = MutableLiveData<OrderReviewFragmentArguments?>()
    val eventShowOrderReviewDialog: LiveData<OrderReviewFragmentArguments?>
        get() = _eventShowOrderReviewDialog

    private val _eventShowGenericError = MutableLiveData<GenericErrorScreenArgs?>()
    val eventShowGenericError: LiveData<GenericErrorScreenArgs?>
        get() = _eventShowGenericError

    private val _eventShowFXRateDescriptionDialog = MutableLiveData<String?>()
    val eventShowFXRateDescriptionDialog: LiveData<String?>
        get() = _eventShowFXRateDescriptionDialog

    private val _eventSetActivityResult = MutableLiveData<Int?>()
    val eventSetActivityResult: LiveData<Int?>
        get() = _eventSetActivityResult

    fun eventShowOrHideLoadingDialogCompleted() {
        _eventShowOrHideLoadingDialog.value = null
    }

    fun eventShowConfirmationReceiptDialogCompleted() {
        _eventShowConfirmationReceiptDialog.value = null
    }

    fun eventShowOrderReviewDialogCompleted() {
        _eventShowOrderReviewDialog.value = null
    }

    fun eventShowGenericErrorCompleted() {
        _eventShowGenericError.value = null
    }

    fun eventShowFXRateDescriptionDialogCompleted() {
        _eventShowFXRateDescriptionDialog.value = null
    }

    fun eventSetActivityResultCompleted() {
        _eventSetActivityResult.value = null
    }

    init {
        refreshNavigationTitle()

        _isLoadingIndicatorVisible.value = false

        viewModelScope.launch {
            asset = onBoardingRepository.getAsset(context, assetID)

            refreshNavigationTitle()
            reloadData(didTriggerFromPendingOrdersPolling = false)
        }
    }

    fun handlePollingResult(pollingResult: PollingResult?) {
        HandlePollingResultUseCase().invoke(
            pollingResult = pollingResult,
            latestPollingRefreshTimestamp = latestPollingRefreshTimestamp
        ) {
            // FIXME: Show loader

            // FIXME: It would be better to update this after we successfully refresh the data.
            latestPollingRefreshTimestamp = Date().time

            reloadData(didTriggerFromPendingOrdersPolling = true)
        }
    }

    fun handleOnRecyclerViewScrolled(recyclerView: RecyclerView) {
        refreshNavigationBarElevation(recyclerView)
    }

    fun handleOnTransactionClicked(transactionItemID: String?) {
        if (transactionItemID == null) return

        val reward = helper.findRewardWithID(transactionItemID)

        if (reward != null) {
            handleOnRewardClicked(transactionItemID)

            return
        }

        val order = helper.findOrderWithID(transactionItemID)

        if (order != null) {
            handleOnOrderClicked(transactionItemID)

            return
        }

        handleOnDividendTransactionClicked(transactionItemID)
    }

    fun handleOnInfoRowInfoButtonClicked(infoRowID: String?) {
        if (infoRowID == InfoRowEnum.FX_RATE.rawValue) {
            showFXFeeDescription()
        }
    }

    fun handleOnGenericErrorRetryButtonClicked() {
        reloadData(didTriggerFromPendingOrdersPolling = false)
    }

    fun handleOnCancelButtonClicked(orderID: String?) {
        if (orderID == null) return

        cancelOrder(orderID)
    }

    private fun reloadData(didTriggerFromPendingOrdersPolling: Boolean) {
        if (!didTriggerFromPendingOrdersPolling) {
            _isLoadingIndicatorVisible.value = true
        }

        getCredentialsCall { succeeded, accessToken, idToken ->
            if (!succeeded || accessToken == null || idToken == null) {
                // FIXME: Handle the error

                _isLoadingIndicatorVisible.value = false

                return@getCredentialsCall
            }

            getAssetRecentActivityCall(
                accessToken = accessToken,
                idToken = idToken,
                assetID = asset?.id
            ) { _, recentActivityItems ->
                _isLoadingIndicatorVisible.value = false

                if (recentActivityItems == null && !didTriggerFromPendingOrdersPolling) {
                    showGenericError()

                    return@getAssetRecentActivityCall
                }

                helper.recentActivityObjects =
                    AssetDetailsHelper.generateRecentActivityObjects(recentActivityItems)

                refreshDataItems()
            }
        }
    }

    private fun getCredentialsCall(callback: ((succeeded: Boolean, accessToken: String?, idToken: String?) -> Unit)?) {
        authRepository.loadAuth0Credentials {
            when (it) {
                is NetworkResource.Success -> {
                    val accessToken = "Bearer ${it.data?.accessToken}"
                    val idToken = "Bearer ${it.data?.idToken}"

                    callback?.invoke(true, accessToken, idToken)
                }

                is NetworkResource.Failure -> {
                    callback?.invoke(false, null, null)
                }
            }
        }
    }

    private fun getAssetRecentActivityCall(
        accessToken: String,
        idToken: String,
        assetID: String?,
        callback: ((succeeded: Boolean, recentActivityItems: List<AssetRecentActivityItem>?) -> Unit)?
    ) {
        getAssetRecentActivityJob?.cancel()

        getAssetRecentActivityJob = viewModelScope.launch(Dispatchers.IO) {
            val result = repository.getAssetRecentActivity(
                accessToken = accessToken,
                idToken = idToken,
                assetID = assetID,
                limit = null
            )

            withContext(Dispatchers.Main) {
                when (result) {
                    is DomainResult.Success -> {
                        callback?.invoke(true, result.body)
                    }

                    else -> {
                        callback?.invoke(false, null)
                    }
                }
            }
        }
    }

    private fun cancelOrder(orderID: String) {
        _eventShowOrHideLoadingDialog.value = true

        getCredentialsCall { succeeded, accessToken, idToken ->
            if (!succeeded || accessToken == null || idToken == null) {
                // FIXME: Handle the error

                _eventShowOrHideLoadingDialog.value = false

                return@getCredentialsCall
            }

            viewModelScope.launch {
                val cancelResult = repository.cancelOrder(
                    accessToken = accessToken,
                    idToken = idToken,
                    orderID = orderID
                )

                if (cancelResult is DomainResult.Error) {
                    // FIXME: Handle the error

                    _eventShowOrHideLoadingDialog.value = false

                    return@launch
                }

                _eventSetActivityResult.value = Activity.RESULT_OK

                preferencesRepository.putLastTransactionCreatedAt(Date().time)

                // Fix: Cancel the previous job

                val getRecentActivityResult = repository.getAssetRecentActivity(
                    accessToken = accessToken,
                    idToken = idToken,
                    assetID = assetID,
                    limit = null
                )

                _eventShowOrHideLoadingDialog.value = false

                (getRecentActivityResult as? DomainResult.Success)?.let {
                    helper.recentActivityObjects =
                        AssetDetailsHelper.generateRecentActivityObjects(it.body)

                    refreshDataItems()
                }
            }
        }
    }

    private fun handleOnOrderClicked(orderID: String) {
        val order = helper.findOrderWithID(orderID) ?: return

        if (order.isMatched == true) {
            showOrderReceipt(order)

            return
        }

        showOrderReview(order)
    }

    private fun handleOnRewardClicked(rewardID: String) {
        val reward = helper.findRewardWithID(rewardID) ?: return
        val isUserEU = (userCompanyEntity == "WEALTHYHOOD_EUROPE")

        val screenArgs = reward.generateRewardReceiptDialogArguments(
            context = context,
            isUserEU = isUserEU,
            userFullName = userFullName,
            currentAsset = asset,
            userLocale = userLocale,
            etfProviders = etfProviders
        )

        _eventShowConfirmationReceiptDialog.value = screenArgs
    }

    private fun handleOnDividendTransactionClicked(transactionID: String) {
        _eventShowConfirmationReceiptDialog.value = helper.generateDividendReceiptScreenArguments(
            userFullName = userFullName,
            transactionID = transactionID,
            userCompanyEntity = userCompanyEntity,
            currentAsset = asset,
            userLocale = userLocale,
            etfProviders = etfProviders
        )
    }

    private fun showOrderReceipt(order: Order) {
        val isUserEU = (userCompanyEntity == "WEALTHYHOOD_EUROPE")

        val screenArgs = order.generateETFOrderReceiptDialogArguments(
            context = context,
            currentAsset = asset,
            isUserEU = isUserEU,
            userFullName = userFullName,
            currencyISOCode = currencyISOCode,
            userLocale = userLocale,
            etfProviders = etfProviders
        )

        _eventShowConfirmationReceiptDialog.value = screenArgs
    }

    private fun showOrderReview(order: Order) {
        val arguments = helper.generateOrderReviewScreenArguments(
            asset = asset,
            currentTickerPrice = currentTickerPrice,
            tradedPrice = tradedPrice,
            tradedCurrency = tradedCurrency,
            currencyISOCode = currencyISOCode,
            userLocale = userLocale,
            order = order,
            etfProviders = etfProviders
        )

        _eventShowOrderReviewDialog.value = arguments
    }

    private fun refreshDataItems() {
        _dataItems.value = generateDataItems()
    }

    private fun generateDataItems(): List<AssetDetailsListAdapter.DataItem> {
        val finalAnswer = mutableListOf<AssetDetailsListAdapter.DataItem>()

        generateRecentActivityItems()?.let {
            finalAnswer.addAll(it)
        }

        return finalAnswer
    }

    private fun generateRecentActivityItems(): List<AssetDetailsListAdapter.DataItem>? {
        if (currentTickerPrice == null) return null

        val transactionDataItems = AssetDetailsHelper.generateTransactionDataItems(
            context = context,
            currencyISOCode = currencyISOCode,
            userLocale = userLocale,
            assetTitle = asset?.title,
            assetDrawableRes = asset?.getIconDrawableRes(context),
            assetIconURI = asset?.generateProviderLogoURI(etfProviders),
            recentActivityObjects = helper.recentActivityObjects
        )

        if (transactionDataItems.isNullOrEmpty()) return null

        val finalAnswer = mutableListOf<AssetDetailsListAdapter.DataItem>()
        finalAnswer.addAll(transactionDataItems)

        return finalAnswer
    }

    private fun refreshNavigationTitle() {
        _navigationTitleText.value = asset?.title
    }

    private fun refreshNavigationBarElevation(recyclerView: RecyclerView) {
        val offset = recyclerView.computeVerticalScrollOffset()

        val elevationRes = if (offset > 0) R.dimen.spacing_2 else R.dimen.spacing_0
        val elevation = context.resources.getDimension(elevationRes)

        _navigationBarElevation.value = elevation
    }

    private fun showFXFeeDescription() {
        val plan = plans.find { it.keyName == planPriceAPIKey }

        val formattedFXFee =
            plan?.getFXFee(preferencesRepository, basePlans)?.formatAsPercentage(
                locale = userLocale
            )

        _eventShowFXRateDescriptionDialog.value = formattedFXFee
    }

    private fun showGenericError() {
        val buttonText = context.resources.getString(
            R.string.generic_error_button_try_again
        )

        val screenArgs = GenericErrorScreenArgs(
            isCancelable = false,
            buttonText = buttonText,
            extras = null
        )

        _eventShowGenericError.value = screenArgs
    }
}
