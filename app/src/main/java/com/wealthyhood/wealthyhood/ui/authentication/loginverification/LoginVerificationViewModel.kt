package com.wealthyhood.wealthyhood.ui.authentication.loginverification

import android.app.Application
import android.graphics.Typeface
import android.os.Build
import android.text.Spannable
import android.text.SpannableString
import android.text.style.ForegroundColorSpan
import android.text.style.StyleSpan
import android.text.style.TypefaceSpan
import androidx.core.content.ContextCompat
import androidx.core.content.res.ResourcesCompat
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import com.auth0.android.result.Credentials
import com.wealthyhood.wealthyhood.R
import com.wealthyhood.wealthyhood.common.ScreenChooser
import com.wealthyhood.wealthyhood.domain.EventEnum
import com.wealthyhood.wealthyhood.domain.EventsHelper
import com.wealthyhood.wealthyhood.extensions.saveLoginInfo
import com.wealthyhood.wealthyhood.model.GenericErrorScreenArgs
import com.wealthyhood.wealthyhood.model.NetworkCallState
import com.wealthyhood.wealthyhood.model.NetworkResource
import com.wealthyhood.wealthyhood.model.ScreenEnum
import com.wealthyhood.wealthyhood.repository.AuthRepository
import com.wealthyhood.wealthyhood.repository.PreferencesRepository

class LoginVerificationViewModel(
    application: Application,
    private val preferencesRepository: PreferencesRepository,
    private val authRepository: AuthRepository,
    private val mail: String?
) : AndroidViewModel(application) {

    private val context = application

    companion object {

        private const val VERIFICATION_CODE_MAX_LENGTH = 6
    }

    private val screenChooser = ScreenChooser(
        context = getApplication(),
        authRepository = authRepository,
        preferencesRepository = preferencesRepository,
        shouldSkipCheckPIN = true,
        shouldTriggerInvestedDashboardDataPreFetch = true,
        viewModelScope = viewModelScope
    ) {
        chooseScreen(it)
    }

    private val _subtitle = MutableLiveData<SpannableString?>()
    val subtitle: LiveData<SpannableString?>
        get() = _subtitle

    private val _verificationCode = MutableLiveData<String?>()
    val verificationCode: LiveData<String?>
        get() = _verificationCode

    private val _errorMessage = MutableLiveData<String?>()
    val errorMessage: LiveData<String?>
        get() = _errorMessage

    private val _isVerifyButtonEnabled = MutableLiveData<Boolean?>()
    val isVerifyButtonEnabled: LiveData<Boolean?>
        get() = _isVerifyButtonEnabled

    private val _networkCallState = MutableLiveData<NetworkCallState?>()
    val networkCallState: LiveData<NetworkCallState?>
        get() = _networkCallState

    private val _eventShowToast = MutableLiveData<String?>()
    val eventShowToast: LiveData<String?>
        get() = _eventShowToast

    private val _eventNavigateToScreen = MutableLiveData<ScreenEnum?>()
    val eventNavigateToScreen: LiveData<ScreenEnum?>
        get() = _eventNavigateToScreen

    private var _eventShowGenericError = MutableLiveData<GenericErrorScreenArgs?>()
    val eventShowGenericError: LiveData<GenericErrorScreenArgs?>
        get() = _eventShowGenericError

    fun eventShowToastCompleted() {
        _eventShowToast.value = null
    }

    fun eventNavigateToScreenCompleted() {
        _eventNavigateToScreen.value = null
    }

    fun eventShowGenericErrorCompleted() {
        _eventShowGenericError.value = null
    }

    init {
        _networkCallState.value = NetworkCallState.Completed
        refreshVerifyButton()

        refreshSubtitle()
        refreshErrorMessage(errorMessage = null)
    }

    fun handleOnVerificationCodeChanged(newVerificationCode: String?) {
        val verificationCodeBeforeRefreshing = _verificationCode.value

        refreshErrorMessage(null)
        refreshVerificationCode(newVerificationCode)

        refreshVerifyButton()

        val verificationCodeAfterRefreshing = _verificationCode.value

        automaticallyTriggerVerifyButtonIfNeeded(
            previousVerificationCode = verificationCodeBeforeRefreshing,
            newVerificationCode = verificationCodeAfterRefreshing
        )
    }

    fun handleOnResendButtonClicked() {
        loginWithMail()
    }

    fun handleOnVerifyButtonClick() {
        val verificationCode = _verificationCode.value ?: return

        if (mail == null) {
            val application = getApplication<Application>()
            val errorMessage = application.resources.getString(R.string.generic_error)

            refreshErrorMessage(errorMessage) // TODO: Use the final error message

            return
        }

        refreshErrorMessage(null)
        _networkCallState.value = NetworkCallState.Executing
        refreshVerifyButton()

        authRepository.getToken(mail, verificationCode) { networkResource ->
            when (networkResource) {
                is NetworkResource.Success -> {
                    handleGetTokenResponse(networkResource.data)
                }

                is NetworkResource.Failure -> {
                    val resources = getApplication<Application>().resources
                    val errorMessage = resources.getString(R.string.verification_error)

                    _networkCallState.value = NetworkCallState.Failed(errorMessage)
                    refreshVerifyButton()

                    refreshErrorMessage(errorMessage)
                }
            }
        }
    }

    private fun loginWithMail() {
        mail?.let {
            authRepository.loginWithMail(it) { networkResource ->
                when (networkResource) {
                    is NetworkResource.Success -> {
                        _eventShowToast.value =
                            getApplication<Application>().resources.getString(R.string.pin_re_send_message)
                    }

                    is NetworkResource.Failure -> {
                        // TODO: Use the final error message
                        val errorMessage = networkResource.message
                        refreshErrorMessage(errorMessage)
                    }
                }
            }
        }
    }

    private fun handleGetTokenResponse(credentials: Credentials?) {
        if (credentials == null) {
            val application = getApplication<Application>()
            val errorMessage = application.resources.getString(R.string.generic_network_error)

            _networkCallState.value = NetworkCallState.Failed(errorMessage)
            refreshVerifyButton()

            // TODO: Use the final error message
            refreshErrorMessage(errorMessage)

            return
        }

        callAuth(
            credentials = credentials
        )
    }

    private fun callAuth(credentials: Credentials) {
        val accessTokenHeader = "Bearer ${credentials.accessToken}"
        val idTokenHeader = "Bearer ${credentials.idToken}"

        authRepository.auth(accessToken = accessTokenHeader, idToken = idTokenHeader) {
            when (it) {
                is NetworkResource.Success -> {
                    val user = it.data?.users?.firstOrNull()

                    if (user?.isUserNew == true) {
                        EventsHelper.sendEvents(
                            context = getApplication(),
                            event = EventEnum.SIGNED_UP,
                            emptyMap()
                        )
                    }

                    val loginInfoSaved = user?.saveLoginInfo(
                        credentials = credentials,
                        preferencesRepository = preferencesRepository,
                        authRepository = authRepository
                    )

                    if (loginInfoSaved != true) {
                        val context = getApplication<Application>()
                        val resources = context.resources

                        // FIXME: Use the final error message
                        // FIXME: Redirect him to the login screen
                        val errorMessage = resources.getString(R.string.verification_error)
                        refreshErrorMessage(errorMessage)

                        _networkCallState.value = NetworkCallState.Failed(errorMessage)
                        refreshVerifyButton()

                        return@auth
                    }

                    navigateToScreen()
                }

                is NetworkResource.Failure -> {
                    // FIXME: Display the error and retry

                    _networkCallState.value = NetworkCallState.Failed(it.message)
                    refreshVerifyButton()

                    val context = getApplication<Application>()
                    val resources = context.resources

                    // FIXME: Use the final error message
                    val errorMessage = resources.getString(R.string.verification_error)
                    refreshErrorMessage(errorMessage)
                }
            }
        }
    }

    private fun automaticallyTriggerVerifyButtonIfNeeded(
        previousVerificationCode: String?,
        newVerificationCode: String?
    ) {
        if (previousVerificationCode == newVerificationCode) return

        val newVerificationCodeLength = newVerificationCode?.length ?: 0
        if (newVerificationCodeLength < VERIFICATION_CODE_MAX_LENGTH) return

        handleOnVerifyButtonClick()
    }

    fun navigateToScreen() {
        screenChooser.chooseScreen(shouldInvalidateData = true)
    }

    private fun chooseScreen(screen: ScreenEnum) {
        if (screen is ScreenEnum.Undefined) {
            showGenericError()
        } else {
            _eventNavigateToScreen.value = screen
        }
    }

    private fun refreshVerificationCode(newVerificationCode: String?) {
        val previousVerificationCode = _verificationCode.value
        if (newVerificationCode == previousVerificationCode) return

        _verificationCode.value = newVerificationCode
    }

    private fun refreshSubtitle() {
        _subtitle.value = generateSubtitleSpannableString()
    }

    private fun refreshErrorMessage(errorMessage: String?) {
        _errorMessage.value = errorMessage
    }

    private fun refreshVerifyButton() {
        _isVerifyButtonEnabled.value =
            (_verificationCode.value?.length == VERIFICATION_CODE_MAX_LENGTH) && _networkCallState.value != NetworkCallState.Executing
    }

    private fun generateSubtitleSpannableString(): SpannableString {
        val context = getApplication<Application>()
        val resources = context.resources

        val finalText = resources.getString(
            R.string.login_verification_header_subtitle,
            mail
        )

        val spannableString = SpannableString(finalText)

        if (mail.isNullOrBlank()) return spannableString

        val mailStartIndex = finalText.indexOf(mail)
        val mailEndIndex = mailStartIndex + mail.length

        if (mailStartIndex < 0) return spannableString

        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.P) {
            ResourcesCompat.getFont(context, R.font.poppins_semi_bold)?.let { typeface ->
                if (mailEndIndex >= mailStartIndex) {
                    spannableString.setSpan(
                        TypefaceSpan(typeface),
                        mailStartIndex,
                        mailEndIndex,
                        Spannable.SPAN_EXCLUSIVE_EXCLUSIVE
                    )
                }
            }
        } else {
            if (mailEndIndex >= mailStartIndex) {
                spannableString.setSpan(
                    StyleSpan(Typeface.BOLD),
                    mailStartIndex,
                    mailEndIndex,
                    Spannable.SPAN_EXCLUSIVE_EXCLUSIVE
                )
            }
        }

        if (mailEndIndex >= mailStartIndex) {
            val color = ContextCompat.getColor(context, R.color.wealthyhood_burple)

            spannableString.setSpan(
                ForegroundColorSpan(color),
                mailStartIndex,
                mailEndIndex,
                Spannable.SPAN_EXCLUSIVE_EXCLUSIVE
            )
        }

        return spannableString
    }

    private fun showGenericError() {
        val buttonText = context.resources.getString(
            R.string.generic_error_button_try_again
        )

        val screenArgs = GenericErrorScreenArgs(
            isCancelable = false,
            buttonText = buttonText,
            extras = null
        )

        _eventShowGenericError.value = screenArgs
    }
}
