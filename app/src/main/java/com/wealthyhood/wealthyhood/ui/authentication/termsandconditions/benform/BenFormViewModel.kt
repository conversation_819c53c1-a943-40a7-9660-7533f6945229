package com.wealthyhood.wealthyhood.ui.authentication.termsandconditions.benform

import android.app.Application
import android.text.SpannableString
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.recyclerview.widget.RecyclerView
import com.blongho.country_data.World
import com.wealthyhood.wealthyhood.R
import com.wealthyhood.wealthyhood.domain.GetUserUseCase
import com.wealthyhood.wealthyhood.extensions.cleanSpecialFormattingCharacters
import com.wealthyhood.wealthyhood.extensions.convertDateOfBirthToUTCLong
import com.wealthyhood.wealthyhood.extensions.findBoldSubstrings
import com.wealthyhood.wealthyhood.extensions.formatSubstringsAsBold
import com.wealthyhood.wealthyhood.extensions.formatSubstringsWithColor
import com.wealthyhood.wealthyhood.extensions.generateFullAddress
import com.wealthyhood.wealthyhood.extensions.generateFullName
import com.wealthyhood.wealthyhood.extensions.generateLocalisedCountryName
import com.wealthyhood.wealthyhood.extensions.generateStringUsingPattern
import com.wealthyhood.wealthyhood.extensions.getDrawableRes
import com.wealthyhood.wealthyhood.model.GenericErrorScreenArgs
import com.wealthyhood.wealthyhood.model.NetworkResource
import com.wealthyhood.wealthyhood.repository.AuthRepository
import com.wealthyhood.wealthyhood.repository.MyAccountRepository
import com.wealthyhood.wealthyhood.repository.PreferencesRepository
import com.wealthyhood.wealthyhood.service.GetUserQueryParams
import com.wealthyhood.wealthyhood.service.User
import com.wealthyhood.wealthyhood.ui.authentication.termsandconditions.benform.BenFormListAdapter.DataItem
import java.util.Date
import kotlin.math.abs

class BenFormViewModel(application: Application) : AndroidViewModel(application) {

    private val context = application

    private val authRepository = AuthRepository(application) // TODO: Use DI
    private val myAccountRepository = MyAccountRepository() // TODO: Use DI
    private val preferencesRepository = PreferencesRepository(application) // TODO: Use DI

    private val getUserUseCase = GetUserUseCase()

    private var user: User? = null

    private val _navigationBarElevation = MutableLiveData<Float?>()
    val navigationBarElevation: LiveData<Float?>
        get() = _navigationBarElevation

    private val _isNavigationTitleVisible = MutableLiveData<Boolean?>()
    val isNavigationTitleVisible: LiveData<Boolean?>
        get() = _isNavigationTitleVisible

    private val _navigationTitleText = MutableLiveData<String?>()
    val navigationTitleText: LiveData<String?>
        get() = _navigationTitleText

    private val _dataItems = MutableLiveData<List<DataItem>?>()
    val dataItems: LiveData<List<DataItem>?>
        get() = _dataItems

    private val _eventShowGenericError = MutableLiveData<GenericErrorScreenArgs?>()
    val eventShowGenericError: LiveData<GenericErrorScreenArgs?>
        get() = _eventShowGenericError

    fun eventShowGenericErrorCompleted() {
        _eventShowGenericError.value = null
    }

    init {
        World.init(getApplication())

        refreshNavigationTitle()
        refreshDataItems()

        reloadData()
    }

    fun handleOnRecyclerViewScrolled(recyclerView: RecyclerView) {
        refreshNavigationBarElevation(recyclerView)
        refreshNavigationBarTitleVisibility(recyclerView)
    }

    fun handleOnGenericErrorRetryButtonClicked() {
        reloadData()
    }

    private fun getCredentialsCall(callback: ((succeeded: Boolean, accessToken: String?, idToken: String?) -> Unit)?) {
        authRepository.loadAuth0Credentials {
            when (it) {
                is NetworkResource.Success -> {
                    val accessToken = "Bearer ${it.data?.accessToken}"
                    val idToken = "Bearer ${it.data?.idToken}"

                    callback?.invoke(true, accessToken, idToken)
                }

                is NetworkResource.Failure -> {
                    callback?.invoke(false, null, null)
                }
            }
        }
    }

    private fun getUserCall(
        accessToken: String,
        idToken: String,
        callback: ((succeeded: Boolean, user: User?) -> Unit)?
    ) {
        val params = GetUserQueryParams(
            populate = "addresses"
        )

        getUserUseCase(
            context = getApplication(),
            myAccountRepository = myAccountRepository,
            preferencesRepository = preferencesRepository,
            accessToken = accessToken,
            idToken = idToken,
            queryParams = params
        ) {
            when (it) {
                is NetworkResource.Success -> {
                    callback?.invoke(true, it.data)
                }

                is NetworkResource.Failure -> {
                    callback?.invoke(false, null)
                }
            }
        }
    }

    private fun reloadData() {
        getCredentialsCall { _, accessToken, idToken ->
            if (accessToken == null || idToken == null) {
                // FIXME: Handle the error

                return@getCredentialsCall
            }

            getUserCall(
                accessToken = accessToken,
                idToken = idToken
            ) { _, user ->
                if (user == null) {
                    showGenericError()

                    return@getUserCall
                }

                this.user = user

                refreshDataItems()
            }
        }
    }

    private fun refreshNavigationTitle() {
        _navigationTitleText.value = context.resources.getString(R.string.ben_form_screen_title)
    }

    private fun refreshDataItems() {
        _dataItems.value = generateDataItems()
    }

    private fun generateDataItems(): List<DataItem> {
        val finalAnswer = mutableListOf<DataItem>()

        finalAnswer.add(generateHeaderItem())
        finalAnswer.addAll(generatePersonalDetailsSectionItems())
        finalAnswer.add(generateCertificationSectionHeaderItem())
        finalAnswer.add(generateCertificationTextItem())

        return finalAnswer
    }

    private fun generateHeaderItem(): DataItem.HeaderItem {
        val spacing12 = context.resources.getDimensionPixelSize(R.dimen.spacing_12)
        val spacing32 = context.resources.getDimensionPixelSize(R.dimen.spacing_32)

        return DataItem.HeaderItem(
            id = "headerItem",
            paddingTop = spacing12,
            paddingBottom = spacing32,
            title = context.resources.getString(R.string.ben_form_screen_title)
        )
    }

    private fun generatePersonalDetailsSectionHeaderItem(): DataItem.SectionHeaderItem {
        val spacing0 = context.resources.getDimensionPixelSize(R.dimen.spacing_0)
        val spacing16 = context.resources.getDimensionPixelSize(R.dimen.spacing_16)

        return DataItem.SectionHeaderItem(
            id = "personalDetailsSectionHeaderItem",
            paddingStart = spacing16,
            paddingTop = spacing0,
            paddingEnd = spacing16,
            paddingBottom = spacing16,
            textAppearanceRes = R.style.HeadingsH3Mobile_PrimaryColor,
            title = context.resources.getString(R.string.ben_form_personal_details_section_header)
        )
    }

    private fun generatePersonalDetailsSectionItems(): List<DataItem> {
        val finalAnswer = mutableListOf<DataItem>()

        finalAnswer.add(generatePersonalDetailsSectionHeaderItem())

        if (user == null) {
            finalAnswer.add(generatePersonalDetailsLoadingItem())
        } else {
            finalAnswer.addAll(generateInfoRowItems())
        }

        return finalAnswer
    }

    private fun generatePersonalDetailsLoadingItem(): DataItem.LoadingItem {
        val spacing16 = context.resources.getDimensionPixelSize(R.dimen.spacing_16)

        return DataItem.LoadingItem(
            id = "personalDetailsLoadingItem",
            paddingTop = spacing16,
            paddingBottom = spacing16
        )
    }

    private fun generateInfoRowItems(): List<DataItem.InfoRowItem> {
        val finalAnswer = mutableListOf<DataItem.InfoRowItem>()

        val resources = context.resources

        val spacing0 = resources.getDimensionPixelSize(R.dimen.spacing_0)
        val spacing16 = resources.getDimensionPixelSize(R.dimen.spacing_16)

        finalAnswer.add(
            DataItem.InfoRowItem(
                id = "fullNameInfoRowItem",
                paddingTop = spacing16,
                paddingBottom = spacing0,
                labelText = resources.getString(R.string.full_name_label),
                valueText = user?.generateFullName(),
                valueDrawableRes = null,
                shouldShowSeparator = true
            )
        )

        val dateOfBirthValue = user?.convertDateOfBirthToUTCLong()?.let {
            val date = Date(it)

            date.generateStringUsingPattern(
                pattern = "dd MMM yyyy"
            )
        }
        finalAnswer.add(
            DataItem.InfoRowItem(
                id = "dateOfBirthInfoRowItem",
                paddingTop = spacing16,
                paddingBottom = spacing0,
                labelText = resources.getString(R.string.date_of_birth),
                valueText = dateOfBirthValue,
                valueDrawableRes = null,
                shouldShowSeparator = true
            )
        )

        val nationalityCountry = user?.nationalities?.firstOrNull()?.let { countryCode ->
            World.getCountryFrom(countryCode)
        }

        val flagImageDrawableRes = nationalityCountry?.alpha2?.let { alpha2 ->
            val flagImageName = alpha2.lowercase() + "_flag"
            context.getDrawableRes(flagImageName)
        }

        finalAnswer.add(
            DataItem.InfoRowItem(
                id = "nationalityInfoRowItem",
                paddingTop = spacing16,
                paddingBottom = spacing0,
                labelText = resources.getString(R.string.citizenship),
                valueText = nationalityCountry?.name,
                valueDrawableRes = flagImageDrawableRes,
                shouldShowSeparator = true
            )
        )

        val tinValueText = user?.taxResidency?.value ?: "-"

        finalAnswer.add(
            DataItem.InfoRowItem(
                id = "niNumberInfoRowItem",
                paddingTop = spacing16,
                paddingBottom = spacing0,
                labelText = resources.getString(R.string.ni_number_label),
                valueText = tinValueText,
                valueDrawableRes = null,
                shouldShowSeparator = true
            )
        )

        finalAnswer.add(
            DataItem.InfoRowItem(
                id = "emailInfoRowItem",
                paddingTop = spacing16,
                paddingBottom = spacing0,
                labelText = resources.getString(R.string.email_label),
                valueText = user?.email,
                valueDrawableRes = null,
                shouldShowSeparator = true
            )
        )

        finalAnswer.add(
            DataItem.InfoRowItem(
                id = "addressInfoRowItem",
                paddingTop = spacing16,
                paddingBottom = spacing0,
                labelText = resources.getString(R.string.address_label),
                valueText = user?.addresses?.firstOrNull()?.generateFullAddress(),
                valueDrawableRes = null,
                shouldShowSeparator = true
            )
        )

        finalAnswer.add(
            DataItem.InfoRowItem(
                id = "postcodeInfoRowItem",
                paddingTop = spacing16,
                paddingBottom = spacing0,
                labelText = resources.getString(R.string.post_code),
                valueText = user?.addresses?.firstOrNull()?.postCode,
                valueDrawableRes = null,
                shouldShowSeparator = true
            )
        )

        finalAnswer.add(
            DataItem.InfoRowItem(
                id = "countryInfoRowItem",
                paddingTop = spacing16,
                paddingBottom = spacing16,
                labelText = resources.getString(R.string.country),
                valueText = user?.addresses?.firstOrNull()?.generateLocalisedCountryName(),
                valueDrawableRes = null,
                shouldShowSeparator = false
            )
        )

        return finalAnswer
    }

    private fun generateCertificationSectionHeaderItem(): DataItem.SectionHeaderItem {
        val spacing16 = context.resources.getDimensionPixelSize(R.dimen.spacing_16)
        val spacing40 = context.resources.getDimensionPixelSize(R.dimen.spacing_40)

        return DataItem.SectionHeaderItem(
            id = "certificationSectionHeaderItem",
            paddingStart = spacing16,
            paddingTop = spacing40,
            paddingEnd = spacing16,
            paddingBottom = spacing16,
            textAppearanceRes = R.style.HeadingsH3Mobile_PrimaryColor,
            title = context.resources.getString(R.string.certification_label)
        )
    }

    private fun generateCertificationTextItem(): DataItem.TextItem {
        val spacing0 = context.resources.getDimensionPixelSize(R.dimen.spacing_0)
        val spacing40 = context.resources.getDimensionPixelSize(R.dimen.spacing_40)

        return DataItem.TextItem(
            id = "certificationTextItem",
            paddingTop = spacing0,
            paddingBottom = spacing40,
            text = generateCertificationSpannableString()
        )
    }

    private fun generateCertificationSpannableString(): SpannableString {
        val context = getApplication<Application>()
        val resources = context.resources

        val string = resources.getString(R.string.ben_form_certification_text)

        val linkSubstrings = string.findBoldSubstrings()

        val finalString = string.cleanSpecialFormattingCharacters()

        val spannableString = SpannableString(finalString)

        spannableString.formatSubstringsAsBold(
            context = context,
            substrings = linkSubstrings
        )

        spannableString.formatSubstringsWithColor(
            context = context,
            substrings = linkSubstrings,
            colorRes = R.color.grey_90
        )

        return spannableString
    }

    private fun refreshNavigationBarElevation(recyclerView: RecyclerView) {
        val offset = recyclerView.computeVerticalScrollOffset()

        val elevationRes = if (offset > 0) R.dimen.spacing_2 else R.dimen.spacing_0
        val elevation = context.resources.getDimension(elevationRes)

        _navigationBarElevation.value = elevation
    }

    private fun refreshNavigationBarTitleVisibility(recyclerView: RecyclerView) {
        _isNavigationTitleVisible.value = shouldShowNavigationBarTitle(recyclerView)
    }

    private fun shouldShowNavigationBarTitle(recyclerView: RecyclerView): Boolean {
        val view =
            recyclerView.findChildViewUnder(0f, 0f) ?: return false
        val viewHolder = recyclerView.findContainingViewHolder(view) ?: return false
        val position = viewHolder.absoluteAdapterPosition

        // There is also the following useful property: viewHolder.itemView.top

        if (position > 0) return true

        (viewHolder as? BenFormHeaderViewHolder)?.let {
            val viewHolderTop = abs(viewHolder.itemView.top)
            val titleBottom = it.computeTitleBottom()

            return viewHolderTop >= titleBottom
        }

        return false
    }

    private fun showGenericError() {
        val buttonText = context.resources.getString(
            R.string.generic_error_button_try_again
        )

        val screenArgs = GenericErrorScreenArgs(
            isCancelable = false,
            buttonText = buttonText,
            extras = null
        )

        _eventShowGenericError.value = screenArgs
    }
}
