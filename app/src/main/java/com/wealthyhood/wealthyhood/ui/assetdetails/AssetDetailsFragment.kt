package com.wealthyhood.wealthyhood.ui.assetdetails

import android.app.Activity
import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.net.Uri
import android.os.Bundle
import android.text.SpannableString
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.activity.result.contract.ActivityResultContracts
import androidx.core.content.ContextCompat
import androidx.core.view.ViewCompat
import androidx.core.view.WindowInsetsCompat
import androidx.core.view.updateLayoutParams
import androidx.fragment.app.Fragment
import androidx.lifecycle.ViewModelProvider
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.RecyclerView
import androidx.recyclerview.widget.SimpleItemAnimator
import com.github.mikephil.charting.data.Entry
import com.wealthyhood.wealthyhood.R
import com.wealthyhood.wealthyhood.common.INVESTMENT_PRODUCTS_UPDATED_ACTION
import com.wealthyhood.wealthyhood.databinding.FragmentAssetDetailsBinding
import com.wealthyhood.wealthyhood.extensions.animateTitleToAlpha
import com.wealthyhood.wealthyhood.extensions.findUserLocale
import com.wealthyhood.wealthyhood.extensions.setup
import com.wealthyhood.wealthyhood.extensions.updateElevation
import com.wealthyhood.wealthyhood.extensions.updateForTopInset
import com.wealthyhood.wealthyhood.extensions.updateTitleText
import com.wealthyhood.wealthyhood.model.AboutAssetScreenArgs
import com.wealthyhood.wealthyhood.model.AssetDetailsDateRange
import com.wealthyhood.wealthyhood.model.AssetNewsArticlesScreenArgs
import com.wealthyhood.wealthyhood.model.ConfirmationReceiptScreenArgs
import com.wealthyhood.wealthyhood.model.DescriptionScreenArgs
import com.wealthyhood.wealthyhood.model.DescriptionTypeEnum
import com.wealthyhood.wealthyhood.model.ETFInvestmentScreenArguments
import com.wealthyhood.wealthyhood.model.GenericDescriptionScreenArguments
import com.wealthyhood.wealthyhood.model.OrderReviewFragmentArguments
import com.wealthyhood.wealthyhood.model.OrderReviewFragmentArguments.Companion.convertToOrderReviewScreenArgs
import com.wealthyhood.wealthyhood.model.RecentActivityScreenArguments
import com.wealthyhood.wealthyhood.model.RegularBankTransferScreenArgs
import com.wealthyhood.wealthyhood.model.TopUpOptionsScreenArgs
import com.wealthyhood.wealthyhood.repository.PreferencesRepository
import com.wealthyhood.wealthyhood.ui.assetdetails.aboutasset.AboutAssetFragment
import com.wealthyhood.wealthyhood.ui.assetdetails.recentactivity.RecentActivityActivity
import com.wealthyhood.wealthyhood.ui.assetnewsarticles.AssetNewsArticlesActivity
import com.wealthyhood.wealthyhood.ui.confirmationreceipt.ConfirmationReceiptFragment
import com.wealthyhood.wealthyhood.ui.controlcenter.ControlCenterAutomationDescriptionFragment
import com.wealthyhood.wealthyhood.ui.description.DescriptionFragment
import com.wealthyhood.wealthyhood.ui.genericdescription.GenericDescriptionFragment
import com.wealthyhood.wealthyhood.ui.investment.etfbuy.ETFBuyActivity
import com.wealthyhood.wealthyhood.ui.investment.etfsell.ETFSellActivity
import com.wealthyhood.wealthyhood.ui.loadingdialog.LoadingDialogFragment
import com.wealthyhood.wealthyhood.ui.orderreview.OrderReviewFragment
import com.wealthyhood.wealthyhood.ui.regularbanktransfer.RegularBankTransferActivity
import com.wealthyhood.wealthyhood.ui.topupoptions.TopUpOptionsFragment
import com.wealthyhood.wealthyhood.viewholders.AssetNewsArticleViewHolder
import com.wealthyhood.wealthyhood.viewholders.TagViewHolder
import com.wealthyhood.wealthyhood.viewholders.TransactionViewHolder
import io.sentry.Sentry

class AssetDetailsFragment : Fragment() {

    companion object {

        private const val ARG_PORTFOLIO_ID = "AssetDetailsFragment.portfolioID"
        private const val ARG_ASSET_ID = "AssetDetailsFragment.assetID"
        private const val ARG_SHOULD_SHOW_ACTION_BUTTONS =
            "AssetDetailsFragment.shouldShowActionButtons"

        fun newInstance(
            portfolioID: String?,
            assetID: String?,
            shouldShowActionButtons: Boolean?
        ): AssetDetailsFragment {
            val arguments = Bundle()

            arguments.putString(ARG_PORTFOLIO_ID, portfolioID)
            arguments.putString(ARG_ASSET_ID, assetID)
            arguments.putBoolean(ARG_SHOULD_SHOW_ACTION_BUTTONS, shouldShowActionButtons ?: true)

            val fragment = AssetDetailsFragment()
            fragment.arguments = arguments

            return fragment
        }
    }

    private lateinit var _viewModel: AssetDetailsViewModel
    private val viewModel get() = _viewModel

    private lateinit var _binding: FragmentAssetDetailsBinding
    private val binding get() = _binding

    private var isNavigationTitleVisible = false

    private val investmentProductsUpdatedEventReceiver = object : BroadcastReceiver() {

        override fun onReceive(context: Context?, intent: Intent?) {
            handleOnInvestmentProductsUpdatedEventReceived()
        }
    }

    private val etfBuyActivityResultLauncher = registerForActivityResult(
        ActivityResultContracts.StartActivityForResult()
    ) { result ->
        if (result.resultCode == Activity.RESULT_OK) {
            requireActivity().setResult(Activity.RESULT_OK)
            requireActivity().finish()
        }
    }

    private val etfSellActivityResultLauncher = registerForActivityResult(
        ActivityResultContracts.StartActivityForResult()
    ) { result ->
        if (result.resultCode == Activity.RESULT_OK) {
            requireActivity().setResult(Activity.RESULT_OK)
            requireActivity().finish()
        }
    }

    private val reloadTransactionsLauncher = registerForActivityResult(
        ActivityResultContracts.StartActivityForResult()
    ) { result ->
        if (result.resultCode == Activity.RESULT_OK) {
            viewModel.handleOnReloadTransactionsRequest()
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        // Θέλουμε να παρακολουθούμε για events ακόμη και αν υπάρχει άλλο screen από πάνω.
        registerBroadcastReceiver()

        setupFragmentResultListeners()
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        setupViewModel()
        setupBinding(inflater, container)

        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        setupWindowInsetsListener()
        setupNavigationBar()
        setupButtons()
        setupRecyclerView()

        observeViewModel()
    }

    override fun onDestroy() {
        unregisterBroadcastReceiver()

        super.onDestroy()
    }

    private fun setupFragmentResultListeners() {
        childFragmentManager.setFragmentResultListener(
            OrderReviewFragment.REQUEST_KEY_SHOW_DESCRIPTION,
            this
        ) { _, bundle ->
            val infoRowID = bundle.getString(OrderReviewFragment.BUNDLE_KEY_INFO_ROW_ID)
            viewModel.handleOnInfoRowInfoButtonClicked(infoRowID)
        }

        childFragmentManager.setFragmentResultListener(
            OrderReviewFragment.REQUEST_KEY_CONFIRM,
            this
        ) { _, bundle ->
            val orderID = bundle.getString(OrderReviewFragment.BUNDLE_KEY_ITEM_ID)

            viewModel.handleOnCancelButtonClicked(orderID)
        }

        // TopUpOptionsFragment

        childFragmentManager.setFragmentResultListener(
            TopUpOptionsFragment.REQUEST_KEY_SELECT_OPTION,
            this
        ) { _, bundle ->
            val topUpOptionID = bundle.getString(TopUpOptionsFragment.BUNDLE_KEY_SELECTED_OPTION_ID)

            viewModel.handleOnTopUpOptionClicked(topUpOptionID)
        }
    }

    private fun setupViewModel() {
        val portfolioID = arguments?.getString(ARG_PORTFOLIO_ID)
        val assetID = arguments?.getString(ARG_ASSET_ID)
        val shouldShowActionButtons = arguments?.getBoolean(ARG_SHOULD_SHOW_ACTION_BUTTONS)

        val viewModelFactory = AssetDetailsViewModelFactory(
            application = requireActivity().application,
            portfolioID = portfolioID,
            assetID = assetID,
            shouldShowActionButtons = shouldShowActionButtons
        )

        val viewModelClass = AssetDetailsViewModel::class.java
        _viewModel = ViewModelProvider(this, viewModelFactory)[viewModelClass]
    }

    private fun setupBinding(inflater: LayoutInflater, container: ViewGroup?) {
        _binding = FragmentAssetDetailsBinding.inflate(inflater, container, false)
    }

    private fun setupWindowInsetsListener() {
        // https://developer.android.com/develop/ui/views/layout/sw-keyboard
        // https://developer.android.com/develop/ui/views/layout/edge-to-edge

        ViewCompat.setOnApplyWindowInsetsListener(binding.root) { _, windowInsets ->
            val insets = windowInsets.getInsets(WindowInsetsCompat.Type.systemBars())

            binding.navigationBarComponent.updateForTopInset(insets.top)

            binding.bottomButtonsConstraintLayout.updateLayoutParams<ViewGroup.MarginLayoutParams> {
                bottomMargin = insets.bottom
            }

            WindowInsetsCompat.CONSUMED
        }
    }

    private fun registerBroadcastReceiver() {
        val activity = activity ?: return

        val filter = IntentFilter(INVESTMENT_PRODUCTS_UPDATED_ACTION)
        val flags = ContextCompat.RECEIVER_NOT_EXPORTED

        ContextCompat.registerReceiver(
            activity,
            investmentProductsUpdatedEventReceiver,
            filter,
            flags
        )
    }

    private fun unregisterBroadcastReceiver() {
        activity?.unregisterReceiver(investmentProductsUpdatedEventReceiver)
    }

    private fun handleOnInvestmentProductsUpdatedEventReceived() {
        viewModel.handleOnInvestmentProductsUpdatedEventReceived()
    }

    private fun setupNavigationBar() {
        binding.navigationBarComponent.setup(
            shouldShowCloseButton = true,
            closeButtonCallback = {
                requireActivity().finish()
            },
            titleText = null,
            titleAlpha = 0f,
            rightButtonProperties = null
        )
    }

    private fun setupButtons() {
        binding.buyButton.setOnClickListener {
            viewModel.handleOnBuyButtonClicked()
        }

        binding.sellButton.setOnClickListener {
            viewModel.handleOnSellButtonClicked()
        }
    }

    private fun setupRecyclerView() {
        val spanCount = 2

        val gridLayoutManager = GridLayoutManager(requireContext(), spanCount)

        gridLayoutManager.spanSizeLookup = object : GridLayoutManager.SpanSizeLookup() {

            override fun getSpanSize(position: Int): Int {

                return when (binding.recyclerView.adapter?.getItemViewType(position)) {
                    AssetDetailsListAdapter.ITEM_VIEW_TYPE_TOP_HOLDING -> 1
                    else -> spanCount
                }
            }
        }

        (binding.recyclerView.itemAnimator as? SimpleItemAnimator)?.supportsChangeAnimations = false
        binding.recyclerView.layoutManager = gridLayoutManager

        val headerListener =
            object : AssetDetailsHeaderViewHolder.AssetDetailsHeaderViewHolderListener {

                override fun onExpandButtonClicked() {
                    viewModel.handleOnHeaderExpandButtonClicked()
                }
            }

        val sectionHeaderListener = object :
            AssetDetailsSectionHeaderViewHolder.AssetDetailsSectionHeaderViewHolderListener {

            override fun onInfoButtonClicked(sectionHeaderID: String?) {
                viewModel.handleOnSectionHeaderInfoButtonClicked(sectionHeaderID)
            }

            override fun onSeeAllButtonClicked(sectionHeaderID: String?) {
                viewModel.handleOnSectionHeaderSeeAllButtonClicked(sectionHeaderID)
            }
        }

        val lineChartListener =
            object : AssetDetailsPerformanceViewHolder.AssetDetailsPerformanceViewHolderListener {

                override fun onChartEntrySelected(entry: Entry?) {
                    viewModel.handleOnChartEntrySelected(entry)
                }

                override fun onDateRangeSelected(dateRange: AssetDetailsDateRange) {
                    viewModel.handleOnDateRangeSelected(dateRange)
                }

                override fun requestDisallowInterceptTouchEvent(disallow: Boolean) {
                    binding.recyclerView.requestDisallowInterceptTouchEvent(disallow)
                }

                override fun onChartErrorTryAgainButtonPressed() {
                    viewModel.handleOnChartErrorTryAgainClicked()
                }

                override fun shouldShowMarkerForEntry(entry: Entry?): Boolean {
                    return viewModel.shouldShowMarkerForEntry(entry)
                }

                override fun getMarkerTypeTextForEntry(entry: Entry?): String? {
                    return viewModel.getMarkerTypeTextForEntry(entry)
                }

                override fun getMarkerQuantityTextForEntry(entry: Entry?): String? {
                    return viewModel.getMarkerQuantityTextForEntry(entry)
                }
            }

        val doubleInfoRowListener = object :
            AssetDetailsDoubleInfoRowViewHolder.AssetDetailsDoubleInfoRowViewHolderListener {

            override fun onInfoButtonClicked(descriptionType: DescriptionTypeEnum?) {
                viewModel.handleOnMetricInfoButtonClicked(descriptionType)
            }
        }

        val newsArticleListener =
            object : AssetNewsArticleViewHolder.AssetNewsArticleViewHolderListener {

                override fun onItemClicked(itemID: String?) {
                    viewModel.handleOnNewsArticleClicked(itemID)
                }
            }

        val kidListener = object : AssetDetailsKIDViewHolder.AssetDetailsKIDViewHolderListener {

            override fun onItemClicked() {
                viewModel.handleOnKIDItemClicked()
            }
        }

        val footerListener = object :
            AssetDetailsAveragePriceTargetViewHolder.AssetDetailsAveragePriceTargetViewHolderListener {

            override fun onInfoButtonClicked() {
                viewModel.handleOnAveragePriceTargetInfoButtonClicked()
            }
        }

        val tagListener = object : TagViewHolder.TagViewHolderListener {

            override fun onItemClicked(itemID: String?) {
                viewModel.handleOnTagClicked(itemID)
            }
        }

        val transactionListener = object : TransactionViewHolder.TransactionViewHolderListener {

            override fun onItemClicked(itemID: String?) {
                viewModel.handleOnTransactionClicked(itemID)
            }
        }

        // TODO: Use MVVM

        val userLocale = PreferencesRepository(requireContext()).findUserLocale()

        binding.recyclerView.adapter = AssetDetailsListAdapter(
            userLocale = userLocale,
            headerListener = headerListener,
            topHoldingsListener = null,
            sectionHeaderListener = sectionHeaderListener,
            lineChartListener = lineChartListener,
            doubleInfoRowListener = doubleInfoRowListener,
            newsArticleListener = newsArticleListener,
            kidListener = kidListener,
            footerListener = footerListener,
            tagListener = tagListener,
            transactionListener = transactionListener
        )

        binding.recyclerView.addOnScrollListener(object : RecyclerView.OnScrollListener() {

            override fun onScrolled(recyclerView: RecyclerView, dx: Int, dy: Int) {
                super.onScrolled(recyclerView, dx, dy)

                viewModel.handleOnRecyclerViewScrolled(recyclerView)
            }
        })
    }

    private fun observeViewModel() {
        viewModel.navigationBarElevation.observe(viewLifecycleOwner) {
            refreshNavigationBarElevation(it)
        }

        viewModel.isNavigationTitleVisible.observe(viewLifecycleOwner) {
            refreshNavigationBarTitleVisibility(it)
        }

        viewModel.navigationTitleText.observe(viewLifecycleOwner) {
            refreshNavigationTitleText(it)
        }

        viewModel.dataItems.observe(viewLifecycleOwner) {
            refreshDataItems(it)
        }

        viewModel.isBuyButtonVisible.observe(viewLifecycleOwner) {
            refreshBuyButtonVisibility(it)
        }

        viewModel.isSellButtonVisible.observe(viewLifecycleOwner) {
            refreshSellButtonVisibility(it)
        }

        viewModel.eventRefreshTransactions.observe(viewLifecycleOwner) {
            viewModel.handlePollingResult(it)
        }

        viewModel.eventShowOrHideLoadingDialog.observe(viewLifecycleOwner) {
            it?.let {
                if (it) showLoading()
                else hideLoading()

                viewModel.eventShowOrHideLoadingDialogCompleted()
            }
        }

        viewModel.eventShowCompanyDetailsDialog.observe(viewLifecycleOwner) {
            it?.let {
                showCompanyDetailsDialog(it)

                viewModel.eventShowCompanyDetailsDialogCompleted()
            }
        }

        viewModel.eventShowFXRateDescriptionDialog.observe(viewLifecycleOwner) {
            it?.let {
                showFXRateDescriptionDialog(it)

                viewModel.eventShowFXRateDescriptionDialogCompleted()
            }
        }

        viewModel.eventShowDescriptionDialog.observe(viewLifecycleOwner) {
            it?.let {
                showDescriptionDialog(it)

                viewModel.eventShowDescriptionDialogCompleted()
            }
        }

        viewModel.eventShowGenericDescription.observe(viewLifecycleOwner) {
            it?.let {
                showGenericDescription(it)

                viewModel.eventShowGenericDescriptionCompleted()
            }
        }

        viewModel.eventShowConfirmationReceiptDialog.observe(viewLifecycleOwner) {
            it?.let {
                showConfirmationReceiptDialog(it)

                viewModel.eventShowConfirmationReceiptDialogCompleted()
            }
        }

        viewModel.eventShowOrderReviewDialog.observe(viewLifecycleOwner) {
            it?.let {
                showOrderReviewDialog(it)

                viewModel.eventShowOrderReviewDialogCompleted()
            }
        }

        viewModel.eventNavigateToETFBuyScreen.observe(viewLifecycleOwner) {
            it?.let {
                navigateToETFBuyScreen(it)

                viewModel.eventNavigateToETFBuyScreenCompleted()
            }
        }

        viewModel.eventNavigateToETFSellScreen.observe(viewLifecycleOwner) {
            it?.let {
                navigateToETFSellScreen(it)

                viewModel.eventNavigateToETFSellScreenCompleted()
            }
        }

        viewModel.eventNavigateToRecentActivityScreen.observe(viewLifecycleOwner) {
            it?.let {
                navigateToRecentActivityScreen(it)

                viewModel.eventNavigateToRecentActivityScreenCompleted()
            }
        }

        viewModel.eventOpenBrowser.observe(viewLifecycleOwner) {
            it?.let {
                openBrowser(it)

                viewModel.eventOpenBrowserCompleted()
            }
        }

        viewModel.eventNavigateToNewsArticlesScreen.observe(viewLifecycleOwner) {
            it?.let {
                navigateToNewsArticlesScreen(it)

                viewModel.eventNavigateToNewsArticlesScreenCompleted()
            }
        }

        viewModel.eventShowTopUpOptionsDialog.observe(viewLifecycleOwner) {
            it?.let {
                showTopUpOptionsDialog(it)

                viewModel.eventShowTopUpOptionsDialogCompleted()
            }
        }

        viewModel.eventNavigateToRegularBankTransfer.observe(viewLifecycleOwner) {
            it?.let {
                navigateToRegularBankTransfer(it)

                viewModel.eventNavigateToRegularBankTransferCompleted()
            }
        }
    }

    private fun refreshNavigationBarElevation(elevation: Float?) {
        binding.navigationBarComponent.updateElevation(elevation)
    }

    private fun refreshNavigationBarTitleVisibility(isVisible: Boolean?) {
        if (isVisible == null) return
        if (isVisible == isNavigationTitleVisible) return

        isNavigationTitleVisible = isVisible

        val toAlpha = if (isVisible) 1.0f else 0f

        binding.navigationBarComponent.animateTitleToAlpha(toAlpha)
    }

    private fun refreshNavigationTitleText(text: String?) {
        binding.navigationBarComponent.updateTitleText(text)
    }

    private fun refreshDataItems(dataItems: List<AssetDetailsListAdapter.DataItem>?) {
        (binding.recyclerView.adapter as? AssetDetailsListAdapter)?.submitList(dataItems)
    }

    private fun refreshBuyButtonVisibility(isVisible: Boolean?) {
        val visibility = if (isVisible == true) View.VISIBLE else View.GONE
        binding.buyButton.visibility = visibility
    }

    private fun refreshSellButtonVisibility(isVisible: Boolean?) {
        val visibility = if (isVisible == true) View.VISIBLE else View.GONE
        binding.sellButton.visibility = visibility
    }

    private fun showLoading() {
        val isShowingLoadingDialog =
            parentFragmentManager.findFragmentByTag(LoadingDialogFragment.TAG) != null

        if (isShowingLoadingDialog) return

        val fragment = LoadingDialogFragment.newInstance(
            isWithRoundedCorners = false
        )

        parentFragmentManager.beginTransaction()
            .add(R.id.fragment_container, fragment, LoadingDialogFragment.TAG)
            .commit()
    }

    private fun hideLoading() {
        val loadingDialog =
            parentFragmentManager.findFragmentByTag(LoadingDialogFragment.TAG) ?: return

        parentFragmentManager.beginTransaction()
            .remove(loadingDialog)
            .commit()
    }

    private fun showCompanyDetailsDialog(screenArgs: AboutAssetScreenArgs) {
        val fragment = AboutAssetFragment.newInstance(screenArgs)
        fragment.show(childFragmentManager, "CompanyDetailsFragment")
    }

    private fun showFXRateDescriptionDialog(formattedFXRate: String) {
        val title = resources.getString(R.string.fx_rate_description_title)

        val description = resources.getString(
            R.string.fx_rate_description_text,
            formattedFXRate
        )

        // TODO: Create a generic Fragment to use.
        val fragment = ControlCenterAutomationDescriptionFragment.newInstance(
            title = title,
            description = SpannableString(description),
            shouldShowCloseButton = false,
            closeButtonText = null,
            shouldCreateRebalanceAutomationAfterClosing = false
        )

        fragment.show(childFragmentManager, "ControlCenterAutomationDescriptionFragment")
    }

    private fun showDescriptionDialog(screenArgs: DescriptionScreenArgs) {
        val fragment = DescriptionFragment.newInstance(screenArgs)

        fragment.show(childFragmentManager, "DescriptionFragment")
    }

    private fun showGenericDescription(arguments: GenericDescriptionScreenArguments) {
        val fragment = GenericDescriptionFragment.newInstance(
            title = arguments.title,
            description = arguments.description,
            contentPaddingTop = arguments.contentPaddingTop,
            contentPaddingBottom = arguments.contentPaddingBottom
        )

        fragment.show(childFragmentManager, "GenericDescriptionFragment")
    }

    private fun showConfirmationReceiptDialog(screenArgs: ConfirmationReceiptScreenArgs) {
        val fragment = ConfirmationReceiptFragment.newInstance(screenArgs)

        fragment.show(childFragmentManager, "ConfirmationReceiptFragment")
    }

    private fun showOrderReviewDialog(arguments: OrderReviewFragmentArguments) {
        val screenArgs = arguments.convertToOrderReviewScreenArgs()
        val fragment = OrderReviewFragment.newInstance(screenArgs)

        fragment.show(childFragmentManager, "OrderReviewFragment")
    }

    private fun navigateToETFBuyScreen(arguments: ETFInvestmentScreenArguments) {
        val intent = ETFBuyActivity.newIntent(
            context = requireContext(),
            etfID = arguments.etfID,
            etfName = arguments.etfName,
            nextMarketOpen = arguments.nextMarketOpen
        )

        etfBuyActivityResultLauncher.launch(intent)
    }

    private fun navigateToETFSellScreen(arguments: ETFInvestmentScreenArguments) {

        val intent = ETFSellActivity.newIntent(
            context = requireContext(),
            etfID = arguments.etfID,
            etfName = arguments.etfName,
            nextMarketOpen = arguments.nextMarketOpen
        )

        etfSellActivityResultLauncher.launch(intent)
    }

    private fun navigateToRecentActivityScreen(arguments: RecentActivityScreenArguments) {
        val intent = RecentActivityActivity.newIntent(
            context = requireContext(),
            assetID = arguments.assetID,
            currentTickerPrice = arguments.currentTickerPrice,
            tradedPrice = arguments.tradedPrice,
            tradedCurrency = arguments.tradedCurrency,
            userFullName = arguments.userFullName,
            planPriceAPIKey = arguments.planPriceAPIKey
        )

        reloadTransactionsLauncher.launch(intent)
    }

    private fun openBrowser(link: String) {
        val intent = Intent(Intent.ACTION_VIEW, Uri.parse(link))

        try {
            startActivity(intent)
        } catch (e: Exception) {
            // FIXME: Inform the user about the error.
            Sentry.captureMessage("Could not open the link in the Browser: ${e.message}")
        }
    }

    private fun navigateToNewsArticlesScreen(screenArgs: AssetNewsArticlesScreenArgs) {
        val intent = AssetNewsArticlesActivity.newIntent(
            context = requireContext(),
            screenArgs = screenArgs
        )

        startActivity(intent)
    }

    private fun showTopUpOptionsDialog(screenArgs: TopUpOptionsScreenArgs) {
        val fragment = TopUpOptionsFragment.newInstance(screenArgs)

        fragment.show(childFragmentManager, "TopUpOptionsFragment")
    }

    private fun navigateToRegularBankTransfer(screenArgs: RegularBankTransferScreenArgs) {
        val intent = RegularBankTransferActivity.newIntent(requireContext(), screenArgs)

        startActivity(intent)
    }
}
