package com.wealthyhood.wealthyhood.ui.confirmationreceipt.viewholder

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.core.view.updatePadding
import androidx.recyclerview.widget.RecyclerView
import com.wealthyhood.wealthyhood.R
import com.wealthyhood.wealthyhood.databinding.ListItemConfirmationReceiptDateBinding

class ConfirmationReceiptDateViewHolder private constructor(
    private val binding: ListItemConfirmationReceiptDateBinding
) : RecyclerView.ViewHolder(binding.root) {

    companion object {

        fun from(parent: ViewGroup): ConfirmationReceiptDateViewHolder {
            val layoutInflater = LayoutInflater.from(parent.context)

            val binding =
                ListItemConfirmationReceiptDateBinding.inflate(layoutInflater, parent, false)

            return ConfirmationReceiptDateViewHolder(binding)
        }
    }

    fun bind(
        paddingStart: Int?,
        paddingTop: Int?,
        paddingEnd: Int?,
        paddingBottom: Int?,
        dateText: String?,
        timeText: String?
    ) {
        refreshPadding(
            paddingStart = paddingStart,
            paddingTop = paddingTop,
            paddingEnd = paddingEnd,
            paddingBottom = paddingBottom
        )

        refreshDate(dateText)
        refreshTime(timeText)
    }

    private fun refreshPadding(
        paddingStart: Int?,
        paddingTop: Int?,
        paddingEnd: Int?,
        paddingBottom: Int?
    ) {
        val spacing0 = itemView.context.resources.getDimensionPixelSize(R.dimen.spacing_0)

        val finalPaddingStart = paddingStart ?: spacing0
        val finalPaddingTop = paddingTop ?: spacing0
        val finalPaddingEnd = paddingEnd ?: spacing0
        val finalPaddingBottom = paddingBottom ?: spacing0

        binding.rootConstraintLayout.updatePadding(
            finalPaddingStart,
            finalPaddingTop,
            finalPaddingEnd,
            finalPaddingBottom
        )
    }

    private fun refreshDate(text: String?) {
        binding.dateTextView.text = text
    }

    private fun refreshTime(text: String?) {
        binding.timeTextView.text = text
    }
}
