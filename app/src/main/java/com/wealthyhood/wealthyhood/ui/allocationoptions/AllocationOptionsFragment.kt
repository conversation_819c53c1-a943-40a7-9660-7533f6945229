package com.wealthyhood.wealthyhood.ui.allocationoptions

import android.app.Dialog
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.setFragmentResult
import androidx.lifecycle.ViewModelProvider
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.SimpleItemAnimator
import com.google.android.material.bottomsheet.BottomSheetDialog
import com.google.android.material.bottomsheet.BottomSheetDialogFragment
import com.google.gson.Gson
import com.wealthyhood.wealthyhood.R
import com.wealthyhood.wealthyhood.databinding.FragmentAllocationOptionsBinding
import com.wealthyhood.wealthyhood.extensions.convertToAllocationOptionsScreenArgs
import com.wealthyhood.wealthyhood.extensions.showCustomSnack
import com.wealthyhood.wealthyhood.model.AllocationOptionsScreenArgs
import com.wealthyhood.wealthyhood.model.GenericDescriptionScreenArguments
import com.wealthyhood.wealthyhood.ui.genericdescription.GenericDescriptionFragment
import com.wealthyhood.wealthyhood.ui.investment.portfoliobuy.scheduleoptions.ScheduleOptionsListAdapter
import com.wealthyhood.wealthyhood.ui.investment.portfoliobuy.scheduleoptions.ScheduleOptionsOptionViewHolder

@Deprecated("ScheduleOptionsFragment, AllocationOptionsFragment and TopUpOptionsFragment should be one")
class AllocationOptionsFragment : BottomSheetDialogFragment() {

    companion object {

        private const val ARG_SCREEN_ARGS = "AllocationOptionsFragment.screenArgs"

        const val REQUEST_KEY_SELECT_OPTION = "AllocationOptionsFragment.selectOption"

        const val BUNDLE_KEY_SELECTED_OPTION_ID = "AllocationOptionsFragment.selectedOptionID"

        fun newInstance(screenArgs: AllocationOptionsScreenArgs): AllocationOptionsFragment {
            val arguments = Bundle()

            val screenArgsJSONString = Gson().toJson(screenArgs)
            arguments.putString(ARG_SCREEN_ARGS, screenArgsJSONString)

            val fragment = AllocationOptionsFragment()
            fragment.arguments = arguments

            return fragment
        }
    }

    private lateinit var _viewModel: AllocationOptionsViewModel
    private val viewModel get() = _viewModel

    private lateinit var _binding: FragmentAllocationOptionsBinding
    private val binding get() = _binding

    override fun onCreateDialog(savedInstanceState: Bundle?): Dialog {
        val dialog = super.onCreateDialog(savedInstanceState)

        (dialog as? BottomSheetDialog)?.behavior?.let { behavior ->
            behavior.skipCollapsed = true
            behavior.isFitToContents = true

            behavior.isDraggable = true
            behavior.isHideable = true
        }

        isCancelable = true

        return dialog
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        setupViewModel()
        setupBinding(inflater, container)

        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        setupRecyclerView()

        observeViewModel()
    }

    override fun getTheme(): Int {
        return R.style.CustomBottomSheetDialogTheme
    }

    private fun setupViewModel() {
        val screenArgsJSONString = arguments?.getString(ARG_SCREEN_ARGS)
        val screenArgs = screenArgsJSONString?.convertToAllocationOptionsScreenArgs()

        val viewModelFactory = AllocationOptionsViewModelFactory(
            application = requireActivity().application,
            screenArgs = screenArgs
        )

        val viewModelClass = AllocationOptionsViewModel::class.java
        _viewModel = ViewModelProvider(this, viewModelFactory)[viewModelClass]
    }

    private fun setupBinding(inflater: LayoutInflater, container: ViewGroup?) {
        _binding = FragmentAllocationOptionsBinding.inflate(inflater, container, false)
    }

    private fun setupRecyclerView() {
        (binding.recyclerView.itemAnimator as? SimpleItemAnimator)?.supportsChangeAnimations = false
        binding.recyclerView.layoutManager = LinearLayoutManager(requireContext())

        val optionListener =
            object : ScheduleOptionsOptionViewHolder.ScheduleOptionsOptionViewHolderListener {

                override fun onOptionClicked(optionID: String?) {
                    viewModel.handleOnOptionClicked(optionID)
                }

                override fun onInfoButtonClicked(optionID: String?) {
                    viewModel.handleOnScheduleOptionsInfoButtonClicked(optionID)
                }
            }

        binding.recyclerView.adapter = ScheduleOptionsListAdapter(
            optionListener = optionListener
        )
    }

    private fun observeViewModel() {
        viewModel.dataItems.observe(viewLifecycleOwner) {
            refreshDataItems(it)
        }

        viewModel.eventShowGenericDescription.observe(viewLifecycleOwner) {
            it?.let {
                showGenericDescription(it)

                viewModel.eventShowGenericDescriptionCompleted()
            }
        }

        viewModel.eventShowCustomSnack.observe(viewLifecycleOwner) {
            it?.let {
                showCustomSnack(it)

                viewModel.eventShowCustomSnackCompleted()
            }
        }

        viewModel.eventAskForOptionSelection.observe(viewLifecycleOwner) {
            it?.let {
                askForOptionSelection(it)

                viewModel.eventAskForOptionSelectionCompleted()
            }
        }
    }

    private fun refreshDataItems(dataItems: List<ScheduleOptionsListAdapter.DataItem>?) {
        (binding.recyclerView.adapter as? ScheduleOptionsListAdapter)?.submitList(dataItems)
    }

    private fun showGenericDescription(arguments: GenericDescriptionScreenArguments) {
        val fragment = GenericDescriptionFragment.newInstance(
            title = arguments.title,
            description = arguments.description,
            contentPaddingTop = arguments.contentPaddingTop,
            contentPaddingBottom = arguments.contentPaddingBottom
        )

        fragment.show(childFragmentManager, "GenericDescriptionFragment")
    }

    private fun showCustomSnack(message: String?) {
        showCustomSnack(binding.root, message)
    }

    private fun askForOptionSelection(optionID: String?) {
        val bundle = Bundle()

        bundle.putString(BUNDLE_KEY_SELECTED_OPTION_ID, optionID)

        setFragmentResult(REQUEST_KEY_SELECT_OPTION, bundle)

        dismiss()
    }
}
