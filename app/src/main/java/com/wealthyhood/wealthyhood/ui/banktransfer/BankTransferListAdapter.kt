package com.wealthyhood.wealthyhood.ui.banktransfer

import android.text.SpannableString
import android.view.ViewGroup
import androidx.annotation.DrawableRes
import androidx.annotation.StyleRes
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.ListAdapter
import androidx.recyclerview.widget.RecyclerView
import com.wealthyhood.wealthyhood.viewholders.BankAccountWithBadgeViewHolder
import com.wealthyhood.wealthyhood.viewholders.BankAccountWithBadgeViewHolder.BankAccountWithBadgeViewHolderListener
import com.wealthyhood.wealthyhood.viewholders.HeaderViewHolder

class BankTransferListAdapter(
    private val actionListener: BankTransferActionViewHolder.BankTransferActionViewHolderListener?,
    private val bankAccountListener: BankAccountWithBadgeViewHolderListener?
) : ListAdapter<BankTransferListAdapter.DataItem, RecyclerView.ViewHolder>(DataItemDiffCallback()) {

    companion object {

        const val ITEM_VIEW_TYPE_HEADER = 0
        const val ITEM_VIEW_TYPE_ACTION = 1
        const val ITEM_VIEW_TYPE_BANK_ACCOUNT = 2
    }

    sealed class DataItem {

        abstract val id: String

        data class HeaderItem(
            override val id: String,
            val headerType: String?,
            val paddingStart: Int?,
            val paddingTop: Int?,
            val paddingEnd: Int?,
            val paddingBottom: Int?,
            @StyleRes val textAppearanceRes: Int,
            val title: SpannableString?
        ) : DataItem()

        data class ActionItem(
            override val id: String,
            @DrawableRes val imageDrawableRes: Int?,
            val paddingStart: Int?,
            val paddingTop: Int?,
            val paddingEnd: Int?,
            val paddingBottom: Int?,
            val title: String?,
            val subtitle: String?,
            val shouldShowBadge: Boolean,
            val badgeText: String?,
            val isDisabled: Boolean
        ) : DataItem()

        data class BankAccountItem(
            override val id: String,
            val paddingStart: Int?,
            val paddingTop: Int?,
            val paddingEnd: Int?,
            val paddingBottom: Int?,
            val containerPadding: Int?,
            val hasBorder: Boolean,
            val imageURI: String?,
            val titleText: String?,
            val descriptionText: String?,
            val shouldShowBadge: Boolean,
            val badgeText: String?
        ) : DataItem()
    }

    class DataItemDiffCallback : DiffUtil.ItemCallback<DataItem>() {

        override fun areItemsTheSame(oldItem: DataItem, newItem: DataItem): Boolean {
            return oldItem.id == newItem.id
        }

        override fun areContentsTheSame(oldItem: DataItem, newItem: DataItem): Boolean {
            return oldItem == newItem
        }
    }

    override fun getItemViewType(position: Int): Int {
        return when (getItem(position)) {
            is DataItem.HeaderItem -> ITEM_VIEW_TYPE_HEADER
            is DataItem.ActionItem -> ITEM_VIEW_TYPE_ACTION
            is DataItem.BankAccountItem -> ITEM_VIEW_TYPE_BANK_ACCOUNT
        }
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): RecyclerView.ViewHolder {
        return when (viewType) {
            ITEM_VIEW_TYPE_HEADER -> HeaderViewHolder.from(parent)
            ITEM_VIEW_TYPE_ACTION -> BankTransferActionViewHolder.from(parent, actionListener)
            else -> BankAccountWithBadgeViewHolder.from(parent, bankAccountListener)
        }
    }

    override fun onBindViewHolder(holder: RecyclerView.ViewHolder, position: Int) {
        when (holder) {
            is HeaderViewHolder -> {
                val dataItem = getItem(position) as DataItem.HeaderItem

                holder.bind(
                    headerType = dataItem.headerType,
                    paddingStart = dataItem.paddingStart,
                    paddingTop = dataItem.paddingTop,
                    paddingEnd = dataItem.paddingEnd,
                    paddingBottom = dataItem.paddingBottom,
                    textAppearanceRes = dataItem.textAppearanceRes,
                    spannableStringTitle = dataItem.title,
                    textAlignment = null
                )
            }

            is BankTransferActionViewHolder -> {
                val dataItem = getItem(position) as DataItem.ActionItem

                holder.bind(
                    itemID = dataItem.id,
                    imageDrawableRes = dataItem.imageDrawableRes,
                    paddingStart = dataItem.paddingStart,
                    paddingTop = dataItem.paddingTop,
                    paddingEnd = dataItem.paddingEnd,
                    paddingBottom = dataItem.paddingBottom,
                    title = dataItem.title,
                    subtitle = dataItem.subtitle,
                    shouldShowBadge = dataItem.shouldShowBadge,
                    badgeText = dataItem.badgeText,
                    isDisabled = dataItem.isDisabled
                )
            }

            is BankAccountWithBadgeViewHolder -> {
                val dataItem = getItem(position) as DataItem.BankAccountItem

                holder.bind(
                    itemID = dataItem.id,
                    paddingStart = dataItem.paddingStart,
                    paddingTop = dataItem.paddingTop,
                    paddingEnd = dataItem.paddingEnd,
                    paddingBottom = dataItem.paddingBottom,
                    containerPadding = dataItem.containerPadding,
                    hasBorder = dataItem.hasBorder,
                    imageURI = dataItem.imageURI,
                    titleText = dataItem.titleText,
                    descriptionText = dataItem.descriptionText,
                    shouldShowBadge = dataItem.shouldShowBadge,
                    badgeText = dataItem.badgeText
                )
            }
        }
    }
}
