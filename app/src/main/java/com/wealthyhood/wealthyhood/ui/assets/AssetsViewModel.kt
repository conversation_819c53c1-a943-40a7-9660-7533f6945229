package com.wealthyhood.wealthyhood.ui.assets

import android.app.Application
import android.text.SpannableString
import androidx.annotation.ColorRes
import androidx.annotation.DrawableRes
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import androidx.recyclerview.widget.RecyclerView
import com.wealthyhood.wealthyhood.R
import com.wealthyhood.wealthyhood.database.Asset
import com.wealthyhood.wealthyhood.database.InvestmentProduct
import com.wealthyhood.wealthyhood.database.WealthyhoodDatabase
import com.wealthyhood.wealthyhood.domain.GetInvestmentProductsUseCase
import com.wealthyhood.wealthyhood.domain.VirtualTreeItem
import com.wealthyhood.wealthyhood.extensions.convertToListOfVirtualTreeItems
import com.wealthyhood.wealthyhood.extensions.findUserLocale
import com.wealthyhood.wealthyhood.extensions.formatAsPercentage
import com.wealthyhood.wealthyhood.extensions.generateFormattedCurrency
import com.wealthyhood.wealthyhood.extensions.generateProviderLogoURI
import com.wealthyhood.wealthyhood.extensions.getIconDrawableRes
import com.wealthyhood.wealthyhood.extensions.isStock
import com.wealthyhood.wealthyhood.model.NetworkResource
import com.wealthyhood.wealthyhood.repository.AuthRepository
import com.wealthyhood.wealthyhood.repository.OnBoardingRepository
import com.wealthyhood.wealthyhood.repository.PreferencesRepository
import com.wealthyhood.wealthyhood.repository.Repository
import com.wealthyhood.wealthyhood.ui.assets.AssetsListAdapter.DataItem
import com.wealthyhood.wealthyhood.viewholders.HeaderViewHolder
import kotlinx.coroutines.async
import kotlinx.coroutines.launch
import java.util.Date
import kotlin.math.abs

class AssetsViewModel(
    application: Application,
    private val screenTitle: String?,
    private val virtualTreeJSON: String?,
    private val shouldShowSectionHeaders: Boolean?,
    private val shouldShowFloatingTitleBar: Boolean?,
    private val floatingTitleBarTextArg: String?
) : AndroidViewModel(application) {

    private val context = application

    private val database = WealthyhoodDatabase.getInstance(application)
    private val authRepository = AuthRepository(application) // TODO: Use DI
    private val preferencesRepository = PreferencesRepository(application) // TODO: Use DI
    private val onBoardingRepository = OnBoardingRepository() // TODO: Use DI
    private val repository = Repository() // TODO: Use DI

    private val getInvestmentProductsUseCase = GetInvestmentProductsUseCase(
        preferencesRepository = preferencesRepository,
        repository = repository,
        database = database
    )

    private val allAssetClasses = onBoardingRepository.getAssetClasses(preferencesRepository)
    private val allBondCategories = onBoardingRepository.getBondCategories(preferencesRepository)
    private val allSectors = onBoardingRepository.getSectors(preferencesRepository)
    private val etfProviders = onBoardingRepository.getETFProviders(preferencesRepository)

    private var allAssets: List<Asset>? = null

    private val userLocale = preferencesRepository.findUserLocale()

    private var virtualTree: List<VirtualTreeItem>? = null

    private var investmentProducts: List<InvestmentProduct>? = null

    private var isReloadingData = false
    private var investmentProductsLastReloadedAt = -1L

    private val _navigationBarElevation = MutableLiveData<Float?>()
    val navigationBarElevation: LiveData<Float?>
        get() = _navigationBarElevation

    private val _isNavigationTitleVisible = MutableLiveData<Boolean?>()
    val isNavigationTitleVisible: LiveData<Boolean?>
        get() = _isNavigationTitleVisible

    private val _navigationTitleText = MutableLiveData<String?>()
    val navigationTitleText: LiveData<String?>
        get() = _navigationTitleText

    private val _floatingTitleBarText = MutableLiveData<String?>()
    val floatingTitleBarText: LiveData<String?>
        get() = _floatingTitleBarText

    private val _isTitleViewVisible = MutableLiveData<Boolean?>()
    val isTitleViewVisible: LiveData<Boolean?>
        get() = _isTitleViewVisible

    private val _floatingTabItems = MutableLiveData<List<String>?>()
    val floatingTabItems: LiveData<List<String>?>
        get() = _floatingTabItems

    private val _selectedTabIndex = MutableLiveData<Int?>()
    val selectedTabIndex: LiveData<Int?>
        get() = _selectedTabIndex

    private val _dataItems = MutableLiveData<List<DataItem>?>()
    val dataItems: LiveData<List<DataItem>?>
        get() = _dataItems

    private val _eventNavigateToAssetDetailsScreen = MutableLiveData<String?>()
    val eventNavigateToAssetDetailsScreen: LiveData<String?>
        get() = _eventNavigateToAssetDetailsScreen

    fun eventNavigateToAssetDetailsScreenCompleted() {
        _eventNavigateToAssetDetailsScreen.value = null
    }

    init {
        viewModelScope.launch {
            allAssets = onBoardingRepository.getAssetsCoroutines(
                context = context,
                shouldExcludeDeprecated = true
            )

            refreshDataItems()
        }

        initializeVirtualTree()

        refreshNavigationTitle()
        refreshFloatingTitleBarText()
        refreshDataItems()
        refreshFloatingTabItems()

        reloadData()
    }

    fun handleOnResume() {
        reloadInvestmentProductsFromCacheIfNeeded()
    }

    fun handleOnInvestmentProductsUpdatedEventReceived() {
        reloadInvestmentProductsFromCacheIfNeeded()
    }

    fun calculateRecyclerViewTopOffset(): Float {
        // Navigation bar height
        val spacing48 = context.resources.getDimension(R.dimen.spacing_48)

        // Floating title bar height
        val spacing114 = context.resources.getDimension(R.dimen.spacing_114)

        return spacing114 - spacing48
    }

    fun handleOnRecyclerViewScrolled(recyclerView: RecyclerView) {
        refreshNavigationBarElevation(recyclerView)
        refreshNavigationBarTitleVisibility(recyclerView)

        refreshTitleViewVisibility(recyclerView)
        setActiveTab(recyclerView)
    }

    fun dataItemPositionForTabIndex(tabIndex: Int): Int? {
        _dataItems.value?.forEachIndexed { index, dataItem ->
            val headerItem = (dataItem as? DataItem.HeaderItem) ?: return@forEachIndexed
            if (headerItem.headerType != "sectionHeader") return@forEachIndexed

            if (headerItem.tabIndex == tabIndex) {
                return index
            }
        }

        return null
    }

    fun handleOnAssetClicked(assetID: String?) {
        _eventNavigateToAssetDetailsScreen.value = assetID
    }

    private fun initializeVirtualTree() {
        val tempTree = virtualTreeJSON?.convertToListOfVirtualTreeItems()

        virtualTree = if (shouldShowSectionHeaders != true) {
            tempTree?.filter { it.type == VirtualTreeItem.SECTION_TYPE_ASSET }
        } else tempTree
    }

    private fun refreshNavigationTitle() {
        _navigationTitleText.value = screenTitle
    }

    private fun refreshFloatingTitleBarText() {
        _floatingTitleBarText.value = floatingTitleBarTextArg
    }

    private fun refreshFloatingTabItems() {
        _floatingTabItems.value = generateFloatingTabItems()
    }

    private fun refreshDataItems() {
        _dataItems.value = generateDataItems()
    }

    private fun reloadData() {
        //_eventShowOrHideLoadingDialog.value = true
        isReloadingData = true

        getCredentialsCall { _, accessToken, idToken ->
            if (accessToken == null || idToken == null) {
                // TODO: Handle the error

                isReloadingData = false

                //_eventShowOrHideLoadingDialog.value = false

                return@getCredentialsCall
            }

            getDataFromAPI(
                accessToken = accessToken,
                idToken = idToken
            )
        }
    }

    private fun shouldReloadInvestmentProductsFromCache(lastDataReloadAt: Long = this.investmentProductsLastReloadedAt): Boolean {
        if (lastDataReloadAt == -1L) return true

        val investmentProductsUpdatedAt = preferencesRepository.getInvestmentProductsUpdatedAt()
        if (investmentProductsUpdatedAt > lastDataReloadAt) return true

        return false
    }

    private fun reloadInvestmentProductsFromCacheIfNeeded() {
        if (isReloadingData) return
        if (!shouldReloadInvestmentProductsFromCache()) return

        investmentProductsLastReloadedAt = Date().time

        viewModelScope.launch {
            investmentProducts = database.investmentProductDAO.getAllInvestmentProducts()
            refreshDataItems()
        }
    }

    private fun getDataFromAPI(accessToken: String, idToken: String) {
        viewModelScope.launch {
            val investmentProductsDeferred = async {
                getInvestmentProductsCall(accessToken = accessToken, idToken = idToken)
            }

            val investmentProductResult = investmentProductsDeferred.await()

            (investmentProductResult as? NetworkResource.Success)?.let {
                investmentProductsLastReloadedAt = Date().time

                investmentProducts = it.data
            }

            isReloadingData = false

            refreshDataItems()
        }
    }

    private suspend fun getInvestmentProductsCall(
        accessToken: String,
        idToken: String
    ): NetworkResource<List<InvestmentProduct>> {
        return getInvestmentProductsUseCase(
            accessToken = accessToken,
            idToken = idToken,
            shouldGetFromCache = true
        )
    }

    private fun getCredentialsCall(callback: ((succeeded: Boolean, accessToken: String?, idToken: String?) -> Unit)?) {
        authRepository.loadAuth0Credentials {
            when (it) {
                is NetworkResource.Success -> {
                    val accessToken = "Bearer ${it.data?.accessToken}"
                    val idToken = "Bearer ${it.data?.idToken}"

                    callback?.invoke(true, accessToken, idToken)
                }

                is NetworkResource.Failure -> {
                    callback?.invoke(false, null, null)
                }
            }
        }
    }

    private fun generateFloatingTabItems(): List<String> {
        val finalAnswer = mutableListOf<String>()

        _dataItems.value?.forEach { dataItem ->
            (dataItem as? DataItem.HeaderItem)?.let { headerItem ->
                if (headerItem.headerType != "sectionHeader") return@forEach

                finalAnswer.add(headerItem.title?.toString() ?: "")
            }
        }

        return finalAnswer
    }

    private fun generateDataItems(): List<DataItem> {
        val finalAnswer = mutableListOf<DataItem>()

        generateHeaderItem()?.let {
            finalAnswer.add(it)
        }

        virtualTree?.forEach { virtualTreeItem ->
            val itemID = virtualTreeItem.id
            val tabIndex = virtualTreeItem.sectionIndex

            when (virtualTreeItem.type) {
                VirtualTreeItem.SECTION_TYPE_ASSET -> {
                    val asset = allAssets?.find { it.id == itemID }

                    generateAssetItem(
                        asset = asset,
                        tabIndex = tabIndex
                    )?.let {
                        finalAnswer.add(it)
                    }
                }

                VirtualTreeItem.SECTION_TYPE_SECTOR -> {
                    val sector = allSectors?.find { it.id == itemID }

                    finalAnswer.add(
                        generateSectionHeaderItem(
                            sectionID = itemID,
                            tabIndex = tabIndex,
                            title = sector?.title
                        )
                    )
                }

                VirtualTreeItem.SECTION_TYPE_BOND_CATEGORY -> {
                    val bondCategory = allBondCategories?.find { it.id == itemID }

                    finalAnswer.add(
                        generateSectionHeaderItem(
                            sectionID = itemID,
                            tabIndex = tabIndex,
                            title = bondCategory?.title
                        )
                    )
                }

                VirtualTreeItem.SECTION_TYPE_ETF_ASSETS_SECTION -> {
                    finalAnswer.add(
                        generateSectionHeaderItem(
                            sectionID = itemID,
                            tabIndex = tabIndex,
                            title = context.resources.getString(R.string.etfs_label)
                        )
                    )
                }

                VirtualTreeItem.SECTION_TYPE_STOCK_ASSETS_SECTION -> {
                    finalAnswer.add(
                        generateSectionHeaderItem(
                            sectionID = itemID,
                            tabIndex = tabIndex,
                            title = context.resources.getString(R.string.stocks_label)
                        )
                    )
                }

                VirtualTreeItem.SECTION_TYPE_ASSET_CLASS -> {
                    val assetClass = allAssetClasses?.find { it.id == itemID }

                    finalAnswer.add(
                        generateSectionHeaderItem(
                            sectionID = itemID,
                            tabIndex = tabIndex,
                            title = assetClass?.fieldName
                        )
                    )
                }
            }
        }

        return finalAnswer
    }

    private fun generateHeaderItem(): DataItem.HeaderItem? {
        if (screenTitle == null) return null

        val spacing12 = context.resources.getDimensionPixelSize(R.dimen.spacing_12)
        val spacing16 = context.resources.getDimensionPixelSize(R.dimen.spacing_16)
        val spacing48 = context.resources.getDimensionPixelSize(R.dimen.spacing_48)

        val paddingBottom = if (shouldShowSectionHeaders == true) spacing16 else spacing48

        val spannableStringTitle = SpannableString(screenTitle)

        return DataItem.HeaderItem(
            id = "headerItem",
            tabIndex = 0,
            headerType = "header",
            paddingStart = spacing16,
            paddingTop = spacing12,
            paddingEnd = spacing16,
            paddingBottom = paddingBottom,
            textAppearanceRes = R.style.ScreenTitle,
            title = spannableStringTitle
        )
    }

    private fun generateSectionHeaderItem(
        sectionID: String,
        tabIndex: Int,
        title: String?
    ): DataItem.HeaderItem {
        val spacing16 = context.resources.getDimensionPixelSize(R.dimen.spacing_16)
        val spacing32 = context.resources.getDimensionPixelSize(R.dimen.spacing_32)

        val finalTitle = if (sectionID == "general") {
            context.resources.getString(R.string.add_remove_general_header)
        } else title

        val finalSpannableStringTitle = finalTitle?.let {
            SpannableString(it)
        }

        return DataItem.HeaderItem(
            id = "sectionHeader_${sectionID}",
            tabIndex = tabIndex,
            headerType = "sectionHeader",
            paddingStart = spacing16,
            paddingTop = spacing32,
            paddingEnd = spacing16,
            paddingBottom = spacing16,
            textAppearanceRes = R.style.PageTitleH3_AssetsSectionHeader,
            title = finalSpannableStringTitle
        )
    }

    private fun generateAssetItem(asset: Asset?, tabIndex: Int): DataItem.AssetWithMoneyItem? {
        if (asset == null) return null

        val spacing12 = context.resources.getDimensionPixelSize(R.dimen.spacing_12)
        val spacing16 = context.resources.getDimensionPixelSize(R.dimen.spacing_16)

        val etfLabel = context.resources.getString(R.string.etf_label)

        val badgeText = if (asset.isStock()) null else etfLabel
        val shouldShowBadge = (badgeText != null)

        val subtitle = "${asset.tickerWithCurrency} • ${asset.shortDescription}"

        val investmentProduct = investmentProducts?.find { it.commonID == asset.id }

        @ColorRes var moneySubtitleColorRes: Int? = null
        @DrawableRes var moneySubtitleImageDrawableRes: Int? = null
        var moneySubtitle: String? = null

        investmentProduct?.currentTickerMonthlyReturnPercentage?.let { percentage ->
            if (percentage < 0) {
                moneySubtitleColorRes = R.color.system_alerts_danger_color
                moneySubtitleImageDrawableRes = R.drawable.ic_arrow_drop_down_rounded
            } else {
                moneySubtitleColorRes = R.color.system_alerts_success_color
                moneySubtitleImageDrawableRes = R.drawable.ic_arrow_drop_up_rounded
            }

            moneySubtitle = percentage.toDouble().formatAsPercentage(
                minimumFractionDigits = 2,
                maximumFractionDigits = 2,
                locale = userLocale
            )
        }

        val tradedPrice = investmentProduct?.tradedPrice
        val tradedCurrency = investmentProduct?.tradedCurrency

        val moneyTitle = tradedPrice?.generateFormattedCurrency(
            currencyISOCode = tradedCurrency,
            locale = userLocale
        )

        return DataItem.AssetWithMoneyItem(
            id = asset.id,
            tabIndex = tabIndex,
            paddingStart = spacing16,
            paddingTop = spacing12,
            paddingEnd = spacing16,
            paddingBottom = spacing12,
            imageDrawableRes = asset.getIconDrawableRes(context),
            imageURI = asset.generateProviderLogoURI(etfProviders),
            title = asset.title,
            shouldShowBadge = shouldShowBadge,
            badgeText = badgeText,
            shouldShowSubtitle = true,
            subtitle = subtitle,
            shouldShowMoneyTitle = true,
            moneyTitle = moneyTitle,
            shouldShowMoneySubtitle = true,
            moneySubtitleImageDrawableRes = moneySubtitleImageDrawableRes,
            moneySubtitle = moneySubtitle,
            moneySubtitleColorRes = moneySubtitleColorRes
        )
    }

    private fun refreshNavigationBarElevation(recyclerView: RecyclerView) {
        val offset = recyclerView.computeVerticalScrollOffset()

        val elevationRes = if (offset > 0) R.dimen.spacing_2 else R.dimen.spacing_0
        val elevation = context.resources.getDimension(elevationRes)

        _navigationBarElevation.value = elevation
    }

    private fun refreshNavigationBarTitleVisibility(recyclerView: RecyclerView) {
        _isNavigationTitleVisible.value = shouldShowNavigationBarTitle(recyclerView)
    }

    private fun refreshTitleViewVisibility(recyclerView: RecyclerView) {
        _isTitleViewVisible.value = shouldShowTitleViewBasedOnScrollOffset(recyclerView)
    }

    private fun setActiveTab(recyclerView: RecyclerView) {
        val offset = calculateRecyclerViewTopOffset()

        val childView = recyclerView.findChildViewUnder(0f, offset) ?: return
        val containingView = recyclerView.findContainingItemView(childView) ?: return
        val viewHolder = recyclerView.findContainingViewHolder(containingView) ?: return

        val position = viewHolder.absoluteAdapterPosition
        val tabIndex = _dataItems.value?.get(position)?.tabIndex ?: return

        if (tabIndex != _selectedTabIndex.value) {
            _selectedTabIndex.value = tabIndex
        }
    }

    private fun shouldShowNavigationBarTitle(recyclerView: RecyclerView): Boolean {
        val view =
            recyclerView.findChildViewUnder(0f, 0f) ?: return false
        val viewHolder = recyclerView.findContainingViewHolder(view) ?: return false
        val position = viewHolder.absoluteAdapterPosition

        // There is also the following useful property: viewHolder.itemView.top

        if (position > 0) return true

        (viewHolder as? HeaderViewHolder)?.let {
            val viewHolderTop = abs(viewHolder.itemView.top)
            val titleBottom = it.computeTitleBottom()

            return viewHolderTop >= titleBottom
        }

        return false
    }

    private fun shouldShowTitleViewBasedOnScrollOffset(recyclerView: RecyclerView): Boolean {
        if (shouldShowSectionHeaders != true) return false
        if (shouldShowFloatingTitleBar != true) return false

        val offset = calculateRecyclerViewTopOffset()

        val view = recyclerView.findChildViewUnder(0f, offset) ?: return false
        val viewHolder = recyclerView.findContainingViewHolder(view) ?: return false

        // There is also the following useful property: viewHolder.itemView.top

        return when (viewHolder) {
            is HeaderViewHolder -> {
                // If we don't use the isAfterHeader flag, the floating title bar
                // will appear as soon as the screen appears. This is because the total height
                // of the cells before the first section header is lower then the height
                // of the floating title bar.

                val isSectionHeader = (viewHolder.getHeaderType() == "sectionHeader")
                val isAfterHeader = (viewHolder.absoluteAdapterPosition > 1)

                return (isSectionHeader && isAfterHeader)
            }

            else -> true
        }
    }
}
