package com.wealthyhood.wealthyhood.ui.assetdetails

import android.annotation.SuppressLint
import android.graphics.LinearGradient
import android.graphics.Shader
import android.view.HapticFeedbackConstants
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.annotation.ColorInt
import androidx.annotation.DrawableRes
import androidx.core.content.ContextCompat
import androidx.recyclerview.widget.RecyclerView
import com.github.mikephil.charting.components.LimitLine
import com.github.mikephil.charting.components.YAxis
import com.github.mikephil.charting.data.Entry
import com.github.mikephil.charting.data.LineData
import com.github.mikephil.charting.data.LineDataSet
import com.github.mikephil.charting.formatter.ValueFormatter
import com.github.mikephil.charting.highlight.Highlight
import com.github.mikephil.charting.listener.OnChartValueSelectedListener
import com.wealthyhood.wealthyhood.R
import com.wealthyhood.wealthyhood.common.AssetTransactionMarkerView
import com.wealthyhood.wealthyhood.databinding.ListItemAssetDetailsPerformanceBinding
import com.wealthyhood.wealthyhood.extensions.findCoordinate
import com.wealthyhood.wealthyhood.extensions.formatNumber
import com.wealthyhood.wealthyhood.extensions.generateFormattedCurrency
import com.wealthyhood.wealthyhood.extensions.isInvisible
import com.wealthyhood.wealthyhood.model.AssetDetailsDateRange
import com.wealthyhood.wealthyhood.model.NetworkCallState
import com.wealthyhood.wealthyhood.ui.lab.RestoreToDefaultGestureChartListener
import java.util.Locale

class AssetDetailsPerformanceViewHolder private constructor(
    private val binding: ListItemAssetDetailsPerformanceBinding,
    private val listener: AssetDetailsPerformanceViewHolderListener?,
    private val userLocale: Locale
) : RecyclerView.ViewHolder(binding.root) {

    interface AssetDetailsPerformanceViewHolderListener {

        fun onChartEntrySelected(entry: Entry?)
        fun onDateRangeSelected(dateRange: AssetDetailsDateRange)
        fun requestDisallowInterceptTouchEvent(disallow: Boolean)
        fun onChartErrorTryAgainButtonPressed()
        fun shouldShowMarkerForEntry(entry: Entry?): Boolean
        fun getMarkerTypeTextForEntry(entry: Entry?): String?
        fun getMarkerQuantityTextForEntry(entry: Entry?): String?
    }

    companion object {

        fun from(
            parent: ViewGroup,
            listener: AssetDetailsPerformanceViewHolderListener?,
            userLocale: Locale
        ): AssetDetailsPerformanceViewHolder {
            val layoutInflater = LayoutInflater.from(parent.context)
            val binding =
                ListItemAssetDetailsPerformanceBinding.inflate(layoutInflater, parent, false)

            return AssetDetailsPerformanceViewHolder(
                binding = binding,
                listener = listener,
                userLocale = userLocale
            )
        }
    }

    private var isLongPressing = false

    private var currencyISOCode: String? = null
    private var selectedEntry: Entry? = null

    init {
        setupLineChart()
        setupDateRangeButtons()
        setupErrorTryAgainButton()
    }

    fun bind(
        totalText: String?,
        currencyISOCode: String?,
        description: String?,
        @ColorInt descriptionColor: Int?,
        @DrawableRes descriptionDrawableRes: Int?,
        dateText: String?,
        chartEntries: List<Entry>,
        networkCallState: NetworkCallState?,
        selectedEntry: Entry?,
        selectedDateRange: AssetDetailsDateRange
    ) {
        this.selectedEntry = selectedEntry
        this.currencyISOCode = currencyISOCode

        updateTotalText(totalText)
        updateDescription(description, dateText, descriptionColor, descriptionDrawableRes)
        updateLineChart(chartEntries, selectedEntry)
        updateDateRange(selectedDateRange)
        updateNetworkCallState(networkCallState)
    }

    private fun updateTotalText(text: String?) {
        binding.totalTextView.text = text
    }

    private fun updateDescription(
        description: String?,
        dateText: String?,
        @ColorInt descriptionColor: Int?,
        @DrawableRes descriptionDrawableRes: Int?
    ) {
        val isDateText = dateText != null

        binding.dateTextView.isInvisible(!isDateText)
        binding.descriptionTextView.isInvisible(isDateText)
        binding.moneyArrowImageView.isInvisible(isDateText)

        if (isDateText) {
            binding.dateTextView.text = dateText

            return
        }

        binding.descriptionTextView.text = description
        binding.moneyArrowImageView.setImageDrawable(null)

        descriptionColor?.let {
            binding.descriptionTextView.setTextColor(descriptionColor)
            binding.moneyArrowImageView.setColorFilter(descriptionColor)
        }

        descriptionDrawableRes?.let {
            binding.moneyArrowImageView.setImageResource(descriptionDrawableRes)
        }
    }

    private fun updateLineChart(entries: List<Entry>, selectedEntry: Entry?) {
        setYMinAndMax(entries)

        binding.lineChart.data = generateChartData(entries)

        val entry = findChartEntry(selectedEntry)
        markChartEntryAsSelected(entry)

        binding.lineChart.invalidate()
    }

    private fun setYMinAndMax(entries: List<Entry>) {
        val axisMinimum = entries.findCoordinate(lookOnXAxis = false, lookForMin = true) ?: return
        val axisMaximum = entries.findCoordinate(lookOnXAxis = false, lookForMin = false) ?: return

        binding.lineChart.axisRight.resetAxisMaximum()

        binding.lineChart.axisRight.axisMinimum = axisMinimum
        binding.lineChart.axisRight.axisMaximum = axisMaximum
    }

    private fun updateDateRange(dateRange: AssetDetailsDateRange) {
        resetSelectionStatusOfDateRangeViews()

        val selectedColor =
            ContextCompat.getColor(itemView.context, R.color.lab_insights_selected_tab_text)

        when (dateRange) {
            AssetDetailsDateRange.ONE_WEEK -> {
                binding.dateRangeIndicatorView1.visibility = View.VISIBLE
                binding.dateRangeButton1.setTextAppearance(R.style.DateRange_Selected)
                binding.dateRangeButton1.setTextColor(selectedColor) // TODO: Remove the color from the Style
            }

            AssetDetailsDateRange.ONE_MONTH -> {
                binding.dateRangeIndicatorView2.visibility = View.VISIBLE
                binding.dateRangeButton2.setTextAppearance(R.style.DateRange_Selected)
                binding.dateRangeButton2.setTextColor(selectedColor)
            }

            AssetDetailsDateRange.THREE_MONTHS -> {
                binding.dateRangeIndicatorView3.visibility = View.VISIBLE
                binding.dateRangeButton3.setTextAppearance(R.style.DateRange_Selected)
                binding.dateRangeButton3.setTextColor(selectedColor)
            }

            AssetDetailsDateRange.SIX_MONTHS -> {
                binding.dateRangeIndicatorView4.visibility = View.VISIBLE
                binding.dateRangeButton4.setTextAppearance(R.style.DateRange_Selected)
                binding.dateRangeButton4.setTextColor(selectedColor)
            }

            AssetDetailsDateRange.ONE_YEAR -> {
                binding.dateRangeIndicatorView5.visibility = View.VISIBLE
                binding.dateRangeButton5.setTextAppearance(R.style.DateRange_Selected)
                binding.dateRangeButton5.setTextColor(selectedColor)
            }

            else -> {
                binding.dateRangeIndicatorView6.visibility = View.VISIBLE
                binding.dateRangeButton6.setTextAppearance(R.style.DateRange_Selected)
                binding.dateRangeButton6.setTextColor(selectedColor)
            }
        }
    }

    private fun updateNetworkCallState(networkCallState: NetworkCallState?) {
        if (networkCallState == NetworkCallState.Executing) {
            binding.totalTextView.visibility = View.GONE
            binding.descriptionTextView.visibility = View.GONE
            binding.moneyArrowImageView.visibility = View.GONE
            binding.dateTextView.visibility = View.GONE

            binding.chartDataErrorLayout.root.visibility = View.GONE
            binding.lineChart.visibility = View.INVISIBLE
            binding.circularProgressIndicator.visibility = View.VISIBLE

            binding.dateRangeConstraintLayout.visibility = View.GONE

            return
        }

        if (networkCallState is NetworkCallState.Failed) {
            binding.totalTextView.visibility = View.GONE
            binding.descriptionTextView.visibility = View.GONE
            binding.moneyArrowImageView.visibility = View.GONE
            binding.dateTextView.visibility = View.GONE

            binding.chartDataErrorLayout.root.visibility = View.VISIBLE
            binding.lineChart.visibility = View.INVISIBLE
            binding.circularProgressIndicator.visibility = View.GONE

            binding.dateRangeConstraintLayout.visibility = View.GONE

            return
        }

        binding.totalTextView.visibility = View.VISIBLE
        binding.chartDataErrorLayout.root.visibility = View.GONE

        binding.lineChart.visibility = View.VISIBLE
        binding.circularProgressIndicator.visibility = View.GONE

        binding.dateRangeConstraintLayout.visibility = View.VISIBLE
    }

    private fun setupDateRangeButtons() {
        binding.dateRangeButton1.setOnClickListener {
            listener?.onDateRangeSelected(AssetDetailsDateRange.ONE_WEEK)
        }

        binding.dateRangeButton2.setOnClickListener {
            listener?.onDateRangeSelected(AssetDetailsDateRange.ONE_MONTH)
        }

        binding.dateRangeButton3.setOnClickListener {
            listener?.onDateRangeSelected(AssetDetailsDateRange.THREE_MONTHS)
        }

        binding.dateRangeButton4.setOnClickListener {
            listener?.onDateRangeSelected(AssetDetailsDateRange.SIX_MONTHS)
        }

        binding.dateRangeButton5.setOnClickListener {
            listener?.onDateRangeSelected(AssetDetailsDateRange.ONE_YEAR)
        }

        binding.dateRangeButton6.setOnClickListener {
            listener?.onDateRangeSelected(AssetDetailsDateRange.MAX)
        }
    }

    private fun setupErrorTryAgainButton() {
        binding.chartDataErrorLayout.chartErrorRetryButton.setOnClickListener {
            listener?.onChartErrorTryAgainButtonPressed()
        }
    }

    @SuppressLint("ClickableViewAccessibility")
    private fun setupLineChart() {
        binding.lineChart.legend.isEnabled = false
        binding.lineChart.description.isEnabled = false
        binding.lineChart.setScaleEnabled(false)

        binding.lineChart.xAxis.isEnabled = true

        binding.lineChart.xAxis.setDrawGridLines(false)
        binding.lineChart.xAxis.setDrawAxisLine(false)
        binding.lineChart.xAxis.setDrawLabels(false)

        binding.lineChart.axisLeft.isEnabled = false

        binding.lineChart.axisRight.setDrawAxisLine(false)
        binding.lineChart.axisRight.setDrawGridLines(false)
        binding.lineChart.axisRight.setDrawLabels(true)
        binding.lineChart.axisRight.textColor =
            ContextCompat.getColor(itemView.context, R.color.wealthyhood_burple)
        binding.lineChart.axisRight.textSize = 14f
        binding.lineChart.axisRight.setLabelCount(3, true)

        binding.lineChart.isHighlightPerDragEnabled = false
        binding.lineChart.isHighlightPerTapEnabled = false

        val markerListener = object : AssetTransactionMarkerView.CustomMarkerViewListener {

            override fun shouldShowMarker(): Boolean {
                return listener?.shouldShowMarkerForEntry(selectedEntry) ?: false
            }

            override fun typeText(): String? {
                return listener?.getMarkerTypeTextForEntry(selectedEntry)
            }

            override fun quantityText(): String? {
                return listener?.getMarkerQuantityTextForEntry(selectedEntry)
            }
        }

        binding.lineChart.setDrawMarkers(true)

        val markerView = AssetTransactionMarkerView(itemView.context)
        markerView.listener = markerListener

        binding.lineChart.marker = markerView

        binding.lineChart.axisRight.valueFormatter = object : ValueFormatter() {

            override fun getFormattedValue(value: Float): String {
                val formattedValue = if (currencyISOCode != null) {
                    value.generateFormattedCurrency(
                        currencyISOCode = currencyISOCode,
                        maximumFractionDigits = 1,
                        locale = userLocale
                    )
                } else {
                    value.formatNumber(locale = userLocale)
                }

                return "        $formattedValue"
            }
        }

        binding.lineChart.setMaxVisibleValueCount(10000)

        binding.lineChart.onChartGestureListener = RestoreToDefaultGestureChartListener(
            lineChart = binding.lineChart,
            onRestore = {
                listener?.onChartEntrySelected(it)
            },
            onLongPressed = {
                isLongPressing = true

                binding.lineChart.isHighlightPerDragEnabled = true
                binding.lineChart.isHighlightPerTapEnabled = true

                binding.lineChart.performHapticFeedback(HapticFeedbackConstants.LONG_PRESS)
                listener?.requestDisallowInterceptTouchEvent(true)
            },
            onGestureEnded = {
                isLongPressing = false

                binding.lineChart.isHighlightPerDragEnabled = false
                binding.lineChart.isHighlightPerTapEnabled = false

                listener?.requestDisallowInterceptTouchEvent(false)
            }
        )

        binding.lineChart.setOnChartValueSelectedListener(object : OnChartValueSelectedListener {

            override fun onValueSelected(e: Entry?, h: Highlight?) {
                if (!isLongPressing) return

                listener?.onChartEntrySelected(e)
            }

            override fun onNothingSelected() {}
        })
    }

    private fun findChartEntry(entry: Entry?): Entry? {
        if (entry == null) return null

        return binding.lineChart.data.dataSets?.firstOrNull()?.getEntryForXValue(entry.x, entry.y)
    }

    private fun markChartEntryAsSelected(entry: Entry?) {
        drawLineChartSelectionCircleForEntry(entry)
    }

    private fun drawLineChartSelectionCircleForEntry(entry: Entry?) {
        if (entry == null) return

        drawLineChartLimitLineForValue(entry.x)
    }

    private fun drawLineChartLimitLineForValue(value: Float) {
        val context = itemView.context
        val resources = context.resources

        val limitLine = LimitLine(value)

        val lineWidth = resources.getDimension(R.dimen.line_chart_limit_line_width)

        limitLine.lineWidth = lineWidth
        limitLine.lineColor = ContextCompat.getColor(context, R.color.wealthyhood_burple_20)

        val axis = binding.lineChart.xAxis

        axis.removeAllLimitLines()

        if (isLongPressing) {
            axis.addLimitLine(limitLine)
        }
    }

    private fun applyGradientToLineChart() {
        val context = itemView.context

        val paint = binding.lineChart.renderer.paintRender
        val width = binding.lineChart.width.toFloat() * 0.5f

        val startColor = ContextCompat.getColor(context, R.color.lab_insights_selected_tab_text_0)
        val endColor = ContextCompat.getColor(context, R.color.lab_insights_selected_tab_text)
        val gradient = LinearGradient(
            0f,
            0f, width,
            0f,
            startColor,
            endColor,
            Shader.TileMode.CLAMP
        )

        paint.shader = gradient
        binding.lineChart.invalidate()
    }

    private fun generateChartData(entries: List<Entry>): LineData {
        val context = itemView.context

        val lineDataSet = LineDataSet(entries, null)

        lineDataSet.setDrawCircles(false)
        lineDataSet.lineWidth =
            context.resources.getDimension(R.dimen.line_chart_line_width)
        lineDataSet.color = ContextCompat.getColor(context, R.color.lab_insights_selected_tab_text)
        lineDataSet.setDrawValues(false)
        lineDataSet.mode = LineDataSet.Mode.HORIZONTAL_BEZIER
        lineDataSet.setDrawIcons(true)
        lineDataSet.setDrawHorizontalHighlightIndicator(false)
        lineDataSet.setDrawVerticalHighlightIndicator(false)
        lineDataSet.axisDependency = YAxis.AxisDependency.RIGHT

        return LineData(lineDataSet)
    }

    private fun resetSelectionStatusOfDateRangeViews() {
        val color = ContextCompat.getColor(itemView.context, R.color.wealthyhood_dark_blue_60)

        binding.dateRangeIndicatorView1.visibility = View.INVISIBLE
        binding.dateRangeButton1.setTextAppearance(R.style.DateRange_Unselected)
        binding.dateRangeButton1.setTextColor(color) // TODO: Remove the color from the Style

        binding.dateRangeIndicatorView2.visibility = View.INVISIBLE
        binding.dateRangeButton2.setTextAppearance(R.style.DateRange_Unselected)
        binding.dateRangeButton2.setTextColor(color)

        binding.dateRangeIndicatorView3.visibility = View.INVISIBLE
        binding.dateRangeButton3.setTextAppearance(R.style.DateRange_Unselected)
        binding.dateRangeButton3.setTextColor(color)

        binding.dateRangeIndicatorView4.visibility = View.INVISIBLE
        binding.dateRangeButton4.setTextAppearance(R.style.DateRange_Unselected)
        binding.dateRangeButton4.setTextColor(color)

        binding.dateRangeIndicatorView5.visibility = View.INVISIBLE
        binding.dateRangeButton5.setTextAppearance(R.style.DateRange_Unselected)
        binding.dateRangeButton5.setTextColor(color)

        binding.dateRangeIndicatorView6.visibility = View.INVISIBLE
        binding.dateRangeButton6.setTextAppearance(R.style.DateRange_Unselected)
        binding.dateRangeButton6.setTextColor(color)
    }
}
