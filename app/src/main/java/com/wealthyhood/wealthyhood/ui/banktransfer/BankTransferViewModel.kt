package com.wealthyhood.wealthyhood.ui.banktransfer

import android.app.Application
import android.text.SpannableString
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import androidx.recyclerview.widget.RecyclerView
import com.wealthyhood.wealthyhood.R
import com.wealthyhood.wealthyhood.common.parseSuccessResponse
import com.wealthyhood.wealthyhood.model.BankTransferScreenArgs
import com.wealthyhood.wealthyhood.model.BanksSheetScreenArgs
import com.wealthyhood.wealthyhood.model.DepositScreenArgs
import com.wealthyhood.wealthyhood.model.NetworkResource
import com.wealthyhood.wealthyhood.model.RegularBankTransferScreenArgs
import com.wealthyhood.wealthyhood.repository.AuthRepository
import com.wealthyhood.wealthyhood.repository.PreferencesRepository
import com.wealthyhood.wealthyhood.repository.Repository
import com.wealthyhood.wealthyhood.service.BankAccount
import com.wealthyhood.wealthyhood.service.EuropeProvider
import com.wealthyhood.wealthyhood.ui.banktransfer.BankTransferListAdapter.DataItem
import com.wealthyhood.wealthyhood.viewholders.HeaderViewHolder
import kotlinx.coroutines.async
import kotlinx.coroutines.launch
import kotlin.math.abs

class BankTransferViewModel(
    application: Application,
    private val screenArgs: BankTransferScreenArgs?
) : AndroidViewModel(application) {

    companion object {

        private const val EASY_BANK_TRANSFER_ACTION_ITEM_ID = "easyBankTransferActionItem"
        private const val REGULAR_BANK_TRANSFER_ACTION_ITEM_ID = "regularBankTransferActionItem"
    }

    private val context = application

    private val preferencesRepository = PreferencesRepository(application) // TODO: Use DI
    private val authRepository = AuthRepository(application) // TODO: Use DI
    private val repository = Repository() // TODO: Use DI

    private var filteredBankAccounts: List<BankAccount>? = null
    private var europeProviders: List<EuropeProvider>? = null

    private val _navigationBarElevation = MutableLiveData<Float?>()
    val navigationBarElevation: LiveData<Float?>
        get() = _navigationBarElevation

    private val _isNavigationTitleVisible = MutableLiveData<Boolean?>()
    val isNavigationTitleVisible: LiveData<Boolean?>
        get() = _isNavigationTitleVisible

    private val _navigationTitleText = MutableLiveData<String?>()
    val navigationTitleText: LiveData<String?>
        get() = _navigationTitleText

    private val _isPrimaryButtonVisible = MutableLiveData<Boolean?>()
    val isPrimaryButtonVisible: LiveData<Boolean?>
        get() = _isPrimaryButtonVisible

    private val _dataItems = MutableLiveData<List<DataItem>?>()
    val dataItems: LiveData<List<DataItem>?>
        get() = _dataItems

    private val _eventNavigateToRegularBankTransferScreen =
        MutableLiveData<RegularBankTransferScreenArgs?>()
    val eventNavigateToRegularBankTransferScreen: LiveData<RegularBankTransferScreenArgs?>
        get() = _eventNavigateToRegularBankTransferScreen

    private val _eventShowBanksSheetScreen = MutableLiveData<BanksSheetScreenArgs?>()
    val eventShowBanksSheetScreen: LiveData<BanksSheetScreenArgs?>
        get() = _eventShowBanksSheetScreen

    private val _eventNavigateToDepositScreen = MutableLiveData<DepositScreenArgs?>()
    val eventNavigateToDepositScreen: LiveData<DepositScreenArgs?>
        get() = _eventNavigateToDepositScreen

    private val _eventNavigateToDashboardScreen = MutableLiveData<Boolean?>()
    val eventNavigateToDashboardScreen: LiveData<Boolean?>
        get() = _eventNavigateToDashboardScreen

    fun eventNavigateToRegularBankTransferScreenCompleted() {
        _eventNavigateToRegularBankTransferScreen.value = null
    }

    fun eventShowBanksSheetScreenCompleted() {
        _eventShowBanksSheetScreen.value = null
    }

    fun eventNavigateToDepositScreenCompleted() {
        _eventNavigateToDepositScreen.value = null
    }

    fun eventNavigateToDashboardScreenCompleted() {
        _eventNavigateToDashboardScreen.value = null
    }

    init {
        refreshNavigationTitle()
        refreshPrimaryButtonVisibility()
        refreshDataItems()

        reloadData()
    }

    fun handleOnRecyclerViewScrolled(recyclerView: RecyclerView) {
        refreshNavigationBarElevation(recyclerView)
        refreshNavigationBarTitleVisibility(recyclerView)
    }

    fun handleOnPrimaryButtonClicked() {
        _eventNavigateToDashboardScreen.value = true
    }

    fun handleOnActionClicked(itemID: String?) {
        if (itemID == REGULAR_BANK_TRANSFER_ACTION_ITEM_ID) {
            _eventNavigateToRegularBankTransferScreen.value = RegularBankTransferScreenArgs(
                userIBAN = screenArgs?.userIBAN,
                shouldShowPrimaryButton = screenArgs?.shouldShowPrimaryButton
            )

            return
        }

//        val screenArgs = BanksSheetScreenArgs(
//            banks = europeProviders
//        )
//
//        _eventShowBanksSheetScreen.value = screenArgs
    }

    fun handleOnBankAccountClicked(bankAccountID: String?) {
        val bankAccount = filteredBankAccounts?.find { it.id == bankAccountID } ?: return

        val shouldShowBadge = (bankAccount.supportsEasyTransfer == true)
        val badgeText = context.resources.getString(R.string.easy_label)

        val screenArgs = DepositScreenArgs(
            shouldShowPaymentMethodBadge = shouldShowBadge,
            paymentMethodBadgeText = badgeText,
            paymentMethodDescriptionText = bankAccount.displayAccountIdentifier,
            selectedBankAccountID = bankAccount.id,
            selectedBankID = null,
            paymentMethodTitleText = bankAccount.displayBankName,
            paymentMethodImageURL = bankAccount.bankIconURL
        )

        _eventNavigateToDepositScreen.value = screenArgs
    }

    fun handleOnBankClicked(bankID: String?) {
        val bank = europeProviders?.find { it.id == bankID } ?: return

        val screenArgs = DepositScreenArgs(
            shouldShowPaymentMethodBadge = false,
            paymentMethodBadgeText = null,
            paymentMethodDescriptionText = null,
            selectedBankAccountID = null,
            selectedBankID = bank.id,
            paymentMethodTitleText = bank.name,
            paymentMethodImageURL = bank.logoURL
        )

        _eventNavigateToDepositScreen.value = screenArgs
    }

    private fun reloadData() {
        //_eventShowOrHideLoadingDialog.value = true

        getCredentialsCall { _, accessToken, idToken ->
            if (accessToken == null || idToken == null) {
                // TODO: Handle the failure

                //_eventShowOrHideLoadingDialog.value = false

                return@getCredentialsCall
            }

            viewModelScope.launch {
                val userID = preferencesRepository.getUserID()

                val bankAccountsDeferred = async {
                    repository.getLinkedBankAccounts(
                        accessToken = accessToken,
                        idToken = idToken,
                        userID = userID
                    )
                }

                val europeProvidersDeferred = async {
                    repository.getEuropeProviders(
                        accessToken = accessToken,
                        idToken = idToken,
                        scope = "PAY"
                    )
                }

                val bankAccountsResult = bankAccountsDeferred.await()
                val europeProvidersResult = europeProvidersDeferred.await()

                europeProviders = parseSuccessResponse(europeProvidersResult)

                filteredBankAccounts = parseSuccessResponse(bankAccountsResult)?.data?.filter {
                    it.supportsEasyTransfer == true
                }

                refreshDataItems()

                //_eventShowOrHideLoadingDialog.value = false
            }
        }
    }

    private fun getCredentialsCall(callback: ((succeeded: Boolean, accessToken: String?, idToken: String?) -> Unit)?) {
        authRepository.loadAuth0Credentials {
            when (it) {
                is NetworkResource.Success -> {
                    val accessToken = "Bearer ${it.data?.accessToken}"
                    val idToken = "Bearer ${it.data?.idToken}"

                    callback?.invoke(true, accessToken, idToken)
                }

                is NetworkResource.Failure -> {
                    callback?.invoke(false, null, null)
                }
            }
        }
    }

    private fun refreshNavigationTitle() {
        _navigationTitleText.value = generateScreenTitle()
    }

    private fun refreshPrimaryButtonVisibility() {
        val isVisible = (screenArgs?.shouldShowPrimaryButton == true)
        _isPrimaryButtonVisible.value = isVisible
    }

    private fun refreshDataItems() {
        _dataItems.value = generateDataItems()
    }

    private fun generateDataItems(): List<DataItem> {
        val finalAnswer = mutableListOf<DataItem>()

        finalAnswer.add(generateHeaderItem())

        generateRecentSectionItems()?.let {
            finalAnswer.addAll(it)
        }

        generateRecommendedSectionItems()?.let {
            finalAnswer.addAll(it)
        }

        finalAnswer.add(generateOtherSectionHeaderItem())
        finalAnswer.add(generateEasyBankTransferItem())

        return finalAnswer
    }

    private fun generateHeaderItem(): DataItem.HeaderItem {
        val spacing12 = context.resources.getDimensionPixelSize(R.dimen.spacing_12)
        val spacing16 = context.resources.getDimensionPixelSize(R.dimen.spacing_16)

        val screenTitle = generateScreenTitle()
        val spannableStringTitle = SpannableString(screenTitle)

        return DataItem.HeaderItem(
            id = "headerItem",
            headerType = null,
            paddingStart = spacing16,
            paddingTop = spacing12,
            paddingEnd = spacing16,
            paddingBottom = null,
            textAppearanceRes = R.style.ScreenTitle,
            title = spannableStringTitle
        )
    }

    private fun generateRecentSectionItems(): List<DataItem>? {
        val bankAccountItems = generateBankAccountItems()

        if (bankAccountItems.isEmpty()) return null

        val finalAnswer = mutableListOf<DataItem>()

        finalAnswer.add(generateRecentSectionHeaderItem())
        finalAnswer.addAll(bankAccountItems)

        return finalAnswer
    }

    private fun generateRecentSectionHeaderItem(): DataItem.HeaderItem {
        val spacing16 = context.resources.getDimensionPixelSize(R.dimen.spacing_16)
        val spacing32 = context.resources.getDimensionPixelSize(R.dimen.spacing_32)

        val title = context.getString(R.string.recent_label)
        val spannableStringTitle = SpannableString(title)

        return DataItem.HeaderItem(
            id = "recentSectionHeaderItem",
            headerType = null,
            paddingStart = spacing16,
            paddingTop = spacing32,
            paddingEnd = spacing16,
            paddingBottom = null,
            textAppearanceRes = R.style.HeadingsH3Mobile_PrimaryColor,
            title = spannableStringTitle
        )
    }

    private fun generateBankAccountItems(): List<DataItem.BankAccountItem> {
        val finalAnswer = mutableListOf<DataItem.BankAccountItem>()

        filteredBankAccounts?.forEachIndexed { index, bankAccount ->
            val europeProviderID = bankAccount.id ?: return@forEachIndexed

            val spacing8 = context.resources.getDimensionPixelSize(R.dimen.spacing_8)
            val spacing12 = context.resources.getDimensionPixelSize(R.dimen.spacing_12)
            val spacing16 = context.resources.getDimensionPixelSize(R.dimen.spacing_16)

            val paddingTop = if (index == 0) spacing16 else spacing8

            val shouldShowBadge = (bankAccount.supportsEasyTransfer == true)
            val badgeText = context.resources.getString(R.string.easy_label)

            val item = DataItem.BankAccountItem(
                id = europeProviderID,
                paddingStart = spacing16,
                paddingTop = paddingTop,
                paddingEnd = spacing16,
                paddingBottom = null,
                containerPadding = spacing12,
                hasBorder = true,
                imageURI = bankAccount.bankIconURL,
                titleText = bankAccount.displayBankName,
                descriptionText = bankAccount.displayAccountIdentifier,
                shouldShowBadge = shouldShowBadge,
                badgeText = badgeText
            )

            finalAnswer.add(item)
        }

        return finalAnswer
    }

    private fun generateRecommendedSectionItems(): List<DataItem>? {
        if (europeProviders.isNullOrEmpty()) return null

        val finalAnswer = mutableListOf<DataItem>()

        finalAnswer.add(generateRecommendedSectionHeaderItem())
        finalAnswer.add(generateRegularBankTransferItem())

        return finalAnswer
    }

    private fun generateRecommendedSectionHeaderItem(): DataItem.HeaderItem {
        val spacing16 = context.resources.getDimensionPixelSize(R.dimen.spacing_16)
        val spacing32 = context.resources.getDimensionPixelSize(R.dimen.spacing_32)
        val spacing40 = context.resources.getDimensionPixelSize(R.dimen.spacing_40)

        val paddingTop = if (filteredBankAccounts.isNullOrEmpty()) {
            spacing32
        } else spacing40

        val title = context.getString(R.string.recommended_label)
        val spannableStringTitle = SpannableString(title)

        return DataItem.HeaderItem(
            id = "recommendedSectionHeaderItem",
            headerType = null,
            paddingStart = spacing16,
            paddingTop = paddingTop,
            paddingEnd = spacing16,
            paddingBottom = null,
            textAppearanceRes = R.style.HeadingsH3Mobile_PrimaryColor,
            title = spannableStringTitle
        )
    }

    private fun generateEasyBankTransferItem(): DataItem.ActionItem {
        val spacing16 = context.resources.getDimensionPixelSize(R.dimen.spacing_16)

        val title = context.getString(R.string.easy_bank_transfer_action_title_text)
        val subtitle = context.getString(R.string.easy_bank_transfer_action_subtitle_text)
        val badgeText = context.getString(R.string.coming_soon)

        return DataItem.ActionItem(
            id = EASY_BANK_TRANSFER_ACTION_ITEM_ID,
            imageDrawableRes = R.drawable.ic_electric_bolt_filled,
            paddingStart = spacing16,
            paddingTop = spacing16,
            paddingEnd = spacing16,
            paddingBottom = null,
            title = title,
            subtitle = subtitle,
            shouldShowBadge = true,
            badgeText = badgeText,
            isDisabled = false
        )
    }

    private fun generateOtherSectionHeaderItem(): DataItem.HeaderItem {
        val spacing16 = context.resources.getDimensionPixelSize(R.dimen.spacing_16)
        val spacing40 = context.resources.getDimensionPixelSize(R.dimen.spacing_40)

        val title = context.getString(R.string.other_label)
        val spannableStringTitle = SpannableString(title)

        return DataItem.HeaderItem(
            id = "otherSectionHeaderItem",
            headerType = null,
            paddingStart = spacing16,
            paddingTop = spacing40,
            paddingEnd = spacing16,
            paddingBottom = null,
            textAppearanceRes = R.style.HeadingsH3Mobile_PrimaryColor,
            title = spannableStringTitle
        )
    }

    private fun generateRegularBankTransferItem(): DataItem.ActionItem {
        val spacing16 = context.resources.getDimensionPixelSize(R.dimen.spacing_16)

        val title = context.getString(R.string.regular_bank_transfer_action_title_text)
        val subtitle = context.getString(R.string.regular_bank_transfer_action_subtitle_text)

        return DataItem.ActionItem(
            id = REGULAR_BANK_TRANSFER_ACTION_ITEM_ID,
            imageDrawableRes = R.drawable.ic_nest_clock_farsight_analog,
            paddingStart = spacing16,
            paddingTop = spacing16,
            paddingEnd = spacing16,
            paddingBottom = null,
            title = title,
            subtitle = subtitle,
            shouldShowBadge = false,
            badgeText = null,
            isDisabled = false
        )
    }

    private fun refreshNavigationBarElevation(recyclerView: RecyclerView) {
        val offset = recyclerView.computeVerticalScrollOffset()

        val elevationRes = if (offset > 0) R.dimen.spacing_2 else R.dimen.spacing_0
        val elevation = context.resources.getDimension(elevationRes)

        _navigationBarElevation.value = elevation
    }

    private fun refreshNavigationBarTitleVisibility(recyclerView: RecyclerView) {
        _isNavigationTitleVisible.value = shouldShowNavigationBarTitle(recyclerView)
    }

    private fun shouldShowNavigationBarTitle(recyclerView: RecyclerView): Boolean {
        val view =
            recyclerView.findChildViewUnder(0f, 0f) ?: return false
        val viewHolder = recyclerView.findContainingViewHolder(view) ?: return false
        val position = viewHolder.absoluteAdapterPosition

        // There is also the following useful property: viewHolder.itemView.top

        if (position > 0) return true

        (viewHolder as? HeaderViewHolder)?.let {
            val viewHolderTop = abs(viewHolder.itemView.top)
            val titleBottom = it.computeTitleBottom()

            return viewHolderTop >= titleBottom
        }

        return false
    }

    private fun generateScreenTitle(): String {
        return context.getString(R.string.bank_transfer_screen_title_text)
    }
}
