package com.wealthyhood.wealthyhood.ui.assetdetails

import android.content.Context
import androidx.annotation.DrawableRes
import com.google.gson.Gson
import com.wealthyhood.wealthyhood.R
import com.wealthyhood.wealthyhood.database.Asset
import com.wealthyhood.wealthyhood.extensions.convertToOrder
import com.wealthyhood.wealthyhood.extensions.convertToReward
import com.wealthyhood.wealthyhood.extensions.convertToTransaction
import com.wealthyhood.wealthyhood.extensions.generateDividendReceiptScreenArguments
import com.wealthyhood.wealthyhood.extensions.generateInfoRows
import com.wealthyhood.wealthyhood.extensions.generateOrderReviewFooterProperties
import com.wealthyhood.wealthyhood.extensions.generateProviderLogoURI
import com.wealthyhood.wealthyhood.extensions.generateReviewTitle
import com.wealthyhood.wealthyhood.extensions.getIconDrawableRes
import com.wealthyhood.wealthyhood.extensions.getStatusEnum
import com.wealthyhood.wealthyhood.model.ConfirmationReceiptScreenArgs
import com.wealthyhood.wealthyhood.model.OrderReviewFragmentArguments
import com.wealthyhood.wealthyhood.service.AssetRecentActivityItem
import com.wealthyhood.wealthyhood.service.ETFProvider
import com.wealthyhood.wealthyhood.service.Order
import com.wealthyhood.wealthyhood.service.Portfolio
import com.wealthyhood.wealthyhood.service.Reward
import com.wealthyhood.wealthyhood.service.Transaction
import com.wealthyhood.wealthyhood.ui.myaccount.transactions.TransactionsHelper
import com.wealthyhood.wealthyhood.ui.myaccount.transactions.TransactionsHelper.generateOrderTransaction
import java.util.Locale

class AssetDetailsHelper(private val context: Context) {

    companion object {

        const val ORDER_TRANSACTION_TYPE = "order"
        const val REWARD_TRANSACTION_TYPE = "reward"
        const val DIVIDEND_TRANSACTION_TYPE = "dividend"

        fun generateRecentActivityObjects(recentActivityItems: List<AssetRecentActivityItem>?): List<Any>? {
            if (recentActivityItems.isNullOrEmpty()) return null

            val finalAnswer = mutableListOf<Any>()

            val gson = Gson()

            recentActivityItems.forEach { recentActivityItem ->
                val type = recentActivityItem.type ?: return@forEach
                val item = recentActivityItem.item ?: return@forEach

                val itemJSONString = gson.toJson(item)

                if (type == ORDER_TRANSACTION_TYPE) {
                    itemJSONString?.convertToOrder()?.let {
                        finalAnswer.add(it)
                    }
                } else if (type == REWARD_TRANSACTION_TYPE) {
                    itemJSONString?.convertToReward()?.let {
                        finalAnswer.add(it)
                    }
                } else if (type == DIVIDEND_TRANSACTION_TYPE) {
                    itemJSONString?.convertToTransaction()?.let {
                        finalAnswer.add(it)
                    }
                }
            }

            return finalAnswer
        }

        fun generateTransactionDataItems(
            context: Context,
            currencyISOCode: String?,
            userLocale: Locale,
            assetTitle: String?,
            assetDrawableRes: Int?,
            assetIconURI: String?,
            recentActivityObjects: List<Any>?
        ): List<AssetDetailsListAdapter.DataItem>? {
            if (recentActivityObjects.isNullOrEmpty()) return null

            val finalAnswer = mutableListOf<AssetDetailsListAdapter.DataItem>()

            recentActivityObjects.forEach { recentActivityObject ->
                if (recentActivityObject is Order) {
                    generateTransactionItemFromOrder(
                        context = context,
                        currencyISOCode = currencyISOCode,
                        userLocale = userLocale,
                        assetTitle = assetTitle,
                        assetDrawableRes = assetDrawableRes,
                        assetIconURI = assetIconURI,
                        order = recentActivityObject
                    )?.let {
                        finalAnswer.add(it)
                    }
                } else if (recentActivityObject is Reward) {
                    generateTransactionItemFromReward(
                        context = context,
                        assetTitle = assetTitle,
                        assetDrawableRes = assetDrawableRes,
                        assetIconURI = assetIconURI,
                        reward = recentActivityObject,
                        userLocale = userLocale
                    )?.let {
                        finalAnswer.add(it)
                    }
                } else if (recentActivityObject is Transaction) {
                    generateTransactionItemFromDividendTransaction(
                        context = context,
                        assetTitle = assetTitle,
                        assetDrawableRes = assetDrawableRes,
                        assetIconURI = assetIconURI,
                        dividendTransaction = recentActivityObject,
                        userLocale = userLocale
                    )?.let {
                        finalAnswer.add(it)
                    }
                }
            }

            return finalAnswer
        }

        private fun generateTransactionItemFromOrder(
            context: Context,
            currencyISOCode: String?,
            userLocale: Locale,
            assetTitle: String?,
            assetDrawableRes: Int?,
            assetIconURI: String?,
            order: Order
        ): AssetDetailsListAdapter.DataItem.TransactionDataItem? {
            val orderID = order.id ?: return null

            val parentTransaction = order.getTransactionAsObject()
            val statusEnum = order.getStatusEnum()

            val badgeText =
                if (parentTransaction?.category == TransactionsHelper.TransactionCategory.Rebalance.rawValue) {
                    context.resources.getString(R.string.rebalance)
                } else null

            val shouldShowRebalanceBadge = (badgeText != null)

            val transactionItem = generateOrderTransaction(
                context = context,
                currencyISOCode = currencyISOCode,
                userLocale = userLocale,
                transactionID = orderID,
                assetTitle = assetTitle,
                assetDrawableRes = assetDrawableRes,
                assetIconURI = assetIconURI,
                statusEnum = statusEnum,
                order = order,
                shouldShowRebalanceBadge = shouldShowRebalanceBadge,
                displayDate = order.displayDate,
                activityFilter = null,
                cashFlowSign = null
            )

            return AssetDetailsListAdapter.DataItem.TransactionDataItem(
                id = transactionItem.id,
                paddingStart = transactionItem.paddingStart,
                paddingTop = transactionItem.paddingTop,
                paddingEnd = transactionItem.paddingEnd,
                paddingBottom = transactionItem.paddingBottom,
                shouldShowSmallImage = transactionItem.shouldShowSmallImage,
                smallImageDrawableRes = transactionItem.smallImageDrawableRes,
                smallImageURI = transactionItem.smallImageURI,
                shouldShowBigImage = transactionItem.shouldShowBigImage,
                bigImageDrawableRes = transactionItem.bigImageDrawableRes,
                bigImageURI = transactionItem.bigImageURI,
                titleText = transactionItem.titleText,
                shouldShowBadge = transactionItem.shouldShowBadge,
                badgeBackgroundColorRes = transactionItem.badgeBackgroundColorRes,
                badgeText = transactionItem.badgeText,
                badgeTextColorRes = transactionItem.badgeTextColorRes,
                shouldShowStatusIcon = transactionItem.shouldShowStatusIcon,
                statusIconDrawablesRes = transactionItem.statusIconDrawablesRes,
                statusIconTintColorRes = transactionItem.statusIconTintColorRes,
                statusText = transactionItem.statusText,
                statusTextColorRes = transactionItem.statusTextColorRes,
                shouldShowStatusDot = transactionItem.shouldShowStatusDot,
                shouldShowSecondaryStatusText = transactionItem.shouldShowSecondaryStatusText,
                secondaryStatusText = transactionItem.secondaryStatusText,
                shouldShowPaymentViews = transactionItem.shouldShowPaymentViews,
                paymentDrawableRes = transactionItem.paymentDrawableRes,
                paymentText = transactionItem.paymentText,
                shouldShowAmount = transactionItem.shouldShowAmount,
                amountText = transactionItem.amountText,
                shouldShowType = transactionItem.shouldShowType,
                typeText = transactionItem.typeText,
                typeTextColorRes = transactionItem.typeTextColorRes
            )
        }

        private fun generateTransactionItemFromReward(
            context: Context,
            assetTitle: String?,
            @DrawableRes assetDrawableRes: Int?,
            assetIconURI: String?,
            reward: Reward,
            userLocale: Locale
        ): AssetDetailsListAdapter.DataItem.TransactionDataItem? {
            val transactionItem = TransactionsHelper.generateReward(
                context = context,
                reward = reward,
                activityFilter = null,
                assetTitle = assetTitle,
                assetDrawableRes = assetDrawableRes,
                assetIconURI = assetIconURI,
                userLocale = userLocale
            ) ?: return null

            return AssetDetailsListAdapter.DataItem.TransactionDataItem(
                id = transactionItem.id,
                paddingStart = transactionItem.paddingStart,
                paddingTop = transactionItem.paddingTop,
                paddingEnd = transactionItem.paddingEnd,
                paddingBottom = transactionItem.paddingBottom,
                shouldShowSmallImage = transactionItem.shouldShowSmallImage,
                smallImageDrawableRes = transactionItem.smallImageDrawableRes,
                smallImageURI = transactionItem.smallImageURI,
                shouldShowBigImage = transactionItem.shouldShowBigImage,
                bigImageDrawableRes = transactionItem.bigImageDrawableRes,
                bigImageURI = transactionItem.bigImageURI,
                titleText = transactionItem.titleText,
                shouldShowBadge = transactionItem.shouldShowBadge,
                badgeBackgroundColorRes = transactionItem.badgeBackgroundColorRes,
                badgeText = transactionItem.badgeText,
                badgeTextColorRes = transactionItem.badgeTextColorRes,
                shouldShowStatusIcon = transactionItem.shouldShowStatusIcon,
                statusIconDrawablesRes = transactionItem.statusIconDrawablesRes,
                statusIconTintColorRes = transactionItem.statusIconTintColorRes,
                statusText = transactionItem.statusText,
                statusTextColorRes = transactionItem.statusTextColorRes,
                shouldShowStatusDot = transactionItem.shouldShowStatusDot,
                shouldShowSecondaryStatusText = transactionItem.shouldShowSecondaryStatusText,
                secondaryStatusText = transactionItem.secondaryStatusText,
                shouldShowPaymentViews = transactionItem.shouldShowPaymentViews,
                paymentDrawableRes = transactionItem.paymentDrawableRes,
                paymentText = transactionItem.paymentText,
                shouldShowAmount = transactionItem.shouldShowAmount,
                amountText = transactionItem.amountText,
                shouldShowType = transactionItem.shouldShowType,
                typeText = transactionItem.typeText,
                typeTextColorRes = transactionItem.typeTextColorRes
            )
        }

        private fun generateTransactionItemFromDividendTransaction(
            context: Context,
            assetTitle: String?,
            @DrawableRes assetDrawableRes: Int?,
            assetIconURI: String?,
            dividendTransaction: Transaction,
            userLocale: Locale
        ): AssetDetailsListAdapter.DataItem.TransactionDataItem? {
            if (dividendTransaction.category != TransactionsHelper.TransactionCategory.Dividend.rawValue) return null

            val transactionItem = TransactionsHelper.generateDividendTransaction(
                context = context,
                transaction = dividendTransaction,
                activityFilter = null,
                assetTitle = assetTitle,
                assetDrawableRes = assetDrawableRes,
                assetIconURI = assetIconURI,
                userLocale = userLocale,
                cashFlowSign = null
            ) ?: return null

            return AssetDetailsListAdapter.DataItem.TransactionDataItem(
                id = transactionItem.id,
                paddingStart = transactionItem.paddingStart,
                paddingTop = transactionItem.paddingTop,
                paddingEnd = transactionItem.paddingEnd,
                paddingBottom = transactionItem.paddingBottom,
                shouldShowSmallImage = transactionItem.shouldShowSmallImage,
                smallImageDrawableRes = transactionItem.smallImageDrawableRes,
                smallImageURI = transactionItem.smallImageURI,
                shouldShowBigImage = transactionItem.shouldShowBigImage,
                bigImageDrawableRes = transactionItem.bigImageDrawableRes,
                bigImageURI = transactionItem.bigImageURI,
                titleText = transactionItem.titleText,
                shouldShowBadge = transactionItem.shouldShowBadge,
                badgeBackgroundColorRes = transactionItem.badgeBackgroundColorRes,
                badgeText = transactionItem.badgeText,
                badgeTextColorRes = transactionItem.badgeTextColorRes,
                shouldShowStatusIcon = transactionItem.shouldShowStatusIcon,
                statusIconDrawablesRes = transactionItem.statusIconDrawablesRes,
                statusIconTintColorRes = transactionItem.statusIconTintColorRes,
                statusText = transactionItem.statusText,
                statusTextColorRes = transactionItem.statusTextColorRes,
                shouldShowStatusDot = transactionItem.shouldShowStatusDot,
                shouldShowSecondaryStatusText = transactionItem.shouldShowSecondaryStatusText,
                secondaryStatusText = transactionItem.secondaryStatusText,
                shouldShowPaymentViews = transactionItem.shouldShowPaymentViews,
                paymentDrawableRes = transactionItem.paymentDrawableRes,
                paymentText = transactionItem.paymentText,
                shouldShowAmount = transactionItem.shouldShowAmount,
                amountText = transactionItem.amountText,
                shouldShowType = transactionItem.shouldShowType,
                typeText = transactionItem.typeText,
                typeTextColorRes = transactionItem.typeTextColorRes
            )
        }
    }

    var portfolio: Portfolio? = null
    var recentActivityObjects: List<Any>? = null

    fun findOrderWithID(orderID: String): Order? {
        return recentActivityObjects?.find {
            it is Order && it.id == orderID
        } as? Order
    }

    fun findRewardWithID(rewardID: String): Reward? {
        return recentActivityObjects?.find {
            it is Reward && it.id == rewardID
        } as? Reward
    }

    private fun findTransactionWithID(transactionID: String): Transaction? {
        return recentActivityObjects?.find {
            it is Transaction && it.id == transactionID
        } as? Transaction
    }

    fun hasUserQuantityOfThatAsset(assetID: String?): Boolean {
        val assetHolding: Portfolio.Holding? = portfolio?.holdings?.find { holding ->
            holding.assetCommonID == assetID
        }

        return assetHolding?.quantity != null && assetHolding.quantity > 0
    }

    fun generateOrderReviewScreenArguments(
        asset: Asset?,
        currentTickerPrice: Float?,
        tradedPrice: Float?,
        tradedCurrency: String?,
        currencyISOCode: String?,
        userLocale: Locale,
        order: Order,
        etfProviders: List<ETFProvider>?
    ): OrderReviewFragmentArguments {
        val title = order.generateReviewTitle(
            context = context,
            assetTitle = asset?.title
        )

        val infoRows = order.generateInfoRows(
            context = context,
            tradedPrice = tradedPrice,
            tradedCurrency = tradedCurrency,
            currencyISOCode = currencyISOCode,
            userLocale = userLocale,
            shouldShowCashback = false
        )

        val footerProperties = order.generateOrderReviewFooterProperties(context)

        return OrderReviewFragmentArguments(
            titleImageRes = asset?.getIconDrawableRes(context),
            iconURI = asset?.generateProviderLogoURI(etfProviders),
            shouldShowTitleImageBackground = false,
            itemID = order.id,
            title = title,
            infoRows = infoRows,
            footerProperties = footerProperties
        )
    }

    fun generateDividendReceiptScreenArguments(
        userFullName: String?,
        transactionID: String,
        userCompanyEntity: String?,
        currentAsset: Asset?,
        userLocale: Locale,
        etfProviders: List<ETFProvider>?
    ): ConfirmationReceiptScreenArgs? {
        val dividend = findTransactionWithID(transactionID)

        return dividend?.generateDividendReceiptScreenArguments(
            context = context,
            userFullName = userFullName,
            userCompanyEntity = userCompanyEntity,
            currentAsset = currentAsset,
            userLocale = userLocale,
            etfProviders = etfProviders
        )
    }

    fun generateSectorDistributionItemDesignProperties(distributionItemID: String): Pair<Int, Int> {
        return when (distributionItemID) {
            "technology" -> Pair(R.drawable.technology, R.color.technology)
            "consumer" -> Pair(R.drawable.consumer, R.color.consumer)
            "healthcare" -> Pair(R.drawable.healthcare, R.color.healthcare)
            "financials" -> Pair(R.drawable.financials, R.color.financials)
            "communication" -> Pair(R.drawable.communication, R.color.communication)
            "industrials" -> Pair(R.drawable.industrials, R.color.industrials)
            "energy" -> Pair(R.drawable.energy, R.color.energy)
            "utilities" -> Pair(R.drawable.utilities, R.color.utilities)
            "real-estate" -> Pair(R.drawable.real_estate, R.color.real_estate)
            else -> Pair(R.drawable.materials, R.color.materials)
        }
    }
}
