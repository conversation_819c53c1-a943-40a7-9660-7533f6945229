package com.wealthyhood.wealthyhood.ui.billing

import android.app.Activity
import android.app.Application
import android.content.res.Resources
import android.text.SpannableString
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import androidx.recyclerview.widget.RecyclerView
import com.wealthyhood.wealthyhood.R
import com.wealthyhood.wealthyhood.common.parseSuccessResponse
import com.wealthyhood.wealthyhood.domain.GetPortfoliosCoroutinesUseCase
import com.wealthyhood.wealthyhood.domain.GetTrueLayerProvidersUseCase
import com.wealthyhood.wealthyhood.domain.GetUserCoroutinesUseCase
import com.wealthyhood.wealthyhood.extensions.convertToPaymentMethod
import com.wealthyhood.wealthyhood.extensions.findUserLocale
import com.wealthyhood.wealthyhood.extensions.generateFullName
import com.wealthyhood.wealthyhood.extensions.generateImageDrawableRes
import com.wealthyhood.wealthyhood.extensions.generatePlanActivationCompletedScreenArgs
import com.wealthyhood.wealthyhood.extensions.generatePlanBannerProperties
import com.wealthyhood.wealthyhood.extensions.generateSubtitle
import com.wealthyhood.wealthyhood.extensions.isEU
import com.wealthyhood.wealthyhood.extensions.isFree
import com.wealthyhood.wealthyhood.extensions.isLifetime
import com.wealthyhood.wealthyhood.extensions.isMonthly
import com.wealthyhood.wealthyhood.extensions.isYearly
import com.wealthyhood.wealthyhood.model.DomainResult
import com.wealthyhood.wealthyhood.model.NetworkResource
import com.wealthyhood.wealthyhood.model.PaymentMethodsScreenArguments
import com.wealthyhood.wealthyhood.model.PlanActivationCompletedScreenArgs
import com.wealthyhood.wealthyhood.model.ShowGooglePayDialogScreenArguments
import com.wealthyhood.wealthyhood.model.TrulayerProvidersGetModeEnum
import com.wealthyhood.wealthyhood.repository.AuthRepository
import com.wealthyhood.wealthyhood.repository.MyAccountRepository
import com.wealthyhood.wealthyhood.repository.PlansRepository
import com.wealthyhood.wealthyhood.repository.PreferencesRepository
import com.wealthyhood.wealthyhood.repository.Repository
import com.wealthyhood.wealthyhood.service.ApiResponse
import com.wealthyhood.wealthyhood.service.FileCheckerService
import com.wealthyhood.wealthyhood.service.GetProvidersResponse
import com.wealthyhood.wealthyhood.service.GetUserQueryParams
import com.wealthyhood.wealthyhood.service.InitiateStripeResponse
import com.wealthyhood.wealthyhood.service.PaymentMethod
import com.wealthyhood.wealthyhood.service.Plan
import com.wealthyhood.wealthyhood.service.Portfolio
import com.wealthyhood.wealthyhood.service.Subscription
import com.wealthyhood.wealthyhood.service.Transaction
import com.wealthyhood.wealthyhood.service.TrueLayerProvider
import com.wealthyhood.wealthyhood.service.User
import com.wealthyhood.wealthyhood.ui.billing.BillingListAdapter.DataItem
import io.sentry.Sentry
import io.sentry.SentryLevel
import kotlinx.coroutines.async
import kotlinx.coroutines.launch
import kotlin.math.abs

class BillingViewModel(application: Application) : AndroidViewModel(application) {

    private val context = application
    val resources: Resources = context.resources

    companion object {

        private enum class CurrentPlanCallToAction {
            NONE,
            UPGRADE,
            RENEW,
            SAVE_WITH_ANNUAL
        }
    }

    private val preferencesRepository = PreferencesRepository(application) // TODO: Use DI
    private val authRepository = AuthRepository(application) // TODO: Use DI
    private val myAccountRepository = MyAccountRepository() // TODO: Use DI
    private val plansRepository = PlansRepository(preferencesRepository) // TODO: Use DI
    private val repository = Repository() // TODO: Use DI

    private var currencyISOCode = preferencesRepository.getUserCurrency()
    private var userLocale = preferencesRepository.findUserLocale()
    private var companyEntity = preferencesRepository.getUserCompanyEntity()

    private val allPlans = plansRepository.getAllPlans()
    private val basePlans = plansRepository.getAllBasePlans(context)

    private val getTrueLayerProvidersUseCase = GetTrueLayerProvidersUseCase(
        repository = repository
    )

    private val getUserUseCase = GetUserCoroutinesUseCase(
        preferencesRepository = preferencesRepository,
        repository = repository
    )

    private val getPortfoliosUseCase = GetPortfoliosCoroutinesUseCase(
        preferencesRepository = preferencesRepository,
        repository = repository
    )

    private var isGooglePayAvailable = false

    private var accessToken: String = ""
    private var idToken: String = ""

    private var user: User? = null
    private var portfolio: Portfolio? = null

    private var paymentMethods: List<PaymentMethod>? = null
    private var allTransactions: List<Transaction>? = null
    private var trueLayerProviders: List<TrueLayerProvider>? = null
    private var costsAndChargesFileExists = false

    private var latestPaymentSetupIntentID: String? = null

    private val _navigationBarElevation = MutableLiveData<Float?>()
    val navigationBarElevation: LiveData<Float?>
        get() = _navigationBarElevation

    private val _isNavigationTitleVisible = MutableLiveData<Boolean?>()
    val isNavigationTitleVisible: LiveData<Boolean?>
        get() = _isNavigationTitleVisible

    private val _dataItems = MutableLiveData<List<DataItem>?>()
    val dataItems: LiveData<List<DataItem>?>
        get() = _dataItems

    private val _primaryButtonText = MutableLiveData<String?>()
    val primaryButtonText: LiveData<String?>
        get() = _primaryButtonText

    private val _isDividerViewVisible = MutableLiveData<Boolean?>()
    val isDividerViewVisible: LiveData<Boolean?>
        get() = _isDividerViewVisible

    private val _isPrimaryButtonVisible = MutableLiveData<Boolean?>()
    val isPrimaryButtonVisible: LiveData<Boolean?>
        get() = _isPrimaryButtonVisible

    private val _isSecondaryButtonVisible = MutableLiveData<Boolean?>()
    val isSecondaryButtonVisible: LiveData<Boolean?>
        get() = _isSecondaryButtonVisible

    private val _eventShowOrHideLoadingDialog = MutableLiveData<Boolean?>()
    val eventShowOrHideLoadingDialog: LiveData<Boolean?>
        get() = _eventShowOrHideLoadingDialog

    private val _eventShowToast = MutableLiveData<String?>()
    val eventShowToast: LiveData<String?>
        get() = _eventShowToast

    private val _eventShowChargesHistoryMessage = MutableLiveData<Boolean?>()
    val eventShowChargesHistoryMessage: LiveData<Boolean?>
        get() = _eventShowChargesHistoryMessage

    private val _eventNavigateToPlansScreen = MutableLiveData<String?>()
    val eventNavigateToPlansScreen: LiveData<String?>
        get() = _eventNavigateToPlansScreen

    private val _eventNavigateToTransactionsScreen = MutableLiveData<Boolean?>()
    val eventNavigateToTransactionsScreen: LiveData<Boolean?>
        get() = _eventNavigateToTransactionsScreen

    private val _eventCloseScreen = MutableLiveData<Boolean?>()
    val eventCloseScreen: LiveData<Boolean?>
        get() = _eventCloseScreen

    private val _eventOpenBrowser = MutableLiveData<String?>()
    val eventOpenBrowser: LiveData<String?>
        get() = _eventOpenBrowser

    private val _eventSetActivityResult = MutableLiveData<Int?>()
    val eventSetActivityResult: LiveData<Int?>
        get() = _eventSetActivityResult

    private val _eventNavigateToPaymentMethods = MutableLiveData<PaymentMethodsScreenArguments?>()
    val eventNavigateToPaymentMethods: LiveData<PaymentMethodsScreenArguments?>
        get() = _eventNavigateToPaymentMethods

    private val _eventShowGooglePayDialog = MutableLiveData<ShowGooglePayDialogScreenArguments?>()
    val eventShowGooglePayDialog: LiveData<ShowGooglePayDialogScreenArguments?>
        get() = _eventShowGooglePayDialog

    private val _eventNavigateToPlanActivationCompletedScreen =
        MutableLiveData<PlanActivationCompletedScreenArgs?>()
    val eventNavigateToPlanActivationCompletedScreen: LiveData<PlanActivationCompletedScreenArgs?>
        get() = _eventNavigateToPlanActivationCompletedScreen

    fun eventShowOrHideLoadingDialogCompleted() {
        _eventShowOrHideLoadingDialog.value = null
    }

    fun eventShowToastCompleted() {
        _eventShowToast.value = null
    }

    fun eventShowChargesHistoryMessageCompleted() {
        _eventShowChargesHistoryMessage.value = null
    }

    fun eventNavigateToPlansScreenCompleted() {
        _eventNavigateToPlansScreen.value = null
    }

    fun eventNavigateToTransactionsScreenCompleted() {
        _eventNavigateToTransactionsScreen.value = null
    }

    fun eventCloseScreenCompleted() {
        _eventCloseScreen.value = null
    }

    fun eventOpenBrowserCompleted() {
        _eventOpenBrowser.value = null
    }

    fun eventSetActivityResultCompleted() {
        _eventSetActivityResult.value = null
    }

    fun eventNavigateToPaymentMethodsCompleted() {
        _eventNavigateToPaymentMethods.value = null
    }

    fun eventShowGooglePayDialogCompleted() {
        _eventShowGooglePayDialog.value = null
    }

    fun eventNavigateToPlanActivationCompletedScreenCompleted() {
        _eventNavigateToPlanActivationCompletedScreen.value = null
    }

    init {
        refreshBottomButtons()
        refreshDataItems()

        reloadData()
    }

    fun handleOnRecyclerViewScrolled(recyclerView: RecyclerView) {
        refreshNavigationBarElevation(recyclerView)
        refreshNavigationBarTitleVisibility(recyclerView)
    }

    private fun refreshBottomButtons() {
        val currentPlan = findCurrentPlan()
        val currentPlanCallToAction = generateCurrentPlanCallToAction(currentPlan)

        refreshDividerViewVisibility(currentPlanCallToAction)
        refreshPrimaryButtonVisibility(currentPlanCallToAction)
        refreshSecondaryButtonVisibility(currentPlanCallToAction)

        refreshPrimaryButtonText(
            currentPlan = currentPlan,
            currentPlanCallToAction = currentPlanCallToAction
        )
    }

    private fun refreshDividerViewVisibility(currentPlanCallToAction: CurrentPlanCallToAction) {
        _isDividerViewVisible.value = (currentPlanCallToAction != CurrentPlanCallToAction.NONE)
    }

    private fun refreshPrimaryButtonVisibility(currentPlanCallToAction: CurrentPlanCallToAction) {
        _isPrimaryButtonVisible.value = (currentPlanCallToAction != CurrentPlanCallToAction.NONE)
    }

    private fun refreshSecondaryButtonVisibility(currentPlanCallToAction: CurrentPlanCallToAction) {
        _isSecondaryButtonVisible.value =
            currentPlanCallToAction == CurrentPlanCallToAction.SAVE_WITH_ANNUAL
    }

    private fun refreshPrimaryButtonText(
        currentPlan: Plan?,
        currentPlanCallToAction: CurrentPlanCallToAction
    ) {
        _primaryButtonText.value = when (currentPlanCallToAction) {
            CurrentPlanCallToAction.NONE -> null

            CurrentPlanCallToAction.UPGRADE -> {
                resources.getString(R.string.upgrade_your_plan_label)
            }

            CurrentPlanCallToAction.RENEW -> {
                resources.getString(R.string.plans_renew_plan_button_title, currentPlan?.title)
            }

            CurrentPlanCallToAction.SAVE_WITH_ANNUAL -> {
                findCorrespondingAvailableYearlyPlan(currentPlan)?.let { correspondingYearlyPlan ->
                    resources.getString(
                        R.string.plans_save_with_annual_plan_button_title,
                        correspondingYearlyPlan.savePercentage
                    )
                }
            }
        }
    }

    fun handleOnPrimaryButtonClicked() {
        val currentPlan = findCurrentPlan()
        val currentPlanCallToAction = generateCurrentPlanCallToAction(currentPlan)

        if (currentPlanCallToAction == CurrentPlanCallToAction.UPGRADE) {
            _eventNavigateToPlansScreen.value = Plan.PLUS_PLAN_YEARLY_PRICE_API_KEY

            return
        }

        if (currentPlanCallToAction == CurrentPlanCallToAction.RENEW) {
            renewSubscription()

            return
        }

        if (currentPlanCallToAction == CurrentPlanCallToAction.SAVE_WITH_ANNUAL) {
            val yearlyPlan = findCorrespondingAvailableYearlyPlan(currentPlan) ?: return

            updateSubscription(plan = yearlyPlan)
        }
    }

    fun handleOnSecondaryButtonClicked() {
        val currentPlan = findCurrentPlan()
        val yearlyPlan = findCorrespondingAvailableYearlyPlan(currentPlan)

        _eventNavigateToPlansScreen.value = yearlyPlan?.keyName
    }

    private fun updateSubscription(plan: Plan?) {
        _eventShowOrHideLoadingDialog.value = true

        getCredentialsCall { _, accessToken, idToken ->
            if (accessToken == null || idToken == null) {
                // TODO: Handle the failure

                _eventShowOrHideLoadingDialog.value = false

                return@getCredentialsCall
            }

            updateSubscriptionCall(
                accessToken = accessToken,
                idToken = idToken,
                plan = plan,
                category = "CardPaymentSubscription"
            ) { _, subscription ->
                _eventShowOrHideLoadingDialog.value = false

                if (subscription == null) {
                    // TODO: Handle the failure

                    return@updateSubscriptionCall
                }

                val screenArgs =
                    subscription.generatePlanActivationCompletedScreenArgs(
                        context = getApplication(),
                        allPlans = allPlans,
                        basePlans = basePlans,
                        previousPriceAPIKey = user?.subscription?.price,
                        isUserEU = (user?.isEU() == true)
                    )

                _eventNavigateToPlanActivationCompletedScreen.value = screenArgs
            }
        }
    }

    private fun getCredentialsCall(callback: ((succeeded: Boolean, accessToken: String?, idToken: String?) -> Unit)?) {
        authRepository.loadAuth0Credentials {
            when (it) {
                is NetworkResource.Success -> {
                    val accessToken = "Bearer ${it.data?.accessToken}"
                    val idToken = "Bearer ${it.data?.idToken}"

                    callback?.invoke(true, accessToken, idToken)
                }

                is NetworkResource.Failure -> {
                    callback?.invoke(false, null, null)
                }
            }
        }
    }

    private fun updateSubscriptionCall(
        accessToken: String,
        idToken: String,
        plan: Plan?,
        category: String?,
        callback: ((success: Boolean, subscription: Subscription?) -> Unit)?
    ) {
        myAccountRepository.updateSubscription(
            accessToken = accessToken,
            idToken = idToken,
            subscriptionID = user?.subscription?.id,
            priceAPIKey = plan?.keyName,
            category = category
        ) {
            when (it) {
                is NetworkResource.Success -> {
                    callback?.invoke(true, it.data)
                }

                is NetworkResource.Failure -> {
                    callback?.invoke(false, null)
                }
            }
        }
    }

    private fun generateCurrentPlanCallToAction(currentPlan: Plan?): CurrentPlanCallToAction {
        if (currentPlan?.isFree() == true) {
            return CurrentPlanCallToAction.UPGRADE
        }

        if (user?.subscription?.expiration?.downgradesTo != null) {
            return CurrentPlanCallToAction.RENEW
        }

        if (currentPlan?.isMonthly() == true) {
            return CurrentPlanCallToAction.SAVE_WITH_ANNUAL
        }

        return CurrentPlanCallToAction.NONE
    }

    private fun findCorrespondingAvailableYearlyPlan(plan: Plan?): Plan? {
        return allPlans.find { it.basePlanID == plan?.basePlanID && it.isYearly() }
    }

    fun handleOnGooglePayReady(ready: Boolean) {
        isGooglePayAvailable = ready
    }

    fun handleOnReloadDataRequest() {
        _eventSetActivityResult.value = Activity.RESULT_OK

        reloadData()
    }

    fun handleOnPaymentMethodClicked(paymentMethodID: String?) {
        // If the user has no full name, we still want to show the payment methods
        val finalUserFullName = user?.generateFullName() ?: ""

        _eventNavigateToPaymentMethods.value = PaymentMethodsScreenArguments(
            isGooglePayAvailable = isGooglePayAvailable,
            userFullName = finalUserFullName
        )
    }

    fun handleOnCloseButtonClicked() {
        _eventCloseScreen.value = true
    }

    fun handleOnPlanInfoButtonClicked() {
        val plan = findCurrentPlan() ?: return

        _eventNavigateToPlansScreen.value = plan.keyName
    }

    fun handleOnPlanButtonClicked() {
        val priceAPIKey = user?.subscription?.price ?: return

        if (priceAPIKey == Plan.BASIC_PLAN_PRICE_API_KEY) {
            _eventNavigateToPlansScreen.value = Plan.PLUS_PLAN_YEARLY_PRICE_API_KEY

            return
        }

        if (user?.subscription?.expiration != null) {
            renewSubscription()
        }
    }

    fun handleOnInfoButtonClicked() {
        _eventShowChargesHistoryMessage.value = true
    }

    fun handleOnSeeAllSubscriptionsButtonClicked() {
        _eventNavigateToTransactionsScreen.value = true
    }

    fun handleOnFooterLinkClicked(link: String?) {
        _eventOpenBrowser.value = link
    }

    fun handleOnNewPaymentMethodSelected(
        didSelectGooglePay: Boolean?,
        paymentMethodJSONString: String?
    ) {
        if (didSelectGooglePay == true) {
            getDataAndShowGooglePayDialog()

            return
        }

        val paymentMethod = paymentMethodJSONString?.convertToPaymentMethod()

        updatePaymentMethod(paymentMethod?.providers?.stripe?.id)
    }

    private fun getDataAndShowGooglePayDialog() {
        _eventShowOrHideLoadingDialog.value = true

        getCredentialsCall { _, accessToken, idToken ->
            if (accessToken == null || idToken == null) {
                // TODO: Handle the failure

                _eventShowOrHideLoadingDialog.value = false

                return@getCredentialsCall
            }

            viewModelScope.launch {
                val initiateStripeDeferred = async { initiateStripeCall() }

                val initiateStripeResult = initiateStripeDeferred.await()

                handleOnInitiateStripeResult(initiateStripeResult)

                _eventShowOrHideLoadingDialog.value = false
            }
        }
    }

    fun handleOnGooglePayCompleted() {
        _eventShowOrHideLoadingDialog.value = true

        getCredentialsCall { _, accessToken, idToken ->
            if (accessToken == null || idToken == null) {
                // TODO: Handle the failure

                _eventShowOrHideLoadingDialog.value = false

                return@getCredentialsCall
            }

            viewModelScope.launch {
                val completeStripeDeferred = async {
                    repository.completeStripe(
                        accessToken = accessToken,
                        idToken = idToken,
                        setupIntentID = latestPaymentSetupIntentID
                    )
                }

                val completeStripeResult = completeStripeDeferred.await()

                handleOnCompleteStripeResult(completeStripeResult)
            }
        }
    }

    private fun handleOnInitiateStripeResult(result: DomainResult<InitiateStripeResponse>?) {
        val response = parseSuccessResponse(result)

        val setupIntentClientSecret = response?.setupIntentClientSecret
        val setupIntentID = response?.setupIntentID

        latestPaymentSetupIntentID = setupIntentID

        showGooglePayDialog(setupIntentClientSecret)
    }

    private fun handleOnCompleteStripeResult(result: DomainResult<PaymentMethod>?) {
        val paymentMethod = parseSuccessResponse(result)

        val paymentMethodStripeID = paymentMethod?.providers?.stripe?.id

        if (paymentMethodStripeID == null) {
            _eventShowOrHideLoadingDialog.value = false

            return
        }

        updatePaymentMethod(paymentMethodStripeID)
    }

    private fun showGooglePayDialog(clientSecret: String?) {
        val currencyISOCode = currencyISOCode

        if (currencyISOCode == null) {
            // TODO: We should show an error message

            Sentry.captureMessage(
                "BillingViewModel.showGooglePayDialog: No currencyISOCode.",
                SentryLevel.ERROR
            )

            return
        }

        _eventShowGooglePayDialog.value = ShowGooglePayDialogScreenArguments(
            clientSecret = clientSecret,
            currencyCode = currencyISOCode,
            amount = 0L
        )
    }

    private fun updatePaymentMethod(paymentMethodStripeID: String?) {
        _eventShowOrHideLoadingDialog.value = true

        viewModelScope.launch {
            val updatePaymentDeferred = async { updatePaymentMethodCall(paymentMethodStripeID) }

            val updatePaymentResult = updatePaymentDeferred.await()

            if (updatePaymentResult is DomainResult.Error) {
                _eventShowOrHideLoadingDialog.value = false

                // TODO: Handle the error

                return@launch
            }

            val getUserDeferred = async { getUserCall() }
            val paymentMethodsDeferred = async { getPaymentMethodsCall() }

            val userResult = getUserDeferred.await()
            val paymentMethodsResult = paymentMethodsDeferred.await()

            // Μόλις λάβαμε νέο user οπότε το userCurrency μπορεί να έχει αλλάξει.
            currencyISOCode = preferencesRepository.getUserCurrency()
            userLocale = preferencesRepository.findUserLocale()
            companyEntity = preferencesRepository.getUserCompanyEntity()

            user = parseSuccessResponse(userResult)
            handleGetPaymentMethodsResponse(paymentMethodsResult)

            _eventShowOrHideLoadingDialog.value = false

            refreshDataItems()
        }
    }

    private fun checkCostsAndChargesFileExists() {
        val url = generateCostsAndChargesLink() ?: return

        FileCheckerService.checkIfFileExists(url) {
            costsAndChargesFileExists = true
            refreshDataItems()
        }
    }

    private fun reloadData() {
        _eventShowOrHideLoadingDialog.value = true

        authRepository.loadAuth0Credentials {
            when (it) {
                is NetworkResource.Success -> {
                    accessToken = "Bearer ${it.data?.accessToken}"
                    idToken = "Bearer ${it.data?.idToken}"

                    fetchDataFromApi()
                }

                is NetworkResource.Failure -> {
                    // TODO: Handle the failure
                    _eventShowOrHideLoadingDialog.value = false
                }
            }
        }
    }

    private fun fetchDataFromApi() {
        viewModelScope.launch {
            val getUserDeferred = async { getUserCall() }
            val getPortfolioDeferred = async { getPortfoliosCall() }
            val getTrueLayerProvidersDeferred = async { getTrueLayerProvidersCall() }
            val getBillingDeferred = async { getBillingCall() }
            val paymentMethodsDeferred = async { getPaymentMethodsCall() }

            val userResult = getUserDeferred.await()
            val portfolioResult = getPortfolioDeferred.await()
            val trueLayerProvidersResult = getTrueLayerProvidersDeferred.await()
            val billingResult = getBillingDeferred.await()
            val paymentMethodsResult = paymentMethodsDeferred.await()

            // Μόλις λάβαμε νέο user οπότε το userCurrency μπορεί να έχει αλλάξει.
            currencyISOCode = preferencesRepository.getUserCurrency()
            userLocale = preferencesRepository.findUserLocale()
            companyEntity = preferencesRepository.getUserCompanyEntity()

            user = parseSuccessResponse(userResult)
            portfolio =
                parseSuccessResponse(portfolioResult)?.find { portfolio -> portfolio.isReal == true }
            trueLayerProviders = parseSuccessResponse(trueLayerProvidersResult)?.results
            allTransactions = parseSuccessResponse(billingResult)

            handleGetPaymentMethodsResponse(paymentMethodsResult)

            refreshDataItems()
            refreshBottomButtons()

            checkCostsAndChargesFileExists()

            _eventShowOrHideLoadingDialog.value = false
        }
    }

    private fun handleGetPaymentMethodsResponse(response: DomainResult<ApiResponse<List<PaymentMethod>>>?) {
        paymentMethods = parseSuccessResponse(response)?.data
    }

    private suspend fun getUserCall(): NetworkResource<User> {
        val queryParams = GetUserQueryParams(populate = "subscription,addresses")

        return getUserUseCase.invoke(
            context = context,
            accessToken = accessToken,
            idToken = idToken,
            queryParams = queryParams
        )
    }

    private suspend fun getPortfoliosCall(): NetworkResource<List<Portfolio>> {
        return getPortfoliosUseCase.invoke(
            accessToken = accessToken,
            idToken = idToken,
            parameters = null,
            shouldGetFromCache = true
        )
    }

    private suspend fun getTrueLayerProvidersCall(): DomainResult<GetProvidersResponse>? {
        return getTrueLayerProvidersUseCase.invoke(
            getMode = TrulayerProvidersGetModeEnum.HISTORY
        )
    }

    private suspend fun getBillingCall(): DomainResult<List<Transaction>>? {
        return repository.getBilling(
            accessToken = accessToken,
            idToken = idToken,
            limit = null
        )
    }

    private suspend fun getPaymentMethodsCall(): DomainResult<ApiResponse<List<PaymentMethod>>>? {
        return repository.getPaymentMethods(
            accessToken = accessToken,
            idToken = idToken
        )
    }

    private suspend fun updatePaymentMethodCall(paymentMethodStripeID: String?): DomainResult<Subscription>? {
        return repository.updatePaymentMethodForSubscription(
            accessToken = accessToken,
            idToken = idToken,
            paymentMethodStripeID = paymentMethodStripeID
        )
    }

    private suspend fun initiateStripeCall(): DomainResult<InitiateStripeResponse>? {
        return repository.initiateStripe(
            accessToken = accessToken,
            idToken = idToken
        )
    }

    private suspend fun renewSubscriptionCall(): DomainResult<Subscription>? {
        return repository.renewSubscription(
            accessToken = accessToken,
            idToken = idToken,
            subscriptionID = user?.subscription?.id
        )
    }

    private fun renewSubscription() {
        _eventShowOrHideLoadingDialog.value = true

        viewModelScope.launch {
            val renewSubscriptionDeferred = async { renewSubscriptionCall() }
            renewSubscriptionDeferred.await()

            val getUserDeferred = async { getUserCall() }

            val userResult = getUserDeferred.await()

            // Μόλις λάβαμε νέο user οπότε το userCurrency μπορεί να έχει αλλάξει.
            currencyISOCode = preferencesRepository.getUserCurrency()
            userLocale = preferencesRepository.findUserLocale()
            companyEntity = preferencesRepository.getUserCompanyEntity()

            user = parseSuccessResponse(userResult)

            _eventSetActivityResult.value = Activity.RESULT_OK
            showPlanRenewalSuccessfulMessage()

            refreshDataItems()
            refreshBottomButtons()

            _eventShowOrHideLoadingDialog.value = false
        }
    }

    private fun refreshDataItems() {
        _dataItems.value = generateDataItems()
    }

    private fun generateDataItems(): List<DataItem> {
        val finalAnswer = mutableListOf<DataItem>()

        finalAnswer.add(
            generateHeaderItem()
        )

        generatePlanItem()?.let {
            finalAnswer.add(it)
        }

        generatePaymentMethodSectionItems()?.let {
            finalAnswer.addAll(it)
        }

        val subscriptionItems = generateSubscriptionItems()

        if (subscriptionItems.isNotEmpty()) {
            finalAnswer.add(
                generateSubscriptionsSectionHeaderItem()
            )

            finalAnswer.addAll(subscriptionItems)
        }

        generateFooterItem()?.let {
            finalAnswer.add(it)
        }

        return finalAnswer
    }

    private fun generateFooterItem(): DataItem.FooterItem? {
        val isUserEU = (companyEntity == "WEALTHYHOOD_EUROPE")
        if (isUserEU) return null

        if (!costsAndChargesFileExists) return null
        val link = generateCostsAndChargesLink() ?: return null

        return DataItem.FooterItem(
            id = "footerItem",
            link = link
        )
    }

    private fun generateCostsAndChargesLink(): String? {
        portfolio?.wealthKernel?.portfolioID?.let {
            return resources.getString(R.string.costs_and_charges_url_template, it)
        }

        return null
    }

    private fun generateHeaderItem(): DataItem.HeaderItem {
        return DataItem.HeaderItem(
            id = "headerItem",
            screenTitle = resources.getString(R.string.my_account_billing_action_text),
            hasDescription = true
        )
    }

    private fun generatePlanItem(): DataItem.PlanItem? {
        val planBannerProperties = user?.generatePlanBannerProperties(
            context = context,
            plansRepository = plansRepository
        ) ?: return null

        return DataItem.PlanItem(
            id = "planItem",
            backgroundDrawableRes = planBannerProperties.backgroundDrawableRes,
            iconDrawableRes = planBannerProperties.iconDrawableRes,
            title = planBannerProperties.title,
            subtitle = planBannerProperties.subtitle,
            infoButtonTintColorRes = R.color.white,
            subtitleTextColorRes = R.color.white,
            buttonTitle = planBannerProperties.buttonTitle
        )
    }

    private fun generatePaymentMethodSectionItems(): List<DataItem>? {
        val paymentMethodItem = generatePaymentMethodItem() ?: return null

        val finalAnswer = mutableListOf<DataItem>()

        finalAnswer.add(generatePaymentMethodSectionHeaderItem())
        finalAnswer.add(paymentMethodItem)

        return finalAnswer
    }

    private fun generatePaymentMethodSectionHeaderItem(): DataItem.PaymentMethodSectionHeaderItem {
        val spacing16 = context.resources.getDimensionPixelSize(R.dimen.spacing_16)
        val spacing40 = context.resources.getDimensionPixelSize(R.dimen.spacing_40)

        val title = context.resources.getString(R.string.billing_payment_method_section_title)
        val spannableString = SpannableString(title)

        return DataItem.PaymentMethodSectionHeaderItem(
            id = "paymentMethodSectionHeaderItem",
            paddingStart = spacing16,
            paddingTop = spacing40,
            paddingEnd = spacing16,
            paddingBottom = null,
            textAppearanceRes = R.style.PageTitleH3_AssetsSectionHeader,
            title = spannableString
        )
    }

    private fun generatePaymentMethodItem(): DataItem.PaymentMethodItem? {
        val currentPlan = findCurrentPlan() ?: return null

        if (currentPlan.isFree() || currentPlan.isLifetime()) return null

        val paymentMethodID = user?.subscription?.getPaymentMethodAsString()
        val paymentMethod = paymentMethods?.find { it.id == paymentMethodID } ?: return null

        val spacing16 = context.resources.getDimensionPixelSize(R.dimen.spacing_16)

        val subtitle = paymentMethod.generateSubtitle()

        val imageDrawableRes = if (paymentMethod.wallet != null) {
            R.drawable.google_pay_mark
        } else {
            paymentMethod.generateImageDrawableRes()
        }

        val title = if (paymentMethod.wallet != null) {
            resources.getString(R.string.google_pay_label)
        } else user?.generateFullName()

        return DataItem.PaymentMethodItem(
            id = "paymentMethodItem",
            imageDrawableRes = imageDrawableRes,
            paddingStart = spacing16,
            paddingTop = spacing16,
            paddingEnd = spacing16,
            paddingBottom = null,
            title = title,
            subtitle = subtitle,
            shouldShowArrowImage = true
        )
    }

    private fun generateSubscriptionsSectionHeaderItem(): DataItem.SubscriptionsSectionHeaderItem {
        val transactionsSize = allTransactions?.size ?: 0
        val shouldSeeAllBeVisible = transactionsSize > 5

        return DataItem.SubscriptionsSectionHeaderItem(
            id = "subscriptionsSectionHeaderItem",
            shouldSeeAllBeVisible = shouldSeeAllBeVisible
        )
    }

    private fun generateSubscriptionItems(): List<DataItem.TransactionDataItem> {
        return BillingHelper.generateSubscriptionItems(
            context = context,
            currencyISOCode = currencyISOCode,
            userLocale = userLocale,
            allTransactions = allTransactions,
            plansRepository = plansRepository,
            trueLayerProviders = trueLayerProviders,
            limit = 5
        )
    }

    private fun findCurrentPlan(): Plan? {
        val priceAPIKey = user?.subscription?.price ?: return null

        return allPlans.find {
            it.keyName == priceAPIKey
        }
    }

    private fun showPlanRenewalSuccessfulMessage() {
        val planTitle = findCurrentPlan()?.title

        _eventShowToast.value = resources.getString(
            R.string.plan_renewal_successful_message,
            planTitle
        )
    }

    private fun refreshNavigationBarElevation(recyclerView: RecyclerView) {
        val offset = recyclerView.computeVerticalScrollOffset()

        val elevationRes = if (offset > 0) R.dimen.spacing_2 else R.dimen.spacing_0
        val elevation = context.resources.getDimension(elevationRes)

        _navigationBarElevation.value = elevation
    }

    private fun refreshNavigationBarTitleVisibility(recyclerView: RecyclerView) {
        _isNavigationTitleVisible.value = shouldShowNavigationBarTitle(recyclerView)
    }

    private fun shouldShowNavigationBarTitle(recyclerView: RecyclerView): Boolean {
        val view =
            recyclerView.findChildViewUnder(0f, 0f) ?: return false
        val viewHolder = recyclerView.findContainingViewHolder(view) ?: return false
        val position = viewHolder.absoluteAdapterPosition

        // There is also the following useful property: viewHolder.itemView.top

        if (position > 0) return true

        (viewHolder as? BillingHeaderViewHolder)?.let {
            val viewHolderTop = abs(viewHolder.itemView.top)
            val titleBottom = it.computeTitleBottom()

            return viewHolderTop >= titleBottom
        }

        return false
    }
}
