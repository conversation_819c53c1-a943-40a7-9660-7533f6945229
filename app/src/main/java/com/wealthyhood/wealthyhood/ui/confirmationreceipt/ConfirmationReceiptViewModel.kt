package com.wealthyhood.wealthyhood.ui.confirmationreceipt

import android.app.Application
import android.graphics.drawable.Drawable
import android.graphics.drawable.GradientDrawable
import androidx.annotation.ColorInt
import androidx.annotation.ColorRes
import androidx.annotation.StringRes
import androidx.core.content.ContextCompat
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import com.wealthyhood.wealthyhood.R
import com.wealthyhood.wealthyhood.common.parseSuccessResponse
import com.wealthyhood.wealthyhood.domain.ConfirmationReceiptInfoRowTypeEnum
import com.wealthyhood.wealthyhood.model.ConfirmationReceiptScreenArgs
import com.wealthyhood.wealthyhood.model.ConfirmationReceiptTypeEnum
import com.wealthyhood.wealthyhood.model.DomainResult
import com.wealthyhood.wealthyhood.model.NetworkResource
import com.wealthyhood.wealthyhood.repository.AuthRepository
import com.wealthyhood.wealthyhood.repository.PreferencesRepository
import com.wealthyhood.wealthyhood.repository.Repository
import com.wealthyhood.wealthyhood.ui.confirmationreceipt.ConfirmationReceiptListAdapter.DataItem
import kotlinx.coroutines.launch

class ConfirmationReceiptViewModel(
    application: Application,
    private val screenArgs: ConfirmationReceiptScreenArgs?
) : AndroidViewModel(application) {

    private val context = application

    data class ScreenColors(
        @ColorInt val gradientStartColor: Int,
        @ColorInt val gradientMiddleColor: Int,
        @ColorInt val gradientEndColor: Int,
        @ColorInt val titleSeparatorViewColor: Int,
        @ColorInt val dividerImageTintColor: Int
    )

    private val preferencesRepository = PreferencesRepository(application) // TODO: Use DI
    private val authRepository = AuthRepository(application) // TODO: Use DI
    private val repository = Repository() // TODO: Use DI

    private val screenColors = generateScreenColors()

    private val _backgroundGradientDrawable = MutableLiveData<Drawable?>()
    val backgroundGradientDrawable: LiveData<Drawable?>
        get() = _backgroundGradientDrawable

    private val _dataItems = MutableLiveData<List<DataItem>?>()
    val dataItems: LiveData<List<DataItem>?>
        get() = _dataItems

    private val _eventShowOrHideLoadingDialog = MutableLiveData<Boolean?>()
    val eventShowOrHideLoadingDialog: LiveData<Boolean?>
        get() = _eventShowOrHideLoadingDialog

    private val _eventOpenBrowser = MutableLiveData<String?>()
    val eventOpenBrowser: LiveData<String?>
        get() = _eventOpenBrowser

    fun eventShowOrHideLoadingDialogCompleted() {
        _eventShowOrHideLoadingDialog.value = null
    }

    fun eventOpenBrowserCompleted() {
        _eventOpenBrowser.value = null
    }

    init {
        refreshBackgroundGradient()
        refreshDataItems()
    }

    fun handleOnTradeConfirmationActionClicked() {
        generateTradeConfirmation()
    }

    private fun getCredentialsCall(callback: ((succeeded: Boolean, accessToken: String?, idToken: String?) -> Unit)?) {
        authRepository.loadAuth0Credentials {
            when (it) {
                is NetworkResource.Success -> {
                    val accessToken = "Bearer ${it.data?.accessToken}"
                    val idToken = "Bearer ${it.data?.idToken}"

                    callback?.invoke(true, accessToken, idToken)
                }

                is NetworkResource.Failure -> {
                    callback?.invoke(false, null, null)
                }
            }
        }
    }

    private fun generateTradeConfirmation() {
        val receiptType = screenArgs?.receiptType ?: return

        if (receiptType == ConfirmationReceiptTypeEnum.REWARD) {
            generateRewardTradeConfirmation()

            return
        }

        generateOrderTradeConfirmation()
    }

    private fun generateOrderTradeConfirmation() {
        _eventShowOrHideLoadingDialog.value = true

        getCredentialsCall { _, accessToken, idToken ->
            if (accessToken == null || idToken == null) {
                // TODO: Handle the failure

                _eventShowOrHideLoadingDialog.value = false

                return@getCredentialsCall
            }

            val orderID = screenArgs?.orderID

            viewModelScope.launch {
                val result = repository.generateOrderTradeConfirmation(
                    accessToken = accessToken,
                    idToken = idToken,
                    orderID = orderID
                )

                _eventShowOrHideLoadingDialog.value = false

                (result as? DomainResult.Error)?.let {
                    // TODO: Handle the failure

                    return@launch
                }

                val tradeConfirmationURI = parseSuccessResponse(result)?.fileURI
                openBrowser(tradeConfirmationURI)
            }
        }
    }

    private fun generateRewardTradeConfirmation() {
        _eventShowOrHideLoadingDialog.value = true

        getCredentialsCall { _, accessToken, idToken ->
            if (accessToken == null || idToken == null) {
                // TODO: Handle the failure

                _eventShowOrHideLoadingDialog.value = false

                return@getCredentialsCall
            }

            val orderID = screenArgs?.orderID

            viewModelScope.launch {
                val result = repository.generateRewardTradeConfirmation(
                    accessToken = accessToken,
                    idToken = idToken,
                    orderID = orderID
                )

                _eventShowOrHideLoadingDialog.value = false

                (result as? DomainResult.Error)?.let {
                    // TODO: Handle the failure

                    return@launch
                }

                val tradeConfirmationURI = parseSuccessResponse(result)?.fileURI
                openBrowser(tradeConfirmationURI)
            }
        }
    }

    private fun refreshDataItems() {
        _dataItems.value = generateDataItems()
    }

    private fun generateDataItems(): List<DataItem> {
        val finalAnswer = mutableListOf<DataItem>()

        finalAnswer.add(generateHeaderItem())
        finalAnswer.add(generateDateItem())
        finalAnswer.add(generateInfoColumnsItem())
        finalAnswer.add(generateDividerItem())
        finalAnswer.addAll(generateInfoRows())
        generateTradeConfirmationActionItem()?.let { finalAnswer.add(it) }
        finalAnswer.add(generateWealthyhoodLogoItem())

        return finalAnswer
    }

    private fun generateHeaderItem(): DataItem.HeaderItem {
        val spacing16 = context.resources.getDimensionPixelSize(R.dimen.spacing_16)

        val iconDrawableRes = screenArgs?.iconDrawableRes
        val iconURI = screenArgs?.iconURI
        val titleText = screenArgs?.sideDescription
        val subtitleText = screenArgs?.title
        val titleTextColor = screenArgs?.color

        return DataItem.HeaderItem(
            id = "headerItem",
            paddingStart = spacing16,
            paddingTop = null,
            paddingEnd = spacing16,
            paddingBottom = null,
            iconDrawableRes = iconDrawableRes,
            iconURI = iconURI,
            titleText = titleText,
            titleTextColor = titleTextColor,
            subtitleText = subtitleText
        )
    }

    private fun generateDateItem(): DataItem.DateItem {
        val spacing16 = context.resources.getDimensionPixelSize(R.dimen.spacing_16)
        val spacing32 = context.resources.getDimensionPixelSize(R.dimen.spacing_32)

        val dateText = screenArgs?.date
        val timeText = screenArgs?.time

        return DataItem.DateItem(
            id = "dateItem",
            paddingStart = spacing16,
            paddingTop = spacing32,
            paddingEnd = spacing16,
            paddingBottom = null,
            dateText = dateText,
            timeText = timeText
        )
    }

    private fun generateInfoColumnsItem(): DataItem.InfoColumnsItem {
        val resources = context.resources

        val spacing8 = resources.getDimensionPixelSize(R.dimen.spacing_8)
        val spacing16 = resources.getDimensionPixelSize(R.dimen.spacing_16)

        val firstColumnLabelText = resources.getString(R.string.amount_label)
        val firstColumnValueText = screenArgs?.amount

        val secondColumnLabelText = resources.getString(R.string.shares_label)
        val secondColumnValueText = screenArgs?.shares

        val thirdColumnLabelText = resources.getString(R.string.per_share)
        val thirdColumnValueText = screenArgs?.perShare

        val shouldShowFirstInfoColumnOnly = (screenArgs?.shouldShowFirstInfoColumnOnly == true)

        return DataItem.InfoColumnsItem(
            id = "infoColumnsItem",
            paddingStart = spacing16,
            paddingTop = spacing8,
            paddingEnd = spacing16,
            paddingBottom = null,
            shouldShowFirstColumnOnly = shouldShowFirstInfoColumnOnly,
            firstColumnLabelText = firstColumnLabelText,
            firstColumnValueText = firstColumnValueText,
            secondColumnLabelText = secondColumnLabelText,
            secondColumnValueText = secondColumnValueText,
            thirdColumnLabelText = thirdColumnLabelText,
            thirdColumnValueText = thirdColumnValueText
        )
    }

    private fun generateDividerItem(): DataItem.DividerItem {
        val resources = context.resources

        val spacing16 = resources.getDimensionPixelSize(R.dimen.spacing_16)
        val spacing24 = resources.getDimensionPixelSize(R.dimen.spacing_24)

        val titleText = resources.getString(R.string.market_order)

        val titleSeparatorViewColor = screenColors.titleSeparatorViewColor
        val dividerImageTintColor = screenColors.dividerImageTintColor

        val shouldShowTitle = (screenArgs?.receiptType != ConfirmationReceiptTypeEnum.DIVIDEND)

        return DataItem.DividerItem(
            id = "dividerItem",
            paddingStart = spacing16,
            paddingTop = spacing24,
            paddingEnd = spacing16,
            paddingBottom = spacing24,
            shouldShowTitle = shouldShowTitle,
            titleText = titleText,
            titleSeparatorViewColor = titleSeparatorViewColor,
            dividerImageTintColor = dividerImageTintColor
        )
    }

    private fun generateInfoRows(): List<DataItem.InfoRowItem> {
        val types = if (screenArgs?.receiptType == ConfirmationReceiptTypeEnum.DIVIDEND) {
            listOf(
                ConfirmationReceiptInfoRowTypeEnum.Client,
                ConfirmationReceiptInfoRowTypeEnum.ISIN,
                ConfirmationReceiptInfoRowTypeEnum.ReportingFirm
            )
        } else {
            listOf(
                ConfirmationReceiptInfoRowTypeEnum.Client,
                ConfirmationReceiptInfoRowTypeEnum.OrderID,
                ConfirmationReceiptInfoRowTypeEnum.ISIN,
                ConfirmationReceiptInfoRowTypeEnum.Commission,
                ConfirmationReceiptInfoRowTypeEnum.FXRate,
                ConfirmationReceiptInfoRowTypeEnum.ReportingFirm
            )
        }

        return generateInfoRowItems(types)
    }

    private fun generateTradeConfirmationActionItem(): DataItem.ActionItem? {
        val receiptType = screenArgs?.receiptType
        if (receiptType == ConfirmationReceiptTypeEnum.DIVIDEND) return null

        val userCompanyEntity = preferencesRepository.getUserCompanyEntity()
        val isUserEU = (userCompanyEntity == "WEALTHYHOOD_EUROPE")

        if (!isUserEU) return null

        val resources = context.resources

        val spacing16 = resources.getDimensionPixelSize(R.dimen.spacing_16)
        val spacing18 = resources.getDimensionPixelSize(R.dimen.spacing_18)
        val spacing42 = resources.getDimensionPixelSize(R.dimen.spacing_42)

        val imageDrawableRes = R.drawable.ic_description
        val titleText = resources.getString(R.string.trade_confirmation_action_text)

        return DataItem.ActionItem(
            id = "tradeConfirmationActionItem",
            paddingStart = spacing16,
            paddingTop = spacing42,
            paddingEnd = spacing16,
            paddingBottom = spacing18,
            imageDrawableRes = imageDrawableRes,
            titleText = titleText
        )
    }

    private fun generateWealthyhoodLogoItem(): DataItem.WealthyhoodLogoItem {
        val resources = context.resources

        val spacing16 = resources.getDimensionPixelSize(R.dimen.spacing_16)
        val spacing24 = resources.getDimensionPixelSize(R.dimen.spacing_24)

        return DataItem.WealthyhoodLogoItem(
            id = "wealthyhoodLogoItem",
            paddingStart = spacing16,
            paddingTop = spacing24,
            paddingEnd = spacing16,
            paddingBottom = null
        )
    }

    private fun generateInfoRowItems(types: List<ConfirmationReceiptInfoRowTypeEnum>): List<DataItem.InfoRowItem> {
        return types.mapNotNull { type ->
            when (type) {
                ConfirmationReceiptInfoRowTypeEnum.Client -> {
                    val labelText = R.string.client
                    val valueText = screenArgs?.userFullName ?: return@mapNotNull null

                    generateInfoRow(
                        id = "clientInfoRow",
                        labelTextRes = labelText,
                        valueText = valueText
                    )
                }

                ConfirmationReceiptInfoRowTypeEnum.Commission -> {
                    val labelText = R.string.commission
                    val valueText = screenArgs?.commission ?: return@mapNotNull null

                    generateInfoRow(
                        id = "commissionInfoRow",
                        labelTextRes = labelText,
                        valueText = valueText
                    )
                }

                ConfirmationReceiptInfoRowTypeEnum.FXRate -> {
                    val valueText = screenArgs?.fxRate ?: return@mapNotNull null
                    val labelText = R.string.fx_rate_info_row_label

                    generateInfoRow(
                        id = "fxRateInfoRow",
                        labelTextRes = labelText,
                        valueText = valueText
                    )
                }

                ConfirmationReceiptInfoRowTypeEnum.ISIN -> {
                    val labelText = R.string.isin_label
                    val valueText = screenArgs?.isin ?: return@mapNotNull null

                    generateInfoRow(
                        id = "isinInfoRow",
                        labelTextRes = labelText,
                        valueText = valueText
                    )
                }

                ConfirmationReceiptInfoRowTypeEnum.OrderID -> {
                    val labelText = R.string.order_id_label
                    val valueText = screenArgs?.displayOrderID ?: return@mapNotNull null

                    generateInfoRow(
                        id = "orderIDInfoRow",
                        labelTextRes = labelText,
                        valueText = valueText
                    )
                }

                ConfirmationReceiptInfoRowTypeEnum.ReportingFirm -> {
                    val labelText = R.string.reporting_firm
                    val valueText = screenArgs?.firm ?: return@mapNotNull null

                    generateInfoRow(
                        id = "reportingFirmInfoRow",
                        labelTextRes = labelText,
                        valueText = valueText
                    )
                }
            }
        }
    }

    private fun generateInfoRow(
        id: String,
        @StringRes labelTextRes: Int,
        valueText: String?
    ): DataItem.InfoRowItem {
        val resources = context.resources

        val spacing12 = resources.getDimensionPixelSize(R.dimen.spacing_12)
        val spacing16 = resources.getDimensionPixelSize(R.dimen.spacing_16)

        val labelText = resources.getString(labelTextRes)

        return DataItem.InfoRowItem(
            id = id,
            paddingStart = spacing16,
            paddingTop = spacing12,
            paddingEnd = spacing16,
            paddingBottom = null,
            labelText = labelText,
            valueText = valueText,
            shouldShowSeparator = true
        )
    }

    private fun generateScreenColors(): ScreenColors {
        @ColorRes val gradientStartColorRes: Int
        @ColorRes val gradientMiddleColorRes: Int
        @ColorRes val gradientEndColorRes: Int

        @ColorRes val titleSeparatorViewColorRes: Int
        @ColorRes val dividerImageTintColorRes: Int

        if (screenArgs?.receiptType == ConfirmationReceiptTypeEnum.ORDER) {
            if (screenArgs.sideDescription == "Buy") {
                gradientStartColorRes = R.color.confirmation_receipt_green_gradient_start
                gradientMiddleColorRes = R.color.confirmation_receipt_green_gradient_middle
                gradientEndColorRes = R.color.confirmation_receipt_green_gradient_end

                titleSeparatorViewColorRes = R.color.green_50
                dividerImageTintColorRes = R.color.success_inverted
            } else {
                gradientStartColorRes = R.color.confirmation_receipt_red_gradient_start
                gradientMiddleColorRes = R.color.confirmation_receipt_red_gradient_middle
                gradientEndColorRes = R.color.confirmation_receipt_red_gradient_end

                titleSeparatorViewColorRes = R.color.red_50
                dividerImageTintColorRes = R.color.system_alerts_danger_color_5
            }
        } else {
            gradientStartColorRes = R.color.confirmation_receipt_blue_gradient_start
            gradientMiddleColorRes = R.color.confirmation_receipt_blue_gradient_middle
            gradientEndColorRes = R.color.confirmation_receipt_blue_gradient_end

            titleSeparatorViewColorRes = R.color.primary_50
            dividerImageTintColorRes = R.color.primary_5
        }

        val gradientStartColor = ContextCompat.getColor(context, gradientStartColorRes)
        val gradientMiddleColor = ContextCompat.getColor(context, gradientMiddleColorRes)
        val gradientEndColor = ContextCompat.getColor(context, gradientEndColorRes)

        val titleSeparatorViewColor = ContextCompat.getColor(context, titleSeparatorViewColorRes)
        val dividerImageTintColor = ContextCompat.getColor(context, dividerImageTintColorRes)

        return ScreenColors(
            gradientStartColor = gradientStartColor,
            gradientMiddleColor = gradientMiddleColor,
            gradientEndColor = gradientEndColor,
            titleSeparatorViewColor = titleSeparatorViewColor,
            dividerImageTintColor = dividerImageTintColor,
        )
    }

    private fun refreshBackgroundGradient() {
        val spacing32 = context.resources.getDimension(R.dimen.spacing_32)

        val drawable = GradientDrawable(
            GradientDrawable.Orientation.TOP_BOTTOM,
            intArrayOf(
                screenColors.gradientStartColor,
                screenColors.gradientMiddleColor,
                screenColors.gradientEndColor
            )
        )

        drawable.shape = GradientDrawable.RECTANGLE

        drawable.cornerRadii = floatArrayOf(
            spacing32, spacing32,
            spacing32, spacing32,
            0f, 0f,
            0f, 0f
        )

        _backgroundGradientDrawable.value = drawable
    }

    private fun openBrowser(fileURI: String?) {
        if (fileURI == null) return

        val uri = "https://docs.google.com/gview?embedded=true&url=$fileURI"
        _eventOpenBrowser.value = uri
    }
}
