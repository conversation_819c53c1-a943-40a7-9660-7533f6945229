package com.wealthyhood.wealthyhood.ui.accountdetails

import android.app.Application
import android.text.Spannable
import android.text.SpannableString
import android.text.TextPaint
import android.text.style.ClickableSpan
import android.view.View
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import com.wealthyhood.wealthyhood.R
import com.wealthyhood.wealthyhood.model.DescriptionScreenArgs
import com.wealthyhood.wealthyhood.model.DescriptionTypeEnum
import com.wealthyhood.wealthyhood.repository.PreferencesRepository
import io.sentry.Sentry
import io.sentry.SentryLevel

class CloseAccountConfirmationViewModel(application: Application) : AndroidViewModel(application) {

    private val preferencesRepository = PreferencesRepository(application) // TODO: Use DI

    private val _thirdParagraph = MutableLiveData<SpannableString?>()
    val thirdParagraph: LiveData<SpannableString?>
        get() = _thirdParagraph

    private val _eventShowDescriptionDialog = MutableLiveData<DescriptionScreenArgs?>()
    val eventShowDescriptionDialog: LiveData<DescriptionScreenArgs?>
        get() = _eventShowDescriptionDialog

    private val _eventOpenBrowser = MutableLiveData<String?>()
    val eventOpenBrowser: LiveData<String?>
        get() = _eventOpenBrowser

    fun eventShowDescriptionDialogCompleted() {
        _eventShowDescriptionDialog.value = null
    }

    fun eventOpenBrowserCompleted() {
        _eventOpenBrowser.value = null
    }

    init {
        refreshThirdParagraph()
    }

    fun handleOnInformationClicked() {
        _eventShowDescriptionDialog.value = DescriptionScreenArgs(
            descriptionType = DescriptionTypeEnum.CLOSE_ACCOUNT,
            savingsProductFeeDetails = null
        )
    }

    private fun handleOnTermsClicked() {
        val url = preferencesRepository.getLegalPages()?.platformInvestorTerms

        if (url == null) {
            Sentry.captureMessage("No platformInvestorTerms URL", SentryLevel.WARNING)

            return
        }

        _eventOpenBrowser.value = url
    }

    private fun refreshThirdParagraph() {
        _thirdParagraph.value = generateThirdParagraphSpannableString()
    }

    private fun generateThirdParagraphSpannableString(): SpannableString {
        val context = getApplication<Application>()
        val resources = context.resources

        val termsText = resources.getString(R.string.terms_and_conditions_literal)

        val finalText = resources.getString(
            R.string.close_account_description_third_paragraph,
            termsText
        )

        val termsStartIndex = finalText.indexOf(termsText)
        val termsEndIndex = termsStartIndex + termsText.length

        val spannableString = SpannableString(finalText)

        val termsClickableSpan = object : ClickableSpan() {

            override fun onClick(p0: View) {
                handleOnTermsClicked()
            }

            override fun updateDrawState(ds: TextPaint) {
                super.updateDrawState(ds)

                ds.isUnderlineText = false
            }
        }

        if (termsEndIndex >= termsStartIndex) {
            spannableString.setSpan(
                termsClickableSpan,
                termsStartIndex,
                termsEndIndex,
                Spannable.SPAN_EXCLUSIVE_EXCLUSIVE
            )
        }

        return spannableString
    }
}
