package com.wealthyhood.wealthyhood.ui.assetdetails

import android.view.ViewGroup
import androidx.annotation.ColorInt
import androidx.annotation.ColorRes
import androidx.annotation.DrawableRes
import androidx.annotation.StyleRes
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.ListAdapter
import androidx.recyclerview.widget.RecyclerView
import com.github.mikephil.charting.data.Entry
import com.wealthyhood.wealthyhood.model.AssetDetailsDateRange
import com.wealthyhood.wealthyhood.model.DescriptionTypeEnum
import com.wealthyhood.wealthyhood.model.NetworkCallState
import com.wealthyhood.wealthyhood.ui.assetdetails.aboutasset.AboutAssetHoldingViewHolder
import com.wealthyhood.wealthyhood.viewholders.AssetNewsArticleViewHolder
import com.wealthyhood.wealthyhood.viewholders.TagViewHolder
import com.wealthyhood.wealthyhood.viewholders.TagsViewHolder
import com.wealthyhood.wealthyhood.viewholders.TransactionViewHolder
import java.util.Locale

class AssetDetailsListAdapter(
    private val userLocale: Locale,
    private val headerListener: AssetDetailsHeaderViewHolder.AssetDetailsHeaderViewHolderListener?,
    private val topHoldingsListener: AssetDetailsTopHoldingsViewHolder.AssetDetailsTopHoldingsViewHolderListener?,
    private val sectionHeaderListener: AssetDetailsSectionHeaderViewHolder.AssetDetailsSectionHeaderViewHolderListener?,
    private val lineChartListener: AssetDetailsPerformanceViewHolder.AssetDetailsPerformanceViewHolderListener?,
    private val doubleInfoRowListener: AssetDetailsDoubleInfoRowViewHolder.AssetDetailsDoubleInfoRowViewHolderListener?,
    private val newsArticleListener: AssetNewsArticleViewHolder.AssetNewsArticleViewHolderListener?,
    private val kidListener: AssetDetailsKIDViewHolder.AssetDetailsKIDViewHolderListener?,
    private val footerListener: AssetDetailsAveragePriceTargetViewHolder.AssetDetailsAveragePriceTargetViewHolderListener?,
    private val tagListener: TagViewHolder.TagViewHolderListener?,
    private val transactionListener: TransactionViewHolder.TransactionViewHolderListener?
) : ListAdapter<AssetDetailsListAdapter.DataItem, RecyclerView.ViewHolder>(DataItemDiffCallback()) {

    companion object {

        const val ITEM_VIEW_TYPE_HEADER = 0
        const val ITEM_VIEW_TYPE_TOP_HOLDINGS = 1
        const val ITEM_VIEW_TYPE_SECTION_HEADER = 2
        const val ITEM_VIEW_TYPE_LINE_CHART = 3
        const val ITEM_VIEW_TYPE_DOUBLE_INFO_ROW = 4
        const val ITEM_VIEW_TYPE_PROGRESS = 5
        const val ITEM_VIEW_TYPE_MIRROR_PROGRESS = 6
        const val ITEM_VIEW_TYPE_AVERAGE_PRICE_TARGET = 8
        const val ITEM_VIEW_TYPE_DISCLAIMER = 9
        const val ITEM_VIEW_TYPE_KID = 10
        const val ITEM_VIEW_TYPE_NEWS_ARTICLE = 11
        const val ITEM_VIEW_TYPE_TAGS = 12
        const val ITEM_VIEW_TYPE_TOP_HOLDING = 13
        const val ITEM_VIEW_TYPE_TRANSACTION = 14
    }

    sealed class DataItem {

        abstract val id: String

        data class HeaderItem(
            override val id: String,
            val paddingTop: Int?,
            val paddingBottom: Int?,
            @DrawableRes val imageDrawableRes: Int?,
            val imageURI: String?,
            val title: String?,
            val subtitle: String?
        ) : DataItem()

        data class TopHoldingsItem(
            override val id: String,
            val paddingTop: Int?,
            val paddingBottom: Int?,
            val holdingsIconURLs: List<String>,
            val totalMoreText: String?
        ) : DataItem()

        data class SectionHeaderItem(
            override val id: String,
            val paddingStart: Int?,
            val paddingTop: Int?,
            val paddingEnd: Int?,
            val paddingBottom: Int?,
            @StyleRes val textAppearanceRes: Int,
            val title: String?,
            val shouldShowInfoButton: Boolean,
            val shouldShowSeeAllButton: Boolean
        ) : DataItem()

        data class LineChartItem(
            override val id: String,
            val totalText: String?,
            val currencyISOCode: String?,
            val description: String?,
            @ColorInt val descriptionColor: Int?,
            @DrawableRes val descriptionDrawableRes: Int?,
            val dateText: String?,
            val chartEntries: List<Entry>,
            val networkCallState: NetworkCallState?,
            val selectedChartEntry: Entry?,
            val selectedDateRange: AssetDetailsDateRange
        ) : DataItem()

        data class DoubleInfoRowItem(
            override val id: String,
            @DrawableRes val backgroundRes: Int?,
            val paddingTop: Int?,
            val paddingBottom: Int?,
            val firstLabelText: String?,
            @ColorInt val firstLabelTextColor: Int?,
            val firstValueText: String?,
            val firstDescriptionType: DescriptionTypeEnum?,
            val secondLabelText: String?,
            @ColorInt val secondLabelTextColor: Int?,
            val secondValueText: String?,
            val secondDescriptionType: DescriptionTypeEnum?
        ) : DataItem()

        data class ProgressItem(
            override val id: String,
            val paddingTop: Int?,
            val paddingBottom: Int?,
            @ColorInt val color: Int,
            val progress: Int,
            val text: String?,
            val isTextSelected: Boolean?,
            val footerText: String?
        ) : DataItem()

        data class MirrorProgressItem(
            override val id: String,
            val paddingTop: Int?,
            val paddingBottom: Int?,
            @DrawableRes val drawableRes: Int?,
            @ColorInt val color: Int,
            val text: String?,
            val progress: Int,
            val progressText: String?
        ) : DataItem()

        data class AveragePriceTargetItem(
            override val id: String,
            val paddingTop: Int?,
            val paddingBottom: Int?,
            val amountText: String?,
            val changeText: String?,
            @ColorInt val changeTextColor: Int?,
            val shouldShowInfoButton: Boolean
        ) : DataItem()

        data class DisclaimerItem(
            override val id: String,
            val paddingTop: Int?,
            val paddingBottom: Int?
        ) : DataItem()

        data class KIDItem(
            override val id: String,
            val paddingTop: Int?,
            val paddingBottom: Int?
        ) : DataItem()

        data class NewsArticleItem(
            override val id: String,
            val paddingStart: Int?,
            val paddingTop: Int?,
            val paddingEnd: Int?,
            val paddingBottom: Int?,
            val imageURL: String?,
            val title: String?,
            @DrawableRes val subtitleDrawableRes: Int,
            val subtitleCategoryText: String?,
            val subtitleTimeText: String?
        ) : DataItem()

        data class TagsItem(
            override val id: String,
            val paddingStart: Int?,
            val paddingTop: Int?,
            val paddingEnd: Int?,
            val paddingBottom: Int?,
            val tagItems: List<AssetDetailsTagsListAdapter.DataItem>?
        ) : DataItem()

        data class TopHoldingItem(
            override val id: String,
            val iconUrl: String?,
            val title: String?,
            val subtitle: String?
        ) : DataItem()

        data class TransactionDataItem(
            override val id: String,
            val paddingStart: Int?,
            val paddingTop: Int?,
            val paddingEnd: Int?,
            val paddingBottom: Int?,
            val shouldShowSmallImage: Boolean,
            @DrawableRes val smallImageDrawableRes: Int?,
            val smallImageURI: String?,
            val shouldShowBigImage: Boolean,
            @DrawableRes val bigImageDrawableRes: Int?,
            val bigImageURI: String?,
            val titleText: String?,
            val shouldShowBadge: Boolean,
            @ColorRes val badgeBackgroundColorRes: Int?,
            val badgeText: String?,
            val shouldShowStatusIcon: Boolean,
            @ColorRes val badgeTextColorRes: Int?,
            @DrawableRes val statusIconDrawablesRes: Int?,
            @ColorRes val statusIconTintColorRes: Int?,
            val statusText: String?,
            @ColorRes val statusTextColorRes: Int?,
            val shouldShowStatusDot: Boolean,
            val shouldShowSecondaryStatusText: Boolean,
            val secondaryStatusText: String?,
            val shouldShowPaymentViews: Boolean,
            @DrawableRes val paymentDrawableRes: Int?,
            val paymentText: String?,
            val shouldShowAmount: Boolean,
            val amountText: String?,
            val shouldShowType: Boolean,
            val typeText: String?,
            @ColorRes val typeTextColorRes: Int?
        ) : DataItem()
    }

    class DataItemDiffCallback : DiffUtil.ItemCallback<DataItem>() {

        override fun areItemsTheSame(oldItem: DataItem, newItem: DataItem): Boolean {
            return oldItem.id == newItem.id
        }

        override fun areContentsTheSame(oldItem: DataItem, newItem: DataItem): Boolean {
            return oldItem == newItem
        }
    }

    override fun getItemViewType(position: Int): Int {
        return when (getItem(position)) {
            is DataItem.HeaderItem -> ITEM_VIEW_TYPE_HEADER
            is DataItem.TopHoldingsItem -> ITEM_VIEW_TYPE_TOP_HOLDINGS
            is DataItem.SectionHeaderItem -> ITEM_VIEW_TYPE_SECTION_HEADER
            is DataItem.LineChartItem -> ITEM_VIEW_TYPE_LINE_CHART
            is DataItem.DoubleInfoRowItem -> ITEM_VIEW_TYPE_DOUBLE_INFO_ROW
            is DataItem.ProgressItem -> ITEM_VIEW_TYPE_PROGRESS
            is DataItem.MirrorProgressItem -> ITEM_VIEW_TYPE_MIRROR_PROGRESS
            is DataItem.AveragePriceTargetItem -> ITEM_VIEW_TYPE_AVERAGE_PRICE_TARGET
            is DataItem.DisclaimerItem -> ITEM_VIEW_TYPE_DISCLAIMER
            is DataItem.KIDItem -> ITEM_VIEW_TYPE_KID
            is DataItem.NewsArticleItem -> ITEM_VIEW_TYPE_NEWS_ARTICLE
            is DataItem.TagsItem -> ITEM_VIEW_TYPE_TAGS
            is DataItem.TopHoldingItem -> ITEM_VIEW_TYPE_TOP_HOLDING
            is DataItem.TransactionDataItem -> ITEM_VIEW_TYPE_TRANSACTION
        }
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): RecyclerView.ViewHolder {
        return when (viewType) {
            ITEM_VIEW_TYPE_HEADER -> AssetDetailsHeaderViewHolder.from(
                parent = parent,
                listener = headerListener
            )

            ITEM_VIEW_TYPE_TOP_HOLDINGS -> AssetDetailsTopHoldingsViewHolder.from(
                parent = parent,
                listener = topHoldingsListener
            )

            ITEM_VIEW_TYPE_SECTION_HEADER -> AssetDetailsSectionHeaderViewHolder.from(
                parent = parent,
                listener = sectionHeaderListener
            )

            ITEM_VIEW_TYPE_LINE_CHART -> AssetDetailsPerformanceViewHolder.from(
                parent = parent,
                listener = lineChartListener,
                userLocale = userLocale
            )

            ITEM_VIEW_TYPE_DOUBLE_INFO_ROW -> AssetDetailsDoubleInfoRowViewHolder.from(
                parent = parent,
                listener = doubleInfoRowListener
            )

            ITEM_VIEW_TYPE_PROGRESS -> AssetDetailsProgressViewHolder.from(
                parent = parent
            )

            ITEM_VIEW_TYPE_MIRROR_PROGRESS -> AssetDetailsMirrorProgressViewHolder.from(
                parent = parent
            )

            ITEM_VIEW_TYPE_AVERAGE_PRICE_TARGET -> AssetDetailsAveragePriceTargetViewHolder.from(
                parent = parent,
                listener = footerListener
            )

            ITEM_VIEW_TYPE_DISCLAIMER -> AssetDetailsDisclaimerViewHolder.from(
                parent = parent
            )

            ITEM_VIEW_TYPE_NEWS_ARTICLE -> AssetNewsArticleViewHolder.from(
                parent = parent,
                listener = newsArticleListener
            )

            ITEM_VIEW_TYPE_TAGS -> TagsViewHolder.from(
                parent = parent,
                tagListener = tagListener
            )

            ITEM_VIEW_TYPE_TOP_HOLDING -> AboutAssetHoldingViewHolder.from(parent)

            ITEM_VIEW_TYPE_TRANSACTION -> TransactionViewHolder.from(
                parent = parent,
                listener = transactionListener
            )

            else -> AssetDetailsKIDViewHolder.from(
                parent = parent,
                listener = kidListener
            )
        }
    }

    override fun onBindViewHolder(holder: RecyclerView.ViewHolder, position: Int) {
        when (holder) {
            is AssetDetailsHeaderViewHolder -> {
                val dataItem = getItem(position) as DataItem.HeaderItem

                holder.bind(
                    paddingTop = dataItem.paddingTop,
                    paddingBottom = dataItem.paddingBottom,
                    imageDrawableRes = dataItem.imageDrawableRes,
                    imageURI = dataItem.imageURI,
                    title = dataItem.title,
                    subtitle = dataItem.subtitle
                )
            }

            is AssetDetailsTopHoldingsViewHolder -> {
                val dataItem = getItem(position) as DataItem.TopHoldingsItem

                holder.bind(
                    paddingTop = dataItem.paddingTop,
                    paddingBottom = dataItem.paddingBottom,
                    holdingsIconURLs = dataItem.holdingsIconURLs,
                    totalMoreText = dataItem.totalMoreText
                )
            }

            is AssetDetailsSectionHeaderViewHolder -> {
                val dataItem = getItem(position) as DataItem.SectionHeaderItem

                holder.bind(
                    sectionHeaderID = dataItem.id,
                    paddingStart = dataItem.paddingStart,
                    paddingTop = dataItem.paddingTop,
                    paddingEnd = dataItem.paddingEnd,
                    paddingBottom = dataItem.paddingBottom,
                    textAppearanceRes = dataItem.textAppearanceRes,
                    title = dataItem.title,
                    shouldShowInfoButton = dataItem.shouldShowInfoButton,
                    shouldShowSeeAllButton = dataItem.shouldShowSeeAllButton
                )
            }

            is AssetDetailsPerformanceViewHolder -> {
                val lineChartItem = getItem(position) as DataItem.LineChartItem

                holder.bind(
                    totalText = lineChartItem.totalText,
                    currencyISOCode = lineChartItem.currencyISOCode,
                    description = lineChartItem.description,
                    descriptionColor = lineChartItem.descriptionColor,
                    descriptionDrawableRes = lineChartItem.descriptionDrawableRes,
                    dateText = lineChartItem.dateText,
                    chartEntries = lineChartItem.chartEntries,
                    networkCallState = lineChartItem.networkCallState,
                    selectedEntry = lineChartItem.selectedChartEntry,
                    selectedDateRange = lineChartItem.selectedDateRange
                )
            }

            is AssetDetailsDoubleInfoRowViewHolder -> {
                val dataItem = getItem(position) as DataItem.DoubleInfoRowItem

                holder.bind(
                    backgroundRes = dataItem.backgroundRes,
                    paddingTop = dataItem.paddingTop,
                    paddingBottom = dataItem.paddingBottom,
                    firstLabelText = dataItem.firstLabelText,
                    firstLabelTextColor = dataItem.firstLabelTextColor,
                    firstValueText = dataItem.firstValueText,
                    firstDescriptionType = dataItem.firstDescriptionType,
                    secondLabelText = dataItem.secondLabelText,
                    secondLabelTextColor = dataItem.secondLabelTextColor,
                    secondValueText = dataItem.secondValueText,
                    secondDescriptionType = dataItem.secondDescriptionType
                )
            }

            is AssetDetailsProgressViewHolder -> {
                val dataItem = getItem(position) as DataItem.ProgressItem

                holder.bind(
                    paddingTop = dataItem.paddingTop,
                    paddingBottom = dataItem.paddingBottom,
                    color = dataItem.color,
                    progress = dataItem.progress,
                    text = dataItem.text,
                    isTextSelected = dataItem.isTextSelected,
                    footerText = dataItem.footerText
                )
            }

            is AssetDetailsMirrorProgressViewHolder -> {
                val dataItem = getItem(position) as DataItem.MirrorProgressItem

                holder.bind(
                    paddingTop = dataItem.paddingTop,
                    paddingBottom = dataItem.paddingBottom,
                    drawableRes = dataItem.drawableRes,
                    color = dataItem.color,
                    text = dataItem.text,
                    progress = dataItem.progress,
                    progressText = dataItem.progressText
                )
            }

            is AssetDetailsAveragePriceTargetViewHolder -> {
                val dataItem = getItem(position) as DataItem.AveragePriceTargetItem

                holder.bind(
                    paddingTop = dataItem.paddingTop,
                    paddingBottom = dataItem.paddingBottom,
                    amountText = dataItem.amountText,
                    changeText = dataItem.changeText,
                    changeTextColor = dataItem.changeTextColor,
                    shouldShowInfoButton = dataItem.shouldShowInfoButton
                )
            }

            is AssetDetailsKIDViewHolder -> {
                val dataItem = getItem(position) as DataItem.KIDItem

                holder.bind(
                    paddingTop = dataItem.paddingTop,
                    paddingBottom = dataItem.paddingBottom
                )
            }

            is AssetDetailsDisclaimerViewHolder -> {
                val dataItem = getItem(position) as DataItem.DisclaimerItem

                holder.bind(
                    paddingTop = dataItem.paddingTop,
                    paddingBottom = dataItem.paddingBottom
                )
            }

            is AssetNewsArticleViewHolder -> {
                val dataItem = getItem(position) as DataItem.NewsArticleItem

                holder.bind(
                    itemID = dataItem.id,
                    paddingStart = dataItem.paddingStart,
                    paddingTop = dataItem.paddingTop,
                    paddingEnd = dataItem.paddingEnd,
                    paddingBottom = dataItem.paddingBottom,
                    imageURL = dataItem.imageURL,
                    title = dataItem.title,
                    subtitleDrawableRes = dataItem.subtitleDrawableRes,
                    subtitleCategoryText = dataItem.subtitleCategoryText,
                    subtitleTimeText = dataItem.subtitleTimeText
                )
            }

            is TagsViewHolder -> {
                val dataItem = getItem(position) as DataItem.TagsItem

                holder.bind(
                    paddingStart = dataItem.paddingStart,
                    paddingTop = dataItem.paddingTop,
                    paddingEnd = dataItem.paddingEnd,
                    paddingBottom = dataItem.paddingBottom,
                    tagItems = dataItem.tagItems
                )
            }

            is AboutAssetHoldingViewHolder -> {
                val dataItem = getItem(position) as DataItem.TopHoldingItem

                holder.bind(
                    iconUrl = dataItem.iconUrl,
                    title = dataItem.title,
                    subtitle = dataItem.subtitle
                )
            }

            is TransactionViewHolder -> {
                val dataItem = (getItem(position) as DataItem.TransactionDataItem)

                holder.bind(
                    itemID = dataItem.id,
                    paddingStart = dataItem.paddingStart,
                    paddingTop = dataItem.paddingTop,
                    paddingEnd = dataItem.paddingEnd,
                    paddingBottom = dataItem.paddingBottom,
                    shouldShowSmallImage = dataItem.shouldShowSmallImage,
                    smallImageDrawableRes = dataItem.smallImageDrawableRes,
                    smallImageURI = dataItem.smallImageURI,
                    shouldShowBigImage = dataItem.shouldShowBigImage,
                    bigImageDrawableRes = dataItem.bigImageDrawableRes,
                    bigImageURI = dataItem.bigImageURI,
                    titleText = dataItem.titleText,
                    shouldShowBadge = dataItem.shouldShowBadge,
                    badgeBackgroundColorRes = dataItem.badgeBackgroundColorRes,
                    badgeText = dataItem.badgeText,
                    badgeTextColorRes = dataItem.badgeTextColorRes,
                    shouldShowStatusIcon = dataItem.shouldShowStatusIcon,
                    statusIconDrawablesRes = dataItem.statusIconDrawablesRes,
                    statusIconTintColorRes = dataItem.statusIconTintColorRes,
                    statusText = dataItem.statusText,
                    statusTextColorRes = dataItem.statusTextColorRes,
                    shouldShowStatusDot = dataItem.shouldShowStatusDot,
                    shouldShowSecondaryStatusText = dataItem.shouldShowSecondaryStatusText,
                    secondaryStatusText = dataItem.secondaryStatusText,
                    shouldShowPaymentViews = dataItem.shouldShowPaymentViews,
                    paymentDrawableRes = dataItem.paymentDrawableRes,
                    paymentText = dataItem.paymentText,
                    shouldShowAmount = dataItem.shouldShowAmount,
                    amountText = dataItem.amountText,
                    shouldShowType = dataItem.shouldShowType,
                    typeText = dataItem.typeText,
                    typeTextColorRes = dataItem.typeTextColorRes
                )
            }
        }
    }
}
