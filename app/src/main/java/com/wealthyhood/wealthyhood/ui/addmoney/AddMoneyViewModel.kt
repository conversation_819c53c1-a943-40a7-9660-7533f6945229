package com.wealthyhood.wealthyhood.ui.addmoney

import android.app.Application
import android.text.method.DigitsKeyListener
import android.widget.ScrollView
import androidx.core.content.ContextCompat
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.map
import androidx.lifecycle.viewModelScope
import com.wealthyhood.wealthyhood.R
import com.wealthyhood.wealthyhood.common.Utils.generateErrorMessages
import com.wealthyhood.wealthyhood.common.parseSuccessApiResponse
import com.wealthyhood.wealthyhood.common.parseSuccessResponse
import com.wealthyhood.wealthyhood.domain.DropDownProperties
import com.wealthyhood.wealthyhood.domain.FindDefaultBuyPaymentMethodUseCase
import com.wealthyhood.wealthyhood.domain.GenerateBuyPaymentMethodsUseCase
import com.wealthyhood.wealthyhood.domain.GetPortfoliosCoroutinesUseCase
import com.wealthyhood.wealthyhood.domain.InvestmentModalHelper
import com.wealthyhood.wealthyhood.domain.ModalNavigationBarProperties
import com.wealthyhood.wealthyhood.domain.PaymentMethodDropDownProperties
import com.wealthyhood.wealthyhood.domain.generatePaymentMethodDropDownPropertiesFromPaymentMethod
import com.wealthyhood.wealthyhood.extensions.DayChoiceExtensions
import com.wealthyhood.wealthyhood.extensions.convertCurrencyCodeToSymbol
import com.wealthyhood.wealthyhood.extensions.findNewlyAddedBankAccount
import com.wealthyhood.wealthyhood.extensions.findUserLocale
import com.wealthyhood.wealthyhood.extensions.generateDescription
import com.wealthyhood.wealthyhood.extensions.generateFormattedCurrency
import com.wealthyhood.wealthyhood.extensions.generateInfoRowRepeatingText
import com.wealthyhood.wealthyhood.extensions.getBankAccountID
import com.wealthyhood.wealthyhood.extensions.getCashForCurrency
import com.wealthyhood.wealthyhood.extensions.toScaled
import com.wealthyhood.wealthyhood.extensions.toScaledString
import com.wealthyhood.wealthyhood.model.AddMoneyLoadingScreenArgs
import com.wealthyhood.wealthyhood.model.AddMoneyScreenArgs
import com.wealthyhood.wealthyhood.model.BankAccountsFragmentArguments
import com.wealthyhood.wealthyhood.model.DayChoice
import com.wealthyhood.wealthyhood.model.DayPickerScreenArgs
import com.wealthyhood.wealthyhood.model.DomainResult
import com.wealthyhood.wealthyhood.model.GenericErrorScreenArgs
import com.wealthyhood.wealthyhood.model.NetworkResource
import com.wealthyhood.wealthyhood.model.PaymentMethod
import com.wealthyhood.wealthyhood.model.RegularBankTransferScreenArgs
import com.wealthyhood.wealthyhood.model.ScheduleOptionsScreenArgs
import com.wealthyhood.wealthyhood.model.SetupDirectDebitScreenArgs
import com.wealthyhood.wealthyhood.model.SetupDirectDebitSuccessfulScreenArgs
import com.wealthyhood.wealthyhood.model.TopUpOptionsScreenArgs
import com.wealthyhood.wealthyhood.model.UpdateRecurringMessageScreenArgs
import com.wealthyhood.wealthyhood.model.ValidationMessage
import com.wealthyhood.wealthyhood.model.WalletOrderReviewScreenArgs
import com.wealthyhood.wealthyhood.repository.AuthRepository
import com.wealthyhood.wealthyhood.repository.PreferencesRepository
import com.wealthyhood.wealthyhood.repository.Repository
import com.wealthyhood.wealthyhood.repository.ScheduleOptionsRepository
import com.wealthyhood.wealthyhood.service.Automation
import com.wealthyhood.wealthyhood.service.BankAccount
import com.wealthyhood.wealthyhood.service.Mandate
import com.wealthyhood.wealthyhood.service.Portfolio
import com.wealthyhood.wealthyhood.ui.topupoptions.TopUpOptionsViewModel
import io.sentry.Sentry
import io.sentry.SentryLevel
import kotlinx.coroutines.async
import kotlinx.coroutines.launch
import java.util.Calendar
import java.util.Date

class AddMoneyViewModel(
    application: Application,
    private val screenArgs: AddMoneyScreenArgs?
) : AndroidViewModel(application) {

    companion object {

        private const val MAX_DIGITS_AFTER_DECIMAL_SEPARATOR = 2
        private const val MINIMUM_LIMIT_VALIDATION_MESSAGE_TYPE = 0
    }

    private val context = application

    private val preferencesRepository = PreferencesRepository(application) // TODO: Use DI
    private val authRepository = AuthRepository(application) // TODO: Use DI
    private val scheduleOptionsRepository = ScheduleOptionsRepository() // TODO: Use DI
    private val repository = Repository() // TODO: Use DI

    private val getPortfoliosCoroutinesUseCase = GetPortfoliosCoroutinesUseCase(
        preferencesRepository = preferencesRepository,
        repository = repository
    )

    private val helper = InvestmentModalHelper(preferencesRepository)

    private val defaultScheduleOption: String? = null

    private val dayChoices = repository.generateDayChoices()
    private var selectedDayChoice: DayChoice? = null

    private val currencyISOCode = preferencesRepository.getUserCurrency()
    private val userLocale = preferencesRepository.findUserLocale()
    private val userCompanyEntity = preferencesRepository.getUserCompanyEntity()

    private val scheduleOptions = scheduleOptionsRepository.getScheduleOptions(application)

    private var selectedScheduleOptionID: String? = null

    private val savingsConfig = preferencesRepository.getSavingsConfig()

    private var portfolio: Portfolio? = null
    private var linkedBankAccounts: List<BankAccount>? = null
    private var paymentMethods: List<PaymentMethod>? = null
    private var mandates: List<Mandate>? = null
    private var existingAutomation: Automation? = null

    private val _navigationBarProperties = MutableLiveData<ModalNavigationBarProperties?>()
    val navigationBarProperties: LiveData<ModalNavigationBarProperties?>
        get() = _navigationBarProperties

    private val _navigationBarElevation = MutableLiveData<Float?>()
    val navigationBarElevation: LiveData<Float?>
        get() = _navigationBarElevation

    private val _currencySymbol = MutableLiveData<String?>()
    val currencySymbol: LiveData<String?>
        get() = _currencySymbol

    val amountText: LiveData<String?>
        get() = helper.amountText

    val digitsKeyListener: LiveData<DigitsKeyListener?>
        get() = helper.digitsKeyListener

    private val _messageText = MutableLiveData<String?>()
    val messageText: LiveData<String?>
        get() = _messageText

    private val _validationMessage = MutableLiveData<ValidationMessage?>()
    val validationMessage: LiveData<ValidationMessage?>
        get() = _validationMessage

    private val _selectedPaymentMethod = MutableLiveData<PaymentMethod?>()

    val selectedPaymentMethodDropDownProperties = _selectedPaymentMethod.map {
        generateSelectedPaymentMethodDropDownProperties()
    }

    private val _isPrimaryButtonEnabled = MutableLiveData<Boolean?>()
    val isPrimaryButtonEnabled: LiveData<Boolean?>
        get() = _isPrimaryButtonEnabled

    private val _primaryButtonText = MutableLiveData<String?>()
    val primaryButtonText: LiveData<String?>
        get() = _primaryButtonText

    private val _eventShowOrHideLoadingDialog = MutableLiveData<Boolean?>()
    val eventShowOrHideLoadingDialog: LiveData<Boolean?>
        get() = _eventShowOrHideLoadingDialog

    private val _eventShowBankAccountsDialog = MutableLiveData<BankAccountsFragmentArguments?>()
    val eventShowBankAccountsDialog: LiveData<BankAccountsFragmentArguments?>
        get() = _eventShowBankAccountsDialog

    private val _eventShowAddBankAccountDialog = MutableLiveData<Boolean?>()
    val eventShowAddBankAccountDialog: LiveData<Boolean?>
        get() = _eventShowAddBankAccountDialog

    private val _eventShowOrderReviewDialog = MutableLiveData<WalletOrderReviewScreenArgs?>()
    val eventShowOrderReviewDialog: LiveData<WalletOrderReviewScreenArgs?>
        get() = _eventShowOrderReviewDialog

    private val _eventNavigateToLoadingScreen = MutableLiveData<AddMoneyLoadingScreenArgs?>()
    val eventNavigateToLoadingScreen: LiveData<AddMoneyLoadingScreenArgs?>
        get() = _eventNavigateToLoadingScreen

    private val _eventShowDayPickerDialog = MutableLiveData<DayPickerScreenArgs?>()
    val eventShowDayPickerDialog: LiveData<DayPickerScreenArgs?>
        get() = _eventShowDayPickerDialog

    private val _eventShowScheduleOptionsDialog = MutableLiveData<ScheduleOptionsScreenArgs?>()
    val eventShowScheduleOptionsDialog: LiveData<ScheduleOptionsScreenArgs?>
        get() = _eventShowScheduleOptionsDialog

    private val _eventNavigateToSetupDirectDebitScreen =
        MutableLiveData<SetupDirectDebitScreenArgs?>()
    val eventNavigateToSetupDirectDebitScreen: LiveData<SetupDirectDebitScreenArgs?>
        get() = _eventNavigateToSetupDirectDebitScreen

    private val _eventNavigateToSuccessfulScreen =
        MutableLiveData<SetupDirectDebitSuccessfulScreenArgs?>()
    val eventNavigateToSuccessfulScreen: LiveData<SetupDirectDebitSuccessfulScreenArgs?>
        get() = _eventNavigateToSuccessfulScreen

    private val _eventShowUpdateRecurringMessageDialog =
        MutableLiveData<UpdateRecurringMessageScreenArgs?>()
    val eventShowUpdateRecurringMessageDialog: LiveData<UpdateRecurringMessageScreenArgs?>
        get() = _eventShowUpdateRecurringMessageDialog

    private val _eventShowTopUpOptionsDialog = MutableLiveData<TopUpOptionsScreenArgs?>()
    val eventShowTopUpOptionsDialog: LiveData<TopUpOptionsScreenArgs?>
        get() = _eventShowTopUpOptionsDialog

    private val _eventNavigateToRegularBankTransfer =
        MutableLiveData<RegularBankTransferScreenArgs?>()
    val eventNavigateToRegularBankTransfer: LiveData<RegularBankTransferScreenArgs?>
        get() = _eventNavigateToRegularBankTransfer

    private val _eventShowIBANError = MutableLiveData<String?>()
    val eventShowIBANError: LiveData<String?>
        get() = _eventShowIBANError

    private val _eventShowGenericError = MutableLiveData<GenericErrorScreenArgs?>()
    val eventShowGenericError: LiveData<GenericErrorScreenArgs?>
        get() = _eventShowGenericError

    fun eventShowOrHideLoadingDialogCompleted() {
        _eventShowOrHideLoadingDialog.value = null
    }

    fun eventShowBankAccountsDialogCompleted() {
        _eventShowBankAccountsDialog.value = null
    }

    fun eventShowAddBankAccountDialogCompleted() {
        _eventShowAddBankAccountDialog.value = null
    }

    fun eventShowOrderReviewDialogCompleted() {
        _eventShowOrderReviewDialog.value = null
    }

    fun eventNavigateToLoadingScreenCompleted() {
        _eventNavigateToLoadingScreen.value = null
    }

    fun eventShowDayPickerDialogCompleted() {
        _eventShowDayPickerDialog.value = null
    }

    fun eventShowScheduleOptionsDialogCompleted() {
        _eventShowScheduleOptionsDialog.value = null
    }

    fun eventNavigateToSetupDirectDebitScreenCompleted() {
        _eventNavigateToSetupDirectDebitScreen.value = null
    }

    fun eventNavigateToSuccessfulScreenCompleted() {
        _eventNavigateToSuccessfulScreen.value = null
    }

    fun eventShowUpdateRecurringMessageDialogCompleted() {
        _eventShowUpdateRecurringMessageDialog.value = null
    }

    fun eventShowTopUpOptionsDialogCompleted() {
        _eventShowTopUpOptionsDialog.value = null
    }

    fun eventNavigateToRegularBankTransferCompleted() {
        _eventNavigateToRegularBankTransfer.value = null
    }

    fun eventShowIBANErrorCompleted() {
        _eventShowIBANError.value = null
    }

    fun eventShowGenericErrorCompleted() {
        _eventShowGenericError.value = null
    }

    init {
        helper.refreshKeyboardProperties(true)

        refreshNavigationBarProperties()

        refreshDefaultScheduleOption()

        refreshCurrencySymbol()
        refreshMessageText()
        refreshValidationMessage()
        refreshPaymentMethods(currencyISOCode)

        val paymentMethod = findPaymentMethodToSelect(newlyAddedBankAccount = null)
        selectPaymentMethod(paymentMethod?.id)

        refreshPrimaryButton()

        reloadData(didAddNewBankAccount = false)
    }

    fun getDecimalSeparator(): String {
        return helper.getDecimalSeparator()
    }

    fun handleOnScrollViewScrolled(view: ScrollView?) {
        refreshNavigationBarElevation(view)
    }

    fun handleOnGenericErrorRetryButtonClicked() {
        reloadData(didAddNewBankAccount = false)
    }

    fun handleOnTopUpOptionClicked(topUpOptionID: String?) {
        if (topUpOptionID != TopUpOptionsViewModel.REGULAR_BANK_TRANSFER_OPTION_ID) return

        val userIBAN = preferencesRepository.getUserIBAN()

        _eventNavigateToRegularBankTransfer.value = RegularBankTransferScreenArgs(
            userIBAN = userIBAN,
            shouldShowPrimaryButton = false
        )
    }

    fun handleOnAmountTextChanged(text: String?) {
        helper.refreshAmountText(
            input = text,
            maxValidAmount = null,
            maxDigitsAfterDecimalSeparator = MAX_DIGITS_AFTER_DECIMAL_SEPARATOR
        )

        refreshPrimaryButton()
        refreshValidationMessage()
    }

    fun handleOnSelectedPaymentMethodClicked() {
        if (!shouldAllowSelectedPaymentMethodInteraction()) {
            return
        }

        val shouldShowAddBankAccountButton = shouldShowAddBankAccountButton()
        val shouldCheckForIBANError = shouldCheckForIBANError()

        _eventShowBankAccountsDialog.value = BankAccountsFragmentArguments(
            selectedPaymentMethodID = _selectedPaymentMethod.value?.id,
            paymentMethods = paymentMethods,
            shouldShowAddBankAccountButton = shouldShowAddBankAccountButton,
            shouldCheckForIBANError = shouldCheckForIBANError
        )
    }

    fun handleOnNewPaymentMethodSelected(paymentMethodID: String?) {
        selectPaymentMethod(paymentMethodID)
    }

    fun handleOnAddBankAccountButtonClicked() {
        _eventShowAddBankAccountDialog.value = true
    }

    fun handleOnNewBankAccountLinked() {
        reloadData(didAddNewBankAccount = true)
    }

    fun handleOnConfirmDepositButtonClicked() {
        if (willExecuteSingleBuyOrder()) {
            executeAddMoney()

            return
        }

        scheduleRecurringAddMoney()
    }

    private fun scheduleRecurringAddMoney() {
        val selectedPaymentMethod = _selectedPaymentMethod.value

        findMandateForBankAccount(selectedPaymentMethod?.bankAccount?.id)?.let {
            createAutomation(it.id)

            return
        }

        setupDirectDebit()
    }

    private fun findMandateForBankAccount(bankAccountID: String?): Mandate? {
        if (bankAccountID == null) return null

        return mandates?.find {
            it.getBankAccountID() == bankAccountID
        }
    }

    private fun setupDirectDebit() {
        val bankAccount = _selectedPaymentMethod.value?.bankAccount

        val subtitle = context.resources.getString(
            R.string.setup_direct_debit_header_savings_automation_subtitle
        )

        val amount = helper.generateAmount()

        val repeatingText = context.resources.getString(
            R.string.account_details_repeating_investment_value,
            amount.generateFormattedCurrency(
                currencyISOCode = currencyISOCode,
                maximumFractionDigits = 2,
                locale = userLocale
            )
        )

        _eventNavigateToSetupDirectDebitScreen.value = SetupDirectDebitScreenArgs(
            savingsProductID = screenArgs?.savingsProductID,
            subtitle = subtitle,
            portfolioBuyAmount = amount,
            subscriptionID = null,
            subscriptionPriceAPIKey = null,
            bankAccountID = bankAccount?.id,
            bankSortCode = bankAccount?.sortCode,
            bankAccountNumber = bankAccount?.number,
            repeatingText = repeatingText,
            shouldShowPlanActivationCompletedWithOptionsScreen = false,
            shouldShowPaymentMethodUpdatedScreen = false,
            allocationMethod = null,
            dayOfMonth = selectedDayChoice?.value
        )
    }

    private fun createAutomation(mandateID: String?) {
        _eventShowOrHideLoadingDialog.value = true

        getCredentialsCall { _, accessToken, idToken ->
            if (accessToken == null || idToken == null) {
                // TODO: Handle the failure

                _eventShowOrHideLoadingDialog.value = false

                return@getCredentialsCall
            }

            val formattedAmount = helper.generateAmount().toScaledString(
                digits = MAX_DIGITS_AFTER_DECIMAL_SEPARATOR
            )

            viewModelScope.launch {
                val result = repository.createAutomation(
                    accessToken = accessToken,
                    idToken = idToken,
                    category = "SavingsTopUpAutomation",
                    mandateID = mandateID,
                    savingsProductID = screenArgs?.savingsProductID,
                    formattedAmount = formattedAmount,
                    allocationMethod = null,
                    postponeActivation = null,
                    dayOfMonth = selectedDayChoice?.value
                )

                _eventShowOrHideLoadingDialog.value = false

                if (result is DomainResult.Error) {
                    // FIXME: Handle the failure

                    return@launch
                }

                preferencesRepository.putLastAutomationChangeMadeAt(Date().time)

                if (!shouldShowUpdateRecurringMessage()) {
                    // If the user created a new repeating investment, we need
                    // to refresh the dashboard.

                    preferencesRepository.putLastTransactionCreatedAt(Date().time)
                }

                navigateToSuccessfulScreen()
            }
        }
    }

    private fun shouldShowUpdateRecurringMessage(): Boolean {
        if (existingAutomation == null) return false

        return !willExecuteSingleBuyOrder()
    }

    private fun navigateToSuccessfulScreen() {
        val title = context.resources.getString(R.string.setup_direct_debit_successful_bank_title)

        _eventNavigateToSuccessfulScreen.value = SetupDirectDebitSuccessfulScreenArgs(
            title = title,
            subtitle = null,
            didCreateForRepeatingMMF = null,
            repeatingText = null,
            shouldShowPlanActivationCompletedWithOptionsScreen = false,
            shouldShowPaymentMethodUpdatedScreen = false,
            planActivationCompletedScreenArgsJSONString = null
        )
    }

    fun handleOnChipButtonClicked() {
        showScheduleOptions()
    }

    fun handleOnDayChipButtonClicked() {
        val screenArgs = DayPickerScreenArgs(
            dayChoices = dayChoices,
            selectedID = selectedDayChoice?.id
        )

        _eventShowDayPickerDialog.value = screenArgs
    }

    fun handleOnDayChoiceClicked(dayChoiceID: String?) {
        selectedDayChoice = dayChoices.find { it.id == dayChoiceID }

        refreshNavigationBarProperties()
    }

    fun handleOnNewScheduleOptionSelected(optionID: String?) {
        selectScheduleOption(optionID)

        refreshPaymentMethods(currencyISOCode)

        val paymentMethod = findPaymentMethodToSelect(newlyAddedBankAccount = null)
        selectPaymentMethod(paymentMethod?.id)

        refreshValidationMessage()
        refreshPrimaryButton()
    }

    fun handleOnUpdateRecurringButtonClicked() {
        previewAddMoney()
    }

    fun handleOnPrimaryButtonClicked() {
        if (shouldShowProceedButton()) {
            _eventShowAddBankAccountDialog.value = true

            return
        }

        if (shouldShowTopUpButton()) {
            val screenArgs = TopUpOptionsScreenArgs(
                headerTitle = context.getString(R.string.how_do_you_want_to_top_up_label)
            )

            _eventShowTopUpOptionsDialog.value = screenArgs

            return
        }

        if (shouldShowIBANError()) {
            val error = context.resources.getString(R.string.eu_iban_error_message_text)
            _eventShowIBANError.value = error

            return
        }

        refreshValidationMessage()

        _validationMessage.value?.let {
            if (it.extra == MINIMUM_LIMIT_VALIDATION_MESSAGE_TYPE) {
                return
            }
        }

        if (shouldShowUpdateRecurringMessage()) {
            val title = context.resources.getString(
                R.string.savings_product_buy_update_recurring_message_title
            )

            val message = context.resources.getString(
                R.string.savings_product_buy_update_recurring_message_text
            )

            val closeButtonTitle = context.resources.getString(R.string.cancel)
            val doneButtonTitle = context.resources.getString(R.string.button_update)

            val screenArgs = UpdateRecurringMessageScreenArgs(
                title = title,
                message = message,
                cancelButtonTitle = closeButtonTitle,
                doneButtonTitle = doneButtonTitle,
                extra = null
            )

            _eventShowUpdateRecurringMessageDialog.value = screenArgs

            return
        }

        previewAddMoney()
    }

    private fun shouldShowIBANError(): Boolean {
        val shouldCheckForIBANError = shouldCheckForIBANError()

        if (!shouldCheckForIBANError) return false
        val paymentMethod = _selectedPaymentMethod.value ?: return false
        if (paymentMethod.type != PaymentMethod.TYPE_BANK_ACCOUNT) return false
        val bankAccount = paymentMethod.bankAccount ?: return false

        return (bankAccount.isAvailableForDirectDebit != true)
    }

    private fun shouldCheckForIBANError(): Boolean {
        return !willExecuteSingleBuyOrder()
    }

    private fun previewAddMoney() {
        val total = helper.generateAmount().generateFormattedCurrency(
            currencyISOCode = currencyISOCode,
            locale = userLocale
        )

        val arrivingText = generateInfoRowArrivingText()
        val repeatingText = generateInfoRowRepeatingText()

        // Στο didSelectBankAccount περνάμε false για να δείξουμε το απλό disclaimer text,
        // όχι αυτό με το TrueLayer.

        val title: String
        val primaryButtonText: String

        if (willExecuteSingleBuyOrder()) {
            title = context.resources.getString(R.string.add_money_label)
            primaryButtonText = context.resources.getString(R.string.confirm_label)
        } else {
            title = context.resources.getString(R.string.setup_a_monthly_deposit_label)
            primaryButtonText = context.resources.getString(R.string.confirm_monthly_deposit_label)
        }

        _eventShowOrderReviewDialog.value = WalletOrderReviewScreenArgs(
            title = title,
            total = total,
            arrivingText = arrivingText,
            repeatingText = repeatingText,
            primaryButtonText = primaryButtonText,
            didSelectBankAccount = false
        )
    }

    private fun reloadData(didAddNewBankAccount: Boolean) {
        _eventShowOrHideLoadingDialog.value = true

        getCredentialsCall { _, accessToken, idToken ->
            if (accessToken == null || idToken == null) {
                // TODO: Handle the failure

                _eventShowOrHideLoadingDialog.value = false

                showGenericError()

                return@getCredentialsCall
            }

            viewModelScope.launch {
                val userID = preferencesRepository.getUserID()

                val portfoliosDeferred = async {
                    getPortfoliosCoroutinesUseCase(
                        accessToken = accessToken,
                        idToken = idToken,
                        parameters = null,
                        shouldGetFromCache = false
                    )
                }

                val bankAccountsDeferred = async {
                    repository.getLinkedBankAccounts(
                        accessToken = accessToken,
                        idToken = idToken,
                        userID = userID
                    )
                }

                val getMandatesDeferred = async {
                    repository.getMandates(
                        accessToken = accessToken,
                        idToken = idToken,
                        category = "Top-Up"
                    )
                }

                val getAutomationsDeferred = async {
                    repository.getAutomations(
                        accessToken = accessToken,
                        idToken = idToken,
                        category = "SavingsTopUpAutomation"
                    )
                }

                val bankAccountsResult = bankAccountsDeferred.await()
                val portfolioResult = portfoliosDeferred.await()
                val mandatesResult = getMandatesDeferred.await()
                val activeOrPendingAutomationsResult = getAutomationsDeferred.await()

                val networkResources = listOf(
                    portfolioResult
                )

                val domainResults = listOfNotNull(
                    bankAccountsResult,
                    mandatesResult,
                    activeOrPendingAutomationsResult
                )

                val errorMessages = generateErrorMessages(
                    networkResources = networkResources,
                    domainResults = domainResults
                )

                if (errorMessages.isNotEmpty()) {
                    _eventShowOrHideLoadingDialog.value = false

                    val sentryErrorMessage = errorMessages.joinToString("\n")
                    Sentry.captureMessage(sentryErrorMessage, SentryLevel.ERROR)

                    showGenericError()

                    return@launch
                }

                val bankAccounts = parseSuccessResponse(bankAccountsResult)?.data

                val newlyAddedBankAccount = if (didAddNewBankAccount) {
                    bankAccounts?.findNewlyAddedBankAccount(
                        previousBankAccounts = linkedBankAccounts
                    )
                } else null

                linkedBankAccounts = bankAccounts

                val portfolios = parseSuccessResponse(portfolioResult)
                portfolio = portfolios?.find { it.isReal == true }

                mandates = parseSuccessApiResponse(mandatesResult)

                val automations = parseSuccessApiResponse(activeOrPendingAutomationsResult)
                existingAutomation = findExistingAutomation(automations)

                initializeDayChoice()
                refreshNavigationBarProperties()

                fillRepeatingInvestmentAmountNeeded()

                refreshPaymentMethods(currencyISOCode)
                showScheduleOptionsIfNeeded()

                val paymentMethod = findPaymentMethodToSelect(newlyAddedBankAccount)
                selectPaymentMethod(paymentMethod?.id)

                _eventShowOrHideLoadingDialog.value = false
            }
        }
    }

    private fun executeAddMoney() {
        val selectedPaymentMethod = _selectedPaymentMethod.value

        val amount = helper.generateAmount()
        val bankAccountID = selectedPaymentMethod?.bankAccount?.id
        val bankLogoURI = selectedPaymentMethod?.bankAccount?.provider?.logoURI

        _eventNavigateToLoadingScreen.value = AddMoneyLoadingScreenArgs(
            amount = amount.toFloat(),
            savingsProductID = screenArgs?.savingsProductID,
            bankAccountID = bankAccountID,
            bankLogoURI = bankLogoURI
        )
    }

    private fun getCredentialsCall(callback: ((succeeded: Boolean, accessToken: String?, idToken: String?) -> Unit)?) {
        authRepository.loadAuth0Credentials {
            when (it) {
                is NetworkResource.Success -> {
                    val accessToken = "Bearer ${it.data?.accessToken}"
                    val idToken = "Bearer ${it.data?.idToken}"

                    callback?.invoke(true, accessToken, idToken)
                }

                is NetworkResource.Failure -> {
                    val sentryMessage = "loadAuth0Credentials Failure: ${it.message}"
                    Sentry.captureMessage(sentryMessage, SentryLevel.ERROR)

                    callback?.invoke(false, null, null)
                }
            }
        }
    }

    private fun refreshNavigationBarProperties() {
        val titleText = context.resources.getString(R.string.add_money_label)

        _navigationBarProperties.value = ModalNavigationBarProperties(
            titleText = titleText,
            subtitleText = null,
            firstDropDownProperties = generateScheduleDropDownProperties(),
            secondDropDownProperties = generateDayDropDownProperties(),
            assetTagProperties = null
        )
    }

    private fun generateScheduleDropDownProperties(): DropDownProperties {
        val selectedScheduleOption = scheduleOptions.find { it.id == selectedScheduleOptionID }

        return DropDownProperties(
            isVisible = true,
            iconDrawableRes = R.drawable.ic_schedule,
            text = selectedScheduleOption?.title
        )
    }

    private fun generateDayDropDownProperties(): DropDownProperties? {
        if (!shouldShowDayChipButton()) return null

        val text = selectedDayChoice?.text

        return DropDownProperties(
            isVisible = true,
            iconDrawableRes = null,
            text = text
        )
    }

    private fun refreshPrimaryButton() {
        _isPrimaryButtonEnabled.value = shouldEnablePrimaryButton()

        refreshPrimaryButtonText()
    }

    private fun shouldEnablePrimaryButton(): Boolean {
        val amount = helper.generateAmount()
        if (amount <= 0.0) return false

        if (shouldShowProceedButton()) return true
        if (shouldShowTopUpButton()) return true

        generateMinimumLimitErrorValidationMessage()?.let { return false }
        generateMaximumLimitErrorValidationMessage()?.let { return false }

        return true
    }

    private fun refreshPrimaryButtonText() {
        _primaryButtonText.value = if (shouldShowProceedButton()) {
            context.resources.getString(R.string.proceed)
        } else if (shouldShowTopUpButton()) {
            context.resources.getString(R.string.top_up)
        } else {
            context.resources.getString(R.string.review)
        }
    }

    private fun shouldShowProceedButtonUK(): Boolean {
        val willExecuteSingleBuyOrder = willExecuteSingleBuyOrder()

        if (!willExecuteSingleBuyOrder) {
            return linkedBankAccounts.isNullOrEmpty()
        }

        val cash = portfolio?.getCashForCurrency(currencyISOCode) ?: 0.0

        return (linkedBankAccounts.isNullOrEmpty() && cash <= 0)
    }

    private fun shouldShowProceedButtonEU(): Boolean {
        val willExecuteSingleBuyOrder = willExecuteSingleBuyOrder()

        if (willExecuteSingleBuyOrder) return false

        return linkedBankAccounts.isNullOrEmpty()
    }

    private fun shouldShowProceedButton(): Boolean {
        val isUserUK = (userCompanyEntity == "WEALTHYHOOD_UK")
        if (isUserUK) return shouldShowProceedButtonUK()

        return shouldShowProceedButtonEU()
    }

    private fun refreshPaymentMethods(currencyISOCode: String?) {
        paymentMethods = generatePaymentMethods(
            bankAccounts = linkedBankAccounts,
            currencyISOCode = currencyISOCode
        )
    }

    private fun generatePaymentMethods(
        bankAccounts: List<BankAccount>?,
        currencyISOCode: String?
    ): List<PaymentMethod>? {
        if (shouldShowProceedButton()) return null
        val isUserUK = (userCompanyEntity == "WEALTHYHOOD_UK")

        return GenerateBuyPaymentMethodsUseCase().generateAddMoneyPaymentMethods(
            isUserUK = isUserUK,
            willExecuteSingleBuyOrder = willExecuteSingleBuyOrder(),
            portfolio = portfolio,
            bankAccounts = bankAccounts,
            currencyISOCode = currencyISOCode,
            userLocale = userLocale
        )
    }

    private fun selectPaymentMethod(paymentMethodID: String?) {
        val paymentMethod = paymentMethods?.find { it.id == paymentMethodID }
        _selectedPaymentMethod.value = paymentMethod

        refreshValidationMessage()
        refreshPrimaryButton()
    }

    private fun findPaymentMethodToSelect(newlyAddedBankAccount: BankAccount?): PaymentMethod? {
        // Εάν πρόσθεσε ένα νέο bank account, επιλέγουμε αυτό ως payment method.

        val newlyAddedPaymentMethod = paymentMethods?.find {
            it.type == PaymentMethod.TYPE_BANK_ACCOUNT && it.bankAccount?.id == newlyAddedBankAccount?.id
        }

        if (newlyAddedPaymentMethod != null) return newlyAddedPaymentMethod

        val shouldCheckForIBANError = shouldCheckForIBANError()

        return FindDefaultBuyPaymentMethodUseCase().invoke(
            willExecuteSingleBuyOrder = willExecuteSingleBuyOrder(),
            portfolio = portfolio,
            paymentMethods = paymentMethods,
            currencyISOCode = currencyISOCode,
            shouldCheckForIBANError = shouldCheckForIBANError
        )
    }

    private fun refreshCurrencySymbol() {
        _currencySymbol.value = currencyISOCode?.convertCurrencyCodeToSymbol()
    }

    private fun refreshMessageText() {
        val message = context.resources.getString(
            R.string.earn_interest_description,
            screenArgs?.netInterestRate
        )

        _messageText.value = message
    }

    private fun refreshValidationMessage() {
        if (shouldShowProceedButton()) return

        _validationMessage.value = generateValidationMessage()
    }

    private fun generateValidationMessage(): ValidationMessage? {
        if (shouldShowProceedButton()) return null

        val amount = helper.generateAmount()
        if (amount == 0.0) return null

        generateMinimumLimitErrorValidationMessage()?.let {
            return it
        }

        generateMaximumLimitErrorValidationMessage()?.let {
            return it
        }

        return null
    }

    private fun generateMinimumLimitErrorValidationMessage(): ValidationMessage? {
        val monthlyOptionID = ScheduleOptionsRepository.ScheduleOptionEnum.MonthlyScheduleOption.id

        val minAmount = if (selectedScheduleOptionID == monthlyOptionID) {
            50.0
        } else {
            savingsConfig?.minimumSavingsTopUpAmount
        } ?: return null

        val amount = helper.generateAmount()

        if (amount >= minAmount) return null

        val resources = context.resources

        val formattedMinAmount = minAmount.generateFormattedCurrency(
            currencyISOCode = currencyISOCode,
            maximumFractionDigits = 0,
            locale = userLocale
        )

        val message = resources.getString(
            R.string.deposit_minimum_limit_error_message,
            formattedMinAmount
        )

        return ValidationMessage(
            message = message,
            color = ContextCompat.getColor(context, R.color.system_alerts_danger_color),
            extra = MINIMUM_LIMIT_VALIDATION_MESSAGE_TYPE
        )
    }

    private fun generateMaximumLimitErrorValidationMessage(): ValidationMessage? {
        val amount = helper.generateAmount()
        val cash = portfolio?.getCashForCurrency(currencyISOCode) ?: 0.0
        val hasSelectedCash = (_selectedPaymentMethod.value?.id == PaymentMethod.TYPE_CASH_ACCOUNT)

        if (shouldShowTopUpButton()) {
            return ValidationMessage(
                message = context.resources.getString(
                    R.string.portfolio_buy_maximum_limit_eu_error_message,
                    cash.generateFormattedCurrency(
                        currencyISOCode = currencyISOCode,
                        maximumFractionDigits = 2,
                        locale = userLocale
                    )
                ),
                color = ContextCompat.getColor(context, R.color.system_alerts_danger_color),
                extra = null
            )
        }

        if (hasSelectedCash && amount > cash) {
            return ValidationMessage(
                message = context.resources.getString(
                    R.string.portfolio_buy_maximum_limit_error_message,
                    cash.generateFormattedCurrency(
                        currencyISOCode = currencyISOCode,
                        maximumFractionDigits = 2,
                        locale = userLocale
                    )
                ),
                color = ContextCompat.getColor(context, R.color.system_alerts_danger_color),
                extra = null
            )
        }

        return null
    }

    private fun shouldShowTopUpButton(): Boolean {
        val isUserUK = (userCompanyEntity == "WEALTHYHOOD_UK")
        if (isUserUK) return false

        val willExecuteSingleBuyOrder = willExecuteSingleBuyOrder()
        if (!willExecuteSingleBuyOrder) return false

        val hasSelectedCash = (_selectedPaymentMethod.value?.id == PaymentMethod.TYPE_CASH_ACCOUNT)
        if (!hasSelectedCash) return false

        val minimumValidationMessage = generateMinimumLimitErrorValidationMessage()
        if (minimumValidationMessage != null) return false

        val amount = helper.generateAmount()
        val cash = portfolio?.getCashForCurrency(currencyISOCode) ?: 0.0

        return (amount > cash)
    }

    private fun refreshNavigationBarElevation(view: ScrollView?) {
        if (view == null) return

        val offset = view.scrollY

        val elevationRes = if (offset > 0) R.dimen.spacing_2 else R.dimen.spacing_0
        val elevation = context.resources.getDimension(elevationRes)

        _navigationBarElevation.value = elevation
    }

    private fun findExistingAutomation(automations: List<Automation>?): Automation? {
        return automations?.find {
            (it.status == "Active" || it.status == "Pending") && it.savingsProductID == screenArgs?.savingsProductID
        }
    }

    private fun shouldShowDayChipButton(): Boolean {
        val monthlyOptionID = ScheduleOptionsRepository.ScheduleOptionEnum.MonthlyScheduleOption.id
        if (selectedScheduleOptionID != monthlyOptionID) return false

        return true
    }

    private fun showScheduleOptions() {
        val headerTitle = if (existingAutomation != null) {
            context.resources.getString(R.string.schedule_options_header_title)
        } else {
            context.resources.getString(R.string.schedule_options_first_time_header_title)
        }

        val recurringScheduleOptionBankLogoURI =
            existingAutomation?.getMandateAsObject()?.getBankAccountID()?.let { bankAccountID ->
                linkedBankAccounts?.find { it.id == bankAccountID }?.provider?.iconURI
            }

        val customOneTimeOptionText = context.resources.getString(R.string.one_off_label)

        _eventShowScheduleOptionsDialog.value = ScheduleOptionsScreenArgs(
            selectedScheduleOptionID = selectedScheduleOptionID,
            headerTitle = headerTitle,
            customOneTimeOptionText = customOneTimeOptionText,
            isRecurringScheduleOptionSubtitleComplex = (existingAutomation != null),
            recurringScheduleOptionSubtitle = generateRecurringScheduleOptionSubtitle(),
            recurringScheduleOptionBankLogoURI = recurringScheduleOptionBankLogoURI
        )
    }

    private fun shouldShowScheduleOptions(): Boolean {
        return !(preferencesRepository.getDidSeeAddMoneySchedulingOptions())
    }

    private fun selectScheduleOption(optionID: String?) {
        selectedScheduleOptionID = optionID

        initializeDayChoice()
        refreshNavigationBarProperties()

        fillRepeatingInvestmentAmountNeeded()
    }

    private fun findDefaultScheduleOption(): String {
        defaultScheduleOption?.let {
            return it
        }


        if (shouldShowScheduleOptions()) {
            return ScheduleOptionsRepository.ScheduleOptionEnum.MonthlyScheduleOption.id
        }

        return ScheduleOptionsRepository.ScheduleOptionEnum.OneTimeScheduleOption.id
    }

    private fun initializeDayChoice() {
        selectedDayChoice = DayChoiceExtensions.generateInitialChoice(
            dayOfMonth = existingAutomation?.dayOfMonth,
            dayChoices = dayChoices
        )
    }

    private fun fillRepeatingInvestmentAmountNeeded() {
        val monthlyOptionID = ScheduleOptionsRepository.ScheduleOptionEnum.MonthlyScheduleOption.id
        if (selectedScheduleOptionID != monthlyOptionID) return

        val amount =
            existingAutomation?.consideration?.amount?.div(100)?.toDouble()?.toScaled(digits = 2)

        val formattedAmount = helper.formatAmount(amount)

        helper.refreshAmountText(
            input = formattedAmount,
            maxValidAmount = null,
            maxDigitsAfterDecimalSeparator = MAX_DIGITS_AFTER_DECIMAL_SEPARATOR
        )
    }

    private fun getCurrentDayOfMonth(): Int {
        val calendar = Calendar.getInstance()
        calendar.time = Date()

        return calendar.get(Calendar.DAY_OF_MONTH)
    }

    private fun generateRecurringScheduleOptionSubtitle(): String {
        val existingAutomation = existingAutomation

        if (existingAutomation == null) {
            val dayOfMonth = getCurrentDayOfMonth()

            return dayOfMonth.generateInfoRowRepeatingText(context.resources)
        }

        return existingAutomation.generateDescription(
            context = context,
            userLocale = userLocale
        )
    }

    private fun showScheduleOptionsIfNeeded() {
        if (!shouldShowScheduleOptions()) {
            return
        }

        preferencesRepository.putDidSeeAddMoneySchedulingOptions(true)

        showScheduleOptions()
    }

    private fun refreshDefaultScheduleOption() {
        selectScheduleOption(findDefaultScheduleOption())
    }

    private fun generateInfoRowRepeatingText(): String? {
        if (willExecuteSingleBuyOrder()) return null

        val dayOfMonth = selectedDayChoice?.value ?: getCurrentDayOfMonth()

        return dayOfMonth.generateInfoRowRepeatingText(context.resources)
    }

    private fun willExecuteSingleBuyOrder(): Boolean {
        val selectedScheduleOptionID = selectedScheduleOptionID ?: return false

        val selectedScheduleOption = scheduleOptions.find {
            it.id == selectedScheduleOptionID
        } ?: return false

        return selectedScheduleOption.isOneTime
    }

    private fun generateInfoRowArrivingText(): String? {
        if (!willExecuteSingleBuyOrder()) return null

        return context.resources.getString(R.string.instantly)
    }

    private fun shouldShowAddBankAccountButton(): Boolean {
        val isUserUK = (userCompanyEntity == "WEALTHYHOOD_UK")
        val isSingleBuy = willExecuteSingleBuyOrder()

        return (isUserUK || !isSingleBuy)
    }

    private fun shouldAllowSelectedPaymentMethodInteraction(): Boolean {
        val paymentMethodsSize = paymentMethods?.size ?: 0
        val shouldShowAddBankAccountButton = shouldShowAddBankAccountButton()

        return (paymentMethodsSize > 1 || shouldShowAddBankAccountButton)
    }

    private fun generateSelectedPaymentMethodDropDownProperties(): PaymentMethodDropDownProperties {
        val shouldShowArrow = shouldAllowSelectedPaymentMethodInteraction()

        return generatePaymentMethodDropDownPropertiesFromPaymentMethod(
            context = context,
            paymentMethod = _selectedPaymentMethod.value,
            shouldShowArrow = shouldShowArrow
        )
    }

    private fun showGenericError() {
        val buttonText = context.resources.getString(
            R.string.generic_error_button_try_again
        )

        val screenArgs = GenericErrorScreenArgs(
            isCancelable = false,
            buttonText = buttonText,
            extras = null
        )

        _eventShowGenericError.value = screenArgs
    }
}
