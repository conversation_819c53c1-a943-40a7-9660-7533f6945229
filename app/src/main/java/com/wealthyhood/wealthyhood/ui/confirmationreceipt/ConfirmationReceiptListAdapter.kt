package com.wealthyhood.wealthyhood.ui.confirmationreceipt

import android.view.ViewGroup
import androidx.annotation.ColorInt
import androidx.annotation.DrawableRes
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.ListAdapter
import androidx.recyclerview.widget.RecyclerView
import com.wealthyhood.wealthyhood.ui.confirmationreceipt.viewholder.ConfirmationReceiptActionViewHolder
import com.wealthyhood.wealthyhood.ui.confirmationreceipt.viewholder.ConfirmationReceiptActionViewHolder.ConfirmationReceiptActionViewHolderListener
import com.wealthyhood.wealthyhood.ui.confirmationreceipt.viewholder.ConfirmationReceiptDateViewHolder
import com.wealthyhood.wealthyhood.ui.confirmationreceipt.viewholder.ConfirmationReceiptDividerViewHolder
import com.wealthyhood.wealthyhood.ui.confirmationreceipt.viewholder.ConfirmationReceiptHeaderViewHolder
import com.wealthyhood.wealthyhood.ui.confirmationreceipt.viewholder.ConfirmationReceiptInfoColumnsViewHolder
import com.wealthyhood.wealthyhood.ui.confirmationreceipt.viewholder.ConfirmationReceiptWealthyhoodLogoViewHolder
import com.wealthyhood.wealthyhood.viewholders.InfoRowViewHolder

class ConfirmationReceiptListAdapter(
    private val actionListener: ConfirmationReceiptActionViewHolderListener?
) : ListAdapter<ConfirmationReceiptListAdapter.DataItem, RecyclerView.ViewHolder>(
    DataItemDiffCallback()
) {

    companion object {

        const val ITEM_VIEW_TYPE_HEADER = 0
        const val ITEM_VIEW_TYPE_DATE = 1
        const val ITEM_VIEW_TYPE_INFO_COLUMNS = 2
        const val ITEM_VIEW_TYPE_DIVIDER = 3
        const val ITEM_VIEW_TYPE_INFO_ROW = 4
        const val ITEM_VIEW_TYPE_ACTION = 5
        const val ITEM_VIEW_TYPE_WEALTHYHOOD_LOGO = 6
    }

    sealed class DataItem {

        abstract val id: String

        data class HeaderItem(
            override val id: String,
            val paddingStart: Int?,
            val paddingTop: Int?,
            val paddingEnd: Int?,
            val paddingBottom: Int?,
            @DrawableRes val iconDrawableRes: Int?,
            val iconURI: String?,
            val titleText: String?,
            @ColorInt val titleTextColor: Int?,
            val subtitleText: String?
        ) : DataItem()

        data class DateItem(
            override val id: String,
            val paddingStart: Int?,
            val paddingTop: Int?,
            val paddingEnd: Int?,
            val paddingBottom: Int?,
            val dateText: String?,
            val timeText: String?
        ) : DataItem()

        data class InfoColumnsItem(
            override val id: String,
            val paddingStart: Int?,
            val paddingTop: Int?,
            val paddingEnd: Int?,
            val paddingBottom: Int?,
            val shouldShowFirstColumnOnly: Boolean,
            val firstColumnLabelText: String?,
            val firstColumnValueText: String?,
            val secondColumnLabelText: String?,
            val secondColumnValueText: String?,
            val thirdColumnLabelText: String?,
            val thirdColumnValueText: String?
        ) : DataItem()

        data class DividerItem(
            override val id: String,
            val paddingStart: Int?,
            val paddingTop: Int?,
            val paddingEnd: Int?,
            val paddingBottom: Int?,
            val shouldShowTitle: Boolean,
            val titleText: String?,
            @ColorInt val titleSeparatorViewColor: Int?,
            @ColorInt val dividerImageTintColor: Int?
        ) : DataItem()

        data class InfoRowItem(
            override val id: String,
            val paddingStart: Int?,
            val paddingTop: Int?,
            val paddingEnd: Int?,
            val paddingBottom: Int?,
            val labelText: String?,
            val valueText: String?,
            val shouldShowSeparator: Boolean
        ) : DataItem()

        data class ActionItem(
            override val id: String,
            val paddingStart: Int?,
            val paddingTop: Int?,
            val paddingEnd: Int?,
            val paddingBottom: Int?,
            @DrawableRes val imageDrawableRes: Int,
            val titleText: String?
        ) : DataItem()

        data class WealthyhoodLogoItem(
            override val id: String,
            val paddingStart: Int?,
            val paddingTop: Int?,
            val paddingEnd: Int?,
            val paddingBottom: Int?
        ) : DataItem()
    }

    class DataItemDiffCallback : DiffUtil.ItemCallback<DataItem>() {

        override fun areItemsTheSame(oldItem: DataItem, newItem: DataItem): Boolean {
            return oldItem.id == newItem.id
        }

        override fun areContentsTheSame(oldItem: DataItem, newItem: DataItem): Boolean {
            return oldItem == newItem
        }
    }

    override fun getItemViewType(position: Int): Int {
        return when (getItem(position)) {
            is DataItem.HeaderItem -> ITEM_VIEW_TYPE_HEADER
            is DataItem.DateItem -> ITEM_VIEW_TYPE_DATE
            is DataItem.InfoColumnsItem -> ITEM_VIEW_TYPE_INFO_COLUMNS
            is DataItem.DividerItem -> ITEM_VIEW_TYPE_DIVIDER
            is DataItem.InfoRowItem -> ITEM_VIEW_TYPE_INFO_ROW
            is DataItem.ActionItem -> ITEM_VIEW_TYPE_ACTION
            is DataItem.WealthyhoodLogoItem -> ITEM_VIEW_TYPE_WEALTHYHOOD_LOGO
        }
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): RecyclerView.ViewHolder {
        return when (viewType) {
            ITEM_VIEW_TYPE_HEADER -> ConfirmationReceiptHeaderViewHolder.from(parent)
            ITEM_VIEW_TYPE_DATE -> ConfirmationReceiptDateViewHolder.from(parent)
            ITEM_VIEW_TYPE_INFO_COLUMNS -> ConfirmationReceiptInfoColumnsViewHolder.from(parent)
            ITEM_VIEW_TYPE_DIVIDER -> ConfirmationReceiptDividerViewHolder.from(parent)
            ITEM_VIEW_TYPE_INFO_ROW -> InfoRowViewHolder.from(parent)

            ITEM_VIEW_TYPE_ACTION -> ConfirmationReceiptActionViewHolder.from(
                parent,
                actionListener
            )

            else -> ConfirmationReceiptWealthyhoodLogoViewHolder.from(parent)
        }
    }

    override fun onBindViewHolder(holder: RecyclerView.ViewHolder, position: Int) {
        when (holder) {
            is ConfirmationReceiptHeaderViewHolder -> {
                val dataItem = getItem(position) as DataItem.HeaderItem

                holder.bind(
                    paddingStart = dataItem.paddingStart,
                    paddingTop = dataItem.paddingTop,
                    paddingEnd = dataItem.paddingEnd,
                    paddingBottom = dataItem.paddingBottom,
                    iconDrawableRes = dataItem.iconDrawableRes,
                    iconURI = dataItem.iconURI,
                    titleText = dataItem.titleText,
                    titleTextColor = dataItem.titleTextColor,
                    subtitleText = dataItem.subtitleText
                )
            }

            is ConfirmationReceiptDateViewHolder -> {
                val dataItem = getItem(position) as DataItem.DateItem

                holder.bind(
                    paddingStart = dataItem.paddingStart,
                    paddingTop = dataItem.paddingTop,
                    paddingEnd = dataItem.paddingEnd,
                    paddingBottom = dataItem.paddingBottom,
                    dateText = dataItem.dateText,
                    timeText = dataItem.timeText
                )
            }

            is ConfirmationReceiptInfoColumnsViewHolder -> {
                val dataItem = getItem(position) as DataItem.InfoColumnsItem

                holder.bind(
                    paddingStart = dataItem.paddingStart,
                    paddingTop = dataItem.paddingTop,
                    paddingEnd = dataItem.paddingEnd,
                    paddingBottom = dataItem.paddingBottom,
                    shouldShowFirstColumnOnly = dataItem.shouldShowFirstColumnOnly,
                    firstColumnLabelText = dataItem.firstColumnLabelText,
                    firstColumnValueText = dataItem.firstColumnValueText,
                    secondColumnLabelText = dataItem.secondColumnLabelText,
                    secondColumnValueText = dataItem.secondColumnValueText,
                    thirdColumnLabelText = dataItem.thirdColumnLabelText,
                    thirdColumnValueText = dataItem.thirdColumnValueText
                )
            }

            is ConfirmationReceiptDividerViewHolder -> {
                val dataItem = getItem(position) as DataItem.DividerItem

                holder.bind(
                    paddingStart = dataItem.paddingStart,
                    paddingTop = dataItem.paddingTop,
                    paddingEnd = dataItem.paddingEnd,
                    paddingBottom = dataItem.paddingBottom,
                    shouldShowTitle = dataItem.shouldShowTitle,
                    titleText = dataItem.titleText,
                    titleSeparatorViewColor = dataItem.titleSeparatorViewColor,
                    dividerImageTintColor = dataItem.dividerImageTintColor
                )
            }

            is InfoRowViewHolder -> {
                val dataItem = getItem(position) as DataItem.InfoRowItem

                holder.bind(
                    paddingStart = dataItem.paddingStart,
                    paddingTop = dataItem.paddingTop,
                    paddingEnd = dataItem.paddingEnd,
                    paddingBottom = dataItem.paddingBottom,
                    labelText = dataItem.labelText,
                    valueText = dataItem.valueText,
                    shouldShowSeparator = dataItem.shouldShowSeparator
                )
            }

            is ConfirmationReceiptActionViewHolder -> {
                val dataItem = getItem(position) as DataItem.ActionItem

                holder.bind(
                    paddingStart = dataItem.paddingStart,
                    paddingTop = dataItem.paddingTop,
                    paddingEnd = dataItem.paddingEnd,
                    paddingBottom = dataItem.paddingBottom,
                    imageDrawableRes = dataItem.imageDrawableRes,
                    titleText = dataItem.titleText
                )
            }

            is ConfirmationReceiptWealthyhoodLogoViewHolder -> {
                val dataItem = getItem(position) as DataItem.WealthyhoodLogoItem

                holder.bind(
                    paddingStart = dataItem.paddingStart,
                    paddingTop = dataItem.paddingTop,
                    paddingEnd = dataItem.paddingEnd,
                    paddingBottom = dataItem.paddingBottom
                )
            }
        }
    }
}
