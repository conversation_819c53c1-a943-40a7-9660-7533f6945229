package com.wealthyhood.wealthyhood.ui.confirmationreceipt.viewholder

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.core.view.updatePadding
import androidx.recyclerview.widget.RecyclerView
import com.wealthyhood.wealthyhood.R
import com.wealthyhood.wealthyhood.databinding.ListItemConfirmationReceiptInfoColumnsBinding
import com.wealthyhood.wealthyhood.extensions.convertToVisibility

class ConfirmationReceiptInfoColumnsViewHolder private constructor(
    private val binding: ListItemConfirmationReceiptInfoColumnsBinding
) : RecyclerView.ViewHolder(binding.root) {

    companion object {

        fun from(parent: ViewGroup): ConfirmationReceiptInfoColumnsViewHolder {
            val layoutInflater = LayoutInflater.from(parent.context)

            val binding =
                ListItemConfirmationReceiptInfoColumnsBinding.inflate(layoutInflater, parent, false)

            return ConfirmationReceiptInfoColumnsViewHolder(binding)
        }
    }

    fun bind(
        paddingStart: Int?,
        paddingTop: Int?,
        paddingEnd: Int?,
        paddingBottom: Int?,
        shouldShowFirstColumnOnly: Boolean,
        firstColumnLabelText: String?,
        firstColumnValueText: String?,
        secondColumnLabelText: String?,
        secondColumnValueText: String?,
        thirdColumnLabelText: String?,
        thirdColumnValueText: String?
    ) {
        refreshPadding(
            paddingStart = paddingStart,
            paddingTop = paddingTop,
            paddingEnd = paddingEnd,
            paddingBottom = paddingBottom
        )

        refreshFirstColumn(
            labelText = firstColumnLabelText,
            valueText = firstColumnValueText
        )

        refreshSecondColumn(
            isVisible = !shouldShowFirstColumnOnly,
            labelText = secondColumnLabelText,
            valueText = secondColumnValueText
        )

        refreshThirdColumn(
            isVisible = !shouldShowFirstColumnOnly,
            labelText = thirdColumnLabelText,
            valueText = thirdColumnValueText
        )
    }

    private fun refreshPadding(
        paddingStart: Int?,
        paddingTop: Int?,
        paddingEnd: Int?,
        paddingBottom: Int?
    ) {
        val spacing0 = itemView.context.resources.getDimensionPixelSize(R.dimen.spacing_0)

        val finalPaddingStart = paddingStart ?: spacing0
        val finalPaddingTop = paddingTop ?: spacing0
        val finalPaddingEnd = paddingEnd ?: spacing0
        val finalPaddingBottom = paddingBottom ?: spacing0

        binding.rootConstraintLayout.updatePadding(
            finalPaddingStart,
            finalPaddingTop,
            finalPaddingEnd,
            finalPaddingBottom
        )
    }

    private fun refreshFirstColumn(labelText: String?, valueText: String?) {
        binding.firstColumnLabelTextView.text = labelText
        binding.firstColumnValueTextView.text = valueText
    }

    private fun refreshSecondColumn(isVisible: Boolean, labelText: String?, valueText: String?) {
        val visibility = isVisible.convertToVisibility()

        binding.firstSeparatorView.visibility = visibility
        binding.secondColumnLinearLayout.visibility = visibility

        binding.secondColumnLabelTextView.text = labelText
        binding.secondColumnValueTextView.text = valueText
    }

    private fun refreshThirdColumn(isVisible: Boolean, labelText: String?, valueText: String?) {
        val visibility = isVisible.convertToVisibility()

        binding.secondSeparatorView.visibility = visibility
        binding.thirdColumnLinearLayout.visibility = visibility

        binding.thirdColumnLabelTextView.text = labelText
        binding.thirdColumnValueTextView.text = valueText
    }
}
