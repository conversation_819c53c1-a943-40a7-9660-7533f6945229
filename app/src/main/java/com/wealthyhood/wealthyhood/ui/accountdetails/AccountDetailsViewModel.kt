package com.wealthyhood.wealthyhood.ui.accountdetails

import android.app.Application
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.recyclerview.widget.RecyclerView
import com.wealthyhood.wealthyhood.R
import com.wealthyhood.wealthyhood.domain.GetUserUseCase
import com.wealthyhood.wealthyhood.extensions.convertDateOfBirthToUTCLong
import com.wealthyhood.wealthyhood.extensions.generateFullAddress
import com.wealthyhood.wealthyhood.extensions.generateLocalisedCountryName
import com.wealthyhood.wealthyhood.extensions.generateStringUsingPattern
import com.wealthyhood.wealthyhood.model.AccountDetailsScreenArgs
import com.wealthyhood.wealthyhood.model.NetworkResource
import com.wealthyhood.wealthyhood.repository.AuthRepository
import com.wealthyhood.wealthyhood.repository.MyAccountRepository
import com.wealthyhood.wealthyhood.repository.PreferencesRepository
import com.wealthyhood.wealthyhood.service.GetUserQueryParams
import com.wealthyhood.wealthyhood.service.User
import com.wealthyhood.wealthyhood.ui.accountdetails.AccountDetailsListAdapter.DataItem
import java.util.Date
import kotlin.math.abs

class AccountDetailsViewModel(
    application: Application,
    private val screenArgs: AccountDetailsScreenArgs?
) : AndroidViewModel(application) {

    private val context = application

    private val preferencesRepository = PreferencesRepository(application) // TODO: Use DI
    private val authRepository = AuthRepository(application) // TODO: Use DI
    private val myAccountRepository = MyAccountRepository() // TODO: Use DI

    private var accessToken: String? = null
    private var idToken: String? = null

    private var user: User? = null

    private val getUserUseCase = GetUserUseCase()

    private val _navigationBarElevation = MutableLiveData<Float?>()
    val navigationBarElevation: LiveData<Float?>
        get() = _navigationBarElevation

    private val _isNavigationTitleVisible = MutableLiveData<Boolean?>()
    val isNavigationTitleVisible: LiveData<Boolean?>
        get() = _isNavigationTitleVisible

    private val _dataItems = MutableLiveData<List<DataItem>?>()
    val dataItems: LiveData<List<DataItem>?>
        get() = _dataItems

    private val _eventShowOrHideLoadingDialog = MutableLiveData<Boolean?>()
    val eventShowOrHideLoadingDialog: LiveData<Boolean?>
        get() = _eventShowOrHideLoadingDialog

    private val _eventNavigateToIntercomScreen = MutableLiveData<Boolean?>()
    val eventNavigateToIntercomScreen: LiveData<Boolean?>
        get() = _eventNavigateToIntercomScreen

    private val _eventNavigateToCloseAccountScreen = MutableLiveData<Boolean?>()
    val eventNavigateToCloseAccountScreen: LiveData<Boolean?>
        get() = _eventNavigateToCloseAccountScreen

    fun eventShowOrHideLoadingDialogCompleted() {
        _eventShowOrHideLoadingDialog.value = null
    }

    fun eventNavigateToIntercomScreenCompleted() {
        _eventNavigateToIntercomScreen.value = null
    }

    fun eventNavigateToCloseAccountScreenCompleted() {
        _eventNavigateToCloseAccountScreen.value = null
    }

    init {
        refreshDataItems()

        reloadData()
    }

    fun handleOnRecyclerViewScrolled(recyclerView: RecyclerView) {
        refreshNavigationBarElevation(recyclerView)
        refreshNavigationBarTitleVisibility(recyclerView)
    }

    fun onRequestEditButtonClicked() {
        _eventNavigateToIntercomScreen.value = true
    }

    fun onCloseAccountButtonClicked() {
        _eventNavigateToCloseAccountScreen.value = true
    }

    private fun refreshDataItems() {
        _dataItems.value = generateDataItems()
    }

    private fun generateDataItems(): List<DataItem> {
        val finalAnswer = mutableListOf<DataItem>()

        finalAnswer.add(generateHeaderItem())
        finalAnswer.addAll(generateInfoRowItems())
        finalAnswer.add(generateButtonsItem())

        return finalAnswer
    }

    private fun reloadData() {
        screenArgs?.user?.let {
            user = it

            refreshDataItems()

            return
        }

        _eventShowOrHideLoadingDialog.value = true

        authRepository.loadAuth0Credentials {
            when (it) {
                is NetworkResource.Success -> {
                    accessToken = it.data?.accessToken
                    idToken = it.data?.idToken

                    getUser()
                }

                is NetworkResource.Failure -> {
                    // TODO: Handle the failure

                    _eventShowOrHideLoadingDialog.value = false
                }
            }
        }
    }

    private fun getUser() {
        val accessToken = "Bearer $accessToken"
        val idToken = "Bearer $idToken"

        val queryParams = GetUserQueryParams(populate = "addresses,participant")

        getUserUseCase(
            context = getApplication(),
            myAccountRepository = myAccountRepository,
            preferencesRepository = preferencesRepository,
            accessToken = accessToken,
            idToken = idToken,
            queryParams
        ) {
            _eventShowOrHideLoadingDialog.value = false

            when (it) {
                is NetworkResource.Success -> {
                    user = it.data

                    refreshDataItems()
                }

                is NetworkResource.Failure -> {
                    // TODO: Handle the failure
                }
            }
        }
    }

    private fun generateHeaderItem(): DataItem.HeaderItem {
        return DataItem.HeaderItem
    }

    private fun generateInfoRowItems(): List<DataItem.InfoRowItem> {
        val finalAnswer = mutableListOf<DataItem.InfoRowItem>()

        val resources = getApplication<Application>().resources

        user?.firstName?.let {
            finalAnswer.add(
                DataItem.InfoRowItem(
                    id = "nameInfoRowItem",
                    labelText = resources.getString(R.string.account_details_name_label),
                    valueText = it,
                    isSeparatorVisible = true
                )
            )
        }

        user?.lastName?.let {
            finalAnswer.add(
                DataItem.InfoRowItem(
                    id = "surnameInfoRowItem",
                    labelText = resources.getString(R.string.account_details_surname_label),
                    valueText = it,
                    isSeparatorVisible = true
                )
            )
        }

        user?.convertDateOfBirthToUTCLong()?.let {
            val date = Date(it)

            val formattedDate = date.generateStringUsingPattern(
                pattern = "dd MMM yyyy"
            ) ?: return@let

            finalAnswer.add(
                DataItem.InfoRowItem(
                    id = "dateOfBirthInfoRowItem",
                    labelText = resources.getString(R.string.account_details_date_of_birth_label),
                    valueText = formattedDate,
                    isSeparatorVisible = true
                )
            )
        }

        val tinValueText = user?.taxResidency?.value ?: "-"

        finalAnswer.add(
            DataItem.InfoRowItem(
                id = "niNumberInfoRowItem",
                labelText = resources.getString(R.string.ni_number_label),
                valueText = tinValueText,
                isSeparatorVisible = true
            )
        )

        user?.email?.let {
            finalAnswer.add(
                DataItem.InfoRowItem(
                    id = "mailInfoRowItem",
                    labelText = resources.getString(R.string.account_details_email_label),
                    valueText = it,
                    isSeparatorVisible = true
                )
            )
        }

        user?.addresses?.firstOrNull()?.generateFullAddress()?.let {
            finalAnswer.add(
                DataItem.InfoRowItem(
                    id = "addressInfoRowItem",
                    labelText = resources.getString(R.string.account_details_address_label),
                    valueText = it,
                    isSeparatorVisible = true
                )
            )
        }

        user?.addresses?.firstOrNull()?.postCode?.let {
            finalAnswer.add(
                DataItem.InfoRowItem(
                    id = "postCodeInfoRowItem",
                    labelText = resources.getString(R.string.account_details_postcode_label),
                    valueText = it,
                    isSeparatorVisible = true
                )
            )
        }

        user?.addresses?.firstOrNull()?.generateLocalisedCountryName()?.let {
            finalAnswer.add(
                DataItem.InfoRowItem(
                    id = "countryInfoRowItem",
                    labelText = resources.getString(R.string.account_details_country_label),
                    valueText = it,
                    isSeparatorVisible = true
                )
            )
        }

        finalAnswer.lastOrNull()?.isSeparatorVisible = false

        return finalAnswer
    }

    private fun generateButtonsItem(): DataItem.ButtonsItem {
        return DataItem.ButtonsItem
    }

    private fun refreshNavigationBarElevation(recyclerView: RecyclerView) {
        val offset = recyclerView.computeVerticalScrollOffset()

        val elevationRes = if (offset > 0) R.dimen.spacing_2 else R.dimen.spacing_0
        val elevation = context.resources.getDimension(elevationRes)

        _navigationBarElevation.value = elevation
    }

    private fun refreshNavigationBarTitleVisibility(recyclerView: RecyclerView) {
        _isNavigationTitleVisible.value = shouldShowNavigationBarTitle(recyclerView)
    }

    private fun shouldShowNavigationBarTitle(recyclerView: RecyclerView): Boolean {
        val view =
            recyclerView.findChildViewUnder(0f, 0f) ?: return false
        val viewHolder = recyclerView.findContainingViewHolder(view) ?: return false
        val position = viewHolder.absoluteAdapterPosition

        // There is also the following useful property: viewHolder.itemView.top

        if (position > 0) return true

        (viewHolder as? AccountDetailsHeaderViewHolder)?.let {
            val viewHolderTop = abs(viewHolder.itemView.top)
            val titleBottom = it.computeTitleBottom()

            return viewHolderTop >= titleBottom
        }

        return false
    }
}
