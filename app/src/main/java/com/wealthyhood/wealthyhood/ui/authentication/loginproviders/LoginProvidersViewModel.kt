package com.wealthyhood.wealthyhood.ui.authentication.loginproviders

import android.app.Application
import android.text.SpannableString
import android.text.TextPaint
import android.text.style.ClickableSpan
import android.view.View
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import com.auth0.android.Auth0
import com.auth0.android.result.Credentials
import com.wealthyhood.wealthyhood.R
import com.wealthyhood.wealthyhood.common.LogsHelper
import com.wealthyhood.wealthyhood.common.ScreenChooser
import com.wealthyhood.wealthyhood.domain.EventEnum
import com.wealthyhood.wealthyhood.domain.EventsHelper
import com.wealthyhood.wealthyhood.extensions.cleanSpecialFormattingCharacters
import com.wealthyhood.wealthyhood.extensions.findLinkSubstrings
import com.wealthyhood.wealthyhood.extensions.formatSubstringsAsBold
import com.wealthyhood.wealthyhood.extensions.formatSubstringsAsLinks
import com.wealthyhood.wealthyhood.extensions.saveLoginInfo
import com.wealthyhood.wealthyhood.model.NetworkResource
import com.wealthyhood.wealthyhood.model.ScreenEnum
import com.wealthyhood.wealthyhood.repository.AuthRepository
import com.wealthyhood.wealthyhood.repository.PreferencesRepository
import java.util.Locale

class LoginProvidersViewModel(application: Application) : AndroidViewModel(application) {

    val authRepository = AuthRepository(application) // TODO: Use DI
    val preferencesRepository = PreferencesRepository(application) // TODO: Use DI

    private val screenChooser = ScreenChooser(
        context = getApplication(),
        authRepository = authRepository,
        preferencesRepository = preferencesRepository,
        shouldSkipCheckPIN = true,
        shouldTriggerInvestedDashboardDataPreFetch = true,
        viewModelScope = viewModelScope
    ) {
        chooseScreen(it)
    }

    private val _disclaimerText = MutableLiveData<SpannableString?>()
    val disclaimerText: LiveData<SpannableString?>
        get() = _disclaimerText

    private val _eventShowOrHideLoadingDialog = MutableLiveData<Boolean?>()
    val eventShowOrHideLoadingDialog: LiveData<Boolean?>
        get() = _eventShowOrHideLoadingDialog

    private val _eventLoginWithGoogle = MutableLiveData<Auth0?>()
    val eventLoginWithGoogle: LiveData<Auth0?>
        get() = _eventLoginWithGoogle

    private val _eventNavigateToScreen = MutableLiveData<ScreenEnum?>()
    val eventNavigateToScreen: LiveData<ScreenEnum?>
        get() = _eventNavigateToScreen

    private val _eventNavigateToLoginScreen = MutableLiveData<Boolean?>()
    val eventNavigateToLoginScreen: LiveData<Boolean?>
        get() = _eventNavigateToLoginScreen

    private val _eventOpenBrowser = MutableLiveData<String?>()
    val eventOpenBrowser: LiveData<String?>
        get() = _eventOpenBrowser

    fun eventShowOrHideLoadingDialogCompleted() {
        _eventShowOrHideLoadingDialog.value = null
    }

    fun eventLoginWithGoogleCompleted() {
        _eventLoginWithGoogle.value = null
    }

    fun eventNavigateToScreenCompleted() {
        _eventNavigateToScreen.value = null
    }

    fun eventNavigateToLoginScreenCompleted() {
        _eventNavigateToLoginScreen.value = null
    }

    fun eventOpenBrowserCompleted() {
        _eventOpenBrowser.value = null
    }

    init {
        logMessage("LoginProvidersViewModel.init")

        refreshDisclaimerText()
    }

    fun handleOnLoginWithGoogleButtonClicked() {
        logMessage("LoginProvidersViewModel.handleOnLoginWithGoogleButtonClicked")

        sendEvents()

        _eventLoginWithGoogle.value = authRepository.auth0Account
    }

    fun handleOnLoginWithMailButtonClicked() {
        logMessage("LoginProvidersViewModel.handleOnLoginWithMailButtonClicked")

        _eventNavigateToLoginScreen.value = true
    }

    fun handleOnLoginWithGoogleSuccess(credentials: Credentials) {
        logMessage("LoginProvidersViewModel.handleOnLoginWithGoogleSuccess")

        callAuth(credentials)
    }

    fun handleOnLoginWithGoogleFailure() {
        // TODO: Handle the failure
        logMessage("LoginProvidersViewModel.handleOnLoginWithGoogleFailure")
    }

    private fun refreshDisclaimerText() {
        _disclaimerText.value = generateDisclaimerSpannableString()
    }

    private fun callAuth(credentials: Credentials) {
        logMessage("LoginProvidersViewModel.callAuth")

        _eventShowOrHideLoadingDialog.value = true

        val accessTokenHeader = "Bearer ${credentials.accessToken}"
        val idTokenHeader = "Bearer ${credentials.idToken}"

        authRepository.auth(accessToken = accessTokenHeader, idToken = idTokenHeader) {
            when (it) {
                is NetworkResource.Success -> {
                    logMessage("LoginProvidersViewModel.callAuth Success")

                    val user = it.data?.users?.firstOrNull()

                    if (user?.isUserNew == true) {
                        EventsHelper.sendEvents(
                            context = getApplication(),
                            event = EventEnum.SIGNED_UP,
                            emptyMap()
                        )
                    }

                    val loginInfoSaved = user?.saveLoginInfo(
                        credentials = credentials,
                        preferencesRepository = preferencesRepository,
                        authRepository = authRepository
                    )

                    if (loginInfoSaved != true) {
                        // TODO: Handle the failure
                        logMessage("LoginProvidersViewModel.callAuth !loginInfoSaved")

                        _eventShowOrHideLoadingDialog.value = false

                        return@auth
                    }

                    navigateToScreen()
                }

                is NetworkResource.Failure -> {
                    // TODO: Handle the failure
                    logMessage("LoginProvidersViewModel.callAuth Failure")

                    _eventShowOrHideLoadingDialog.value = false
                }
            }
        }
    }

    private fun sendEvents() {
        EventsHelper.sendEvents(
            context = getApplication(),
            event = EventEnum.SUBMIT_EMAIL,
            eventValues = emptyMap()
        )
    }

    private fun navigateToScreen() {
        screenChooser.chooseScreen(shouldInvalidateData = true)
    }

    private fun chooseScreen(screen: ScreenEnum) {
        logMessage("LoginProvidersViewModel.navigateToScreen - ${screen.javaClass.name}")

        _eventShowOrHideLoadingDialog.value = false

        _eventNavigateToScreen.value = screen
    }

    private fun logMessage(message: String) {
        LogsHelper.getInstance().logMessage(message)
    }

    private fun generateDisclaimerSpannableString(): SpannableString {
        val context = getApplication<Application>()
        val resources = context.resources

        val string = resources.getString(R.string.login_disclaimer)

        val linkSubstrings = string.findLinkSubstrings()

        val finalString = string.cleanSpecialFormattingCharacters()

        val spannableString = SpannableString(finalString)

        spannableString.formatSubstringsAsBold(
            context = context,
            substrings = linkSubstrings
        )

        val privacyPolicyClickableSpan = object : ClickableSpan() {

            override fun onClick(p0: View) {
                handleOnPrivacyPolicyClicked()
            }

            override fun updateDrawState(ds: TextPaint) {
                super.updateDrawState(ds)

                ds.isUnderlineText = false
            }
        }

        val clickableSpans = listOf(
            privacyPolicyClickableSpan
        )

        spannableString.formatSubstringsAsLinks(
            substrings = linkSubstrings,
            clickableSpans = clickableSpans
        )

        return spannableString
    }

    private fun handleOnPrivacyPolicyClicked() {
        val defaultLocale = Locale.getDefault()
        val hasUserUKLocale = (defaultLocale == Locale.UK)

        val url = if (hasUserUKLocale) {
            "https://wealthyhood.com/uk/privacy-policy/"
        } else {
            "https://wealthyhood.com/eu/privacy-policy/"
        }

        _eventOpenBrowser.value = url
    }
}
