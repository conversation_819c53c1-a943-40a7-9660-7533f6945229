package com.wealthyhood.wealthyhood.ui.assetdetails.aboutasset

import android.app.Application
import androidx.annotation.StringRes
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import com.wealthyhood.wealthyhood.R
import com.wealthyhood.wealthyhood.database.Asset
import com.wealthyhood.wealthyhood.extensions.isStock
import com.wealthyhood.wealthyhood.model.AboutAssetInfoRowTypeEnum
import com.wealthyhood.wealthyhood.model.AboutAssetScreenArgs
import com.wealthyhood.wealthyhood.repository.OnBoardingRepository
import com.wealthyhood.wealthyhood.ui.assetdetails.aboutasset.AboutAssetListAdapter.DataItem
import kotlinx.coroutines.launch

class AboutAssetViewModel(
    application: Application,
    private val screenArgs: AboutAssetScreenArgs?
) : AndroidViewModel(application) {

    companion object {

        private const val WEBSITE_INFO_ROW_ID = "websiteInfoRow"
    }

    private val context = application

    private val onBoardingRepository = OnBoardingRepository() // TODO: Use DI

    private var asset: Asset? = null

    private var shouldShowFullDescription = false

    private val _dataItems = MutableLiveData<List<DataItem>?>()
    val dataItems: LiveData<List<DataItem>?>
        get() = _dataItems

    private val _eventOpenBrowser = MutableLiveData<String?>()
    val eventOpenBrowser: LiveData<String?>
        get() = _eventOpenBrowser

    fun eventOpenBrowserCompleted() {
        _eventOpenBrowser.value = null
    }

    init {
        viewModelScope.launch {
            asset = onBoardingRepository.getAsset(context, screenArgs?.assetID)

            refreshDataItems()
        }
    }

    fun handleOnDescriptionReadMoreButtonClicked() {
        shouldShowFullDescription = !shouldShowFullDescription

        refreshDataItems()
    }

    fun handleOnInfoRowLabelClicked(infoRowID: String?) {}

    fun handleOnInfoRowValueClicked(infoRowID: String?) {
        if (infoRowID != WEBSITE_INFO_ROW_ID) return

        val infoRow = findInfoRowWithID(infoRowID)
        val url = infoRow?.valueText ?: return

        _eventOpenBrowser.value = "https://$url"
    }

    private fun findInfoRowWithID(infoRowID: String?): DataItem.InfoRowItem? {
        _dataItems.value?.forEach { dataItem ->
            if (dataItem !is DataItem.InfoRowItem) return@forEach

            if (dataItem.id == infoRowID) {
                return dataItem
            }
        }

        return null
    }

    private fun refreshDataItems() {
        _dataItems.value = generateDataItems()
    }

    private fun generateDataItems(): List<DataItem> {
        val finalAnswer = mutableListOf<DataItem>()

        finalAnswer.add(generateHeaderItem())

        if (asset?.isStock() == true) {
            finalAnswer.addAll(generateStockInfoRows())
        } else {
            @StringRes val labelRes = if (asset?.assetClassID == "commodities") {
                R.string.etc_name_label
            } else {
                R.string.etf_name_label
            }

            val label = context.resources.getString(labelRes)
            finalAnswer.addAll(generateETFInfoRows(label))
        }

        finalAnswer.addAll(generateDescriptionSectionItems())

        return finalAnswer
    }

    private fun generateHeaderItem(): DataItem.HeaderItem {
        val title = context.resources.getString(
            R.string.company_details_screen_title,
            screenArgs?.screenTitle
        )

        return DataItem.HeaderItem(
            id = "headerItem",
            title = title
        )
    }

    private fun generateStockInfoRows(): List<DataItem.InfoRowItem> {
        val types = listOf(
            AboutAssetInfoRowTypeEnum.Ticker,

            AboutAssetInfoRowTypeEnum.Exchange,
            AboutAssetInfoRowTypeEnum.IsIN,
            AboutAssetInfoRowTypeEnum.Sector,
            AboutAssetInfoRowTypeEnum.Industry,
            AboutAssetInfoRowTypeEnum.CEO,
            AboutAssetInfoRowTypeEnum.Headquarters,
            AboutAssetInfoRowTypeEnum.Employees,
            AboutAssetInfoRowTypeEnum.Website
        )

        val sectionStarters = listOf(
            AboutAssetInfoRowTypeEnum.Sector,
            AboutAssetInfoRowTypeEnum.CEO
        )

        return generateInfoRows(
            types = types,
            sectionStarters = sectionStarters
        )
    }

    private fun generateDescriptionSectionItems(): List<DataItem> {
        val finalAnswer = mutableListOf<DataItem>()

        finalAnswer.add(generateDescriptionSectionHeaderItem())
        finalAnswer.add(generateDescriptionItem())

        return finalAnswer
    }

    private fun generateETFInfoRows(etfNameInfoRowLabel: String): List<DataItem.InfoRowItem> {
        val types = listOf(
            AboutAssetInfoRowTypeEnum.ETFName(etfNameInfoRowLabel),
            AboutAssetInfoRowTypeEnum.Ticker,
            AboutAssetInfoRowTypeEnum.Exchange,
            AboutAssetInfoRowTypeEnum.IsIN,
            AboutAssetInfoRowTypeEnum.Provider,
            AboutAssetInfoRowTypeEnum.Index,
            AboutAssetInfoRowTypeEnum.Replication,
            AboutAssetInfoRowTypeEnum.AssetClass,
            AboutAssetInfoRowTypeEnum.Sector,
            AboutAssetInfoRowTypeEnum.Allocation
        )

        val sectionStarters = listOf(AboutAssetInfoRowTypeEnum.AssetClass)

        return generateInfoRows(
            types = types,
            sectionStarters = sectionStarters
        )
    }

    private fun generateDescriptionSectionHeaderItem(): DataItem.SectionHeaderItem {
        val spacing16 = context.resources.getDimensionPixelSize(R.dimen.spacing_16)
        val spacing40 = context.resources.getDimensionPixelSize(R.dimen.spacing_40)

        return DataItem.SectionHeaderItem(
            id = "descriptionSectionHeaderItem",
            paddingStart = spacing16,
            paddingTop = spacing40,
            paddingEnd = spacing16,
            paddingBottom = null,
            textAppearanceRes = R.style.HeadingsH3Mobile_PrimaryColor,
            title = context.resources.getString(R.string.description)
        )
    }

    private fun generateDescriptionItem(): DataItem.DescriptionItem {
        val spacing16 = context.resources.getDimensionPixelSize(R.dimen.spacing_16)
        val spacing40 = context.resources.getDimensionPixelSize(R.dimen.spacing_40)

        val description = if (asset?.isStock() == true) {
            screenArgs?.description
        } else {
            asset?.about
        }

        return DataItem.DescriptionItem(
            id = "descriptionItem",
            paddingTop = spacing16,
            paddingBottom = spacing40,
            description = description
        )
    }

    private fun generateInfoRows(
        types: List<AboutAssetInfoRowTypeEnum>,
        sectionStarters: List<AboutAssetInfoRowTypeEnum>
    ): List<DataItem.InfoRowItem> {
        val spacing16 = context.resources.getDimensionPixelSize(R.dimen.spacing_16)

        val finalAnswer = types.mapIndexedNotNull { index, type ->
            val nextType = types.getOrNull(index + 1)
            val shouldShowSeparator = (sectionStarters.contains(nextType))

            when (type) {
                AboutAssetInfoRowTypeEnum.Allocation -> {
                    val valueText = screenArgs?.allocation ?: return@mapIndexedNotNull null

                    DataItem.InfoRowItem(
                        id = "allocationInfoRow",
                        backgroundRes = null,
                        shouldShowSeparator = shouldShowSeparator,
                        paddingTop = spacing16,
                        paddingBottom = null,
                        labelText = context.resources.getString(R.string.allocation_label),
                        shouldShowInfoIcon = false,
                        valueText = valueText,
                        valueTextColorRes = null
                    )
                }

                is AboutAssetInfoRowTypeEnum.AssetClass -> {
                    val valueText = screenArgs?.assetClass ?: return@mapIndexedNotNull null

                    DataItem.InfoRowItem(
                        id = "assetClassInfoRow",
                        backgroundRes = null,
                        shouldShowSeparator = shouldShowSeparator,
                        paddingTop = spacing16,
                        paddingBottom = null,
                        labelText = context.resources.getString(R.string.asset_class_label),
                        shouldShowInfoIcon = false,
                        valueText = valueText,
                        valueTextColorRes = null
                    )
                }

                AboutAssetInfoRowTypeEnum.CEO -> {
                    val valueText = screenArgs?.ceo ?: return@mapIndexedNotNull null

                    DataItem.InfoRowItem(
                        id = "ceoInfoRow",
                        backgroundRes = null,
                        shouldShowSeparator = shouldShowSeparator,
                        paddingTop = spacing16,
                        paddingBottom = null,
                        labelText = context.resources.getString(R.string.ceo_label),
                        shouldShowInfoIcon = false,
                        valueText = valueText,
                        valueTextColorRes = null
                    )
                }

                is AboutAssetInfoRowTypeEnum.ETFName -> {
                    val valueText = asset?.title ?: return@mapIndexedNotNull null

                    DataItem.InfoRowItem(
                        id = "etfNameInfoRow",
                        backgroundRes = null,
                        shouldShowSeparator = shouldShowSeparator,
                        paddingTop = spacing16,
                        paddingBottom = null,
                        labelText = type.labelText,
                        shouldShowInfoIcon = false,
                        valueText = valueText,
                        valueTextColorRes = null
                    )
                }

                AboutAssetInfoRowTypeEnum.Employees -> {
                    val valueText = screenArgs?.employees ?: return@mapIndexedNotNull null

                    DataItem.InfoRowItem(
                        id = "employeesInfoRow",
                        backgroundRes = null,
                        shouldShowSeparator = shouldShowSeparator,
                        paddingTop = spacing16,
                        paddingBottom = null,
                        labelText = context.resources.getString(R.string.employees_label),
                        shouldShowInfoIcon = false,
                        valueText = valueText,
                        valueTextColorRes = null
                    )
                }

                AboutAssetInfoRowTypeEnum.Exchange -> {
                    val valueText = screenArgs?.exchange ?: return@mapIndexedNotNull null

                    DataItem.InfoRowItem(
                        id = "exchangeInfoRow",
                        backgroundRes = null,
                        shouldShowSeparator = shouldShowSeparator,
                        paddingTop = spacing16,
                        paddingBottom = null,
                        labelText = context.resources.getString(R.string.trading_on_label),
                        shouldShowInfoIcon = false,
                        valueText = valueText,
                        valueTextColorRes = null
                    )
                }

                AboutAssetInfoRowTypeEnum.Headquarters -> {
                    val valueText = screenArgs?.headquarters ?: return@mapIndexedNotNull null

                    DataItem.InfoRowItem(
                        id = "headquartersInfoRow",
                        backgroundRes = null,
                        shouldShowSeparator = shouldShowSeparator,
                        paddingTop = spacing16,
                        paddingBottom = null,
                        labelText = context.resources.getString(R.string.headquarters_label),
                        shouldShowInfoIcon = false,
                        valueText = valueText,
                        valueTextColorRes = null
                    )
                }

                AboutAssetInfoRowTypeEnum.Index -> {
                    val indexValue = screenArgs?.index ?: return@mapIndexedNotNull null

                    DataItem.InfoRowItem(
                        id = "indexInfoRow",
                        backgroundRes = null,
                        shouldShowSeparator = shouldShowSeparator,
                        paddingTop = spacing16,
                        paddingBottom = null,
                        labelText = context.resources.getString(R.string.index_label),
                        shouldShowInfoIcon = false,
                        valueText = indexValue,
                        valueTextColorRes = null
                    )
                }

                AboutAssetInfoRowTypeEnum.Industry -> {
                    val valueText = screenArgs?.industry ?: return@mapIndexedNotNull null

                    DataItem.InfoRowItem(
                        id = "industryInfoRow",
                        backgroundRes = null,
                        shouldShowSeparator = shouldShowSeparator,
                        paddingTop = spacing16,
                        paddingBottom = null,
                        labelText = context.resources.getString(R.string.industry_label),
                        shouldShowInfoIcon = false,
                        valueText = valueText,
                        valueTextColorRes = null
                    )
                }

                AboutAssetInfoRowTypeEnum.IsIN -> {
                    val valueText = screenArgs?.isin ?: return@mapIndexedNotNull null

                    DataItem.InfoRowItem(
                        id = "isinInfoRow",
                        backgroundRes = null,
                        shouldShowSeparator = shouldShowSeparator,
                        paddingTop = spacing16,
                        paddingBottom = null,
                        labelText = context.resources.getString(R.string.isin_label),
                        shouldShowInfoIcon = false,
                        valueText = valueText,
                        valueTextColorRes = null
                    )
                }

                AboutAssetInfoRowTypeEnum.Provider -> {
                    val valueText = screenArgs?.provider ?: return@mapIndexedNotNull null

                    DataItem.InfoRowItem(
                        id = "providerInfoRow",
                        backgroundRes = null,
                        shouldShowSeparator = shouldShowSeparator,
                        paddingTop = spacing16,
                        paddingBottom = null,
                        labelText = context.resources.getString(R.string.provider_label),
                        shouldShowInfoIcon = false,
                        valueText = valueText,
                        valueTextColorRes = null
                    )
                }

                AboutAssetInfoRowTypeEnum.Replication -> {
                    val replicationValue = screenArgs?.replication ?: return@mapIndexedNotNull null

                    DataItem.InfoRowItem(
                        id = "replicationInfoRow",
                        backgroundRes = null,
                        shouldShowSeparator = shouldShowSeparator,
                        paddingTop = spacing16,
                        paddingBottom = null,
                        labelText = context.resources.getString(R.string.replication_label),
                        shouldShowInfoIcon = false,
                        valueText = replicationValue,
                        valueTextColorRes = null
                    )
                }

                AboutAssetInfoRowTypeEnum.Sector -> {
                    val valueText = screenArgs?.sector ?: return@mapIndexedNotNull null

                    DataItem.InfoRowItem(
                        id = "sectorInfoRow",
                        backgroundRes = null,
                        shouldShowSeparator = shouldShowSeparator,
                        paddingTop = spacing16,
                        paddingBottom = null,
                        labelText = context.resources.getString(R.string.sector_label),
                        shouldShowInfoIcon = false,
                        valueText = valueText,
                        valueTextColorRes = null
                    )
                }

                is AboutAssetInfoRowTypeEnum.Ticker -> {
                    val valueText = screenArgs?.ticker ?: return@mapIndexedNotNull null

                    DataItem.InfoRowItem(
                        id = "tickerInfoRow",
                        backgroundRes = null,
                        shouldShowSeparator = shouldShowSeparator,
                        paddingTop = spacing16,
                        paddingBottom = null,
                        labelText = context.resources.getString(R.string.ticker_label),
                        shouldShowInfoIcon = false,
                        valueText = valueText,
                        valueTextColorRes = null
                    )
                }

                AboutAssetInfoRowTypeEnum.Website -> {
                    val valueText = screenArgs?.website ?: return@mapIndexedNotNull null

                    DataItem.InfoRowItem(
                        id = WEBSITE_INFO_ROW_ID,
                        backgroundRes = null,
                        shouldShowSeparator = shouldShowSeparator,
                        paddingTop = spacing16,
                        paddingBottom = null,
                        labelText = context.resources.getString(R.string.website_label),
                        shouldShowInfoIcon = false,
                        valueText = valueText,
                        valueTextColorRes = R.color.primary_55
                    )
                }
            }
        }

        val firstItem = finalAnswer.firstOrNull()

        firstItem?.backgroundRes = R.drawable.company_details_info_row_header_background
        firstItem?.paddingBottom = null

        val lastItem = finalAnswer.lastOrNull()

        lastItem?.backgroundRes = R.drawable.company_details_info_row_footer_background
        lastItem?.paddingBottom = spacing16

        return finalAnswer
    }
}
