package com.wealthyhood.wealthyhood.ui.banktransfer

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.annotation.DrawableRes
import androidx.core.view.updatePadding
import androidx.recyclerview.widget.RecyclerView
import com.wealthyhood.wealthyhood.R
import com.wealthyhood.wealthyhood.databinding.ListItemBankTransferActionBinding
import com.wealthyhood.wealthyhood.extensions.convertToVisibility

class BankTransferActionViewHolder private constructor(
    private val binding: ListItemBankTransferActionBinding,
    private val listener: BankTransferActionViewHolderListener?
) : RecyclerView.ViewHolder(binding.root) {

    interface BankTransferActionViewHolderListener {

        fun onItemClicked(itemID: String?)
    }

    companion object {

        fun from(
            parent: ViewGroup,
            listener: BankTransferActionViewHolderListener?
        ): BankTransferActionViewHolder {
            val layoutInflater = LayoutInflater.from(parent.context)
            val binding = ListItemBankTransferActionBinding.inflate(layoutInflater, parent, false)

            return BankTransferActionViewHolder(binding, listener)
        }
    }

    private var itemID: String? = null

    init {
        setupButtons()
    }

    fun bind(
        itemID: String,
        @DrawableRes imageDrawableRes: Int?,
        paddingStart: Int?,
        paddingTop: Int?,
        paddingEnd: Int?,
        paddingBottom: Int?,
        title: String?,
        subtitle: String?,
        shouldShowBadge: Boolean,
        badgeText: String?,
        isDisabled: Boolean
    ) {
        this.itemID = itemID

        refreshPadding(
            paddingStart = paddingStart,
            paddingTop = paddingTop,
            paddingEnd = paddingEnd,
            paddingBottom = paddingBottom
        )

        refreshImage(imageDrawableRes)
        refreshTitle(title)
        refreshSubtitle(subtitle)
        refreshBadge(shouldShowBadge, badgeText)
        refreshEnableStatus(isDisabled)
    }

    private fun refreshBadge(isVisible: Boolean, text: String?) {
        val visibility = isVisible.convertToVisibility()

        binding.comingSoonTextView.visibility = visibility
        binding.comingSoonTextView.text = text
    }

    private fun refreshEnableStatus(isDisabled: Boolean) {
        val alpha = if (isDisabled) 0.4f else 1.0f

        binding.rootConstraintLayout.alpha = alpha
    }

    private fun setupButtons() {
        itemView.setOnClickListener {
            listener?.onItemClicked(itemID)
        }
    }

    private fun refreshImage(@DrawableRes drawableRes: Int?) {
        if (drawableRes == null) {
            binding.imageView.setImageDrawable(null)

            return
        }

        binding.imageView.setImageResource(drawableRes)
    }

    private fun refreshPadding(
        paddingStart: Int?,
        paddingTop: Int?,
        paddingEnd: Int?,
        paddingBottom: Int?
    ) {
        val spacing0 = itemView.context.resources.getDimensionPixelSize(R.dimen.spacing_0)

        val finalPaddingStart = paddingStart ?: spacing0
        val finalPaddingTop = paddingTop ?: spacing0
        val finalPaddingEnd = paddingEnd ?: spacing0
        val finalPaddingBottom = paddingBottom ?: spacing0

        binding.rootConstraintLayout.updatePadding(
            finalPaddingStart,
            finalPaddingTop,
            finalPaddingEnd,
            finalPaddingBottom
        )
    }

    private fun refreshTitle(text: String?) {
        binding.titleTextView.text = text
    }

    private fun refreshSubtitle(text: String?) {
        binding.subtitleTextView.text = text
    }
}
