package com.wealthyhood.wealthyhood.ui.banktransfer

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.core.view.ViewCompat
import androidx.core.view.WindowInsetsCompat
import androidx.core.view.updateLayoutParams
import androidx.fragment.app.Fragment
import androidx.lifecycle.ViewModelProvider
import androidx.recyclerview.widget.DefaultItemAnimator
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.wealthyhood.wealthyhood.databinding.FragmentBankTransferBinding
import com.wealthyhood.wealthyhood.extensions.animateTitleToAlpha
import com.wealthyhood.wealthyhood.extensions.convertToBankTransferScreenArgs
import com.wealthyhood.wealthyhood.extensions.convertToVisibility
import com.wealthyhood.wealthyhood.extensions.setup
import com.wealthyhood.wealthyhood.extensions.updateElevation
import com.wealthyhood.wealthyhood.extensions.updateForTopInset
import com.wealthyhood.wealthyhood.extensions.updateTitleText
import com.wealthyhood.wealthyhood.model.BanksSheetScreenArgs
import com.wealthyhood.wealthyhood.model.DepositScreenArgs
import com.wealthyhood.wealthyhood.model.RegularBankTransferScreenArgs
import com.wealthyhood.wealthyhood.ui.bankssheet.BanksSheetFragment
import com.wealthyhood.wealthyhood.ui.investment.deposit.DepositActivity
import com.wealthyhood.wealthyhood.ui.main.MainActivity
import com.wealthyhood.wealthyhood.ui.regularbanktransfer.RegularBankTransferActivity
import com.wealthyhood.wealthyhood.viewholders.BankAccountWithBadgeViewHolder

class BankTransferFragment : Fragment() {

    companion object {

        private const val ARG_SCREEN_ARGS = "BankTransferFragment.screenArgs"

        fun newInstance(screenArgsJSONString: String?): BankTransferFragment {
            val arguments = Bundle()
            arguments.putString(ARG_SCREEN_ARGS, screenArgsJSONString)

            val fragment = BankTransferFragment()
            fragment.arguments = arguments

            return fragment
        }
    }

    private lateinit var _viewModel: BankTransferViewModel
    private val viewModel get() = _viewModel

    private lateinit var _binding: FragmentBankTransferBinding
    private val binding get() = _binding

    private var isNavigationTitleVisible = false

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        setupFragmentResultListeners()
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        setupViewModel()
        setupBinding(inflater, container)

        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        setupWindowInsetsListener()
        setupNavigationBar()
        setupButtons()
        setupRecyclerView()

        observeViewModel()
    }

    private fun setupFragmentResultListeners() {
        childFragmentManager.setFragmentResultListener(
            BanksSheetFragment.REQUEST_KEY_SELECT_BANK,
            this
        ) { _, bundle ->
            val bankID = bundle.getString(
                BanksSheetFragment.BUNDLE_KEY_BANK_ID
            )

            viewModel.handleOnBankClicked(bankID)
        }
    }

    private fun setupWindowInsetsListener() {
        // https://developer.android.com/develop/ui/views/layout/sw-keyboard
        // https://developer.android.com/develop/ui/views/layout/edge-to-edge

        ViewCompat.setOnApplyWindowInsetsListener(binding.root) { _, windowInsets ->
            val insets = windowInsets.getInsets(WindowInsetsCompat.Type.systemBars())

            binding.navigationBarComponent.updateForTopInset(insets.top)

            binding.bottomButtonsLinearLayout.updateLayoutParams<ViewGroup.MarginLayoutParams> {
                bottomMargin = insets.bottom
            }

            WindowInsetsCompat.CONSUMED
        }
    }

    private fun setupNavigationBar() {
        binding.navigationBarComponent.setup(
            shouldShowCloseButton = true,
            closeButtonCallback = {
                requireActivity().finish()
            },
            titleText = null,
            titleAlpha = 0f,
            rightButtonProperties = null
        )
    }

    private fun setupButtons() {
        binding.primaryButton.setOnClickListener {
            viewModel.handleOnPrimaryButtonClicked()
        }
    }

    private fun setupViewModel() {
        val screenArgsJSONString = arguments?.getString(ARG_SCREEN_ARGS)
        val screenArgs = screenArgsJSONString?.convertToBankTransferScreenArgs()

        val viewModelFactory = BankTransferViewModelFactory(
            application = requireActivity().application,
            screenArgs = screenArgs
        )

        val viewModelClass = BankTransferViewModel::class.java
        _viewModel = ViewModelProvider(this, viewModelFactory)[viewModelClass]
    }

    private fun setupBinding(inflater: LayoutInflater, container: ViewGroup?) {
        _binding = FragmentBankTransferBinding.inflate(inflater, container, false)
    }

    private fun setupRecyclerView() {
        binding.recyclerView.layoutManager = LinearLayoutManager(requireContext())

        val itemAnimator = object : DefaultItemAnimator() {

            override fun onAnimationFinished(viewHolder: RecyclerView.ViewHolder) {
                super.onAnimationFinished(viewHolder)

                //viewModel.handleOnRecyclerViewScrolled(binding.recyclerView)
            }
        }

        itemAnimator.supportsChangeAnimations = false

        binding.recyclerView.itemAnimator = itemAnimator

        val actionListener =
            object : BankTransferActionViewHolder.BankTransferActionViewHolderListener {

                override fun onItemClicked(itemID: String?) {
                    viewModel.handleOnActionClicked(itemID)
                }
            }

        val bankAccountListener =
            object : BankAccountWithBadgeViewHolder.BankAccountWithBadgeViewHolderListener {

                override fun onItemClicked(itemID: String?) {
                    viewModel.handleOnBankAccountClicked(itemID)
                }
            }

        binding.recyclerView.adapter = BankTransferListAdapter(
            actionListener = actionListener,
            bankAccountListener = bankAccountListener
        )

        binding.recyclerView.addOnScrollListener(object : RecyclerView.OnScrollListener() {

            override fun onScrolled(recyclerView: RecyclerView, dx: Int, dy: Int) {
                super.onScrolled(recyclerView, dx, dy)

                viewModel.handleOnRecyclerViewScrolled(recyclerView)
            }
        })
    }

    private fun observeViewModel() {
        viewModel.navigationBarElevation.observe(viewLifecycleOwner) {
            refreshNavigationBarElevation(it)
        }

        viewModel.isNavigationTitleVisible.observe(viewLifecycleOwner) {
            refreshNavigationBarTitleVisibility(it)
        }

        viewModel.navigationTitleText.observe(viewLifecycleOwner) {
            refreshNavigationTitleText(it)
        }

        viewModel.isPrimaryButtonVisible.observe(viewLifecycleOwner) {
            refreshPrimaryButtonVisibility(it)
        }

        viewModel.dataItems.observe(viewLifecycleOwner) {
            refreshDataItems(it)
        }

        viewModel.eventNavigateToRegularBankTransferScreen.observe(viewLifecycleOwner) {
            it?.let {
                navigateToRegularBankTransferScreen(it)

                viewModel.eventNavigateToRegularBankTransferScreenCompleted()
            }
        }

        viewModel.eventShowBanksSheetScreen.observe(viewLifecycleOwner) {
            it?.let {
                showBanksSheetScreen(it)

                viewModel.eventShowBanksSheetScreenCompleted()
            }
        }

        viewModel.eventNavigateToDepositScreen.observe(viewLifecycleOwner) {
            it?.let {
                navigateToDepositScreen(it)

                viewModel.eventNavigateToDepositScreenCompleted()
            }
        }

        viewModel.eventNavigateToDashboardScreen.observe(viewLifecycleOwner) {
            it?.let {
                navigateToDashboardScreen()

                viewModel.eventNavigateToDashboardScreenCompleted()
            }
        }
    }

    private fun refreshNavigationBarElevation(elevation: Float?) {
        binding.navigationBarComponent.updateElevation(elevation)
    }

    private fun refreshNavigationBarTitleVisibility(isVisible: Boolean?) {
        if (isVisible == null) return
        if (isVisible == isNavigationTitleVisible) return

        isNavigationTitleVisible = isVisible

        val toAlpha = if (isVisible) 1.0f else 0f

        binding.navigationBarComponent.animateTitleToAlpha(toAlpha)
    }

    private fun refreshNavigationTitleText(text: String?) {
        binding.navigationBarComponent.updateTitleText(text)
    }

    private fun refreshPrimaryButtonVisibility(isVisible: Boolean?) {
        val visibility = isVisible.convertToVisibility()

        binding.primaryButton.visibility = visibility
        binding.fadeOutView.visibility = visibility
    }

    private fun refreshDataItems(dataItems: List<BankTransferListAdapter.DataItem>?) {
        (binding.recyclerView.adapter as? BankTransferListAdapter)?.submitList(dataItems)
    }

    private fun navigateToRegularBankTransferScreen(screenArgs: RegularBankTransferScreenArgs) {
        val intent = RegularBankTransferActivity.newIntent(
            context = requireContext(),
            screenArgs = screenArgs
        )

        startActivity(intent)
    }

    private fun showBanksSheetScreen(screenArgs: BanksSheetScreenArgs) {
        val fragment = BanksSheetFragment.newInstance(
            screenArgs = screenArgs
        )

        fragment.show(childFragmentManager, "BanksSheetFragment")
    }

    private fun navigateToDepositScreen(screenArgs: DepositScreenArgs) {
        val intent = DepositActivity.newIntent(
            context = requireContext(),
            screenArgs = screenArgs
        )

        startActivity(intent)
    }

    private fun navigateToDashboardScreen() {
        val intent = MainActivity.newIntent(requireContext())

        requireActivity().startActivity(intent)
        requireActivity().finishAffinity()
    }
}
