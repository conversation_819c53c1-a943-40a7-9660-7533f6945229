package com.wealthyhood.wealthyhood.ui.allocationoptions

import android.app.Application
import android.text.SpannableString
import androidx.core.content.ContextCompat
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import com.wealthyhood.wealthyhood.R
import com.wealthyhood.wealthyhood.model.AllocationOptionsScreenArgs
import com.wealthyhood.wealthyhood.model.GenericDescriptionScreenArguments
import com.wealthyhood.wealthyhood.ui.investment.portfoliobuy.scheduleoptions.ScheduleOptionsListAdapter.DataItem

class AllocationOptionsViewModel(
    application: Application,
    private val screenArgs: AllocationOptionsScreenArgs?
) : AndroidViewModel(application) {

    companion object {

        // TODO: Create public constants

        const val MY_INVESTMENTS_OPTION_ID = "myInvestmentsOption"
        const val TARGET_PORTFOLIO_OPTION_ID = "targetPortfolioOption"
    }

    private val context = application

    private var selectedOptionID = screenArgs?.selectedOptionID

    private val _dataItems = MutableLiveData<List<DataItem>?>()
    val dataItems: LiveData<List<DataItem>?>
        get() = _dataItems

    private val _eventShowGenericDescription = MutableLiveData<GenericDescriptionScreenArguments?>()
    val eventShowGenericDescription: LiveData<GenericDescriptionScreenArguments?>
        get() = _eventShowGenericDescription

    private val _eventShowCustomSnack = MutableLiveData<String?>()
    val eventShowCustomSnack: LiveData<String?>
        get() = _eventShowCustomSnack

    private val _eventAskForOptionSelection = MutableLiveData<String?>()
    val eventAskForOptionSelection: LiveData<String?>
        get() = _eventAskForOptionSelection

    fun eventShowGenericDescriptionCompleted() {
        _eventShowGenericDescription.value = null
    }

    fun eventShowCustomSnackCompleted() {
        _eventShowCustomSnack.value = null
    }

    fun eventAskForOptionSelectionCompleted() {
        _eventAskForOptionSelection.value = null
    }

    init {
        refreshDataItems()
    }

    fun handleOnOptionClicked(optionID: String?) {
        val didShowError = showSelectAllocationMethodErrorIfNeeded(optionID)
        if (didShowError) return

        selectedOptionID = optionID

        refreshDataItems()

        _eventAskForOptionSelection.value = optionID
    }

    fun handleOnScheduleOptionsInfoButtonClicked(optionID: String?) {
        val resources = context.resources

        val title: String
        val description: String

        if (optionID == MY_INVESTMENTS_OPTION_ID) {
            title = resources.getString(R.string.allocation_option_my_investments_description_title)

            description = listOf(
                resources.getString(R.string.allocation_option_my_investments_description_paragraph_1)
            ).joinToString("\n\n")
        } else {
            title =
                resources.getString(R.string.allocation_option_target_portfolio_description_title)

            description = listOf(
                resources.getString(R.string.allocation_option_target_portfolio_description_paragraph_1),
                resources.getString(R.string.allocation_option_target_portfolio_description_paragraph_2),
                resources.getString(R.string.allocation_option_target_portfolio_description_paragraph_3)
            ).joinToString("\n\n")
        }

        val spacing24 = resources.getDimensionPixelSize(R.dimen.spacing_24)
        val spacing40 = resources.getDimensionPixelSize(R.dimen.spacing_40)

        _eventShowGenericDescription.value = GenericDescriptionScreenArguments(
            title = title,
            description = SpannableString(description),
            contentPaddingTop = spacing24,
            contentPaddingBottom = spacing40
        )
    }

    private fun refreshDataItems() {
        _dataItems.value = generateDataItems()
    }

    private fun generateDataItems(): List<DataItem> {
        val finalAnswer = mutableListOf<DataItem>()

        finalAnswer.add(
            generateHeaderItem()
        )

        finalAnswer.addAll(
            generateOptionItems()
        )

        return finalAnswer
    }

    private fun generateHeaderItem(): DataItem.HeaderItem {
        return DataItem.HeaderItem(
            id = "headerItem",
            title = screenArgs?.headerTitle
        )
    }

    private fun generateOptionItems(): List<DataItem.OptionItem> {
        val finalAnswer = mutableListOf<DataItem.OptionItem>()

        finalAnswer.addAll(
            generateScheduleOptionItems()
        )

        return finalAnswer
    }

    private fun generateScheduleOptionItems(): List<DataItem.OptionItem> {
        val finalAnswer = mutableListOf<DataItem.OptionItem>()

        finalAnswer.add(generateMyInvestmentsOptionItem())
        finalAnswer.add(generateTargetPortfolioOptionItem())

        return finalAnswer
    }

    private fun generateMyInvestmentsOptionItem(): DataItem.OptionItem {
        val title = context.resources.getString(R.string.my_investments_label)

        val subtitle =
            context.resources.getString(R.string.my_investments_allocation_option_subtitle_text)

        val iconDrawable = ContextCompat.getDrawable(context, R.drawable.ic_list_alt)

        val isSelected = (MY_INVESTMENTS_OPTION_ID == selectedOptionID)
        val isEnabled = !shouldDisableMyInvestmentsAllocationMethodOption()

        return DataItem.OptionItem(
            id = MY_INVESTMENTS_OPTION_ID,
            iconDrawable = iconDrawable,
            shouldShowInfoButton = true,
            shouldShowBadge = false,
            badgeText = null,
            title = title,
            subtitleStyleRes = R.style.ScheduleOptionSubtitle,
            subtitle = subtitle,
            isSubtitleComplex = false,
            bankLogoURI = null,
            isSelected = isSelected,
            isEnabled = isEnabled
        )
    }

    private fun generateTargetPortfolioOptionItem(): DataItem.OptionItem {
        val title = context.resources.getString(R.string.target_portfolio_label)

        val subtitle =
            context.resources.getString(R.string.target_portfolio_allocation_option_subtitle_text)

        val iconDrawable = ContextCompat.getDrawable(context, R.drawable.ic_donut_large)

        val isSelected = (TARGET_PORTFOLIO_OPTION_ID == selectedOptionID)
        val isEnabled = !shouldDisableTargetPortfolioAllocationMethodOption()

        return DataItem.OptionItem(
            id = TARGET_PORTFOLIO_OPTION_ID,
            shouldShowInfoButton = true,
            shouldShowBadge = false,
            badgeText = null,
            iconDrawable = iconDrawable,
            title = title,
            subtitleStyleRes = R.style.ScheduleOptionSubtitle,
            subtitle = subtitle,
            isSubtitleComplex = false,
            bankLogoURI = null,
            isSelected = isSelected,
            isEnabled = isEnabled
        )
    }

    private fun showSelectAllocationMethodErrorIfNeeded(optionID: String?): Boolean {
        if (optionID == TARGET_PORTFOLIO_OPTION_ID) {
            val shouldDisableOption = shouldDisableTargetPortfolioAllocationMethodOption()

            if (shouldDisableOption) {
                _eventShowCustomSnack.value =
                    context.resources.getString(R.string.select_allocation_method_no_target_allocation_error_text)

                return true
            }
        }

        if (optionID == MY_INVESTMENTS_OPTION_ID) {
            val shouldDisableOption = shouldDisableMyInvestmentsAllocationMethodOption()

            if (shouldDisableOption) {
                _eventShowCustomSnack.value =
                    context.resources.getString(R.string.select_allocation_method_no_investments_error_text)

                return true
            }
        }

        return false
    }

    private fun shouldDisableMyInvestmentsAllocationMethodOption(): Boolean {
        val hasHoldings = (screenArgs?.hasHoldings == true)

        if (!hasHoldings) {
            return true
        }

        return false
    }

    private fun shouldDisableTargetPortfolioAllocationMethodOption(): Boolean {
        val hasTargetAllocation = (screenArgs?.hasTargetAllocation == true)

        if (!hasTargetAllocation) {
            return true
        }

        return false
    }
}
