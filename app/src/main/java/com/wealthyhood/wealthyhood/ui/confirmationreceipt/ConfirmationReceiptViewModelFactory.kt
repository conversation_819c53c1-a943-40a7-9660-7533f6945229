package com.wealthyhood.wealthyhood.ui.confirmationreceipt

import android.app.Application
import androidx.lifecycle.ViewModel
import androidx.lifecycle.ViewModelProvider
import com.wealthyhood.wealthyhood.model.ConfirmationReceiptScreenArgs

@Suppress("UNCHECKED_CAST")
class ConfirmationReceiptViewModelFactory(
    private val application: Application,
    private val screenArgs: ConfirmationReceiptScreenArgs?
) : ViewModelProvider.Factory {

    override fun <T : ViewModel> create(modelClass: Class<T>): T {
        if (modelClass.isAssignableFrom(ConfirmationReceiptViewModel::class.java)) {
            return ConfirmationReceiptViewModel(
                application = application,
                screenArgs = screenArgs
            ) as T
        }

        throw IllegalArgumentException("Unknown ViewModel class")
    }
}
