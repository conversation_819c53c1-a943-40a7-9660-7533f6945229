package com.wealthyhood.wealthyhood.ui.assets

import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.content.res.ColorStateList
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.LinearLayout
import android.widget.TextView
import androidx.core.content.ContextCompat
import androidx.core.view.ViewCompat
import androidx.core.view.WindowInsetsCompat
import androidx.core.view.children
import androidx.core.view.updateLayoutParams
import androidx.fragment.app.Fragment
import androidx.lifecycle.ViewModelProvider
import androidx.recyclerview.widget.DefaultItemAnimator
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.google.android.material.tabs.TabLayout
import com.wealthyhood.wealthyhood.R
import com.wealthyhood.wealthyhood.common.INVESTMENT_PRODUCTS_UPDATED_ACTION
import com.wealthyhood.wealthyhood.databinding.FragmentAssetsBinding
import com.wealthyhood.wealthyhood.extensions.animateTitleToAlpha
import com.wealthyhood.wealthyhood.extensions.setup
import com.wealthyhood.wealthyhood.extensions.updateElevation
import com.wealthyhood.wealthyhood.extensions.updateForTopInset
import com.wealthyhood.wealthyhood.extensions.updateTitleText
import com.wealthyhood.wealthyhood.ui.assetdetails.AssetDetailsActivity
import com.wealthyhood.wealthyhood.viewholders.AssetWithMoneyViewHolder

class AssetsFragment : Fragment() {

    companion object {

        private const val ARG_SCREEN_TITLE = "AssetsFragment.screenTitle"
        private const val ARG_VIRTUAL_TREE_JSON = "AssetsFragment.virtualTreeJSON"

        private const val ARG_SHOULD_SHOW_SECTION_HEADERS =
            "AssetsFragment.shouldShowSectionHeaders"

        private const val ARG_SHOULD_SHOW_FLOATING_TITLE_BAR =
            "AssetsFragment.shouldShowFloatingTitleBar"

        private const val ARG_FLOATING_TITLE_BAR_TEXT =
            "AssetsFragment.floatingTitleBarText"

        fun newInstance(
            screenTitle: String?,
            virtualTreeJSON: String?,
            shouldShowSectionHeaders: Boolean,
            shouldShowFloatingTitleBar: Boolean,
            floatingTitleBarText: String?
        ): AssetsFragment {
            val arguments = Bundle()

            arguments.putString(ARG_SCREEN_TITLE, screenTitle)
            arguments.putString(ARG_VIRTUAL_TREE_JSON, virtualTreeJSON)
            arguments.putBoolean(ARG_SHOULD_SHOW_SECTION_HEADERS, shouldShowSectionHeaders)
            arguments.putBoolean(ARG_SHOULD_SHOW_FLOATING_TITLE_BAR, shouldShowFloatingTitleBar)
            arguments.putString(ARG_FLOATING_TITLE_BAR_TEXT, floatingTitleBarText)

            val fragment = AssetsFragment()
            fragment.arguments = arguments

            return fragment
        }
    }

    private lateinit var _viewModel: AssetsViewModel
    private val viewModel get() = _viewModel

    private lateinit var _binding: FragmentAssetsBinding
    private val binding get() = _binding

    private var isNavigationTitleVisible = false
    private var isFloatingTitleBarVisible = false

    private var buildingTabs = false
    private var manuallySelectingTab = true

    private val investmentProductsUpdatedEventReceiver = object : BroadcastReceiver() {

        override fun onReceive(context: Context?, intent: Intent?) {
            handleOnInvestmentProductsUpdatedEventReceived()
        }
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        setupViewModel()
        setupBinding(inflater, container)

        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        setupWindowInsetsListener()
        setupNavigationBar()
        setupFloatingNavigationBar()
        setupFloatingTitleBar()
        setupTabLayout()
        setupRecyclerView()

        observeViewModel()
    }

    override fun onResume() {
        super.onResume()

        registerBroadcastReceiver()

        viewModel.handleOnResume()
    }

    override fun onPause() {
        unregisterBroadcastReceiver()

        super.onPause()
    }

    private fun setupViewModel() {
        val screenTitle = arguments?.getString(ARG_SCREEN_TITLE)
        val virtualTreeJSON = arguments?.getString(ARG_VIRTUAL_TREE_JSON)
        val shouldShowSectionHeaders = arguments?.getBoolean(ARG_SHOULD_SHOW_SECTION_HEADERS)
        val shouldShowFloatingTitleBar = arguments?.getBoolean(ARG_SHOULD_SHOW_FLOATING_TITLE_BAR)
        val floatingTitleBarTextArg = arguments?.getString(ARG_FLOATING_TITLE_BAR_TEXT)

        val viewModelFactory = AssetsViewModelFactory(
            application = requireActivity().application,
            screenTitle = screenTitle,
            virtualTreeJSON = virtualTreeJSON,
            shouldShowSectionHeaders = shouldShowSectionHeaders,
            shouldShowFloatingTitleBar = shouldShowFloatingTitleBar,
            floatingTitleBarTextArg = floatingTitleBarTextArg
        )

        val viewModelClass = AssetsViewModel::class.java
        _viewModel = ViewModelProvider(this, viewModelFactory)[viewModelClass]
    }

    private fun setupBinding(inflater: LayoutInflater, container: ViewGroup?) {
        _binding = FragmentAssetsBinding.inflate(inflater, container, false)
    }

    private fun setupWindowInsetsListener() {
        // https://developer.android.com/develop/ui/views/layout/sw-keyboard
        // https://developer.android.com/develop/ui/views/layout/edge-to-edge

        ViewCompat.setOnApplyWindowInsetsListener(binding.root) { _, windowInsets ->
            val insets = windowInsets.getInsets(WindowInsetsCompat.Type.systemBars())

            val spacing114 = resources.getDimensionPixelSize(R.dimen.spacing_114)

            binding.navigationBarComponent.updateForTopInset(insets.top)
            binding.floatingNavigationBarComponent.updateForTopInset(insets.top)

            binding.recyclerView.updateLayoutParams<ViewGroup.MarginLayoutParams> {
                bottomMargin = insets.bottom
            }

            binding.titleBarConstraintLayout.updateLayoutParams {
                height = spacing114 + insets.top
            }

            WindowInsetsCompat.CONSUMED
        }
    }

    private fun registerBroadcastReceiver() {
        val activity = activity ?: return

        val filter = IntentFilter(INVESTMENT_PRODUCTS_UPDATED_ACTION)
        val flags = ContextCompat.RECEIVER_NOT_EXPORTED

        ContextCompat.registerReceiver(
            activity,
            investmentProductsUpdatedEventReceiver,
            filter,
            flags
        )
    }

    private fun unregisterBroadcastReceiver() {
        activity?.unregisterReceiver(investmentProductsUpdatedEventReceiver)
    }

    private fun handleOnInvestmentProductsUpdatedEventReceived() {
        viewModel.handleOnInvestmentProductsUpdatedEventReceived()
    }

    private fun setupNavigationBar() {
        binding.navigationBarComponent.setup(
            shouldShowCloseButton = true,
            closeButtonCallback = {
                requireActivity().finish()
            },
            titleText = null,
            titleAlpha = 0f,
            rightButtonProperties = null
        )
    }

    private fun setupFloatingNavigationBar() {
        binding.floatingNavigationBarComponent.setup(
            shouldShowCloseButton = true,
            closeButtonCallback = {
                requireActivity().finish()
            },
            titleText = null,
            titleAlpha = 1f,
            rightButtonProperties = null
        )
    }

    private fun setupFloatingTitleBar() {
        val floatingTitleBarHeight =
            resources.getDimension(R.dimen.add_remove_floating_title_bar_height)

        binding.titleBarConstraintLayout.alpha = 0f
        binding.titleBarConstraintLayout.translationY = -floatingTitleBarHeight
    }

    private fun setupTabLayout() {
        val tabSelectedListener = object : TabLayout.OnTabSelectedListener {

            override fun onTabSelected(tab: TabLayout.Tab?) {
                handleOnTabSelected(tab)
            }

            override fun onTabUnselected(tab: TabLayout.Tab?) {
                refreshTab(tab, false)
            }

            override fun onTabReselected(tab: TabLayout.Tab?) {}
        }

        binding.tabLayout.addOnTabSelectedListener(tabSelectedListener)
    }

    private fun setupRecyclerView() {
        binding.recyclerView.layoutManager = LinearLayoutManager(requireContext())

        val itemAnimator = object : DefaultItemAnimator() {

            override fun onAnimationFinished(viewHolder: RecyclerView.ViewHolder) {
                super.onAnimationFinished(viewHolder)

                //viewModel.handleOnRecyclerViewScrolled(binding.recyclerView)
            }
        }

        itemAnimator.supportsChangeAnimations = false

        binding.recyclerView.itemAnimator = itemAnimator

        val assetWithMoneyListener =
            object : AssetWithMoneyViewHolder.AssetWithMoneyViewHolderListener {

                override fun onItemClicked(itemID: String?) {
                    viewModel.handleOnAssetClicked(itemID)
                }
            }

        binding.recyclerView.adapter = AssetsListAdapter(
            assetWithMoneyListener = assetWithMoneyListener
        )

        binding.recyclerView.addOnScrollListener(object : RecyclerView.OnScrollListener() {

            override fun onScrolled(recyclerView: RecyclerView, dx: Int, dy: Int) {
                super.onScrolled(recyclerView, dx, dy)

                viewModel.handleOnRecyclerViewScrolled(recyclerView)
            }
        })
    }

    private fun observeViewModel() {
        viewModel.navigationBarElevation.observe(viewLifecycleOwner) {
            refreshNavigationBarElevation(it)
        }

        viewModel.isNavigationTitleVisible.observe(viewLifecycleOwner) {
            refreshNavigationBarTitleVisibility(it)
        }

        viewModel.navigationTitleText.observe(viewLifecycleOwner) {
            refreshNavigationTitleText(it)
        }

        viewModel.floatingTitleBarText.observe(viewLifecycleOwner) {
            refreshFloatingTitleBarText(it)
        }

        viewModel.isTitleViewVisible.observe(viewLifecycleOwner) {
            it?.let {
                refreshTitleView(it)
            }
        }

        viewModel.floatingTabItems.observe(viewLifecycleOwner) {
            refreshFloatingTabs(it)
        }

        viewModel.selectedTabIndex.observe(viewLifecycleOwner) {
            it?.let {
                automaticallySelectTab(it)
            }
        }

        viewModel.dataItems.observe(viewLifecycleOwner) {
            refreshDataItems(it)
        }

        viewModel.eventNavigateToAssetDetailsScreen.observe(viewLifecycleOwner) {
            it?.let {
                navigateToAssetDetailsScreen(it)

                viewModel.eventNavigateToAssetDetailsScreenCompleted()
            }
        }
    }

    private fun refreshFloatingTabs(tabTitles: List<String>?) {
        buildingTabs = true

        binding.tabLayout.removeAllTabs()

        tabTitles?.forEach { tabTitle ->
            binding.tabLayout.addTab(
                createTab(tabTitle)
            )
        }

        addSpacingBetweenTabs()

        buildingTabs = false
    }

    private fun automaticallySelectTab(tabIndex: Int) {
        manuallySelectingTab = false

        val tabAtIndex = binding.tabLayout.getTabAt(tabIndex)
        binding.tabLayout.selectTab(tabAtIndex)

        manuallySelectingTab = true
    }

    private fun refreshDataItems(dataItems: List<AssetsListAdapter.DataItem>?) {
        (binding.recyclerView.adapter as? AssetsListAdapter)?.submitList(dataItems)
    }

    private fun refreshNavigationBarElevation(elevation: Float?) {
        binding.navigationBarComponent.updateElevation(elevation)
    }

    private fun refreshNavigationBarTitleVisibility(isVisible: Boolean?) {
        if (isVisible == null) return
        if (isVisible == isNavigationTitleVisible) return

        isNavigationTitleVisible = isVisible

        val toAlpha = if (isVisible) 1.0f else 0f

        binding.navigationBarComponent.animateTitleToAlpha(toAlpha)
    }

    private fun refreshNavigationTitleText(text: String?) {
        binding.navigationBarComponent.updateTitleText(text)
    }

    private fun refreshFloatingTitleBarText(text: String?) {
        binding.floatingNavigationBarComponent.updateTitleText(text)
    }

    private fun refreshTitleView(isVisible: Boolean) {
        if (isVisible == isFloatingTitleBarVisible) return

        isFloatingTitleBarVisible = isVisible

        val floatingTitleBarHeight =
            resources.getDimension(R.dimen.add_remove_floating_title_bar_height)

        val toAlpha = if (isVisible) 1.0f else 0f
        val toTranslationY = if (isVisible) 0f else -floatingTitleBarHeight
        val duration = resources.getInteger(android.R.integer.config_shortAnimTime).toLong()

        binding.titleBarConstraintLayout.animate()
            .translationY(toTranslationY)
            .alpha(toAlpha)
            .setDuration(duration)
            .setInterpolator(android.view.animation.AccelerateDecelerateInterpolator())
            .start()
    }

    private fun handleOnTabSelected(tab: TabLayout.Tab?) {
        refreshTab(tab, true)

        if (buildingTabs || !manuallySelectingTab) return

        tab?.let {
            viewModel.dataItemPositionForTabIndex(tab.position)?.let { position ->
                binding.recyclerView.stopScroll()

                (binding.recyclerView.layoutManager as? LinearLayoutManager)?.let { layoutManager ->
                    val offset = viewModel.calculateRecyclerViewTopOffset()

                    // We need to use toInt(). If we use roundToInt() or getDimensionPixelSize()
                    // we will have a problem.
                    layoutManager.scrollToPositionWithOffset(position, offset.toInt())
                }
            }
        }
    }

    private fun refreshTab(tab: TabLayout.Tab?, isSelected: Boolean) {
        val customView = tab?.customView ?: return
        val titleTextView = customView.findViewById<TextView>(R.id.title_text_view) ?: return

        val backgroundColor: Int
        val textAppearance: Int

        if (isSelected) {
            val backgroundColorRes = R.color.wealthyhood_dark_blue
            backgroundColor = ContextCompat.getColor(requireContext(), backgroundColorRes)

            textAppearance = R.style.AddRemovePillTitle_Selected
        } else {
            backgroundColor = ContextCompat.getColor(requireContext(), R.color.white)
            textAppearance = R.style.AddRemovePillTitle_Unselected
        }

        customView.backgroundTintList = ColorStateList.valueOf(backgroundColor)
        titleTextView.setTextAppearance(textAppearance)
    }

    private fun createTab(tabTitle: String): TabLayout.Tab {
        val tab = binding.tabLayout.newTab()

        tab.setCustomView(R.layout.pill_add_remove)

        val titleTextView = tab.customView?.findViewById<TextView>(R.id.title_text_view)
        titleTextView?.text = tabTitle

        return tab
    }

    private fun addSpacingBetweenTabs() {
        (binding.tabLayout.getChildAt(0) as? ViewGroup)?.let { tabs ->
            tabs.children.forEachIndexed { index, tab ->
                (tab.layoutParams as? LinearLayout.LayoutParams)?.let { layoutParams ->
                    layoutParams.marginStart = if (index == 0) 0 else {
                        requireContext().resources.getDimensionPixelSize(R.dimen.add_remove_pill_spacing)
                    }

                    layoutParams.marginEnd = if (index == (tabs.childCount - 1)) 0 else {
                        requireContext().resources.getDimensionPixelSize(R.dimen.add_remove_pill_spacing)
                    }

                    tab.requestLayout()
                }
            }
        }
    }

    private fun navigateToAssetDetailsScreen(assetID: String) {
        val intent = AssetDetailsActivity.newIntent(
            context = requireContext(),
            portfolioID = null,
            assetID = assetID,
            shouldShowActionButtons = true
        )

        startActivity(intent)
    }
}
