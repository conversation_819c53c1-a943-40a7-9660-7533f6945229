package com.wealthyhood.wealthyhood.ui.assetdetails

import android.app.Application
import android.graphics.drawable.Drawable
import android.text.SpannableString
import androidx.annotation.DrawableRes
import androidx.core.content.ContextCompat
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import androidx.recyclerview.widget.RecyclerView
import com.github.mikephil.charting.data.Entry
import com.wealthyhood.wealthyhood.R
import com.wealthyhood.wealthyhood.common.TargetDotSingleton
import com.wealthyhood.wealthyhood.database.Asset
import com.wealthyhood.wealthyhood.database.InvestmentProduct
import com.wealthyhood.wealthyhood.database.WealthyhoodDatabase
import com.wealthyhood.wealthyhood.domain.AssetClassBreakdownItem.Companion.generateFormattedAllocation
import com.wealthyhood.wealthyhood.domain.AssetClassBreakdownItem.Companion.generateTitle
import com.wealthyhood.wealthyhood.domain.GetInvestmentProductsUseCase
import com.wealthyhood.wealthyhood.domain.GetPortfoliosCoroutinesUseCase
import com.wealthyhood.wealthyhood.domain.GetPortfoliosUseCase
import com.wealthyhood.wealthyhood.domain.GetUserUseCase
import com.wealthyhood.wealthyhood.domain.HandleBuyButtonUseCase
import com.wealthyhood.wealthyhood.domain.InfoRowEnum
import com.wealthyhood.wealthyhood.domain.MarketTagProperties
import com.wealthyhood.wealthyhood.domain.generateMarketHoursDescriptionForETF
import com.wealthyhood.wealthyhood.domain.generateMarketHoursDescriptionForStock
import com.wealthyhood.wealthyhood.extensions.convertToColor
import com.wealthyhood.wealthyhood.extensions.findUserLocale
import com.wealthyhood.wealthyhood.extensions.formatAsPercentage
import com.wealthyhood.wealthyhood.extensions.generateAssetClassBreakdownItems
import com.wealthyhood.wealthyhood.extensions.generateAssetClassBreakdownText
import com.wealthyhood.wealthyhood.extensions.generateETFOrderReceiptDialogArguments
import com.wealthyhood.wealthyhood.extensions.generateFormattedCurrency
import com.wealthyhood.wealthyhood.extensions.generateFormattedDate
import com.wealthyhood.wealthyhood.extensions.generateFormattedDescription
import com.wealthyhood.wealthyhood.extensions.generateFormattedNumber
import com.wealthyhood.wealthyhood.extensions.generateFullName
import com.wealthyhood.wealthyhood.extensions.generateProviderLogoURI
import com.wealthyhood.wealthyhood.extensions.generateRewardReceiptDialogArguments
import com.wealthyhood.wealthyhood.extensions.getFXFee
import com.wealthyhood.wealthyhood.extensions.getFormattedExpenseRatio
import com.wealthyhood.wealthyhood.extensions.getIconDrawableRes
import com.wealthyhood.wealthyhood.extensions.isEU
import com.wealthyhood.wealthyhood.extensions.isStock
import com.wealthyhood.wealthyhood.extensions.shouldShowInvestedDashboard
import com.wealthyhood.wealthyhood.model.AboutAssetScreenArgs
import com.wealthyhood.wealthyhood.model.AssetDetailsDateRange
import com.wealthyhood.wealthyhood.model.AssetNewsArticlesScreenArgs
import com.wealthyhood.wealthyhood.model.ConfirmationReceiptScreenArgs
import com.wealthyhood.wealthyhood.model.DescriptionScreenArgs
import com.wealthyhood.wealthyhood.model.DescriptionTypeEnum
import com.wealthyhood.wealthyhood.model.DomainResult
import com.wealthyhood.wealthyhood.model.ETFInvestmentScreenArguments
import com.wealthyhood.wealthyhood.model.GenericDescriptionScreenArguments
import com.wealthyhood.wealthyhood.model.HandlePollingResultUseCase
import com.wealthyhood.wealthyhood.model.NetworkCallState
import com.wealthyhood.wealthyhood.model.NetworkResource
import com.wealthyhood.wealthyhood.model.OrderReviewFragmentArguments
import com.wealthyhood.wealthyhood.model.PollingResult
import com.wealthyhood.wealthyhood.model.RecentActivityScreenArguments
import com.wealthyhood.wealthyhood.model.RegularBankTransferScreenArgs
import com.wealthyhood.wealthyhood.model.TopUpOptionsScreenArgs
import com.wealthyhood.wealthyhood.repository.AuthRepository
import com.wealthyhood.wealthyhood.repository.MyAccountRepository
import com.wealthyhood.wealthyhood.repository.OnBoardingRepository
import com.wealthyhood.wealthyhood.repository.PlansRepository
import com.wealthyhood.wealthyhood.repository.PreferencesRepository
import com.wealthyhood.wealthyhood.repository.Repository
import com.wealthyhood.wealthyhood.service.AssetNewsArticle
import com.wealthyhood.wealthyhood.service.EtfData
import com.wealthyhood.wealthyhood.service.GetInvestmentDetailsResponse
import com.wealthyhood.wealthyhood.service.GetInvestmentProductPriceByTenorResponse
import com.wealthyhood.wealthyhood.service.GetUserQueryParams
import com.wealthyhood.wealthyhood.service.InvestmentProductTenorPrice
import com.wealthyhood.wealthyhood.service.Order
import com.wealthyhood.wealthyhood.service.User
import com.wealthyhood.wealthyhood.ui.assetdetails.AssetDetailsListAdapter.DataItem
import com.wealthyhood.wealthyhood.ui.topupoptions.TopUpOptionsViewModel
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import java.util.Date
import java.util.Locale
import kotlin.math.abs

class AssetDetailsViewModel(
    application: Application,
    private val portfolioID: String?,
    private val assetID: String?,
    private val shouldShowActionButtons: Boolean?
) : AndroidViewModel(application) {

    companion object {

        private const val ORDERS_BATCH_SIZE = 5

        private const val ANALYST_VIEWS_SECTION_HEADER_ITEM_ID = "analystViewsSectionHeaderItem"
        private const val NEWS_SECTION_HEADER_ITEM_ID = "newsSectionHeaderItem"

        private const val TAG_MARKET_CLOSED_ITEM_ID = "tagMarketClosedItem"
        private const val TAG_MARKET_OPEN_ITEM_ID = "tagMarketOpenItem"
        private const val TAG_ADR_ITEM_ID = "tagADRItem"
        private const val TAG_COMMISSION_FREE_ITEM_ID = "tagCommissionFreeItem"
        private const val TAG_FRACTIONAL_ITEM_ID = "tagFractionalItem"
        private const val TAG_SMART_EXECUTION_ITEM_ID = "tagSmartExecutionItem"
    }

    private val context = application

    private val lineChartSolidCircleDrawable by lazy {
        ContextCompat.getDrawable(
            context,
            R.drawable.line_chart_solid_circle
        )
    }

    private val lineChartEmptyCircleDrawable by lazy {
        ContextCompat.getDrawable(
            context,
            R.drawable.line_chart_empty_circle
        )
    }

    private val assetTransactionSelectionDrawable by lazy {
        ContextCompat.getDrawable(
            context,
            R.drawable.asset_transaction_selection
        )
    }

    private val assetTransactionPositiveSelectionDrawable by lazy {
        ContextCompat.getDrawable(
            context,
            R.drawable.asset_transaction_positive_selection
        )
    }

    private val assetTransactionNegativeSelectionDrawable by lazy {
        ContextCompat.getDrawable(
            context,
            R.drawable.asset_transaction_negative_selection
        )
    }

    private val database = WealthyhoodDatabase.getInstance(application)
    private val onBoardingRepository = OnBoardingRepository() // TODO: Use DI
    private val preferencesRepository = PreferencesRepository(application) // TODO: Use DI
    private val plansRepository = PlansRepository(preferencesRepository) // TODO: Use DI
    private val authRepository = AuthRepository(application) // TODO: Use DI
    private val myAccountRepository = MyAccountRepository() // TODO: Use DI
    private val repository = Repository() // TODO: Use DI

    private var currencyISOCode = preferencesRepository.getUserCurrency()
    private var userLocale = preferencesRepository.findUserLocale()
    private var userCompanyEntity = preferencesRepository.getUserCompanyEntity()

    private val getUserUseCase = GetUserUseCase()

    private val getPortfoliosUseCase = GetPortfoliosUseCase(
        preferencesRepository = preferencesRepository,
        myAccountRepository = myAccountRepository
    )

    private val getPortfoliosCoroutinesUseCase = GetPortfoliosCoroutinesUseCase(
        preferencesRepository = preferencesRepository,
        repository = repository
    )

    private val getInvestmentProductsUseCase = GetInvestmentProductsUseCase(
        preferencesRepository = preferencesRepository,
        repository = repository,
        database = database
    )

    private val handleBuyButtonUseCase = HandleBuyButtonUseCase(
        currencyISOCode = currencyISOCode,
        preferencesRepository = preferencesRepository,
        authRepository = authRepository,
        repository = repository,
        getPortfoliosUseCase = getPortfoliosCoroutinesUseCase,
        viewModelScope = viewModelScope,
        showLoadingCallback = { _eventShowOrHideLoadingDialog.value = true },
        hideLoadingCallback = { _eventShowOrHideLoadingDialog.value = false },
        navigateToBuyCallback = { navigateToETFBuy() },
        showTopUpOptionsCallback = { showTopUpOptions() }
    )

    private val plans = plansRepository.getAllPlans()
    private val basePlans = plansRepository.getAllBasePlans(context)

    private val allAssetClasses = onBoardingRepository.getAssetClasses(preferencesRepository)
    private val etfProviders = onBoardingRepository.getETFProviders(preferencesRepository)

    private var getChartDataJob: Job? = null
    private var getAssetRecentActivityJob: Job? = null
    private var getAssetDataJob: Job? = null
    private var getInvestmentProductsJob: Job? = null

    // Η τελευταία φορά που κάναμε update τα data εξαιτίας του polling
    private var latestPollingRefreshTimestamp = 0L

    private var asset: Asset? = null

    private val helper = AssetDetailsHelper(context)

    private var user: User? = null

    private var chartDataCallState: NetworkCallState? = null
    private var selectedDateRange = AssetDetailsDateRange.ONE_YEAR
    private var selectedChartEntry: Entry? = null
    private var allChartTenors: GetInvestmentProductPriceByTenorResponse? = null
    private var chartEntries: List<Entry>? = null

    private var investmentDetails: GetInvestmentDetailsResponse? = null

    private var assetData: EtfData? = null
    private var investmentProduct: InvestmentProduct? = null

    private val _navigationBarElevation = MutableLiveData<Float?>()
    val navigationBarElevation: LiveData<Float?>
        get() = _navigationBarElevation

    private val _isNavigationTitleVisible = MutableLiveData<Boolean?>()
    val isNavigationTitleVisible: LiveData<Boolean?>
        get() = _isNavigationTitleVisible

    private val _navigationTitleText = MutableLiveData<String?>()
    val navigationTitleText: LiveData<String?>
        get() = _navigationTitleText

    private val _dataItems = MutableLiveData<List<DataItem>?>()
    val dataItems: LiveData<List<DataItem>?>
        get() = _dataItems

    private val _isBuyButtonVisible = MutableLiveData<Boolean?>()
    val isBuyButtonVisible: LiveData<Boolean?>
        get() = _isBuyButtonVisible

    private val _isSellButtonVisible = MutableLiveData<Boolean?>()
    val isSellButtonVisible: LiveData<Boolean?>
        get() = _isSellButtonVisible

    val eventRefreshTransactions = TargetDotSingleton.getInstance().pollingResult

    private val _eventShowOrHideLoadingDialog = MutableLiveData<Boolean?>()
    val eventShowOrHideLoadingDialog: LiveData<Boolean?>
        get() = _eventShowOrHideLoadingDialog

    private val _eventShowCompanyDetailsDialog = MutableLiveData<AboutAssetScreenArgs?>()
    val eventShowCompanyDetailsDialog: LiveData<AboutAssetScreenArgs?>
        get() = _eventShowCompanyDetailsDialog

    private val _eventShowFXRateDescriptionDialog = MutableLiveData<String?>()
    val eventShowFXRateDescriptionDialog: LiveData<String?>
        get() = _eventShowFXRateDescriptionDialog

    private val _eventShowDescriptionDialog = MutableLiveData<DescriptionScreenArgs?>()
    val eventShowDescriptionDialog: LiveData<DescriptionScreenArgs?>
        get() = _eventShowDescriptionDialog

    private val _eventShowGenericDescription = MutableLiveData<GenericDescriptionScreenArguments?>()
    val eventShowGenericDescription: LiveData<GenericDescriptionScreenArguments?>
        get() = _eventShowGenericDescription

    private val _eventShowConfirmationReceiptDialog =
        MutableLiveData<ConfirmationReceiptScreenArgs?>()
    val eventShowConfirmationReceiptDialog: LiveData<ConfirmationReceiptScreenArgs?>
        get() = _eventShowConfirmationReceiptDialog

    private val _eventShowOrderReviewDialog = MutableLiveData<OrderReviewFragmentArguments?>()
    val eventShowOrderReviewDialog: LiveData<OrderReviewFragmentArguments?>
        get() = _eventShowOrderReviewDialog

    private val _eventNavigateToETFBuyScreen = MutableLiveData<ETFInvestmentScreenArguments?>()
    val eventNavigateToETFBuyScreen: LiveData<ETFInvestmentScreenArguments?>
        get() = _eventNavigateToETFBuyScreen

    private val _eventNavigateToETFSellScreen = MutableLiveData<ETFInvestmentScreenArguments?>()
    val eventNavigateToETFSellScreen: LiveData<ETFInvestmentScreenArguments?>
        get() = _eventNavigateToETFSellScreen

    private val _eventNavigateToRecentActivityScreen =
        MutableLiveData<RecentActivityScreenArguments?>()
    val eventNavigateToRecentActivityScreen: LiveData<RecentActivityScreenArguments?>
        get() = _eventNavigateToRecentActivityScreen

    private val _eventOpenBrowser = MutableLiveData<String?>()
    val eventOpenBrowser: LiveData<String?>
        get() = _eventOpenBrowser

    private val _eventNavigateToNewsArticlesScreen = MutableLiveData<AssetNewsArticlesScreenArgs?>()
    val eventNavigateToNewsArticlesScreen: LiveData<AssetNewsArticlesScreenArgs?>
        get() = _eventNavigateToNewsArticlesScreen

    private val _eventShowTopUpOptionsDialog = MutableLiveData<TopUpOptionsScreenArgs?>()
    val eventShowTopUpOptionsDialog: LiveData<TopUpOptionsScreenArgs?>
        get() = _eventShowTopUpOptionsDialog

    private val _eventNavigateToRegularBankTransfer =
        MutableLiveData<RegularBankTransferScreenArgs?>()
    val eventNavigateToRegularBankTransfer: LiveData<RegularBankTransferScreenArgs?>
        get() = _eventNavigateToRegularBankTransfer

    fun eventShowOrHideLoadingDialogCompleted() {
        _eventShowOrHideLoadingDialog.value = null
    }

    fun eventShowCompanyDetailsDialogCompleted() {
        _eventShowCompanyDetailsDialog.value = null
    }

    fun eventShowFXRateDescriptionDialogCompleted() {
        _eventShowFXRateDescriptionDialog.value = null
    }

    fun eventShowDescriptionDialogCompleted() {
        _eventShowDescriptionDialog.value = null
    }

    fun eventShowGenericDescriptionCompleted() {
        _eventShowGenericDescription.value = null
    }

    fun eventShowConfirmationReceiptDialogCompleted() {
        _eventShowConfirmationReceiptDialog.value = null
    }

    fun eventShowOrderReviewDialogCompleted() {
        _eventShowOrderReviewDialog.value = null
    }

    fun eventNavigateToETFBuyScreenCompleted() {
        _eventNavigateToETFBuyScreen.value = null
    }

    fun eventNavigateToETFSellScreenCompleted() {
        _eventNavigateToETFSellScreen.value = null
    }

    fun eventNavigateToRecentActivityScreenCompleted() {
        _eventNavigateToRecentActivityScreen.value = null
    }

    fun eventOpenBrowserCompleted() {
        _eventOpenBrowser.value = null
    }

    fun eventNavigateToNewsArticlesScreenCompleted() {
        _eventNavigateToNewsArticlesScreen.value = null
    }

    fun eventShowTopUpOptionsDialogCompleted() {
        _eventShowTopUpOptionsDialog.value = null
    }

    fun eventNavigateToRegularBankTransferCompleted() {
        _eventNavigateToRegularBankTransfer.value = null
    }

    init {
        refreshNavigationTitle()
        refreshButtons()
        refreshDataItems()

        viewModelScope.launch {
            asset = onBoardingRepository.getAsset(context, assetID)

            refreshNavigationTitle()
            refreshDataItems()

            reloadData(shouldShowChartLoader = true)
        }
    }

    fun handleOnInvestmentProductsUpdatedEventReceived() {
        val isUserInvested = (user?.shouldShowInvestedDashboard() == true)
        if (!isUserInvested) return

        // No need to check if reloading. Active calls will cancel.
        reloadData(shouldShowChartLoader = false)
    }

    fun handleOnTopUpOptionClicked(topUpOptionID: String?) {
        if (topUpOptionID != TopUpOptionsViewModel.REGULAR_BANK_TRANSFER_OPTION_ID) return

        val userIBAN = preferencesRepository.getUserIBAN()

        _eventNavigateToRegularBankTransfer.value = RegularBankTransferScreenArgs(
            userIBAN = userIBAN,
            shouldShowPrimaryButton = false
        )
    }

    fun handleOnTagClicked(itemID: String?) {
        showDescriptionForTag(itemID)
    }

    fun handleOnReloadTransactionsRequest() {
        helper.recentActivityObjects = null
        refreshDataItems()

        refreshDataDueToPendingOrdersPolling()
    }

    fun handlePollingResult(pollingResult: PollingResult?) {
        HandlePollingResultUseCase().invoke(
            pollingResult = pollingResult,
            latestPollingRefreshTimestamp = latestPollingRefreshTimestamp
        ) {
            // FIXME: Show loader

            // FIXME: It would be better to update this after we successfully refresh the data.
            latestPollingRefreshTimestamp = Date().time

            refreshDataDueToPendingOrdersPolling()
        }
    }

    fun handleOnRecyclerViewScrolled(recyclerView: RecyclerView) {
        refreshNavigationBarElevation(recyclerView)
        refreshNavigationBarTitleVisibility(recyclerView)
    }

    private fun refreshDataDueToPendingOrdersPolling() {
        getCredentialsCall { _, accessToken, idToken ->
            if (accessToken == null || idToken == null) {
                // TODO: Handle the failure

                return@getCredentialsCall
            }

            getPortfolios(
                accessToken = accessToken,
                idToken = idToken,
                shouldGetFromCache = false
            )

            getAssetRecentActivity(
                accessToken = accessToken,
                idToken = idToken,
                assetID = asset?.id
            )
        }
    }

    fun handleOnHeaderExpandButtonClicked() {
        navigateToCompanyDetailsScreen()
    }

    fun handleOnSectionHeaderInfoButtonClicked(sectionHeaderID: String?) {
        if (sectionHeaderID == ANALYST_VIEWS_SECTION_HEADER_ITEM_ID) {
            _eventShowDescriptionDialog.value = DescriptionScreenArgs(
                descriptionType = DescriptionTypeEnum.ANALYST_VIEWS,
                savingsProductFeeDetails = null
            )
        }
    }

    fun handleOnSectionHeaderSeeAllButtonClicked(sectionHeaderID: String?) {
        if (sectionHeaderID == "recentActivitySectionHeader") {
            val investmentProduct = investmentProduct ?: return

            _eventNavigateToRecentActivityScreen.value = RecentActivityScreenArguments(
                assetID = asset?.id,
                currentTickerPrice = investmentProduct.currentTickerPrice,
                tradedPrice = investmentProduct.tradedPrice,
                tradedCurrency = investmentProduct.tradedCurrency,
                userFullName = user?.generateFullName(),
                planPriceAPIKey = user?.subscription?.price
            )
        } else if (sectionHeaderID == NEWS_SECTION_HEADER_ITEM_ID) {
            _eventNavigateToNewsArticlesScreen.value = AssetNewsArticlesScreenArgs(
                assetID = assetID,
                assetName = asset?.title
            )
        }
    }

    fun handleOnChartEntrySelected(entry: Entry?) {
        selectedChartEntry = entry

        refreshDataItems()
    }

    fun handleOnDateRangeSelected(dateRange: AssetDetailsDateRange) {
        selectedDateRange = dateRange
        selectedChartEntry = null
        chartEntries = generateLineChartData()

        refreshDataItems()
    }

    fun handleOnChartErrorTryAgainClicked() {
        getCredentialsCall { succeeded, accessToken, idToken ->
            if (!succeeded || accessToken == null || idToken == null) {
                // FIXME: Handle the error

                return@getCredentialsCall
            }

            getChartData(
                accessToken = accessToken,
                idToken = idToken,
                shouldShowLoader = true
            )
        }
    }

    fun handleOnTransactionClicked(transactionItemID: String?) {
        if (transactionItemID == null) return

        val reward = helper.findRewardWithID(transactionItemID)

        if (reward != null) {
            handleOnRewardClicked(transactionItemID)

            return
        }

        val order = helper.findOrderWithID(transactionItemID)

        if (order != null) {
            handleOnOrderClicked(transactionItemID)

            return
        }

        handleOnDividendTransactionClicked(transactionItemID)
    }

    fun handleOnMetricInfoButtonClicked(descriptionType: DescriptionTypeEnum?) {
        if (descriptionType == null) return

        _eventShowDescriptionDialog.value = DescriptionScreenArgs(
            descriptionType = descriptionType,
            savingsProductFeeDetails = null
        )
    }

    fun handleOnAveragePriceTargetInfoButtonClicked() {
        _eventShowDescriptionDialog.value = DescriptionScreenArgs(
            descriptionType = DescriptionTypeEnum.AVERAGE_PRICE_TARGET,
            savingsProductFeeDetails = null
        )
    }

    fun handleOnNewsArticleClicked(articleID: String?) {
        if (articleID == null) return
        val article = assetData?.news?.find { it.id == articleID } ?: return

        _eventOpenBrowser.value = article.newsURL
    }

    fun handleOnKIDItemClicked() {
        val kidLink = asset?.kidURI ?: return

        _eventOpenBrowser.value = kidLink
    }

    fun handleOnBuyButtonClicked() {
        handleBuyButtonUseCase(
            navigateToBuyExtras = null,
            shouldCheckForGifts = true
        )
    }

    fun handleOnSellButtonClicked() {
        _eventNavigateToETFSellScreen.value = generateETFInvestmentScreenArguments()
    }

    fun handleOnInfoRowInfoButtonClicked(infoRowID: String?) {
        if (infoRowID == InfoRowEnum.FX_RATE.rawValue) {
            showFXFeeDescription()
        }
    }

    fun handleOnCancelButtonClicked(orderID: String?) {
        if (orderID == null) return

        cancelOrder(orderID)
    }

    fun shouldShowMarkerForEntry(entry: Entry?): Boolean {
        val tenorPrice = (entry?.data as? InvestmentProductTenorPrice)

        // Στην iconForEntry() εμφανίζουμε το icon εάν το entry είναι επιλεγμένο ή αν έχει quantity
        return (tenorPrice?.data?.quantity != null)
    }

    fun getMarkerTypeTextForEntry(entry: Entry?): String? {
        val tenorPrice = (entry?.data as? InvestmentProductTenorPrice)

        return when (tenorPrice?.data?.type) {
            "buy" -> context.resources.getString(R.string.buy)
            "sell" -> context.resources.getString(R.string.sell)
            else -> null
        }
    }

    fun getMarkerQuantityTextForEntry(entry: Entry?): String? {
        val tenorPrice = (entry?.data as? InvestmentProductTenorPrice)
        val quantity = tenorPrice?.data?.quantity ?: return null

        val formattedQuantity = quantity.generateFormattedNumber(
            minimumFractionDigits = 0,
            maximumFractionDigits = 4,
            locale = Locale.getDefault()
        ) ?: return null

        return if (quantity > 0) {
            "+$formattedQuantity"
        } else formattedQuantity
    }

    private fun handleOnOrderClicked(orderID: String) {
        val order = helper.findOrderWithID(orderID) ?: return

        if (order.isMatched == true) {
            showOrderReceipt(order)

            return
        }

        showOrderReview(order)
    }

    private fun handleOnRewardClicked(rewardID: String) {
        // FIXME: If the user is not available nothing happens.

        if (user == null) return
        val reward = helper.findRewardWithID(rewardID) ?: return
        val isUserEU = (user?.isEU() == true)

        val screenArgs = reward.generateRewardReceiptDialogArguments(
            context = context,
            isUserEU = isUserEU,
            userFullName = user?.generateFullName(),
            currentAsset = asset,
            userLocale = userLocale,
            etfProviders = etfProviders
        )

        _eventShowConfirmationReceiptDialog.value = screenArgs
    }

    private fun handleOnDividendTransactionClicked(transactionID: String) {
        // FIXME: If the user is not available nothing happens.

        if (user == null) return

        _eventShowConfirmationReceiptDialog.value = helper.generateDividendReceiptScreenArguments(
            userFullName = user?.generateFullName(),
            transactionID = transactionID,
            userCompanyEntity = userCompanyEntity,
            currentAsset = asset,
            userLocale = userLocale,
            etfProviders = etfProviders
        )
    }

    private fun showOrderReceipt(order: Order) {
        val isUserEU = (user?.isEU() == true)

        val screenArgs = order.generateETFOrderReceiptDialogArguments(
            context = context,
            currentAsset = asset,
            isUserEU = isUserEU,
            userFullName = user?.generateFullName(),
            currencyISOCode = currencyISOCode,
            userLocale = userLocale,
            etfProviders = etfProviders
        )

        _eventShowConfirmationReceiptDialog.value = screenArgs
    }

    private fun showOrderReview(order: Order) {
        val investmentProduct = investmentProduct ?: return

        val arguments = helper.generateOrderReviewScreenArguments(
            asset = asset,
            currentTickerPrice = investmentProduct.currentTickerPrice,
            tradedPrice = investmentProduct.tradedPrice,
            tradedCurrency = investmentProduct.tradedCurrency,
            currencyISOCode = currencyISOCode,
            userLocale = userLocale,
            order = order,
            etfProviders = etfProviders
        )

        _eventShowOrderReviewDialog.value = arguments
    }

    private fun getCredentialsCall(callback: ((succeeded: Boolean, accessToken: String?, idToken: String?) -> Unit)?) {
        authRepository.loadAuth0Credentials {
            when (it) {
                is NetworkResource.Success -> {
                    val accessToken = "Bearer ${it.data?.accessToken}"
                    val idToken = "Bearer ${it.data?.idToken}"

                    callback?.invoke(true, accessToken, idToken)
                }

                is NetworkResource.Failure -> {
                    callback?.invoke(false, null, null)
                }
            }
        }
    }

    private fun getChartDataCall(
        accessToken: String,
        idToken: String,
        successCallback: ((errorMessage: String?, domainResult: GetInvestmentProductPriceByTenorResponse?) -> Unit)? = null
    ) {
        getChartDataJob?.cancel()

        getChartDataJob = viewModelScope.launch(Dispatchers.IO) {
            val result = repository.getInvestmentProductPricesByTenor(
                accessToken = accessToken,
                idToken = idToken,
                assetID = assetID
            )

            withContext(Dispatchers.Main) {
                when (result) {
                    is DomainResult.Success -> {
                        successCallback?.invoke(null, result.body)
                    }

                    else -> {
                        // We have to pass an errorMessage here, even if it's empty,
                        // in order to signal that we have finished with an error.
                        successCallback?.invoke("", null)
                    }
                }
            }
        }
    }

    private fun reloadData(shouldShowChartLoader: Boolean) {
        getCredentialsCall { succeeded, accessToken, idToken ->
            if (!succeeded || accessToken == null || idToken == null) {
                // FIXME: Handle the error

                return@getCredentialsCall
            }

            getUser(
                accessToken = accessToken,
                idToken = idToken
            )

            getChartData(
                accessToken = accessToken,
                idToken = idToken,
                shouldShowLoader = shouldShowChartLoader
            )

            getPortfolios(
                accessToken = accessToken,
                idToken = idToken,
                shouldGetFromCache = true
            )

            getAssetRecentActivity(
                accessToken = accessToken,
                idToken = idToken,
                assetID = asset?.id
            )

            getAssetData(
                accessToken = accessToken,
                idToken = idToken
            )

            getInvestmentProducts(
                accessToken = accessToken,
                idToken = idToken
            )
        }
    }

    private fun getUser(
        accessToken: String,
        idToken: String
    ) {
        getUserUseCase(
            context = getApplication(),
            myAccountRepository = myAccountRepository,
            preferencesRepository = preferencesRepository,
            accessToken = accessToken,
            idToken = idToken,
            queryParams = GetUserQueryParams(
                populate = "addresses,subscription"
            )
        ) {
            when (it) {
                is NetworkResource.Success -> {
                    user = it.data

                    // Μόλις λάβαμε νέο user οπότε το userCurrency μπορεί να έχει αλλάξει.
                    currencyISOCode = preferencesRepository.getUserCurrency()
                    userLocale = preferencesRepository.findUserLocale()

                    refreshButtons()
                    refreshDataItems()
                }

                is NetworkResource.Failure -> {
                    // TODO: Show an error message to retry
                }
            }
        }
    }

    private fun getPortfolios(
        accessToken: String,
        idToken: String,
        shouldGetFromCache: Boolean
    ) {
        getPortfoliosUseCase(
            accessToken = accessToken,
            idToken = idToken,
            parameters = null,
            shouldGetFromCache = shouldGetFromCache
        ) { networkResult ->
            when (networkResult) {
                is NetworkResource.Success -> {
                    helper.portfolio = networkResult.data?.find { it.isReal == true }

                    refreshButtons()
                    refreshDataItems()

                    getMyInvestmentDataIfNeeded(
                        accessToken = accessToken,
                        idToken = idToken
                    )
                }

                is NetworkResource.Failure -> {
                    // TODO: Handle the error
                }
            }
        }
    }

    private fun getChartData(
        accessToken: String,
        idToken: String,
        shouldShowLoader: Boolean
    ) {
        if (shouldShowLoader) {
            chartDataCallState = NetworkCallState.Executing
        }

        refreshDataItems()

        getChartDataCall(
            accessToken = accessToken,
            idToken = idToken
        ) { errorMessage, investmentProductPricesByTenor ->
            if (errorMessage != null) {
                chartDataCallState = NetworkCallState.Failed(errorMessage)
                refreshDataItems()

                return@getChartDataCall
            }

            allChartTenors = investmentProductPricesByTenor
            chartEntries = generateLineChartData()

            chartDataCallState = NetworkCallState.Completed
            refreshDataItems()
        }
    }

    private fun getMyInvestmentDataIfNeeded(
        accessToken: String,
        idToken: String
    ) {
        val hasUserQuantityOfThatAsset =
            helper.hasUserQuantityOfThatAsset(assetID = asset?.id)

        if (!hasUserQuantityOfThatAsset) return

        getMyInvestmentData(
            accessToken = accessToken,
            idToken = idToken,
            assetID = asset?.id
        )
    }

    private fun getMyInvestmentData(
        accessToken: String,
        idToken: String,
        assetID: String?
    ) {
        viewModelScope.launch(Dispatchers.IO) {
            val result = repository.getInvestmentDetails(
                accessToken = accessToken,
                idToken = idToken,
                assetID = assetID
            )

            withContext(Dispatchers.Main) {
                when (result) {
                    is DomainResult.Success -> {
                        investmentDetails = result.body

                        refreshDataItems()
                    }

                    else -> {
                        // TODO: Handle error, error with code ->  result.errorCode
                    }
                }
            }
        }
    }

    private fun getAssetRecentActivity(
        accessToken: String,
        idToken: String,
        assetID: String?
    ) {
        getAssetRecentActivityJob?.cancel()

        getAssetRecentActivityJob = viewModelScope.launch(Dispatchers.IO) {
            val result = repository.getAssetRecentActivity(
                accessToken = accessToken,
                idToken = idToken,
                assetID = assetID,
                limit = ORDERS_BATCH_SIZE
            )

            withContext(Dispatchers.Main) {
                when (result) {
                    is DomainResult.Success -> {
                        helper.recentActivityObjects =
                            AssetDetailsHelper.generateRecentActivityObjects(result.body)

                        refreshDataItems()
                    }

                    else -> {
                        // TODO: Handle the case
                    }
                }
            }
        }
    }

    private fun getAssetData(
        accessToken: String,
        idToken: String
    ) {
        getAssetDataJob?.cancel()

        getAssetDataJob = viewModelScope.launch(Dispatchers.IO) {
            val result = repository.getInvestmentProductEtfData(
                accessToken = accessToken,
                idToken = idToken,
                assetID = assetID
            )

            withContext(Dispatchers.Main) {
                when (result) {
                    is DomainResult.Success -> {
                        assetData = result.body

                        refreshButtons()
                        refreshDataItems()
                    }

                    else -> {
                        // TODO: Handle the case
                    }
                }
            }
        }
    }

    private fun getInvestmentProducts(
        accessToken: String,
        idToken: String
    ) {
        getInvestmentProductsJob?.cancel()

        getInvestmentProductsJob = viewModelScope.launch {
            val result = getInvestmentProductsUseCase(
                accessToken = accessToken,
                idToken = idToken,
                shouldGetFromCache = true
            )

            when (result) {
                is NetworkResource.Success -> {
                    investmentProduct = result.data?.find {
                        it.commonID == asset?.id
                    }

                    refreshDataItems()
                }

                else -> {
                    // TODO: Handle the failure
                }
            }
        }
    }

    private fun cancelOrder(orderID: String) {
        _eventShowOrHideLoadingDialog.value = true

        getCredentialsCall { succeeded, accessToken, idToken ->
            if (!succeeded || accessToken == null || idToken == null) {
                // FIXME: Handle the error

                _eventShowOrHideLoadingDialog.value = false

                return@getCredentialsCall
            }

            viewModelScope.launch {
                val cancelResult = repository.cancelOrder(
                    accessToken = accessToken,
                    idToken = idToken,
                    orderID = orderID
                )

                if (cancelResult is DomainResult.Error) {
                    // FIXME: Handle the error

                    _eventShowOrHideLoadingDialog.value = false

                    return@launch
                }

                // FIXME: Cancel the previous Job? Or set a isCancelingTransaction flag
                //  to avoid duplicate calls

                preferencesRepository.putLastTransactionCreatedAt(Date().time)

                val getRecentActivityResult = repository.getAssetRecentActivity(
                    accessToken = accessToken,
                    idToken = idToken,
                    assetID = assetID,
                    limit = ORDERS_BATCH_SIZE
                )

                _eventShowOrHideLoadingDialog.value = false

                (getRecentActivityResult as? DomainResult.Success)?.let {
                    helper.recentActivityObjects =
                        AssetDetailsHelper.generateRecentActivityObjects(it.body)

                    refreshDataItems()
                }
            }
        }
    }

    private fun refreshDataItems() {
        _dataItems.value = generateDataItems()
    }

    private fun generateDataItems(): List<DataItem> {
        val finalAnswer = mutableListOf<DataItem>()

        finalAnswer.add(generateHeaderItem())

        generateTagsItem()?.let { finalAnswer.add(it) }

        generateTopHoldingsItem()?.let {
            finalAnswer.add(it)
        }

        finalAnswer.add(generateLineChartItem())

        generateMyInvestmentSectionItems()?.let {
            finalAnswer.addAll(it)
        }

        generateRecentActivitySectionItems()?.let {
            finalAnswer.addAll(it)
        }

        generateKeyFactsSectionItems()?.let {
            finalAnswer.addAll(it)
        }

        finalAnswer.addAll(generateMetricsSectionItems())

        generateAssetClassBreakdownSectionItems()?.let {
            finalAnswer.addAll(it)
        }

        generateRegionalBreakdownSectionItems()?.let {
            finalAnswer.addAll(it)
        }

        generateSectorsSectionItems()?.let {
            finalAnswer.addAll(it)
        }

        generateTopHoldingsSectionItems()?.let {
            finalAnswer.addAll(it)
        }

        generateAnalystViewsSectionItems()?.let {
            finalAnswer.addAll(it)
        }

        generateAveragePriceTargetItem()?.let {
            finalAnswer.add(it)
        }

        generateNewsSectionItems()?.let {
            finalAnswer.addAll(it)
        }

        generateKIDSectionItems()?.let {
            finalAnswer.addAll(it)
        }

        finalAnswer.add(generateDisclaimerItem())

        return finalAnswer
    }

    private fun generateHeaderItem(): DataItem.HeaderItem {
        val subtitle = "${asset?.tickerWithCurrency} • ${asset?.shortDescription}"

        val spacing16 = context.resources.getDimensionPixelSize(R.dimen.spacing_16)

        return DataItem.HeaderItem(
            id = "headerItem",
            paddingTop = spacing16,
            paddingBottom = null,
            imageDrawableRes = asset?.getIconDrawableRes(context),
            imageURI = asset?.generateProviderLogoURI(etfProviders),
            title = asset?.title,
            subtitle = subtitle
        )
    }

    private fun generateTagsItem(): DataItem.TagsItem? {
        val tagItems = generateTagItems()

        if (tagItems.isEmpty()) return null

        val spacing18 = context.resources.getDimensionPixelSize(R.dimen.spacing_18)

        return DataItem.TagsItem(
            id = "tagsItem",
            paddingStart = null,
            paddingTop = spacing18,
            paddingEnd = null,
            paddingBottom = null,
            tagItems = generateTagItems()
        )
    }

    private fun generateTagItems(): List<AssetDetailsTagsListAdapter.DataItem.TagItem> {
        val finalAnswer = mutableListOf<AssetDetailsTagsListAdapter.DataItem.TagItem>()

        assetData?.tags?.forEach { tag ->
            val tagItem = generateTagItemForTag(tag) ?: return@forEach

            finalAnswer.add(tagItem)
        }

        return finalAnswer
    }

    private fun generateTagItemForTag(tag: String): AssetDetailsTagsListAdapter.DataItem.TagItem? {
        val spacing2 = context.resources.getDimensionPixelSize(R.dimen.spacing_2)

        return when (tag) {
            "MARKET_CLOSED" -> {
                val tagProperties = MarketTagProperties.generateClosedMarketTagProperties(context)

                AssetDetailsTagsListAdapter.DataItem.TagItem(
                    id = TAG_MARKET_CLOSED_ITEM_ID,
                    paddingStart = spacing2,
                    paddingTop = null,
                    paddingEnd = spacing2,
                    paddingBottom = null,
                    backgroundTintColorRes = tagProperties.backgroundTintColorRes,
                    iconDrawableRes = tagProperties.iconDrawableRes,
                    iconTintColorRes = tagProperties.iconTintColorRes,
                    text = tagProperties.text,
                    arrowIconTintColorRes = tagProperties.arrowIconTintColorRes
                )
            }

            "MARKET_OPEN" -> {
                val tagProperties = MarketTagProperties.generateOpenMarketTagProperties(context)

                AssetDetailsTagsListAdapter.DataItem.TagItem(
                    id = TAG_MARKET_OPEN_ITEM_ID,
                    paddingStart = spacing2,
                    paddingTop = null,
                    paddingEnd = spacing2,
                    paddingBottom = null,
                    backgroundTintColorRes = tagProperties.backgroundTintColorRes,
                    iconDrawableRes = tagProperties.iconDrawableRes,
                    iconTintColorRes = tagProperties.iconTintColorRes,
                    text = tagProperties.text,
                    arrowIconTintColorRes = tagProperties.arrowIconTintColorRes
                )
            }

            "ADR" -> {
                AssetDetailsTagsListAdapter.DataItem.TagItem(
                    id = TAG_ADR_ITEM_ID,
                    paddingStart = spacing2,
                    paddingTop = null,
                    paddingEnd = spacing2,
                    paddingBottom = null,
                    backgroundTintColorRes = R.color.primary_5,
                    iconDrawableRes = R.drawable.ic_public,
                    iconTintColorRes = R.color.primary_50,
                    text = context.resources.getString(R.string.adr_label),
                    arrowIconTintColorRes = R.color.primary_50
                )
            }

            "COMMISSION_FREE" -> {
                AssetDetailsTagsListAdapter.DataItem.TagItem(
                    id = TAG_COMMISSION_FREE_ITEM_ID,
                    paddingStart = spacing2,
                    paddingTop = null,
                    paddingEnd = spacing2,
                    paddingBottom = null,
                    backgroundTintColorRes = R.color.primary_5,
                    iconDrawableRes = R.drawable.ic_money_off,
                    iconTintColorRes = R.color.primary_50,
                    text = context.resources.getString(R.string.commission_free_label),
                    arrowIconTintColorRes = R.color.primary_50
                )
            }

            "FRACTIONAL" -> {
                AssetDetailsTagsListAdapter.DataItem.TagItem(
                    id = TAG_FRACTIONAL_ITEM_ID,
                    paddingStart = spacing2,
                    paddingTop = null,
                    paddingEnd = spacing2,
                    paddingBottom = null,
                    backgroundTintColorRes = R.color.primary_5,
                    iconDrawableRes = R.drawable.ic_layers_filled,
                    iconTintColorRes = R.color.primary_50,
                    text = context.resources.getString(R.string.fractional_label),
                    arrowIconTintColorRes = R.color.primary_50
                )
            }

            "SMART_EXECUTION" -> {
                AssetDetailsTagsListAdapter.DataItem.TagItem(
                    id = TAG_SMART_EXECUTION_ITEM_ID,
                    paddingStart = spacing2,
                    paddingTop = null,
                    paddingEnd = spacing2,
                    paddingBottom = null,
                    backgroundTintColorRes = R.color.primary_5,
                    iconDrawableRes = R.drawable.magic_stars,
                    iconTintColorRes = R.color.primary_50,
                    text = context.resources.getString(R.string.smart_execution_label),
                    arrowIconTintColorRes = R.color.primary_50
                )
            }

            else -> null
        }
    }

    private fun generateTopHoldingsItem(): DataItem.TopHoldingsItem? {
        if (asset?.isStock() == true) return null
        if (asset?.assetClassID == "readyMade") return null

        val topHoldingsIconURLs = generateTopHoldingsIconURLs()
        if (topHoldingsIconURLs.isNullOrEmpty()) return null

        val spacing18 = context.resources.getDimensionPixelSize(R.dimen.spacing_18)

        val totalMoreText = assetData?.holdingsCount?.let { holdingsCount ->
            "+${holdingsCount - 10}"
        }

        return DataItem.TopHoldingsItem(
            id = "topHoldingsItem",
            paddingTop = spacing18,
            paddingBottom = null,
            holdingsIconURLs = topHoldingsIconURLs,
            totalMoreText = totalMoreText
        )
    }

    private fun generateLineChartItem(): DataItem.LineChartItem {
        // TODO: Do not re-generate the line chart data, just update the appropriate entry icons
        val chartEntries = generateLineChartData()
        val finalSelectedChartEntry = (selectedChartEntry ?: chartEntries.lastOrNull())

        var description: String? = null
        var descriptionColor: Int? = null
        var arrowIcon: Int? = null

        var dateText: String? = null

        val lastEntry = chartEntries.lastOrNull()

        if (selectedChartEntry != lastEntry && selectedChartEntry != null) {
            dateText = generatePerformanceDescriptionForEntry(entry = selectedChartEntry)
        } else {
            val tenor = findSelectedTenor()

            tenor?.returns?.let { returns ->
                description = (returns / 100.0).formatAsPercentage(
                    minimumFractionDigits = 2,
                    maximumFractionDigits = 2,
                    locale = userLocale
                )

                if (returns >= 0) {
                    descriptionColor = ContextCompat.getColor(
                        context,
                        R.color.system_alerts_success_color
                    )

                    arrowIcon = R.drawable.ic_arrow_drop_up_rounded
                } else {
                    descriptionColor = ContextCompat.getColor(
                        context,
                        R.color.system_alerts_danger_color
                    )

                    arrowIcon = R.drawable.ic_arrow_drop_down_rounded
                }
            }
        }

        val totalValue = selectedChartEntry?.y ?: investmentProduct?.tradedPrice

        val totalText = totalValue?.let { selectedPrice ->
            investmentProduct?.tradedCurrency?.let { currencyISOCode ->
                selectedPrice.generateFormattedCurrency(
                    currencyISOCode = currencyISOCode,
                    locale = userLocale
                )
            } ?: selectedPrice.toString()
        }

        return DataItem.LineChartItem(
            id = "lineChartItem",
            totalText = totalText,
            currencyISOCode = investmentProduct?.tradedCurrency,
            description = description,
            descriptionColor = descriptionColor,
            descriptionDrawableRes = arrowIcon,
            dateText = dateText,
            chartEntries = chartEntries,
            networkCallState = chartDataCallState,
            selectedChartEntry = finalSelectedChartEntry,
            selectedDateRange = selectedDateRange
        )
    }

    private fun generateMyInvestmentSectionItems(): List<DataItem>? {
        val investmentDetails = investmentDetails ?: return null

        val finalAnswer = mutableListOf<DataItem>()

        finalAnswer.add(generateMyInvestmentSectionHeader())
        finalAnswer.addAll(generateMyInvestmentInfoRows(investmentDetails))

        return finalAnswer
    }

    private fun generateMyInvestmentSectionHeader(): DataItem.SectionHeaderItem {
        val spacing16 = context.resources.getDimensionPixelSize(R.dimen.spacing_16)
        val spacing56 = context.resources.getDimensionPixelSize(R.dimen.spacing_56)

        val title = context.resources.getString(R.string.my_investment)

        return DataItem.SectionHeaderItem(
            id = "myInvestmentSectionHeader",
            paddingStart = spacing16,
            paddingTop = spacing56,
            paddingEnd = spacing16,
            paddingBottom = spacing16,
            textAppearanceRes = R.style.HeadingsH3Mobile_PrimaryColor,
            title = title,
            shouldShowInfoButton = false,
            shouldShowSeeAllButton = false
        )
    }

    private fun generateMyInvestmentInfoRows(investmentDetails: GetInvestmentDetailsResponse): List<DataItem.DoubleInfoRowItem> {
        val finalAnswer = mutableListOf<DataItem.DoubleInfoRowItem>()

        val spacing24 = context.resources.getDimensionPixelSize(R.dimen.spacing_24)
        val spacing32 = context.resources.getDimensionPixelSize(R.dimen.spacing_32)

        val secondLabelTextColor = if (investmentDetails.isPerformancePositive == true) {
            ContextCompat.getColor(context, R.color.green_50)
        } else {
            ContextCompat.getColor(context, R.color.red_50)
        }

        finalAnswer.add(
            DataItem.DoubleInfoRowItem(
                id = "firstMyInvestmentDoubleInfoRow",
                backgroundRes = R.drawable.company_details_info_row_header_background,
                paddingTop = spacing32,
                paddingBottom = null,
                firstLabelText = investmentDetails.currentValue,
                firstLabelTextColor = ContextCompat.getColor(context, R.color.primary_50),
                firstValueText = context.resources.getString(R.string.metric_current_value),
                firstDescriptionType = null,
                secondLabelText = investmentDetails.performanceValue,
                secondLabelTextColor = secondLabelTextColor,
                secondValueText = investmentDetails.performancePercentage,
                secondDescriptionType = null
            )
        )

        finalAnswer.add(
            DataItem.DoubleInfoRowItem(
                id = "secondMyInvestmentDoubleInfoRow",
                backgroundRes = null,
                paddingTop = spacing24,
                paddingBottom = null,
                firstLabelText = investmentDetails.numberOfShares,
                firstLabelTextColor = ContextCompat.getColor(context, R.color.primary_50),
                firstValueText = context.resources.getString(R.string.metric_shares_number),
                firstDescriptionType = null,
                secondLabelText = investmentDetails.averagePricePerShare,
                secondLabelTextColor = ContextCompat.getColor(context, R.color.primary_50),
                secondValueText = context.resources.getString(R.string.avg_buy_price_label),
                secondDescriptionType = null
            )
        )

        finalAnswer.add(
            DataItem.DoubleInfoRowItem(
                id = "thirdMyInvestmentDoubleInfoRow",
                backgroundRes = R.drawable.company_details_info_row_footer_background,
                paddingTop = spacing24,
                paddingBottom = spacing32,
                firstLabelText = investmentDetails.portfolioAllocation,
                firstLabelTextColor = ContextCompat.getColor(context, R.color.primary_50),
                firstValueText = context.resources.getString(R.string.metric_portfolio_allocation),
                firstDescriptionType = null,
                secondLabelText = investmentDetails.totalDividends,
                secondLabelTextColor = ContextCompat.getColor(context, R.color.primary_50),
                secondValueText = context.resources.getString(R.string.lifetime_dividends_label),
                secondDescriptionType = null
            )
        )

        return finalAnswer
    }

    private fun generateRecentActivitySectionItems(): List<DataItem>? {
        // 1. We need the user to show the full user name in the receipts.
        // 2. We need the currentTickerPrice to show the correct prices and number of shares.
        // 3. We also need the currentTickerCurrency for the order reviews but the (2) check is enough.

        if (user == null) return null
        val investmentProduct = investmentProduct ?: return null

        val transactionDataItems = AssetDetailsHelper.generateTransactionDataItems(
            context = context,
            currencyISOCode = currencyISOCode,
            userLocale = userLocale,
            assetTitle = asset?.title,
            assetDrawableRes = asset?.getIconDrawableRes(context),
            assetIconURI = asset?.generateProviderLogoURI(etfProviders),
            recentActivityObjects = helper.recentActivityObjects
        )

        if (transactionDataItems.isNullOrEmpty()) return null

        val finalAnswer = mutableListOf<DataItem>()

        finalAnswer.add(generateRecentActivitySectionHeader())
        finalAnswer.addAll(transactionDataItems)

        return finalAnswer
    }

    private fun generateRecentActivitySectionHeader(): DataItem.SectionHeaderItem {
        val spacing16 = context.resources.getDimensionPixelSize(R.dimen.spacing_16)
        val spacing56 = context.resources.getDimensionPixelSize(R.dimen.spacing_56)

        val title = context.resources.getString(R.string.recent_activity)

        return DataItem.SectionHeaderItem(
            id = "recentActivitySectionHeader",
            paddingStart = spacing16,
            paddingTop = spacing56,
            paddingEnd = spacing16,
            paddingBottom = spacing16,
            textAppearanceRes = R.style.HeadingsH3Mobile_PrimaryColor,
            title = title,
            shouldShowInfoButton = false,
            shouldShowSeeAllButton = true
        )
    }

    private fun generateMetricsSectionItems(): List<DataItem> {
        val finalAnswer = mutableListOf<DataItem>()

        finalAnswer.add(generateMetricsSectionHeader())

        if (asset?.isStock() == true) {
            finalAnswer.addAll(generateStockMetricsInfoRows())
        } else {
            finalAnswer.addAll(generateETFMetricsInfoRows())
        }

        return finalAnswer
    }

    private fun generateMetricsSectionHeader(): DataItem.SectionHeaderItem {
        val spacing16 = context.resources.getDimensionPixelSize(R.dimen.spacing_16)
        val spacing56 = context.resources.getDimensionPixelSize(R.dimen.spacing_56)

        val title = context.resources.getString(R.string.insight_tab_metrics)

        return DataItem.SectionHeaderItem(
            id = "metricsSectionHeader",
            paddingStart = spacing16,
            paddingTop = spacing56,
            paddingEnd = spacing16,
            paddingBottom = spacing16,
            textAppearanceRes = R.style.HeadingsH3Mobile_PrimaryColor,
            title = title,
            shouldShowInfoButton = false,
            shouldShowSeeAllButton = false
        )
    }

    private fun generateStockMetricsInfoRows(): List<DataItem.DoubleInfoRowItem> {
        val finalAnswer = mutableListOf<DataItem.DoubleInfoRowItem>()

        val spacing24 = context.resources.getDimensionPixelSize(R.dimen.spacing_24)
        val spacing32 = context.resources.getDimensionPixelSize(R.dimen.spacing_32)

        val labelColor = ContextCompat.getColor(context, R.color.primary_50)

        finalAnswer.add(
            DataItem.DoubleInfoRowItem(
                id = "firstStockMetricsDoubleInfoRow",
                backgroundRes = R.drawable.company_details_info_row_header_background,
                paddingTop = spacing32,
                paddingBottom = null,
                firstLabelText = assetData?.metrics?.marketCap,
                firstLabelTextColor = labelColor,
                firstValueText = context.resources.getString(R.string.market_cap_label),
                firstDescriptionType = DescriptionTypeEnum.MARKET_CAP,
                secondLabelText = assetData?.metrics?.beta,
                secondLabelTextColor = labelColor,
                secondValueText = context.resources.getString(R.string.beta_label),
                secondDescriptionType = DescriptionTypeEnum.BETA
            )
        )

        finalAnswer.add(
            DataItem.DoubleInfoRowItem(
                id = "secondStockMetricsDoubleInfoRow",
                backgroundRes = null,
                paddingTop = spacing24,
                paddingBottom = null,
                firstLabelText = assetData?.metrics?.peRatio,
                firstLabelTextColor = labelColor,
                firstValueText = context.resources.getString(R.string.p_e_ratio_label),
                firstDescriptionType = DescriptionTypeEnum.P_E_RATIO,
                secondLabelText = assetData?.metrics?.forwardPE,
                secondLabelTextColor = labelColor,
                secondValueText = context.resources.getString(R.string.metric_forward_p_e_ratio),
                secondDescriptionType = DescriptionTypeEnum.FORWARD_P_E_RATIO
            )
        )

        finalAnswer.add(
            DataItem.DoubleInfoRowItem(
                id = "thirdStockMetricsDoubleInfoRow",
                backgroundRes = R.drawable.company_details_info_row_footer_background,
                paddingTop = spacing24,
                paddingBottom = spacing32,
                firstLabelText = assetData?.metrics?.eps,
                firstLabelTextColor = labelColor,
                firstValueText = context.resources.getString(R.string.eps_label),
                firstDescriptionType = DescriptionTypeEnum.EPS,
                secondLabelText = assetData?.metrics?.dividendYield,
                secondLabelTextColor = labelColor,
                secondValueText = context.resources.getString(R.string.metric_dividend_yield),
                secondDescriptionType = DescriptionTypeEnum.DIVIDEND_YIELD
            )
        )

        return finalAnswer
    }

    private fun generateETFMetricsInfoRows(): List<DataItem.DoubleInfoRowItem> {
        val finalAnswer = mutableListOf<DataItem.DoubleInfoRowItem>()

        val spacing24 = context.resources.getDimensionPixelSize(R.dimen.spacing_24)
        val spacing32 = context.resources.getDimensionPixelSize(R.dimen.spacing_32)

        val labelColor = ContextCompat.getColor(context, R.color.primary_50)

        @DrawableRes val firstItemBackgroundRes: Int
        val firstItemPaddingBottom: Int?

        if (asset?.assetClassID == "commodities" || asset?.assetClassID == "readyMade") {
            // Commodities and readyMade have only one info row, so we need
            // 1. all corners to be rounded and
            // 2. bottom padding

            firstItemBackgroundRes = R.drawable.company_details_info_row_background
            firstItemPaddingBottom = spacing32
        } else {
            firstItemBackgroundRes = R.drawable.company_details_info_row_header_background
            firstItemPaddingBottom = null
        }

        finalAnswer.add(
            DataItem.DoubleInfoRowItem(
                id = "firstETFMetricsDoubleInfoRow",
                backgroundRes = firstItemBackgroundRes,
                paddingTop = spacing32,
                paddingBottom = firstItemPaddingBottom,
                firstLabelText = assetData?.indexStats?.expectedReturn,
                firstLabelTextColor = labelColor,
                firstValueText = context.resources.getString(R.string.annualised_return_label),
                firstDescriptionType = DescriptionTypeEnum.ETF_METRIC_EXPECTED_RETURN,
                secondLabelText = assetData?.indexStats?.annualRisk,
                secondLabelTextColor = labelColor,
                secondValueText = context.resources.getString(R.string.metric_risk),
                secondDescriptionType = DescriptionTypeEnum.ETF_METRIC_RISK
            )
        )

        if (asset?.assetClassID == "bonds") {
            finalAnswer.add(
                DataItem.DoubleInfoRowItem(
                    id = "secondETFMetricsDoubleInfoRow",
                    backgroundRes = R.drawable.company_details_info_row_footer_background,
                    paddingTop = spacing24,
                    paddingBottom = spacing32,
                    firstLabelText = assetData?.indexStats?.bondYield,
                    firstLabelTextColor = labelColor,
                    firstValueText = context.resources.getString(R.string.metric_yield),
                    firstDescriptionType = DescriptionTypeEnum.YIELD,
                    secondLabelText = assetData?.indexStats?.coupon,
                    secondLabelTextColor = labelColor,
                    secondValueText = context.resources.getString(R.string.metric_coupon),
                    secondDescriptionType = DescriptionTypeEnum.COUPON
                )
            )
        } else if (asset?.assetClassID == "realEstate" || asset?.assetClassID == "equities") {
            finalAnswer.add(
                DataItem.DoubleInfoRowItem(
                    id = "secondETFMetricsDoubleInfoRow",
                    backgroundRes = R.drawable.company_details_info_row_footer_background,
                    paddingTop = spacing24,
                    paddingBottom = spacing32,
                    firstLabelText = assetData?.indexStats?.fpEarnings,
                    firstLabelTextColor = labelColor,
                    firstValueText = context.resources.getString(R.string.metric_forward_p_e_ratio),
                    firstDescriptionType = DescriptionTypeEnum.ETF_METRIC_FORWARD_P_E_RATIO,
                    secondLabelText = assetData?.indexStats?.dividendYield,
                    secondLabelTextColor = labelColor,
                    secondValueText = context.resources.getString(R.string.metric_dividend_yield),
                    secondDescriptionType = DescriptionTypeEnum.ETF_METRIC_DIVIDEND_YIELD
                )
            )
        }

        return finalAnswer
    }

    private fun generateAssetClassBreakdownSectionItems(): List<DataItem>? {
        if (asset?.assetClassID != "readyMade") return null

        val finalAnswer = mutableListOf<DataItem>()

        finalAnswer.add(generateAssetClassBreakdownSectionHeader())
        finalAnswer.addAll(generateAssetClassBreakdownMirrorProgressItems())

        return finalAnswer
    }

    private fun generateAssetClassBreakdownSectionHeader(): DataItem.SectionHeaderItem {
        val spacing16 = context.resources.getDimensionPixelSize(R.dimen.spacing_16)
        val spacing56 = context.resources.getDimensionPixelSize(R.dimen.spacing_56)

        val title = context.resources.getString(R.string.asset_class_breakdown_label)

        return DataItem.SectionHeaderItem(
            id = "assetClassBreakdownSectionHeader",
            paddingStart = spacing16,
            paddingTop = spacing56,
            paddingEnd = spacing16,
            paddingBottom = spacing16,
            textAppearanceRes = R.style.HeadingsH3Mobile_PrimaryColor,
            title = title,
            shouldShowInfoButton = false,
            shouldShowSeeAllButton = false
        )
    }

    private fun generateAssetClassBreakdownMirrorProgressItems(): List<DataItem> {
        val finalAnswer = mutableListOf<DataItem>()

        val spacing0 = context.resources.getDimensionPixelSize(R.dimen.spacing_0)
        val spacing8 = context.resources.getDimensionPixelSize(R.dimen.spacing_8)

        val assetClassBreakdownItems = asset?.generateAssetClassBreakdownItems(
            assetClasses = allAssetClasses
        )

        assetClassBreakdownItems?.forEachIndexed { index, item ->
            val itemID = "assetClassBreakdownMirrorProgressItem_$index"

            val text = item.generateTitle(context)
            val progress = (item.allocation * 100).toInt()
            val progressText = item.generateFormattedAllocation(userLocale)

            val paddingTop = if (finalAnswer.isEmpty()) spacing0 else spacing8

            val color = item.assetClass.colorClass?.convertToColor() ?: ContextCompat.getColor(
                context,
                R.color.materials
            )

            finalAnswer.add(
                DataItem.MirrorProgressItem(
                    id = itemID,
                    paddingTop = paddingTop,
                    paddingBottom = null,
                    drawableRes = null,
                    color = color,
                    text = text,
                    progress = progress,
                    progressText = progressText
                )
            )
        }

        return finalAnswer
    }

    private fun generateAnalystViewsSectionItems(): List<DataItem>? {
        if (asset?.isStock() != true) return null

        val progressItems = generateAnalystViewsProgressItems()

        if (progressItems.isEmpty()) return null

        val finalAnswer = mutableListOf<DataItem>()

        finalAnswer.add(generateAnalystViewsSectionHeaderItem())
        finalAnswer.addAll(progressItems)

        return finalAnswer
    }

    private fun generateAnalystViewsSectionHeaderItem(): DataItem.SectionHeaderItem {
        val spacing16 = context.resources.getDimensionPixelSize(R.dimen.spacing_16)
        val spacing56 = context.resources.getDimensionPixelSize(R.dimen.spacing_56)

        val title = context.resources.getString(R.string.analyst_views_label)

        return DataItem.SectionHeaderItem(
            id = ANALYST_VIEWS_SECTION_HEADER_ITEM_ID,
            paddingStart = spacing16,
            paddingTop = spacing56,
            paddingEnd = spacing16,
            paddingBottom = spacing16,
            textAppearanceRes = R.style.HeadingsH3Mobile_PrimaryColor,
            title = title,
            shouldShowInfoButton = true,
            shouldShowSeeAllButton = false
        )
    }

    private fun generateAnalystViewsProgressItems(): List<DataItem.ProgressItem> {
        val finalAnswer = mutableListOf<DataItem.ProgressItem>()

        val spacing16 = context.resources.getDimensionPixelSize(R.dimen.spacing_16)

        val greenColor = ContextCompat.getColor(context, R.color.green_50)
        val redColor = ContextCompat.getColor(context, R.color.red_50)
        val yellowColor = ContextCompat.getColor(context, R.color.yellow_30)

        assetData?.analystViews?.percentageBuy?.let { percentage ->
            val formattedPercentage = (percentage / 100.0).formatAsPercentage(
                minimumFractionDigits = 0,
                maximumFractionDigits = 0,
                locale = userLocale
            )

            val text = context.resources.getString(
                R.string.percentage_buy_label,
                formattedPercentage
            )
            val isTextSelected = (assetData?.analystViews?.isMajority == "buy")

            finalAnswer.add(
                DataItem.ProgressItem(
                    id = "buyProgressItem",
                    paddingTop = spacing16,
                    paddingBottom = null,
                    color = greenColor,
                    progress = percentage,
                    text = text,
                    isTextSelected = isTextSelected,
                    footerText = null
                )
            )
        }

        assetData?.analystViews?.percentageHold?.let { percentage ->
            val formattedPercentage = (percentage / 100.0).formatAsPercentage(
                minimumFractionDigits = 0,
                maximumFractionDigits = 0,
                locale = userLocale
            )

            val text = context.resources.getString(
                R.string.percentage_hold_label,
                formattedPercentage
            )
            val isTextSelected = (assetData?.analystViews?.isMajority == "hold")

            finalAnswer.add(
                DataItem.ProgressItem(
                    id = "holdProgressItem",
                    paddingTop = spacing16,
                    paddingBottom = null,
                    color = yellowColor,
                    progress = percentage,
                    text = text,
                    isTextSelected = isTextSelected,
                    footerText = null
                )
            )
        }

        // FIXME: FooterText won't show if there is no percentageSell

        assetData?.analystViews?.percentageSell?.let { percentage ->
            val formattedPercentage = (percentage / 100.0).formatAsPercentage(
                minimumFractionDigits = 0,
                maximumFractionDigits = 0,
                locale = userLocale
            )

            val text = context.resources.getString(
                R.string.percentage_sell_label,
                formattedPercentage
            )
            val isTextSelected = (assetData?.analystViews?.isMajority == "sell")

            val footerText = context.resources.getString(
                R.string.asset_details_total_analysts_message,
                assetData?.analystViews?.totalAnalysts?.toString()
            )

            finalAnswer.add(
                DataItem.ProgressItem(
                    id = "sellProgressItem",
                    paddingTop = spacing16,
                    paddingBottom = null,
                    color = redColor,
                    progress = percentage,
                    text = text,
                    isTextSelected = isTextSelected,
                    footerText = footerText
                )
            )
        }

        return finalAnswer
    }

    private fun generateKeyFactsSectionItems(): List<DataItem>? {
        if (asset?.isStock() == true) return null

        val finalAnswer = mutableListOf<DataItem>()

        finalAnswer.add(generateKeyFactsSectionHeaderItem())
        finalAnswer.addAll(generateKeyFactsInfoRows())

        return finalAnswer
    }

    private fun generateKeyFactsSectionHeaderItem(): DataItem.SectionHeaderItem {
        val spacing16 = context.resources.getDimensionPixelSize(R.dimen.spacing_16)
        val spacing56 = context.resources.getDimensionPixelSize(R.dimen.spacing_56)

        val title = context.resources.getString(R.string.tab_key_facts)

        return DataItem.SectionHeaderItem(
            id = "keyFactsSectionHeader",
            paddingStart = spacing16,
            paddingTop = spacing56,
            paddingEnd = spacing16,
            paddingBottom = spacing16,
            textAppearanceRes = R.style.HeadingsH3Mobile_PrimaryColor,
            title = title,
            shouldShowInfoButton = false,
            shouldShowSeeAllButton = false
        )
    }

    private fun generateKeyFactsInfoRows(): List<DataItem.DoubleInfoRowItem> {
        val finalAnswer = mutableListOf<DataItem.DoubleInfoRowItem>()

        val spacing24 = context.resources.getDimensionPixelSize(R.dimen.spacing_24)
        val spacing32 = context.resources.getDimensionPixelSize(R.dimen.spacing_32)

        val labelColor = ContextCompat.getColor(context, R.color.primary_50)

        finalAnswer.add(
            DataItem.DoubleInfoRowItem(
                id = "firstKeyFactsDoubleInfoRow",
                backgroundRes = R.drawable.company_details_info_row_header_background,
                paddingTop = spacing32,
                paddingBottom = null,
                firstLabelText = assetData?.baseCurrency,
                firstLabelTextColor = labelColor,
                firstValueText = context.resources.getString(R.string.metric_base_currency),
                firstDescriptionType = DescriptionTypeEnum.BASE_CURRENCY,
                secondLabelText = assetData?.about?.income,
                secondLabelTextColor = labelColor,
                secondValueText = context.resources.getString(R.string.metric_income),
                secondDescriptionType = DescriptionTypeEnum.INCOME
            )
        )

        finalAnswer.add(
            DataItem.DoubleInfoRowItem(
                id = "secondKeyFactsDoubleInfoRow",
                backgroundRes = R.drawable.company_details_info_row_footer_background,
                paddingTop = spacing24,
                paddingBottom = spacing32,
                firstLabelText = assetData?.getFormattedExpenseRatio(),
                firstLabelTextColor = labelColor,
                firstValueText = context.resources.getString(R.string.metric_expense_ratio),
                firstDescriptionType = DescriptionTypeEnum.EXPENSE_RATIO,
                secondLabelText = assetData?.about?.provider,
                secondLabelTextColor = labelColor,
                secondValueText = context.resources.getString(R.string.provider_label),
                secondDescriptionType = null
            )
        )

        return finalAnswer
    }

    private fun generateRegionalBreakdownSectionItems(): List<DataItem>? {
        if (asset?.isStock() == true) return null

        val mirrorProgressItems = generateRegionalBreakdownMirrorProgressItems()
        if (mirrorProgressItems.isEmpty()) return null

        val finalAnswer = mutableListOf<DataItem>()

        finalAnswer.add(generateRegionalBreakdownSectionHeaderItem())
        finalAnswer.addAll(mirrorProgressItems)

        return finalAnswer
    }

    private fun generateRegionalBreakdownSectionHeaderItem(): DataItem.SectionHeaderItem {
        val spacing16 = context.resources.getDimensionPixelSize(R.dimen.spacing_16)
        val spacing56 = context.resources.getDimensionPixelSize(R.dimen.spacing_56)

        val title = context.resources.getString(R.string.regional_breakdown_label)

        return DataItem.SectionHeaderItem(
            id = "regionalBreakdownSectionHeader",
            paddingStart = spacing16,
            paddingTop = spacing56,
            paddingEnd = spacing16,
            paddingBottom = spacing16,
            textAppearanceRes = R.style.HeadingsH3Mobile_PrimaryColor,
            title = title,
            shouldShowInfoButton = false,
            shouldShowSeeAllButton = false
        )
    }

    private fun generateRegionalBreakdownMirrorProgressItems(): List<DataItem> {
        val finalAnswer = mutableListOf<DataItem>()

        val spacing0 = context.resources.getDimensionPixelSize(R.dimen.spacing_0)
        val spacing8 = context.resources.getDimensionPixelSize(R.dimen.spacing_8)

        assetData?.geographyDistribution?.forEachIndexed { index, distributionItem ->
            val percentage = distributionItem.percentage ?: return@forEachIndexed

            val paddingTop = if (finalAnswer.isEmpty()) spacing0 else spacing8

            finalAnswer.add(
                DataItem.MirrorProgressItem(
                    id = "regionalBreakdownMirrorProgressItem_$index",
                    paddingTop = paddingTop,
                    paddingBottom = null,
                    drawableRes = null,
                    color = ContextCompat.getColor(context, R.color.primary_50),
                    text = distributionItem.name,
                    progress = percentage.toInt(),
                    progressText = (percentage / 100).formatAsPercentage(locale = userLocale)
                )
            )
        }

        return finalAnswer
    }

    private fun generateSectorsSectionItems(): List<DataItem>? {
        if (asset?.isStock() == true) return null

        val mirrorProgressItems = generateSectorsMirrorProgressItems()
        if (mirrorProgressItems.isEmpty()) return null

        val finalAnswer = mutableListOf<DataItem>()

        finalAnswer.add(generateSectorsSectionHeaderItem())
        finalAnswer.addAll(mirrorProgressItems)

        return finalAnswer
    }

    private fun generateSectorsSectionHeaderItem(): DataItem.SectionHeaderItem {
        val spacing16 = context.resources.getDimensionPixelSize(R.dimen.spacing_16)
        val spacing56 = context.resources.getDimensionPixelSize(R.dimen.spacing_56)

        val title = context.resources.getString(R.string.sectors_label)

        return DataItem.SectionHeaderItem(
            id = "sectorsSectionHeader",
            paddingStart = spacing16,
            paddingTop = spacing56,
            paddingEnd = spacing16,
            paddingBottom = spacing16,
            textAppearanceRes = R.style.HeadingsH3Mobile_PrimaryColor,
            title = title,
            shouldShowInfoButton = false,
            shouldShowSeeAllButton = false
        )
    }

    private fun generateSectorsMirrorProgressItems(): List<DataItem> {
        val finalAnswer = mutableListOf<DataItem>()

        val spacing0 = context.resources.getDimensionPixelSize(R.dimen.spacing_0)
        val spacing8 = context.resources.getDimensionPixelSize(R.dimen.spacing_8)

        assetData?.sectorDistribution?.forEachIndexed { index, distributionItem ->
            val itemID = distributionItem.id ?: return@forEachIndexed
            val percentage = distributionItem.percentage ?: return@forEachIndexed

            val paddingTop = if (finalAnswer.isEmpty()) spacing0 else spacing8

            val designProperties = helper.generateSectorDistributionItemDesignProperties(itemID)

            finalAnswer.add(
                DataItem.MirrorProgressItem(
                    id = itemID,
                    paddingTop = paddingTop,
                    paddingBottom = null,
                    drawableRes = designProperties.first,
                    color = ContextCompat.getColor(context, designProperties.second),
                    text = distributionItem.name,
                    progress = percentage.toInt(),
                    progressText = (percentage / 100).formatAsPercentage(locale = userLocale)
                )
            )
        }

        return finalAnswer
    }

    private fun generateTopHoldingsSectionItems(): List<DataItem>? {
        val topHoldingItems = generateTopHoldingItems()

        if (topHoldingItems.isEmpty()) return null

        val finalAnswer = mutableListOf<DataItem>()

        finalAnswer.add(generateTopHoldingsSectionHeaderItem())
        finalAnswer.addAll(topHoldingItems)

        return finalAnswer
    }

    private fun generateTopHoldingsSectionHeaderItem(): DataItem.SectionHeaderItem {
        val spacing16 = context.resources.getDimensionPixelSize(R.dimen.spacing_16)
        val spacing56 = context.resources.getDimensionPixelSize(R.dimen.spacing_56)

        val title = context.resources.getString(R.string.top_ten_holdings)

        return DataItem.SectionHeaderItem(
            id = "topHoldingsSectionHeader",
            paddingStart = spacing16,
            paddingTop = spacing56,
            paddingEnd = spacing16,
            paddingBottom = spacing16,
            textAppearanceRes = R.style.HeadingsH3Mobile_PrimaryColor,
            title = title,
            shouldShowInfoButton = false,
            shouldShowSeeAllButton = false
        )
    }

    private fun generateTopHoldingItems(): List<DataItem.TopHoldingItem> {
        val finalAnswer = mutableListOf<DataItem.TopHoldingItem>()

        assetData?.topHoldings?.forEachIndexed { index, holding ->
            // Στα Bonds δε θέλουμε να δείχνουμε το icon.

            val iconUrl = if (asset?.assetClassID == "bonds") {
                null
            } else holding.logoUrl

            finalAnswer.add(
                DataItem.TopHoldingItem(
                    id = "topHoldingItem_$index",
                    iconUrl = iconUrl,
                    title = holding.name,
                    subtitle = holding.weight
                )
            )
        }

        return finalAnswer
    }

    private fun generateAveragePriceTargetItem(): DataItem.AveragePriceTargetItem? {
        if (asset?.isStock() != true) return null

        val change = assetData?.analystViews?.priceTargetPercentageDifference ?: return null
        val changeText = "($change)"

        val changeTextColor =
            if (assetData?.analystViews?.isPriceTargetPercentageDifferencePositive == true) {
                ContextCompat.getColor(context, R.color.green_50)
            } else {
                ContextCompat.getColor(context, R.color.red_50)
            }

        val spacing32 = context.resources.getDimensionPixelSize(R.dimen.spacing_32)

        return DataItem.AveragePriceTargetItem(
            id = "footerItem",
            paddingTop = spacing32,
            paddingBottom = null,
            amountText = assetData?.analystViews?.averagePriceTarget,
            changeText = changeText,
            changeTextColor = changeTextColor,
            shouldShowInfoButton = true
        )
    }

    private fun generateNewsSectionItems(): List<DataItem>? {
        val newsItems = generateNewsArticleItems()
        if (newsItems.isNullOrEmpty()) return null

        val finalAnswer = mutableListOf<DataItem>()

        finalAnswer.add(generateNewsSectionHeaderItem())
        finalAnswer.addAll(newsItems)

        return finalAnswer
    }

    private fun generateNewsSectionHeaderItem(): DataItem.SectionHeaderItem {
        val spacing16 = context.resources.getDimensionPixelSize(R.dimen.spacing_16)
        val spacing56 = context.resources.getDimensionPixelSize(R.dimen.spacing_56)

        val title = context.resources.getString(R.string.news_label)

        return DataItem.SectionHeaderItem(
            id = NEWS_SECTION_HEADER_ITEM_ID,
            paddingStart = spacing16,
            paddingTop = spacing56,
            paddingEnd = spacing16,
            paddingBottom = spacing16,
            textAppearanceRes = R.style.HeadingsH3Mobile_PrimaryColor,
            title = title,
            shouldShowInfoButton = false,
            shouldShowSeeAllButton = true
        )
    }

    private fun generateNewsArticleItems(): List<DataItem.NewsArticleItem>? {
        return assetData?.news?.mapNotNull { generateArticleItem(it) }
    }

    private fun generateArticleItem(article: AssetNewsArticle): DataItem.NewsArticleItem? {
        val articleID = article.id ?: return null

        val spacing8 = context.resources.getDimensionPixelSize(R.dimen.spacing_8)
        val spacing16 = context.resources.getDimensionPixelSize(R.dimen.spacing_16)

        val subtitleDrawableRes = when (article.sentiment) {
            "Positive" -> R.drawable.sentiment_good
            "Negative" -> R.drawable.sentiment_bad
            else -> R.drawable.sentiment_neutral
        }

        return DataItem.NewsArticleItem(
            id = articleID,
            paddingStart = spacing16,
            paddingTop = spacing8,
            paddingEnd = spacing16,
            paddingBottom = spacing8,
            imageURL = article.imageURL,
            title = article.title,
            subtitleDrawableRes = subtitleDrawableRes,
            subtitleCategoryText = article.source,
            subtitleTimeText = article.displayDate
        )
    }

    private fun generateKIDSectionItems(): List<DataItem>? {
        if (asset?.isStock() == true) return null

        val finalAnswer = mutableListOf<DataItem>()

        finalAnswer.add(generateKIDSectionHeaderItem())
        finalAnswer.add(generateKIDItem())

        return finalAnswer
    }

    private fun generateKIDSectionHeaderItem(): DataItem.SectionHeaderItem {
        val spacing16 = context.resources.getDimensionPixelSize(R.dimen.spacing_16)
        val spacing56 = context.resources.getDimensionPixelSize(R.dimen.spacing_56)

        val title = context.resources.getString(R.string.documents_label)

        return DataItem.SectionHeaderItem(
            id = "kidSectionHeader",
            paddingStart = spacing16,
            paddingTop = spacing56,
            paddingEnd = spacing16,
            paddingBottom = spacing16,
            textAppearanceRes = R.style.HeadingsH3Mobile_PrimaryColor,
            title = title,
            shouldShowInfoButton = false,
            shouldShowSeeAllButton = false
        )
    }

    private fun generateKIDItem(): DataItem.KIDItem {
        val spacing16 = context.resources.getDimensionPixelSize(R.dimen.spacing_16)

        return DataItem.KIDItem(
            id = "kidItem",
            paddingTop = spacing16,
            paddingBottom = spacing16
        )
    }

    private fun generateDisclaimerItem(): DataItem.DisclaimerItem {
        val spacing56 = context.resources.getDimensionPixelSize(R.dimen.spacing_56)

        return DataItem.DisclaimerItem(
            id = "disclaimerItem",
            paddingTop = spacing56,
            paddingBottom = null
        )
    }

    private fun refreshNavigationTitle() {
        _navigationTitleText.value = asset?.title
    }

    private fun refreshButtons() {
        hideButtons()

        if (!shouldShowActionButtons()) {
            return
        }

        _isBuyButtonVisible.value = true
        _isSellButtonVisible.value = shouldShowSellButton()
    }

    private fun hideButtons() {
        _isBuyButtonVisible.value = false
        _isSellButtonVisible.value = false
    }

    private fun shouldShowActionButtons(): Boolean {
        return ((shouldShowActionButtons ?: true) && assetData != null)
    }

    private fun generateLineChartData(): List<Entry> {
        val finalAnswer = mutableListOf<Entry>()

        val tenor = findSelectedTenor()

        val selectedChartEntryX = selectedChartEntry?.x

        tenor?.data?.forEachIndexed { index, element ->
            val timestamp = element.timestamp ?: return@forEachIndexed
            val price = element.price ?: return@forEachIndexed

            val x = index.toFloat()
            val y = price.toFloat()

            // Θέτουμε και data στο Entry κατά το initialization!
            val entry = Entry(x, y, element)

            entry.icon = iconForEntry(
                entry = entry,
                quantity = element.data?.quantity
            )

            finalAnswer.add(entry)
        }

        if (selectedChartEntryX == null) {
            val lastEntry = finalAnswer.lastOrNull()
            lastEntry?.icon = assetTransactionSelectionDrawable
        }

        return finalAnswer
    }

    private fun iconForEntry(entry: Entry?, quantity: Double?): Drawable? {
        val selectedChartEntryX = selectedChartEntry?.x

        if (entry?.x == selectedChartEntryX) {
            if (quantity == null) {
                return assetTransactionSelectionDrawable
            }

            return if (quantity < 0) {
                assetTransactionNegativeSelectionDrawable
            } else {
                assetTransactionPositiveSelectionDrawable
            }
        }

        return quantity?.let {
            if (quantity < 0) {
                lineChartEmptyCircleDrawable
            } else {
                lineChartSolidCircleDrawable
            }
        }
    }

    private fun shouldShowSellButton(): Boolean {
        val holding = helper.portfolio?.holdings?.find { it.assetCommonID == assetID }

        holding?.quantity?.let {
            if (holding.quantity > 0) {
                return true
            }
        }

        return false
    }

    private fun generatePerformanceDescriptionForEntry(entry: Entry?): String? {
        entry?.x?.let { x ->
            val tenor = findSelectedTenor()

            val pattern = if (tenor?.shouldShowTime == true) {
                "dd MMM yyyy, HH:mm"
            } else "dd MMM yyyy"

            val timestamp = (entry.data as? InvestmentProductTenorPrice)?.timestamp

            return timestamp?.generateFormattedDate(pattern)
        }

        return null
    }

    private fun generateETFInvestmentScreenArguments(): ETFInvestmentScreenArguments {
        val isMarketOpen = (assetData?.marketInfo?.isOpen == true)

        val nextMarketOpen = if (!isMarketOpen) {
            assetData?.marketInfo?.nextMarketOpen
        } else null

        return ETFInvestmentScreenArguments(
            etfID = asset?.id,
            etfName = asset?.title,
            nextMarketOpen = nextMarketOpen
        )
    }

    private fun refreshNavigationBarElevation(recyclerView: RecyclerView) {
        val offset = recyclerView.computeVerticalScrollOffset()

        val elevationRes = if (offset > 0) R.dimen.spacing_2 else R.dimen.spacing_0
        val elevation = context.resources.getDimension(elevationRes)

        _navigationBarElevation.value = elevation
    }

    private fun refreshNavigationBarTitleVisibility(recyclerView: RecyclerView) {
        _isNavigationTitleVisible.value = shouldShowNavigationBarTitle(recyclerView)
    }

    private fun shouldShowNavigationBarTitle(recyclerView: RecyclerView): Boolean {
        val view =
            recyclerView.findChildViewUnder(0f, 0f) ?: return false
        val viewHolder = recyclerView.findContainingViewHolder(view) ?: return false
        val position = viewHolder.absoluteAdapterPosition

        // There is also the following useful property: viewHolder.itemView.top

        if (position > 0) return true

        (viewHolder as? AssetDetailsHeaderViewHolder)?.let {
            val viewHolderTop = abs(viewHolder.itemView.top)
            val titleBottom = it.computeTitleBottom()

            return viewHolderTop >= titleBottom
        }

        return false
    }

    private fun generateTopHoldingsIconURLs(): List<String>? {
        return assetData?.topHoldings?.mapNotNull { it.logoUrl }?.filter { it.isNotBlank() }
    }

    private fun navigateToCompanyDetailsScreen() {
        val assetData = assetData ?: return

        val allocation = asset?.generateAssetClassBreakdownText(
            context = context,
            locale = userLocale,
            assetClasses = allAssetClasses
        )

        _eventShowCompanyDetailsDialog.value = AboutAssetScreenArgs(
            assetID = asset?.id,
            screenTitle = asset?.title,
            ticker = assetData.about?.ticker,
            exchange = assetData.about?.exchange,
            isin = assetData.about?.isin,
            sector = assetData.about?.sector,
            assetClass = assetData.about?.assetClass,
            provider = assetData.about?.provider,
            industry = assetData.about?.industry,
            ceo = assetData.about?.ceo,
            headquarters = assetData.about?.headquarters,
            employees = assetData.about?.employees,
            website = assetData.about?.website,
            description = assetData.about?.description,
            allocation = allocation,
            index = assetData.about?.index,
            replication = assetData.about?.replication
        )
    }

    private fun showFXFeeDescription() {
        val priceAPIKey = user?.subscription?.price
        val plan = plans.find { it.keyName == priceAPIKey }

        val formattedFXFee = plan?.getFXFee(preferencesRepository, basePlans)?.formatAsPercentage(
            locale = userLocale
        )

        _eventShowFXRateDescriptionDialog.value = formattedFXFee
    }

    private fun findSelectedTenor(): GetInvestmentProductPriceByTenorResponse.Tenor? {
        return when (selectedDateRange) {
            AssetDetailsDateRange.ONE_WEEK -> allChartTenors?.oneWeek
            AssetDetailsDateRange.ONE_MONTH -> allChartTenors?.oneMonth
            AssetDetailsDateRange.THREE_MONTHS -> allChartTenors?.threeMonths
            AssetDetailsDateRange.SIX_MONTHS -> allChartTenors?.sixMonths
            AssetDetailsDateRange.ONE_YEAR -> allChartTenors?.oneYear
            AssetDetailsDateRange.MAX -> allChartTenors?.max
        }
    }

    private fun showDescriptionForTag(itemID: String?) {
        val resources = context.resources

        var title: String? = null
        var description: String? = null

        when (itemID) {
            TAG_MARKET_CLOSED_ITEM_ID, TAG_MARKET_OPEN_ITEM_ID -> {
                val isStock = (asset?.isStock() == true)

                if (isStock) {
                    showMarketHoursDescriptionForStock()

                    return
                }

                showMarketHoursDescriptionForETF()
            }

            TAG_ADR_ITEM_ID -> {
                title = resources.getString(R.string.tag_adr_description_title)

                description = listOf(
                    resources.getString(R.string.tag_adr_description_paragraph_1),
                    resources.getString(R.string.tag_adr_description_paragraph_2),
                    resources.getString(R.string.tag_adr_description_paragraph_3)
                ).joinToString("\n\n")
            }

            TAG_COMMISSION_FREE_ITEM_ID -> {
                title = resources.getString(R.string.tag_commission_free_description_title)

                description = listOf(
                    resources.getString(R.string.tag_commission_free_description_paragraph_1),
                    resources.getString(R.string.tag_commission_free_description_paragraph_2)
                ).joinToString("\n\n")
            }

            TAG_FRACTIONAL_ITEM_ID -> {
                title = resources.getString(R.string.tag_fractional_description_title)

                description = listOf(
                    resources.getString(R.string.tag_fractional_description_paragraph_1),
                    resources.getString(R.string.tag_fractional_description_paragraph_2),
                    resources.getString(R.string.tag_fractional_description_paragraph_3)
                ).joinToString("\n\n")
            }

            TAG_SMART_EXECUTION_ITEM_ID -> {
                title = resources.getString(R.string.smart_execution_description_title_text_2)

                description = listOf(
                    resources.getString(R.string.smart_execution_description_paragraph_1_text_2),
                    resources.getString(R.string.smart_execution_description_paragraph_2_text_2),
                    resources.getString(R.string.smart_execution_description_paragraph_3_text_2),
                    resources.getString(R.string.smart_execution_description_paragraph_4_text_2),
                    resources.getString(R.string.smart_execution_description_paragraph_5_text_2)
                ).joinToString("\n\n")
            }
        }

        if (title == null) return
        if (description == null) return

        val spacing30 = resources.getDimensionPixelSize(R.dimen.spacing_30)
        val spacing40 = resources.getDimensionPixelSize(R.dimen.spacing_40)

        val formattedDescription = generateFormattedDescription(
            context = context,
            description = description
        )

        _eventShowGenericDescription.value = GenericDescriptionScreenArguments(
            title = title,
            description = formattedDescription,
            contentPaddingTop = spacing30,
            contentPaddingBottom = spacing40
        )
    }

    private fun showMarketHoursDescriptionForStock() {
        val resources = context.resources

        val title = resources.getString(R.string.stock_market_hours_description_title_text)
        val description = generateMarketHoursDescriptionForStock(context)

        showDescriptionDialog(
            title = title,
            formattedDescription = description
        )
    }

    private fun showMarketHoursDescriptionForETF() {
        val resources = context.resources

        val title = resources.getString(R.string.etf_market_hours_description_title_text)
        val description = generateMarketHoursDescriptionForETF(context)

        showDescriptionDialog(
            title = title,
            formattedDescription = description
        )
    }

    private fun showDescriptionDialog(title: String, formattedDescription: SpannableString) {
        val resources = getApplication<Application>().resources

        val spacing24 = resources.getDimensionPixelSize(R.dimen.spacing_24)
        val spacing40 = resources.getDimensionPixelSize(R.dimen.spacing_40)

        val screenArgs = GenericDescriptionScreenArguments(
            title = title,
            description = formattedDescription,
            contentPaddingTop = spacing24,
            contentPaddingBottom = spacing40
        )

        _eventShowGenericDescription.value = screenArgs
    }

    private fun navigateToETFBuy() {
        _eventNavigateToETFBuyScreen.value = generateETFInvestmentScreenArguments()
    }

    private fun showTopUpOptions() {
        val screenArgs = TopUpOptionsScreenArgs(
            headerTitle = context.getString(R.string.please_top_up_your_account_label)
        )

        _eventShowTopUpOptionsDialog.value = screenArgs
    }
}
