package com.wealthyhood.wealthyhood.ui.controlcenter

import android.app.Application
import android.content.Context
import android.text.SpannableString
import android.text.TextPaint
import android.text.style.ClickableSpan
import android.view.View
import androidx.annotation.StringRes
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import com.wealthyhood.wealthyhood.R
import com.wealthyhood.wealthyhood.common.parseSuccessResponse
import com.wealthyhood.wealthyhood.domain.GetPortfoliosCoroutinesUseCase
import com.wealthyhood.wealthyhood.extensions.cleanSpecialFormattingCharacters
import com.wealthyhood.wealthyhood.extensions.findBoldSubstrings
import com.wealthyhood.wealthyhood.extensions.findLinkSubstrings
import com.wealthyhood.wealthyhood.extensions.findUserLocale
import com.wealthyhood.wealthyhood.extensions.formatSubstringsAsBold
import com.wealthyhood.wealthyhood.extensions.formatSubstringsAsLinks
import com.wealthyhood.wealthyhood.extensions.generateDescription
import com.wealthyhood.wealthyhood.extensions.generateFormattedCurrency
import com.wealthyhood.wealthyhood.extensions.generateLogoURI
import com.wealthyhood.wealthyhood.extensions.getBankAccountID
import com.wealthyhood.wealthyhood.model.ControlCenterAutomationDescriptionScreenArgs
import com.wealthyhood.wealthyhood.model.ControlCenterRepeatingInvestmentScreenArgs
import com.wealthyhood.wealthyhood.model.NetworkResource
import com.wealthyhood.wealthyhood.model.UpdateRecurringMessageScreenArgs
import com.wealthyhood.wealthyhood.repository.AuthRepository
import com.wealthyhood.wealthyhood.repository.MyAccountRepository
import com.wealthyhood.wealthyhood.repository.PreferencesRepository
import com.wealthyhood.wealthyhood.repository.Repository
import com.wealthyhood.wealthyhood.service.Automation
import com.wealthyhood.wealthyhood.service.BankAccount
import com.wealthyhood.wealthyhood.service.Portfolio
import com.wealthyhood.wealthyhood.service.SavingsProduct
import com.wealthyhood.wealthyhood.ui.controlcenter.ControlCenterListAdapter.DataItem
import io.sentry.Sentry
import io.sentry.SentryLevel
import kotlinx.coroutines.async
import kotlinx.coroutines.launch
import java.util.Date

class ControlCenterViewModel(application: Application) : AndroidViewModel(application) {

    companion object {

        private const val DATA_INVALIDATION_TIME = 10L * 60L * 1000L
    }

    private val context = application

    private val preferencesRepository = PreferencesRepository(application) // TODO: Use DI
    private val authRepository = AuthRepository(application) // TODO: Use DI
    private val myAccountRepository = MyAccountRepository() // TODO: Use DI
    private val repository = Repository() // TODO: Use DI

    private val getPortfoliosCoroutinesUseCase = GetPortfoliosCoroutinesUseCase(
        preferencesRepository = preferencesRepository,
        repository = repository
    )

    private val userLocale = preferencesRepository.findUserLocale()

    private var accessToken: String? = null
    private var idToken: String? = null

    private var savingsProducts: List<SavingsProduct>? = null
    private var activeOrPendingAutomations: List<Automation>? = null
    private var linkedBankAccounts: List<BankAccount>? = null

    private var repeatingInvestmentSwitchLastUpdated = Date().time
    private var rebalanceSwitchLastUpdated = Date().time
    private var savingsProductSwitchLastUpdated = mutableMapOf<String, Long>()

    private var isReloadingData = false
    private var lastDataReloadAt: Long = -1

    private val _dataItems = MutableLiveData<List<DataItem>?>()
    val dataItems: LiveData<List<DataItem>?>
        get() = _dataItems

    private val _eventShowOrHideLoadingDialog = MutableLiveData<Boolean?>()
    val eventShowOrHideLoadingDialog: LiveData<Boolean?>
        get() = _eventShowOrHideLoadingDialog

    private val _eventShowAutomationDescription =
        MutableLiveData<ControlCenterAutomationDescriptionScreenArgs?>()
    val eventShowAutomationDescription: LiveData<ControlCenterAutomationDescriptionScreenArgs?>
        get() = _eventShowAutomationDescription

    private val _eventShowRepeatingInvestmentDialog =
        MutableLiveData<ControlCenterRepeatingInvestmentScreenArgs?>()
    val eventShowRepeatingInvestmentDialog: LiveData<ControlCenterRepeatingInvestmentScreenArgs?>
        get() = _eventShowRepeatingInvestmentDialog

    private val _eventShowTerminateRepeatingInvestmentDialog =
        MutableLiveData<UpdateRecurringMessageScreenArgs?>()
    val eventShowTerminateRepeatingInvestmentDialog: LiveData<UpdateRecurringMessageScreenArgs?>
        get() = _eventShowTerminateRepeatingInvestmentDialog

    private val _eventOpenBrowser = MutableLiveData<String?>()
    val eventOpenBrowser: LiveData<String?>
        get() = _eventOpenBrowser

    fun eventShowOrHideLoadingDialogCompleted() {
        _eventShowOrHideLoadingDialog.value = null
    }

    fun eventShowAutomationDescriptionCompleted() {
        _eventShowAutomationDescription.value = null
    }

    fun eventShowRepeatingInvestmentDialogCompleted() {
        _eventShowRepeatingInvestmentDialog.value = null
    }

    fun eventShowTerminateRepeatingInvestmentDialogCompleted() {
        _eventShowTerminateRepeatingInvestmentDialog.value = null
    }

    fun eventOpenBrowserCompleted() {
        _eventOpenBrowser.value = null
    }

    init {
        reloadDataIfNeeded()
        refreshDataItems()
    }

    fun handleOnHiddenChanged(hidden: Boolean) {
        if (!hidden && !isReloadingData) {
            reloadDataIfNeeded()
        }
    }

    fun handleOnAutomationInfoButtonClicked(automationID: String?) {
        // TODO: Create constants

        when (automationID) {
            "repeatingInvestmentItem" -> {
                showRepeatingInvestmentAutomationDescription()
            }

            "rebalanceItem" -> {
                showRebalanceAutomationDescription(
                    shouldCreateRebalanceAutomationAfterClosing = false,
                    shouldShowCloseButton = false
                )
            }

            else -> {
                showSavingsProductAutomationDescription()
            }
        }
    }

    fun handleOnAutomationClicked(automationID: String?) {
        // TODO: Use constants

        if (automationID == "rebalanceItem") {
            return
        }

        val automation: Automation?
        val screenTitle: String
        val primaryActionButtonText: String
        val shouldShowBuyTargetPortfolioSwitch: Boolean

        if (automationID == "repeatingInvestmentItem") {
            automation = getExistingTopUpAutomation()

            screenTitle = context.resources.getString(
                R.string.control_center_repeating_investment_screen_title
            )

            primaryActionButtonText = context.resources.getString(
                R.string.control_center_repeating_investment_screen_update_button_title
            )

            shouldShowBuyTargetPortfolioSwitch = true
        } else {
            automation = getExistingSavingsTopUpAutomation(automationID)

            val savingsProduct = savingsProducts?.find {
                it.savingsProductID == automationID
            }

            screenTitle = context.resources.getString(
                R.string.earn_interest_description,
                savingsProduct?.netInterestRate
            )

            primaryActionButtonText = context.resources.getString(
                R.string.control_center_repeating_investment_update_deposit_button_text
            )

            shouldShowBuyTargetPortfolioSwitch = false
        }

        if (automation == null) return

        _eventShowRepeatingInvestmentDialog.value = ControlCenterRepeatingInvestmentScreenArgs(
            screenTitle = screenTitle,
            primaryActionButtonText = primaryActionButtonText,
            savingsProductID = automation.savingsProductID,
            automationAmount = automation.consideration?.amount?.div(100)?.toDouble(),
            automationDayOfMonth = automation.dayOfMonth,
            automationBankAccountID = automation.getMandateAsObject()?.getBankAccountID(),
            automationAllocationMethod = automation.allocationMethod,
            shouldShowBuyTargetPortfolioSwitch = shouldShowBuyTargetPortfolioSwitch
        )
    }

    fun handleOnAutomationSwitchClicked(automationID: String?, isChecked: Boolean) {
        // TODO: Create constants

        if (automationID == "repeatingInvestmentItem") {
            handleOnRepeatingInvestmentSwitchClicked(isChecked)

            return
        }

        if (automationID == "rebalanceItem") {
            handleOnRebalanceSwitchClicked(isChecked)

            return
        }

        handleOnSavingsProductSwitchClicked(
            savingsProductID = automationID,
            isChecked = isChecked
        )
    }

    private fun handleOnRepeatingInvestmentSwitchClicked(isChecked: Boolean) {
        repeatingInvestmentSwitchLastUpdated = Date().time

        if (isChecked) {
            handleOnRepeatingInvestmentSwitchedOn()

            return
        }

        handleOnRepeatingInvestmentSwitchedOff()
    }

    private fun handleOnRepeatingInvestmentSwitchedOn() {
        val hasTargetPortfolio = preferencesRepository.getPortfolioHasTargetAllocation()

        if (!hasTargetPortfolio) {
            // Αν δεν έχει target portfolio θα πρέπει να ελέγξουμε και τα holdings για να
            // ξέρουμε αν θα εμφανίσουμε error ή θα τον αφήσουμε να σετάρει το automation.

            checkHoldingsBeforeShowingRepeatingInvestmentDialog()

            return
        }

        showRepeatingInvestmentDialog()
    }

    private fun handleOnRepeatingInvestmentSwitchedOff() {
        val title = context.resources.getString(R.string.control_center_terminate_message_title)
        val message = context.resources.getString(R.string.control_center_terminate_message_text)

        showTerminateRepeatingInvestmentDialog(
            title = title,
            message = message,
            extra = null
        )
    }

    private fun checkHoldingsBeforeShowingRepeatingInvestmentDialog() {
        isReloadingData = true
        _eventShowOrHideLoadingDialog.value = true

        getPortfolio { didSucceed, portfolio ->
            isReloadingData = false
            _eventShowOrHideLoadingDialog.value = false

            if (!didSucceed) {
                refreshDataItems()

                return@getPortfolio
            }

            val hasHoldings = portfolio?.holdings.isNullOrEmpty().not()

            if (!hasHoldings) {
                refreshDataItems()

                showSetupPortfolioMessage()

                return@getPortfolio
            }

            showRepeatingInvestmentDialog()
        }
    }

    private fun showRepeatingInvestmentDialog() {
        val screenTitle = context.resources.getString(
            R.string.control_center_repeating_investment_screen_title
        )

        val primaryActionButtonText = context.resources.getString(
            R.string.control_center_repeating_investment_screen_setup_button_title
        )

        _eventShowRepeatingInvestmentDialog.value = ControlCenterRepeatingInvestmentScreenArgs(
            screenTitle = screenTitle,
            primaryActionButtonText = primaryActionButtonText,
            savingsProductID = null,
            automationAmount = null,
            automationDayOfMonth = null,
            automationBankAccountID = null,
            automationAllocationMethod = null,
            shouldShowBuyTargetPortfolioSwitch = true
        )
    }

    private fun showTerminateRepeatingInvestmentDialog(
        title: String,
        message: String,
        extra: String?
    ) {
        val closeButtonTitle = context.resources.getString(
            R.string.control_center_terminate_message_cancel_button_title
        )

        val doneButtonTitle = context.resources.getString(
            R.string.control_center_terminate_message_terminate_button_title
        )

        val screenArgs = UpdateRecurringMessageScreenArgs(
            title = title,
            message = message,
            cancelButtonTitle = closeButtonTitle,
            doneButtonTitle = doneButtonTitle,
            extra = extra
        )

        _eventShowTerminateRepeatingInvestmentDialog.value = screenArgs
    }

    private fun handleOnSavingsProductSwitchClicked(savingsProductID: String?, isChecked: Boolean) {
        if (savingsProductID == null) return

        savingsProductSwitchLastUpdated[savingsProductID] = Date().time

        if (isChecked) {
            val savingsProduct = savingsProducts?.find { it.savingsProductID == savingsProductID }

            val screenTitle = context.resources.getString(
                R.string.earn_interest_description,
                savingsProduct?.netInterestRate
            )

            val primaryActionButtonText = context.resources.getString(
                R.string.control_center_repeating_investment_setup_deposit_button_text
            )

            _eventShowRepeatingInvestmentDialog.value = ControlCenterRepeatingInvestmentScreenArgs(
                screenTitle = screenTitle,
                primaryActionButtonText = primaryActionButtonText,
                savingsProductID = savingsProductID,
                automationAmount = null,
                automationDayOfMonth = null,
                automationBankAccountID = null,
                automationAllocationMethod = null,
                shouldShowBuyTargetPortfolioSwitch = false
            )

            return
        }

        val title = context.resources.getString(
            R.string.control_center_terminate_deposit_message_title
        )

        val message = context.resources.getString(
            R.string.control_center_terminate_deposit_message_text
        )

        showTerminateRepeatingInvestmentDialog(
            title = title,
            message = message,
            extra = savingsProductID
        )
    }

    private fun showAutomationDescription(
        @StringRes titleRes: Int,
        @StringRes descriptionRes: Int,
        shouldShowCloseButton: Boolean,
        @StringRes closeButtonTextRes: Int?,
        shouldCreateRebalanceAutomationAfterClosing: Boolean
    ) {
        val resources = context.resources

        val title = resources.getString(titleRes)

        val formattedAmount1 = 10000.0.generateFormattedCurrency(
            currencyISOCode = "USD",
            maximumFractionDigits = 0,
            locale = userLocale
        )

        val formattedAmount2 = 1000.0.generateFormattedCurrency(
            currencyISOCode = "USD",
            maximumFractionDigits = 0,
            locale = userLocale
        )

        val description = resources.getString(
            descriptionRes,
            formattedAmount1,
            formattedAmount2
        )

        val descriptionSpannable = generateFormattedRebalanceAutomationDescription(
            context = context,
            description = description
        )

        val buttonText = closeButtonTextRes?.let {
            resources.getString(closeButtonTextRes)
        }

        _eventShowAutomationDescription.value = ControlCenterAutomationDescriptionScreenArgs(
            title = title,
            description = descriptionSpannable,
            shouldShowCloseButton = shouldShowCloseButton,
            closeButtonText = buttonText,
            shouldCreateRebalanceAutomationAfterClosing = shouldCreateRebalanceAutomationAfterClosing
        )
    }

    private fun showRepeatingInvestmentAutomationDescription() {
        val titleRes = R.string.control_center_repeating_investment_description_title
        val descriptionRes = R.string.control_center_repeating_investment_description_text
        val shouldShowCloseButton = false
        val closeButtonTextRes = null

        showAutomationDescription(
            titleRes = titleRes,
            descriptionRes = descriptionRes,
            shouldShowCloseButton = shouldShowCloseButton,
            closeButtonTextRes = closeButtonTextRes,
            shouldCreateRebalanceAutomationAfterClosing = false
        )
    }

    private fun showRebalanceAutomationDescription(
        shouldCreateRebalanceAutomationAfterClosing: Boolean,
        shouldShowCloseButton: Boolean
    ) {
        preferencesRepository.putDidSeeAutomatedRebalancingDescription(true)

        val titleRes = R.string.control_center_rebalance_description_title
        val descriptionRes = R.string.control_center_rebalance_description_text
        val closeButtonTextRes = R.string.control_center_rebalance_description_close_button_text

        showAutomationDescription(
            titleRes = titleRes,
            descriptionRes = descriptionRes,
            shouldShowCloseButton = shouldShowCloseButton,
            closeButtonTextRes = closeButtonTextRes,
            shouldCreateRebalanceAutomationAfterClosing = shouldCreateRebalanceAutomationAfterClosing
        )
    }

    private fun showSavingsProductAutomationDescription() {
        val titleRes = R.string.control_center_savings_product_description_title
        val descriptionRes = R.string.control_center_savings_product_description_text
        val shouldShowCloseButton = false
        val closeButtonTextRes = null

        showAutomationDescription(
            titleRes = titleRes,
            descriptionRes = descriptionRes,
            shouldShowCloseButton = shouldShowCloseButton,
            closeButtonTextRes = closeButtonTextRes,
            shouldCreateRebalanceAutomationAfterClosing = false
        )
    }

    private fun showSetupPortfolioMessage() {
        val titleRes = R.string.control_center_setup_portfolio_message_title_text
        val descriptionRes = R.string.control_center_setup_portfolio_message_text
        val shouldShowCloseButton = false
        val closeButtonTextRes = null

        showAutomationDescription(
            titleRes = titleRes,
            descriptionRes = descriptionRes,
            shouldShowCloseButton = shouldShowCloseButton,
            closeButtonTextRes = closeButtonTextRes,
            shouldCreateRebalanceAutomationAfterClosing = false
        )
    }

    private fun showSetupTargetPortfolioMessage() {
        val titleRes = R.string.control_center_setup_target_portfolio_message_title_text
        val descriptionRes = R.string.control_center_setup_target_portfolio_message_text
        val shouldShowCloseButton = false
        val closeButtonTextRes = null

        showAutomationDescription(
            titleRes = titleRes,
            descriptionRes = descriptionRes,
            shouldShowCloseButton = shouldShowCloseButton,
            closeButtonTextRes = closeButtonTextRes,
            shouldCreateRebalanceAutomationAfterClosing = false
        )
    }

    private fun handleOnRebalanceSwitchClicked(isChecked: Boolean) {
        rebalanceSwitchLastUpdated = Date().time

        if (isChecked) {
            handleOnRebalanceSwitchedOn()

            return
        }

        handleOnRebalanceSwitchedOff()
    }

    private fun handleOnRebalanceSwitchedOn() {
        val hasTargetPortfolio = preferencesRepository.getPortfolioHasTargetAllocation()

        if (!hasTargetPortfolio) {
            refreshDataItems()

            showSetupTargetPortfolioMessage()

            return
        }

        showDescriptionOrCreateRebalanceAutomation()
    }

    private fun handleOnRebalanceSwitchedOff() {
        val automationID = getExistingRebalanceAutomation()?.id ?: kotlin.run {
            refreshDataItems()

            return
        }

        cancelAutomation(automationID)
    }

    private fun showDescriptionOrCreateRebalanceAutomation() {
        val didSeeDescription = preferencesRepository.getDidSeeAutomatedRebalancingDescription()

        if (!didSeeDescription) {
            showRebalanceAutomationDescription(
                shouldCreateRebalanceAutomationAfterClosing = true,
                shouldShowCloseButton = true
            )

            return
        }

        createRebalanceAutomation()
    }

    fun handleOnRepeatingInvestmentFinished(shouldReloadData: Boolean) {
        if (shouldReloadData) {
            reloadDataIfNeeded()

            return
        }

        refreshDataItems()
    }

    fun handleOnTerminateRepeatingInvestmentButtonClicked(savingsProductID: String?) {
        val automation = if (savingsProductID == null) {
            getExistingTopUpAutomation()
        } else getExistingSavingsTopUpAutomation(savingsProductID)

        val automationID = automation?.id ?: kotlin.run {
            refreshDataItems()

            return
        }

        cancelAutomation(automationID)
    }

    fun handleOnTerminateRepeatingInvestmentCancelButtonClicked() {
        refreshDataItems()
    }

    fun handleOnAutomationDescriptionDismissed(shouldCreateRebalanceAutomationAfterClosing: Boolean) {
        if (shouldCreateRebalanceAutomationAfterClosing) {
            createRebalanceAutomation()
        }
    }

    private fun reloadDataIfNeeded() {
        if (!shouldReloadData()) return

        reloadData()
    }

    private fun shouldReloadData(lastDataReloadAt: Long = this.lastDataReloadAt): Boolean {
        if (lastDataReloadAt == -1L) return true

        val currentTimestamp = Date().time
        val diff = currentTimestamp - lastDataReloadAt

        if (diff >= DATA_INVALIDATION_TIME) return true

        val lastTopUpAutomationCreatedAt = preferencesRepository.getLastAutomationChangeMadeAt()
        if (lastTopUpAutomationCreatedAt > lastDataReloadAt) return true

        return false
    }

    private fun reloadData() {
        if (isReloadingData) return

        isReloadingData = true
        _eventShowOrHideLoadingDialog.value = true

        getCredentialsCall { _, accessToken, idToken ->
            if (accessToken == null || idToken == null) {
                // TODO: Handle the failure

                isReloadingData = false
                _eventShowOrHideLoadingDialog.value = false

                return@getCredentialsCall
            }

            getDataFromAPI(
                accessToken = accessToken,
                idToken = idToken
            )
        }
    }

    private fun getDataFromAPI(accessToken: String, idToken: String) {
        viewModelScope.launch {
            val userID = preferencesRepository.getUserID()

            val savingsProductsDeferred = async {
                repository.getSavingsProducts(
                    accessToken = accessToken,
                    idToken = idToken
                )
            }

            // Εάν στο παρελθόν έχουμε διαπιστώσει ότι υπάρχει target portfolio, τότε
            // δε χρειάζεται να φέρουμε τα portfolios για να το ελέγξουμε πάλι.
            // Επίσης αν δημιουργήσει ένα νέο target portfolio τότε θα κάνουμε recreate το
            // MainActivity οπότε θα ξαναπεράσει από εδώ.

            val hasTargetAllocation = preferencesRepository.getPortfolioHasTargetAllocation()

            val portfoliosDeferred = if (!hasTargetAllocation) {
                async {
                    getPortfoliosCoroutinesUseCase(
                        accessToken = accessToken,
                        idToken = idToken,
                        parameters = null,
                        shouldGetFromCache = false
                    )
                }
            } else null

            val bankAccountsDeferred = async {
                repository.getLinkedBankAccounts(
                    accessToken = accessToken,
                    idToken = idToken,
                    userID = userID
                )
            }

            val automationsDeferred = async {
                repository.getAutomations(
                    accessToken = accessToken,
                    idToken = idToken,
                    category = null
                )
            }

            val savingsProductsResult = savingsProductsDeferred.await()
            val bankAccountsResult = bankAccountsDeferred.await()
            val automationsResult = automationsDeferred.await()

            // Το use case αποθηκεύει αν υπάρχει target portfolio.
            portfoliosDeferred?.await()

            isReloadingData = false
            _eventShowOrHideLoadingDialog.value = false

            savingsProducts = parseSuccessResponse(savingsProductsResult)

            linkedBankAccounts = parseSuccessResponse(bankAccountsResult)?.data

            activeOrPendingAutomations =
                parseSuccessResponse(automationsResult)?.data?.filter { automation ->
                    // TODO: Create constants
                    automation.status == "Active" || automation.status == "Pending"
                }

            lastDataReloadAt = Date().time

            refreshDataItems()
        }
    }

    private fun getCredentialsCall(callback: ((succeeded: Boolean, accessToken: String?, idToken: String?) -> Unit)?) {
        authRepository.loadAuth0Credentials {
            when (it) {
                is NetworkResource.Success -> {
                    val accessToken = "Bearer ${it.data?.accessToken}"
                    val idToken = "Bearer ${it.data?.idToken}"

                    // TODO: Remove these 2 lines after fully migrating to Coroutines.
                    this.accessToken = it.data?.accessToken
                    this.idToken = it.data?.idToken

                    callback?.invoke(true, accessToken, idToken)
                }

                is NetworkResource.Failure -> {
                    callback?.invoke(false, null, null)
                }
            }
        }
    }

    private fun getPortfolio(callback: (didSucceed: Boolean, portfolio: Portfolio?) -> Unit) {
        getCredentialsCall { _, accessToken, idToken ->
            if (accessToken == null || idToken == null) {
                // TODO: Handle the failure

                callback(false, null)

                return@getCredentialsCall
            }

            viewModelScope.launch {
                val result = getPortfoliosCoroutinesUseCase(
                    accessToken = accessToken,
                    idToken = idToken,
                    parameters = null,
                    shouldGetFromCache = false
                )

                val portfolios = parseSuccessResponse(result)
                val realPortfolio = portfolios?.find { it.isReal == true }

                callback(true, realPortfolio)
            }
        }
    }

    private fun createRebalanceAutomation() {
        isReloadingData = true
        _eventShowOrHideLoadingDialog.value = true

        val accessToken = "Bearer $accessToken"
        val idToken = "Bearer $idToken"

        myAccountRepository.createAutomation(
            accessToken = accessToken,
            idToken = idToken,
            category = "RebalanceAutomation",
            mandateID = null,
            savingsProductID = null,
            formattedAmount = null,
            dayOfMonth = null,
            allocationMethod = null
        ) {
            isReloadingData = false
            _eventShowOrHideLoadingDialog.value = false

            when (it) {
                is NetworkResource.Success -> {
                    preferencesRepository.putLastAutomationChangeMadeAt(Date().time)

                    reloadDataIfNeeded()
                }

                is NetworkResource.Failure -> {
                    // TODO: Handle the failure
                }
            }
        }
    }

    private fun cancelAutomation(automationID: String) {
        isReloadingData = true
        _eventShowOrHideLoadingDialog.value = true

        val accessToken = "Bearer $accessToken"
        val idToken = "Bearer $idToken"

        myAccountRepository.cancelAutomation(
            accessToken = accessToken,
            idToken = idToken,
            automationID = automationID
        ) {
            isReloadingData = false
            _eventShowOrHideLoadingDialog.value = false

            when (it) {
                is NetworkResource.Success -> {
                    preferencesRepository.putLastAutomationChangeMadeAt(Date().time)
                    preferencesRepository.putLastTransactionCreatedAt(Date().time)

                    reloadDataIfNeeded()
                }

                is NetworkResource.Failure -> {
                    // TODO: Handle the failure
                }
            }
        }
    }

    private fun refreshDataItems() {
        _dataItems.value = generateDataItems()
    }

    private fun generateDataItems(): List<DataItem> {
        val finalAnswer = mutableListOf<DataItem>()

        finalAnswer.add(generateHeaderItem())
        finalAnswer.add(generateDescriptionItem())

        finalAnswer.addAll(generateInvestmentsSectionItems())

        generateInterestSectionItems()?.let {
            finalAnswer.addAll(it)
        }

        return finalAnswer
    }

    private fun generateHeaderItem(): DataItem.HeaderItem {
        val spacing16 = context.resources.getDimensionPixelSize(R.dimen.spacing_16)
        val spacing32 = context.resources.getDimensionPixelSize(R.dimen.spacing_32)

        val screenTitle = context.getString(R.string.control_center_header_title)
        val spannableStringTitle = SpannableString(screenTitle)

        return DataItem.HeaderItem(
            id = "headerItem",
            headerType = null,
            paddingStart = spacing16,
            paddingTop = spacing32,
            paddingEnd = spacing16,
            paddingBottom = null,
            textAppearanceRes = R.style.HeadingsH1Mobile_Primary,
            title = spannableStringTitle
        )
    }

    private fun generateDescriptionItem(): DataItem.HeaderItem {
        val spacing16 = context.resources.getDimensionPixelSize(R.dimen.spacing_16)
        val spacing32 = context.resources.getDimensionPixelSize(R.dimen.spacing_32)

        val descriptionText = context.getString(R.string.control_center_header_subtitle)
        val spannableString = SpannableString(descriptionText)

        return DataItem.HeaderItem(
            id = "descriptionItem",
            headerType = null,
            paddingStart = spacing16,
            paddingTop = spacing16,
            paddingEnd = spacing16,
            paddingBottom = spacing32,
            textAppearanceRes = R.style.BodySM_BaseTextColor,
            title = spannableString
        )
    }

    private fun generateInvestmentsSectionItems(): List<DataItem> {
        val finalAnswer = mutableListOf<DataItem>()

        finalAnswer.add(generateInvestmentsSectionHeaderItem())
        finalAnswer.add(generateRepeatingInvestmentItem())
        finalAnswer.add(generateRebalanceItem())

        return finalAnswer
    }

    private fun generateInterestSectionItems(): List<DataItem>? {
        val items = generateSavingsProductItems()

        if (items.isNullOrEmpty()) return null

        val finalAnswer = mutableListOf<DataItem>()

        finalAnswer.add(generateInterestSectionHeaderItem())
        finalAnswer.addAll(items)

        return finalAnswer
    }

    private fun generateInvestmentsSectionHeaderItem(): DataItem.HeaderItem {
        val spacing16 = context.resources.getDimensionPixelSize(R.dimen.spacing_16)
        val spacing20 = context.resources.getDimensionPixelSize(R.dimen.spacing_20)
        val spacing24 = context.resources.getDimensionPixelSize(R.dimen.spacing_24)

        val descriptionText = context.getString(R.string.investments_label)
        val spannableString = SpannableString(descriptionText)

        return DataItem.HeaderItem(
            id = "investmentsSectionHeaderItem",
            headerType = null,
            paddingStart = spacing16,
            paddingTop = spacing24,
            paddingEnd = spacing16,
            paddingBottom = spacing20,
            textAppearanceRes = R.style.HeadingsH3Mobile_PrimaryColor,
            title = spannableString
        )
    }

    private fun generateRepeatingInvestmentItem(): DataItem.AutomationItem {
        val context = getApplication<Application>()
        val resources = context.resources

        val spacing4 = resources.getDimensionPixelSize(R.dimen.spacing_4)
        val spacing16 = resources.getDimensionPixelSize(R.dimen.spacing_16)

        val title = resources.getString(
            R.string.control_center_repeating_investment_automation_title
        )

        val subtitle = generateRepeatingInvestmentSubtitle(savingsProductID = null)
        val bankLogoURI = generateRepeatingInvestmentBankLogoURI(savingsProductID = null)

        val existingTopUpAutomation = getExistingTopUpAutomation()

        val hasBorder: Boolean
        val backgroundColorRes: Int
        val iconBackgroundColorRes: Int
        val iconTintColorRes: Int
        val subtitleTextColorRes: Int
        val subtitleTextStyleRes: Int

        if (existingTopUpAutomation == null) {
            hasBorder = true
            backgroundColorRes = R.color.white
            iconBackgroundColorRes = R.color.primary_5
            iconTintColorRes = R.color.accent
            subtitleTextColorRes = R.color.base
            subtitleTextStyleRes = R.style.BodyXS
        } else {
            hasBorder = false
            backgroundColorRes = R.color.primary_5
            iconBackgroundColorRes = R.color.accent
            iconTintColorRes = R.color.white
            subtitleTextColorRes = R.color.accent
            subtitleTextStyleRes = R.style.BodyXSMedium
        }

        return DataItem.AutomationItem(
            id = "repeatingInvestmentItem",
            paddingStart = spacing16,
            paddingTop = spacing4,
            paddingEnd = spacing16,
            paddingBottom = spacing4,
            hasBorder = hasBorder,
            backgroundColorRes = backgroundColorRes,
            iconBackgroundColorRes = iconBackgroundColorRes,
            iconTintColorRes = iconTintColorRes,
            iconDrawableRes = R.drawable.ic_event_repeat,
            title = title,
            shouldShowInfoButton = true,
            infoIconTintColorRes = R.color.primary_55,
            subtitle = subtitle,
            subtitleTextColorRes = subtitleTextColorRes,
            subtitleTextStyleRes = subtitleTextStyleRes,
            bankLogoURI = bankLogoURI,
            shouldShowSwitch = true,
            isOn = (existingTopUpAutomation != null),
            switchLastUpdated = repeatingInvestmentSwitchLastUpdated,
            shouldShowComingSoon = false
        )
    }

    private fun generateRebalanceItem(): DataItem.AutomationItem {
        val context = getApplication<Application>()
        val resources = context.resources

        val spacing4 = resources.getDimensionPixelSize(R.dimen.spacing_4)
        val spacing16 = resources.getDimensionPixelSize(R.dimen.spacing_16)

        val title =
            resources.getString(R.string.control_center_automated_rebalancing_automation_title)
        val subtitle =
            resources.getString(R.string.control_center_automated_rebalancing_automation_subtitle)

        val hasBorder: Boolean
        val backgroundColorRes: Int
        val iconBackgroundColorRes: Int
        val iconTintColorRes: Int
        val subtitleTextColorRes: Int
        val subtitleTextStyleRes: Int

        val existingRebalanceAutomation = getExistingRebalanceAutomation()

        if (existingRebalanceAutomation == null) {
            hasBorder = true
            backgroundColorRes = R.color.white
            iconBackgroundColorRes = R.color.primary_5
            iconTintColorRes = R.color.accent

            subtitleTextColorRes = R.color.base
            subtitleTextStyleRes = R.style.BodyXS
        } else {
            hasBorder = false
            backgroundColorRes = R.color.primary_5
            iconBackgroundColorRes = R.color.accent
            iconTintColorRes = R.color.white

            subtitleTextColorRes = R.color.accent
            subtitleTextStyleRes = R.style.BodyXSMedium
        }

        return DataItem.AutomationItem(
            id = "rebalanceItem",
            paddingStart = spacing16,
            paddingTop = spacing4,
            paddingEnd = spacing16,
            paddingBottom = spacing4,
            hasBorder = hasBorder,
            backgroundColorRes = backgroundColorRes,
            iconBackgroundColorRes = iconBackgroundColorRes,
            iconTintColorRes = iconTintColorRes,
            iconDrawableRes = R.drawable.ic_autopilot,
            title = title,
            shouldShowInfoButton = true,
            infoIconTintColorRes = R.color.primary_55,
            subtitle = subtitle,
            subtitleTextColorRes = subtitleTextColorRes,
            subtitleTextStyleRes = subtitleTextStyleRes,
            bankLogoURI = null,
            shouldShowSwitch = true,
            isOn = (getExistingRebalanceAutomation() != null),
            switchLastUpdated = rebalanceSwitchLastUpdated,
            shouldShowComingSoon = false
        )
    }

    private fun generateInterestSectionHeaderItem(): DataItem.HeaderItem {
        val spacing16 = context.resources.getDimensionPixelSize(R.dimen.spacing_16)
        val spacing20 = context.resources.getDimensionPixelSize(R.dimen.spacing_20)
        val spacing36 = context.resources.getDimensionPixelSize(R.dimen.spacing_36)

        val descriptionText = context.getString(R.string.interest_label)
        val spannableString = SpannableString(descriptionText)

        return DataItem.HeaderItem(
            id = "interestSectionHeaderItem",
            headerType = null,
            paddingStart = spacing16,
            paddingTop = spacing36,
            paddingEnd = spacing16,
            paddingBottom = spacing20,
            textAppearanceRes = R.style.HeadingsH3Mobile_PrimaryColor,
            title = spannableString
        )
    }

    private fun generateSavingsProductItems(): List<DataItem>? {
        val finalAnswer = mutableListOf<DataItem>()

        val spacing4 = context.resources.getDimensionPixelSize(R.dimen.spacing_4)
        val spacing16 = context.resources.getDimensionPixelSize(R.dimen.spacing_16)

        savingsProducts?.forEach { savingsProduct ->
            val savingsProductID = savingsProduct.savingsProductID ?: return@forEach

            val title = context.resources.getString(
                R.string.earn_interest_description,
                savingsProduct.netInterestRate
            )

            val subtitle = generateRepeatingInvestmentSubtitle(savingsProductID = savingsProductID)

            val bankLogoURI = generateRepeatingInvestmentBankLogoURI(
                savingsProductID = savingsProductID
            )

            // Δεν είναι σωστό να δώσουμε default τιμή το Date().time γιατί κάθε φορά
            // θα είναι διαφορετικό. Η default τιμή θα πρέπει να είναι σταθερή μέχρι
            // να αλλάξει explicitly.

            val switchLastUpdated = savingsProductSwitchLastUpdated[savingsProductID] ?: 0L

            val existingSavingsTopUpAutomation = getExistingSavingsTopUpAutomation(savingsProductID)

            val hasBorder: Boolean
            val backgroundColorRes: Int
            val iconBackgroundColorRes: Int
            val iconTintColorRes: Int
            val infoIconTintColorRes: Int
            val subtitleTextColorRes: Int
            val subtitleTextStyleRes: Int

            if (existingSavingsTopUpAutomation == null) {
                hasBorder = true
                backgroundColorRes = R.color.white
                iconBackgroundColorRes = R.color.pro_5
                iconTintColorRes = R.color.pro_50
                infoIconTintColorRes = R.color.accent
                subtitleTextColorRes = R.color.base
                subtitleTextStyleRes = R.style.BodyXS
            } else {
                hasBorder = false
                backgroundColorRes = R.color.pro_5
                iconBackgroundColorRes = R.color.pro_50
                iconTintColorRes = R.color.white
                infoIconTintColorRes = R.color.pro_50
                subtitleTextColorRes = R.color.pro_50
                subtitleTextStyleRes = R.style.BodyXSMedium
            }

            val isOn = (existingSavingsTopUpAutomation != null)

            val iconDrawableRes = if (savingsProduct.currency == "EUR") {
                R.drawable.ic_currency_reinvest_eur
            } else R.drawable.ic_currency_reinvest_gbp

            val item = DataItem.AutomationItem(
                id = savingsProductID,
                paddingStart = spacing16,
                paddingTop = spacing4,
                paddingEnd = spacing16,
                paddingBottom = spacing4,
                hasBorder = hasBorder,
                backgroundColorRes = backgroundColorRes,
                iconBackgroundColorRes = iconBackgroundColorRes,
                iconTintColorRes = iconTintColorRes,
                iconDrawableRes = iconDrawableRes,
                title = title,
                shouldShowInfoButton = true,
                infoIconTintColorRes = infoIconTintColorRes,
                subtitle = subtitle,
                subtitleTextColorRes = subtitleTextColorRes,
                subtitleTextStyleRes = subtitleTextStyleRes,
                bankLogoURI = bankLogoURI,
                shouldShowSwitch = true,
                isOn = isOn,
                switchLastUpdated = switchLastUpdated,
                shouldShowComingSoon = false
            )

            finalAnswer.add(item)
        }

        return if (finalAnswer.isEmpty()) null else finalAnswer
    }

    private fun generateCashReinvestmentItem(): DataItem.AutomationItem {
        val context = getApplication<Application>()
        val resources = context.resources

        val spacing4 = resources.getDimensionPixelSize(R.dimen.spacing_4)
        val spacing16 = resources.getDimensionPixelSize(R.dimen.spacing_16)

        val title =
            resources.getString(R.string.control_center_cash_reinvestment_automation_title)

        return DataItem.AutomationItem(
            id = "cashReinvestmentItem",
            paddingStart = spacing16,
            paddingTop = spacing4,
            paddingEnd = spacing16,
            paddingBottom = spacing4,
            hasBorder = true,
            backgroundColorRes = R.color.white,
            iconBackgroundColorRes = R.color.stocks,
            iconTintColorRes = R.color.white,
            iconDrawableRes = R.drawable.ic_payments,
            title = title,
            shouldShowInfoButton = false,
            infoIconTintColorRes = R.color.accent,
            subtitle = null,
            subtitleTextColorRes = R.color.accent,
            subtitleTextStyleRes = R.style.BodyXSMedium,
            bankLogoURI = null,
            shouldShowSwitch = false,
            isOn = false,
            switchLastUpdated = 0L,
            shouldShowComingSoon = true
        )
    }

    private fun getExistingTopUpAutomation(): Automation? {
        return activeOrPendingAutomations?.firstOrNull {
            it.category == "TopUpAutomation" // TODO: Create a constant
        }
    }

    private fun getExistingRebalanceAutomation(): Automation? {
        return activeOrPendingAutomations?.firstOrNull {
            it.category == "RebalanceAutomation" // TODO: Create a constant
        }
    }

    private fun getExistingSavingsTopUpAutomation(savingsProductID: String?): Automation? {
        return activeOrPendingAutomations?.firstOrNull {
            // TODO: Create a constant
            it.category == "SavingsTopUpAutomation" && it.savingsProductID == savingsProductID
        }
    }

    private fun generateRepeatingInvestmentSubtitle(savingsProductID: String?): String {
        val existingTopUpAutomation = if (savingsProductID == null) {
            getExistingTopUpAutomation()
        } else {
            getExistingSavingsTopUpAutomation(savingsProductID)
        }

        if (existingTopUpAutomation == null) {
            return if (savingsProductID == null) {
                context.resources.getString(R.string.control_center_repeating_investment_automation_subtitle)
            } else {
                context.resources.getString(R.string.control_center_repeating_investment_automation_deposit_subtitle)
            }
        }

        return existingTopUpAutomation.generateDescription(
            context = context,
            userLocale = userLocale
        )
    }

    private fun generateRepeatingInvestmentBankLogoURI(savingsProductID: String?): String? {
        val automation = if (savingsProductID == null) {
            getExistingTopUpAutomation()
        } else {
            getExistingSavingsTopUpAutomation(savingsProductID)
        } ?: return null

        return automation.getMandateAsObject()?.getBankAccountID()?.let { bankAccountID ->
            val bankAccount = linkedBankAccounts?.find { it.id == bankAccountID }
            bankAccount?.generateLogoURI()
        }
    }

    private fun generateFormattedRebalanceAutomationDescription(
        context: Context,
        description: String
    ): SpannableString {
        val boldSubstrings = description.findBoldSubstrings()
        val linkSubstrings = description.findLinkSubstrings()

        val finalString = description.cleanSpecialFormattingCharacters()

        val spannableString = SpannableString(finalString)

        spannableString.formatSubstringsAsBold(
            context = context,
            substrings = boldSubstrings
        )

        spannableString.formatSubstringsAsBold(
            context = context,
            substrings = linkSubstrings
        )

        val clickableSpan = object : ClickableSpan() {

            override fun onClick(p0: View) {
                openInvestorTermsURL()
            }

            override fun updateDrawState(ds: TextPaint) {
                super.updateDrawState(ds)

                ds.isUnderlineText = false
            }
        }

        spannableString.formatSubstringsAsLinks(
            substrings = linkSubstrings,
            clickableSpans = listOf(clickableSpan)
        )

        return spannableString
    }

    private fun openInvestorTermsURL() {
        val url = preferencesRepository.getLegalPages()?.platformInvestorTerms

        if (url == null) {
            Sentry.captureMessage("No platformInvestorTerms URL", SentryLevel.WARNING)

            return
        }

        _eventOpenBrowser.value = url
    }
}
