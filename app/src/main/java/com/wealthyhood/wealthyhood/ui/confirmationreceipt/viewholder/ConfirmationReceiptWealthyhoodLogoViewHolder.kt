package com.wealthyhood.wealthyhood.ui.confirmationreceipt.viewholder

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.core.view.updatePadding
import androidx.recyclerview.widget.RecyclerView
import com.wealthyhood.wealthyhood.R
import com.wealthyhood.wealthyhood.databinding.ListItemConfirmationReceiptWealthyhoodLogoBinding

class ConfirmationReceiptWealthyhoodLogoViewHolder private constructor(
    private val binding: ListItemConfirmationReceiptWealthyhoodLogoBinding
) : RecyclerView.ViewHolder(binding.root) {

    companion object {

        fun from(parent: ViewGroup): ConfirmationReceiptWealthyhoodLogoViewHolder {
            val layoutInflater = LayoutInflater.from(parent.context)

            val binding = ListItemConfirmationReceiptWealthyhoodLogoBinding.inflate(
                layoutInflater,
                parent,
                false
            )

            return ConfirmationReceiptWealthyhoodLogoViewHolder(binding)
        }
    }

    fun bind(
        paddingStart: Int?,
        paddingTop: Int?,
        paddingEnd: Int?,
        paddingBottom: Int?
    ) {
        refreshPadding(
            paddingStart = paddingStart,
            paddingTop = paddingTop,
            paddingEnd = paddingEnd,
            paddingBottom = paddingBottom
        )
    }

    private fun refreshPadding(
        paddingStart: Int?,
        paddingTop: Int?,
        paddingEnd: Int?,
        paddingBottom: Int?
    ) {
        val spacing0 = itemView.context.resources.getDimensionPixelSize(R.dimen.spacing_0)

        val finalPaddingStart = paddingStart ?: spacing0
        val finalPaddingTop = paddingTop ?: spacing0
        val finalPaddingEnd = paddingEnd ?: spacing0
        val finalPaddingBottom = paddingBottom ?: spacing0

        binding.rootConstraintLayout.updatePadding(
            finalPaddingStart,
            finalPaddingTop,
            finalPaddingEnd,
            finalPaddingBottom
        )
    }
}
