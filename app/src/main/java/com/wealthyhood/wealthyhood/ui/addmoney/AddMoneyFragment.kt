package com.wealthyhood.wealthyhood.ui.addmoney

import android.app.Activity
import android.os.Bundle
import android.text.method.DigitsKeyListener
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.inputmethod.EditorInfo
import androidx.activity.result.contract.ActivityResultContracts
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.core.content.ContextCompat
import androidx.core.view.ViewCompat
import androidx.core.view.WindowInsetsCompat
import androidx.core.view.updateLayoutParams
import androidx.core.widget.addTextChangedListener
import androidx.fragment.app.Fragment
import androidx.lifecycle.ViewModelProvider
import com.google.gson.Gson
import com.wealthyhood.wealthyhood.R
import com.wealthyhood.wealthyhood.databinding.FragmentAddMoneyBinding
import com.wealthyhood.wealthyhood.domain.ModalNavigationBarProperties
import com.wealthyhood.wealthyhood.domain.PaymentMethodDropDownProperties
import com.wealthyhood.wealthyhood.domain.applyTo
import com.wealthyhood.wealthyhood.extensions.applyInsets
import com.wealthyhood.wealthyhood.extensions.applyTo
import com.wealthyhood.wealthyhood.extensions.computeKeyboardGuidelinePercentage
import com.wealthyhood.wealthyhood.extensions.convertToAddMoneyScreenArgs
import com.wealthyhood.wealthyhood.extensions.hideKeyboard
import com.wealthyhood.wealthyhood.extensions.setupKeyboard
import com.wealthyhood.wealthyhood.extensions.showCustomSnack
import com.wealthyhood.wealthyhood.model.AddMoneyLoadingScreenArgs
import com.wealthyhood.wealthyhood.model.BankAccountsFragmentArguments
import com.wealthyhood.wealthyhood.model.DayPickerScreenArgs
import com.wealthyhood.wealthyhood.model.GenericErrorScreenArgs
import com.wealthyhood.wealthyhood.model.LoadingTypeEnum
import com.wealthyhood.wealthyhood.model.RegularBankTransferScreenArgs
import com.wealthyhood.wealthyhood.model.ScheduleOptionsScreenArgs
import com.wealthyhood.wealthyhood.model.SetupDirectDebitScreenArgs
import com.wealthyhood.wealthyhood.model.SetupDirectDebitSuccessfulScreenArgs
import com.wealthyhood.wealthyhood.model.TopUpOptionsScreenArgs
import com.wealthyhood.wealthyhood.model.UpdateRecurringMessageScreenArgs
import com.wealthyhood.wealthyhood.model.ValidationMessage
import com.wealthyhood.wealthyhood.model.WalletOrderReviewScreenArgs
import com.wealthyhood.wealthyhood.model.WalletOrderReviewTypeEnum
import com.wealthyhood.wealthyhood.ui.daypicker.DayPickerFragment
import com.wealthyhood.wealthyhood.ui.genericerror.GenericErrorFragment
import com.wealthyhood.wealthyhood.ui.investment.portfoliobuy.BankAccountsFragment
import com.wealthyhood.wealthyhood.ui.investment.portfoliobuy.scheduleoptions.ScheduleOptionsFragment
import com.wealthyhood.wealthyhood.ui.investment.portfoliobuy.updaterecurringmessage.UpdateRecurringMessageFragment
import com.wealthyhood.wealthyhood.ui.investment.walletorderreview.WalletOrderReviewFragment
import com.wealthyhood.wealthyhood.ui.linkbankaccount.LinkBankAccountActivity
import com.wealthyhood.wealthyhood.ui.loading.LoadingActivity
import com.wealthyhood.wealthyhood.ui.loadingdialog.LoadingDialogFragment
import com.wealthyhood.wealthyhood.ui.regularbanktransfer.RegularBankTransferActivity
import com.wealthyhood.wealthyhood.ui.setupdirectdebit.SetupDirectDebitFragment
import com.wealthyhood.wealthyhood.ui.setupdirectdebitsuccessful.SetupDirectDebitSuccessfulFragment
import com.wealthyhood.wealthyhood.ui.topupoptions.TopUpOptionsFragment

class AddMoneyFragment : Fragment() {

    companion object {

        private const val ARG_SCREEN_ARGS = "AddMoneyFragment.screenArgs"

        fun newInstance(screenArgsJSONString: String?): AddMoneyFragment {
            val arguments = Bundle()
            arguments.putString(ARG_SCREEN_ARGS, screenArgsJSONString)

            val fragment = AddMoneyFragment()
            fragment.arguments = arguments

            return fragment
        }
    }

    private lateinit var _viewModel: AddMoneyViewModel
    private val viewModel get() = _viewModel

    private lateinit var _binding: FragmentAddMoneyBinding
    private val binding get() = _binding

    private val linkBankAccountLauncher = registerForActivityResult(
        ActivityResultContracts.StartActivityForResult()
    ) { result ->
        if (result.resultCode == Activity.RESULT_OK) {
            viewModel.handleOnNewBankAccountLinked()
        }
    }

    private val loadingActivityResultLauncher = registerForActivityResult(
        ActivityResultContracts.StartActivityForResult()
    ) { result ->
        if (result.resultCode == Activity.RESULT_OK) {
            requireActivity().finish()
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        setupFragmentResultListeners()
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        setupViewModel()
        setupBinding(inflater, container)

        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        setupWindowInsetsListener()
        adjustKeyboardGuideline()
        setupKeyboard()
        setUpScrollView()
        setupButtons()
        setupEditText()
        setupBankAccountConstraintLayout()

        observeViewModel()
    }

    private fun setupFragmentResultListeners() {
        childFragmentManager.setFragmentResultListener(
            BankAccountsFragment.REQUEST_KEY_SELECT_BANK_ACCOUNT,
            this
        ) { _, bundle ->
            val bankAccountID = bundle.getString(BankAccountsFragment.BUNDLE_KEY_BANK_ACCOUNT_ID)
            viewModel.handleOnNewPaymentMethodSelected(bankAccountID)
        }

        childFragmentManager.setFragmentResultListener(
            BankAccountsFragment.REQUEST_KEY_ADD_BANK_ACCOUNT,
            this
        ) { _, _ ->
            viewModel.handleOnAddBankAccountButtonClicked()
        }

        childFragmentManager.setFragmentResultListener(
            WalletOrderReviewFragment.REQUEST_KEY_CONFIRM,
            this
        ) { _, _ ->
            viewModel.handleOnConfirmDepositButtonClicked()
        }

        // DayPickerFragment

        childFragmentManager.setFragmentResultListener(
            DayPickerFragment.REQUEST_KEY_SELECT_DAY,
            this
        ) { _, bundle ->
            val selectedDayChoiceID = bundle.getString(DayPickerFragment.BUNDLE_KEY_DAY_ID)

            viewModel.handleOnDayChoiceClicked(selectedDayChoiceID)
        }

        // ScheduleOptionsFragment

        childFragmentManager.setFragmentResultListener(
            ScheduleOptionsFragment.REQUEST_KEY_SELECT_SCHEDULE_OPTION,
            this
        ) { _, bundle ->
            val optionID =
                bundle.getString(ScheduleOptionsFragment.BUNDLE_KEY_SELECTED_SCHEDULE_OPTION_ID)

            viewModel.handleOnNewScheduleOptionSelected(optionID)
        }

        // UpdateRecurringMessageFragment

        childFragmentManager.setFragmentResultListener(
            UpdateRecurringMessageFragment.REQUEST_KEY_DONE,
            this
        ) { _, _ ->
            viewModel.handleOnUpdateRecurringButtonClicked()
        }

        // TopUpOptionsFragment

        childFragmentManager.setFragmentResultListener(
            TopUpOptionsFragment.REQUEST_KEY_SELECT_OPTION,
            this
        ) { _, bundle ->
            val topUpOptionID = bundle.getString(TopUpOptionsFragment.BUNDLE_KEY_SELECTED_OPTION_ID)

            viewModel.handleOnTopUpOptionClicked(topUpOptionID)
        }

        // GenericErrorFragment

        childFragmentManager.setFragmentResultListener(
            GenericErrorFragment.REQUEST_KEY_RETRY,
            this
        ) { _, _ ->
            viewModel.handleOnGenericErrorRetryButtonClicked()
        }
    }

    private fun setupViewModel() {
        val screenArgsJSONString = arguments?.getString(ARG_SCREEN_ARGS)
        val screenArgs = screenArgsJSONString?.convertToAddMoneyScreenArgs()

        val viewModelFactory = AddMoneyViewModelFactory(
            application = requireActivity().application,
            screenArgs = screenArgs
        )

        val viewModelClass = AddMoneyViewModel::class.java
        _viewModel = ViewModelProvider(this, viewModelFactory)[viewModelClass]
    }

    private fun setupBinding(inflater: LayoutInflater, container: ViewGroup?) {
        _binding = FragmentAddMoneyBinding.inflate(inflater, container, false)
    }

    private fun setupWindowInsetsListener() {
        // https://developer.android.com/develop/ui/views/layout/sw-keyboard
        // https://developer.android.com/develop/ui/views/layout/edge-to-edge

        ViewCompat.setOnApplyWindowInsetsListener(binding.root) { _, windowInsets ->
            val insets = windowInsets.getInsets(WindowInsetsCompat.Type.systemBars())

            val spacing16 = resources.getDimensionPixelSize(R.dimen.spacing_16)

            binding.modalNavigationBarComponent.applyInsets(
                context = requireContext(),
                insets = insets
            )

            binding.primaryButton.updateLayoutParams<ViewGroup.MarginLayoutParams> {
                bottomMargin = insets.bottom + spacing16
            }

            WindowInsetsCompat.CONSUMED
        }
    }

    private fun adjustKeyboardGuideline() {
        val guideline = binding.keyboardGuideline
        val params = guideline.layoutParams as ConstraintLayout.LayoutParams

        val guidePercent = computeKeyboardGuidelinePercentage(requireContext())
        params.guidePercent = guidePercent

        guideline.layoutParams = params
    }

    private fun setupKeyboard() {
        binding.keyboardComponent.setupKeyboard(
            decimalSeparator = viewModel.getDecimalSeparator(),
            digitButtonCallback = { handleOnKeyboardButtonClicked(it) },
            backspaceButtonCallback = { handleOnKeyboardBackspaceButtonClicked() }
        )
    }

    private fun handleOnKeyboardButtonClicked(text: String) {
        val currentText = binding.editText.editableText?.toString() ?: ""
        val newText = "$currentText$text"

        binding.editText.setText(newText)
    }

    private fun handleOnKeyboardBackspaceButtonClicked() {
        val currentText = binding.editText.editableText?.toString() ?: ""
        val newText = currentText.dropLast(1)

        binding.editText.setText(newText)
    }

    private fun setUpScrollView() {
        binding.scrollView.setOnScrollChangeListener { _, _, _, _, _ ->
            viewModel.handleOnScrollViewScrolled(binding.scrollView)
        }
    }

    private fun setupButtons() {
        binding.modalNavigationBarComponent.closeButton.setOnClickListener {
            requireActivity().finish()
        }

        setupChipButton()
        setupDayChipButton()

        binding.primaryButton.setOnClickListener {
            viewModel.handleOnPrimaryButtonClicked()

            hideKeyboard()
            binding.editText.clearFocus()
        }
    }

    private fun setupChipButton() {
        val scheduleView =
            binding.modalNavigationBarComponent.firstDropDownComponent.containerLinearLayout

        scheduleView.setOnClickListener {
            viewModel.handleOnChipButtonClicked()

            hideKeyboard()
            binding.editText.clearFocus()
        }
    }

    private fun setupDayChipButton() {
        val dayView =
            binding.modalNavigationBarComponent.secondDropDownComponent.containerLinearLayout

        dayView.setOnClickListener {
            viewModel.handleOnDayChipButtonClicked()

            hideKeyboard()
            binding.editText.clearFocus()
        }
    }

    private fun setupEditText() {
        // Το claude.ai λέει πως το showSoftInputOnFocus μπορεί να γίνει overriden
        // από κάποια πληκτρολόγια ή android skins οπότε το κάνουμε disable για σιγουριά.
        binding.editText.isEnabled = false
        binding.editText.showSoftInputOnFocus = false

        binding.editText.addTextChangedListener {
            clearHintIfNeeded()

            viewModel.handleOnAmountTextChanged(it?.toString())
        }

        binding.editText.setOnEditorActionListener { view, actionCode, _ ->
            if (actionCode == EditorInfo.IME_ACTION_DONE) {
                view.clearFocus()
                //viewModel.handleOnKeyboardDoneButtonClicked(view.text?.toString())
            }

            return@setOnEditorActionListener false
        }
    }

    private fun setupBankAccountConstraintLayout() {
        val component = binding.selectedPaymentMethodComponent.containerLinearLayout

        component.setOnClickListener {
            viewModel.handleOnSelectedPaymentMethodClicked()

            hideKeyboard()
            binding.editText.clearFocus()
        }
    }

    private fun observeViewModel() {
        viewModel.navigationBarProperties.observe(viewLifecycleOwner) {
            refreshNavigationBarProperties(it)
        }

        viewModel.navigationBarElevation.observe(viewLifecycleOwner) {
            refreshNavigationBarElevation(it)
        }

        viewModel.currencySymbol.observe(viewLifecycleOwner) {
            refreshCurrencySymbol(it)
        }

        viewModel.amountText.observe(viewLifecycleOwner) {
            automaticallyRefreshAmount(it)
        }

        viewModel.digitsKeyListener.observe(viewLifecycleOwner) {
            refreshKeyboardProperties(it)
        }

        viewModel.messageText.observe(viewLifecycleOwner) {
            refreshMessageText(it)
        }

        viewModel.validationMessage.observe(viewLifecycleOwner) {
            refreshValidationMessage(it)
        }

        viewModel.selectedPaymentMethodDropDownProperties.observe(viewLifecycleOwner) {
            refreshSelectedPaymentMethod(it)
        }

        viewModel.isPrimaryButtonEnabled.observe(viewLifecycleOwner) {
            refreshPrimaryButton(it)
        }

        viewModel.primaryButtonText.observe(viewLifecycleOwner) {
            refreshPrimaryButtonText(it)
        }

        viewModel.eventShowOrHideLoadingDialog.observe(viewLifecycleOwner) {
            it?.let {
                if (it) showLoading()
                else hideLoading()

                viewModel.eventShowOrHideLoadingDialogCompleted()
            }
        }

        viewModel.eventShowBankAccountsDialog.observe(viewLifecycleOwner) {
            it?.let {
                showBankAccountsDialog(it)

                viewModel.eventShowBankAccountsDialogCompleted()
            }
        }

        viewModel.eventShowAddBankAccountDialog.observe(viewLifecycleOwner) {
            it?.let {
                showAddBankAccountDialog()

                viewModel.eventShowAddBankAccountDialogCompleted()
            }
        }

        viewModel.eventShowOrderReviewDialog.observe(viewLifecycleOwner) {
            it?.let {
                showOrderReviewDialog(it)

                viewModel.eventShowOrderReviewDialogCompleted()
            }
        }

        viewModel.eventNavigateToLoadingScreen.observe(viewLifecycleOwner) {
            it?.let {
                navigateToLoadingScreen(it)

                viewModel.eventNavigateToLoadingScreenCompleted()
            }
        }

        viewModel.eventShowDayPickerDialog.observe(viewLifecycleOwner) {
            it?.let {
                showDayPickerDialog(it)

                viewModel.eventShowDayPickerDialogCompleted()
            }
        }

        viewModel.eventShowScheduleOptionsDialog.observe(viewLifecycleOwner) {
            it?.let {
                showScheduleOptionsDialog(it)

                viewModel.eventShowScheduleOptionsDialogCompleted()
            }
        }

        viewModel.eventNavigateToSetupDirectDebitScreen.observe(viewLifecycleOwner) {
            it?.let {
                navigateToSetupDirectDebitScreen(it)

                viewModel.eventNavigateToSetupDirectDebitScreenCompleted()
            }
        }

        viewModel.eventNavigateToSuccessfulScreen.observe(viewLifecycleOwner) {
            it?.let {
                navigateToSuccessfulScreen(it)

                viewModel.eventNavigateToSuccessfulScreenCompleted()
            }
        }

        viewModel.eventShowUpdateRecurringMessageDialog.observe(viewLifecycleOwner) {
            it?.let {
                showUpdateRecurringMessageDialog(it)

                viewModel.eventShowUpdateRecurringMessageDialogCompleted()
            }
        }

        viewModel.eventShowTopUpOptionsDialog.observe(viewLifecycleOwner) {
            it?.let {
                showTopUpOptionsDialog(it)

                viewModel.eventShowTopUpOptionsDialogCompleted()
            }
        }

        viewModel.eventNavigateToRegularBankTransfer.observe(viewLifecycleOwner) {
            it?.let {
                navigateToRegularBankTransfer(it)

                viewModel.eventNavigateToRegularBankTransferCompleted()
            }
        }

        viewModel.eventShowIBANError.observe(viewLifecycleOwner) {
            it?.let {
                showIBANError(it)

                viewModel.eventShowIBANErrorCompleted()
            }
        }

        viewModel.eventShowGenericError.observe(viewLifecycleOwner) {
            it?.let {
                showGenericError(it)

                viewModel.eventShowGenericErrorCompleted()
            }
        }
    }

    private fun refreshNavigationBarProperties(properties: ModalNavigationBarProperties?) {
        properties?.applyTo(binding.modalNavigationBarComponent)
    }

    private fun refreshNavigationBarElevation(elevation: Float?) {
        val spacing0 = resources.getDimension(R.dimen.spacing_0)

        val container = binding.modalNavigationBarComponent.containerConstraintLayout
        container.elevation = elevation ?: spacing0
    }

    private fun refreshCurrencySymbol(text: String?) {
        binding.currencySymbolTextView.text = text
    }

    private fun automaticallyRefreshAmount(text: String?) {
        if (binding.editText.text?.toString() == text) return

        binding.editText.setText(text)
    }

    private fun refreshKeyboardProperties(digitsKeyListener: DigitsKeyListener?) {
        if (digitsKeyListener == null) return

        binding.editText.keyListener = digitsKeyListener
    }

    private fun refreshMessageText(text: String?) {
        binding.messageTextView.text = text
    }

    private fun refreshValidationMessage(validationMessage: ValidationMessage?) {
        val message = validationMessage?.message ?: kotlin.run {
            binding.validationMessageTextView.visibility = View.GONE

            return
        }

        binding.validationMessageTextView.visibility = View.VISIBLE
        binding.validationMessageTextView.text = message

        val textColor = validationMessage.color ?: ContextCompat.getColor(
            requireContext(),
            R.color.primary
        )

        binding.validationMessageTextView.setTextColor(textColor)
    }

    private fun refreshSelectedPaymentMethod(properties: PaymentMethodDropDownProperties?) {
        properties.applyTo(binding.selectedPaymentMethodComponent)
    }

    private fun refreshPrimaryButton(isEnabled: Boolean?) {
        binding.primaryButton.isEnabled = (isEnabled == true)
        binding.primaryButton.elevation = if (isEnabled == true) {
            resources.getDimension(R.dimen.primary_button_elevation)
        } else 0f
    }

    private fun refreshPrimaryButtonText(text: String?) {
        binding.primaryButton.text = text
    }

    private fun showLoading() {
        val isShowingLoadingDialog =
            parentFragmentManager.findFragmentByTag(LoadingDialogFragment.TAG) != null

        if (isShowingLoadingDialog) return

        val fragment = LoadingDialogFragment.newInstance(
            isWithRoundedCorners = false
        )

        parentFragmentManager.beginTransaction()
            .add(R.id.fragment_container, fragment, LoadingDialogFragment.TAG)
            .commit()
    }

    private fun hideLoading() {
        val loadingDialog =
            parentFragmentManager.findFragmentByTag(LoadingDialogFragment.TAG) ?: return

        parentFragmentManager.beginTransaction()
            .remove(loadingDialog)
            .commit()
    }

    private fun clearHintIfNeeded() {
        // We want to clear the hint because the wrap_contents wraps around it

        val text = binding.editText.text?.toString()

        binding.editText.hint = if (text.isNullOrBlank()) {
            resources.getString(R.string.zero_digit)
        } else null
    }

    private fun showBankAccountsDialog(arguments: BankAccountsFragmentArguments) {
        val shouldDisplayAddAccountButton = (arguments.shouldShowAddBankAccountButton)

        val fragment = BankAccountsFragment.newInstance(
            selectedPaymentMethodID = arguments.selectedPaymentMethodID,
            paymentMethods = arguments.paymentMethods,
            shouldDisplayCashDescription = true,
            shouldDisplayAddAccountButton = shouldDisplayAddAccountButton,
            shouldDisplayNextButton = false,
            shouldCheckForIBANError = arguments.shouldCheckForIBANError
        )

        fragment.show(childFragmentManager, "BankAccountsFragment")
    }

    private fun showAddBankAccountDialog() {
        val intent = LinkBankAccountActivity.newIntent(
            context = requireContext()
        )

        linkBankAccountLauncher.launch(intent)
    }

    private fun showOrderReviewDialog(screenArgs: WalletOrderReviewScreenArgs) {
        val fragment = WalletOrderReviewFragment.newInstance(
            titleImageRes = R.drawable.ic_plus_24,
            title = screenArgs.title,
            total = screenArgs.total,
            arriving = screenArgs.arrivingText,
            repeatingText = screenArgs.repeatingText,
            primaryButtonText = screenArgs.primaryButtonText,
            type = WalletOrderReviewTypeEnum.ADD_MONEY,
            didSelectBankAccount = screenArgs.didSelectBankAccount
        )

        fragment.show(childFragmentManager, "WalletOrderReviewFragment")
    }

    private fun navigateToLoadingScreen(screenArgs: AddMoneyLoadingScreenArgs) {
        val intent = LoadingActivity.newIntent(
            context = requireContext(),
            loadingType = LoadingTypeEnum.ADD_MONEY,
            etfID = null,
            etfName = null,
            portfolioPercentage = null,
            portfolioPercentageEstimation = null,
            amount = screenArgs.amount,
            savingsProductID = screenArgs.savingsProductID,
            bankID = null,
            bankAccountID = screenArgs.bankAccountID,
            bankLogoURI = screenArgs.bankLogoURI,
            giftID = null,
            trueLayerID = null,
            saltedgeCustomID = null,
            transactionPreview = null,
            allocationMethod = null,
            executeETFOrdersInRealtime = null,
            canUnlockFreeShare = null
        )

        loadingActivityResultLauncher.launch(intent)
    }

    private fun showDayPickerDialog(screenArgs: DayPickerScreenArgs) {
        val fragment = DayPickerFragment.newInstance(screenArgs)

        fragment.show(childFragmentManager, "DayPickerFragment")
    }

    private fun showScheduleOptionsDialog(screenArgs: ScheduleOptionsScreenArgs) {
        val fragment = ScheduleOptionsFragment.newInstance(
            screenArgs = screenArgs
        )

        fragment.show(childFragmentManager, "ScheduleOptionsFragment")
    }

    private fun navigateToSetupDirectDebitScreen(screenArgs: SetupDirectDebitScreenArgs) {
        val screenArgsJSONString = Gson().toJson(screenArgs)

        val fragment = SetupDirectDebitFragment.newInstance(
            screenArgsJSONString = screenArgsJSONString
        )

        requireActivity().supportFragmentManager.beginTransaction()
            .setCustomAnimations(R.anim.enter, R.anim.exit, R.anim.pop_enter, R.anim.pop_exit)
            .replace(R.id.fragment_container, fragment)
            .addToBackStack(null)
            .commit()
    }

    private fun navigateToSuccessfulScreen(screenArgs: SetupDirectDebitSuccessfulScreenArgs) {
        val fragment = SetupDirectDebitSuccessfulFragment.newInstance(
            title = screenArgs.title,
            subtitle = screenArgs.subtitle,
            didCreateForRepeatingMMF = screenArgs.didCreateForRepeatingMMF,
            repeatingText = screenArgs.repeatingText,
            shouldShowPlanActivationCompletedWithOptionsScreen = screenArgs.shouldShowPlanActivationCompletedWithOptionsScreen,
            shouldShowPaymentMethodUpdatedScreen = screenArgs.shouldShowPaymentMethodUpdatedScreen,
            planActivationCompletedScreenArgsJSONString = screenArgs.planActivationCompletedScreenArgsJSONString
        )

        requireActivity().supportFragmentManager.beginTransaction()
            .replace(R.id.fragment_container, fragment)
            .commit()
    }

    private fun showUpdateRecurringMessageDialog(screenArgs: UpdateRecurringMessageScreenArgs) {
        val fragment = UpdateRecurringMessageFragment.newInstance(screenArgs)

        fragment.show(childFragmentManager, "UpdateRecurringMessageFragment")
    }

    private fun showTopUpOptionsDialog(screenArgs: TopUpOptionsScreenArgs) {
        val fragment = TopUpOptionsFragment.newInstance(screenArgs)

        fragment.show(childFragmentManager, "TopUpOptionsFragment")
    }

    private fun navigateToRegularBankTransfer(screenArgs: RegularBankTransferScreenArgs) {
        val intent = RegularBankTransferActivity.newIntent(requireContext(), screenArgs)

        startActivity(intent)
    }

    private fun showIBANError(message: String?) {
        binding.root.showCustomSnack(message)
    }

    private fun showGenericError(screenArgs: GenericErrorScreenArgs) {
        val fragment = GenericErrorFragment.newInstance(screenArgs = screenArgs)
        fragment.show(childFragmentManager, "GenericErrorFragment")
    }
}
