package com.wealthyhood.wealthyhood.ui.confirmationreceipt.viewholder

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.annotation.ColorInt
import androidx.annotation.DrawableRes
import androidx.core.content.ContextCompat
import androidx.core.view.updatePadding
import androidx.recyclerview.widget.RecyclerView
import com.wealthyhood.wealthyhood.R
import com.wealthyhood.wealthyhood.databinding.ListItemConfirmationReceiptHeaderBinding
import com.wealthyhood.wealthyhood.extensions.updateImageViewWithIconOrURI

class ConfirmationReceiptHeaderViewHolder private constructor(
    private val binding: ListItemConfirmationReceiptHeaderBinding
) : RecyclerView.ViewHolder(binding.root) {

    companion object {

        fun from(parent: ViewGroup): ConfirmationReceiptHeaderViewHolder {
            val layoutInflater = LayoutInflater.from(parent.context)

            val binding =
                ListItemConfirmationReceiptHeaderBinding.inflate(layoutInflater, parent, false)

            return ConfirmationReceiptHeaderViewHolder(binding)
        }
    }

    fun bind(
        paddingStart: Int?,
        paddingTop: Int?,
        paddingEnd: Int?,
        paddingBottom: Int?,
        @DrawableRes iconDrawableRes: Int?,
        iconURI: String?,
        titleText: String?,
        @ColorInt titleTextColor: Int?,
        subtitleText: String?
    ) {
        refreshPadding(
            paddingStart = paddingStart,
            paddingTop = paddingTop,
            paddingEnd = paddingEnd,
            paddingBottom = paddingBottom
        )

        refreshImage(iconDrawableRes, iconURI)
        refreshTitle(titleText, titleTextColor)
        refreshSubtitle(subtitleText)
    }

    private fun refreshPadding(
        paddingStart: Int?,
        paddingTop: Int?,
        paddingEnd: Int?,
        paddingBottom: Int?
    ) {
        val spacing0 = itemView.context.resources.getDimensionPixelSize(R.dimen.spacing_0)

        val finalPaddingStart = paddingStart ?: spacing0
        val finalPaddingTop = paddingTop ?: spacing0
        val finalPaddingEnd = paddingEnd ?: spacing0
        val finalPaddingBottom = paddingBottom ?: spacing0

        binding.rootConstraintLayout.updatePadding(
            finalPaddingStart,
            finalPaddingTop,
            finalPaddingEnd,
            finalPaddingBottom
        )
    }

    private fun refreshImage(@DrawableRes drawableRes: Int?, imageURI: String?) {
        binding.imageView.updateImageViewWithIconOrURI(
            drawableRes = drawableRes,
            imageURI = imageURI
        )
    }

    private fun refreshTitle(text: String?, @ColorInt textColor: Int?) {
        val defaultColor = ContextCompat.getColor(itemView.context, R.color.primary)
        val finalTextColor = textColor ?: defaultColor

        binding.titleTextView.text = text
        binding.titleTextView.setTextColor(finalTextColor)
    }

    private fun refreshSubtitle(text: String?) {
        binding.subtitleTextView.text = text
    }
}
