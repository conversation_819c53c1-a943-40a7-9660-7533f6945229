package com.wealthyhood.wealthyhood.ui.assetdetails.recentactivity

import android.os.Bundle
import android.text.SpannableString
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.core.view.ViewCompat
import androidx.core.view.WindowInsetsCompat
import androidx.core.view.updateLayoutParams
import androidx.fragment.app.Fragment
import androidx.lifecycle.ViewModelProvider
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import androidx.recyclerview.widget.SimpleItemAnimator
import com.wealthyhood.wealthyhood.R
import com.wealthyhood.wealthyhood.databinding.FragmentRecentActivityBinding
import com.wealthyhood.wealthyhood.extensions.findUserLocale
import com.wealthyhood.wealthyhood.extensions.setup
import com.wealthyhood.wealthyhood.extensions.updateElevation
import com.wealthyhood.wealthyhood.extensions.updateForTopInset
import com.wealthyhood.wealthyhood.extensions.updateTitleText
import com.wealthyhood.wealthyhood.model.ConfirmationReceiptScreenArgs
import com.wealthyhood.wealthyhood.model.GenericErrorScreenArgs
import com.wealthyhood.wealthyhood.model.OrderReviewFragmentArguments
import com.wealthyhood.wealthyhood.model.OrderReviewFragmentArguments.Companion.convertToOrderReviewScreenArgs
import com.wealthyhood.wealthyhood.repository.PreferencesRepository
import com.wealthyhood.wealthyhood.ui.assetdetails.AssetDetailsListAdapter
import com.wealthyhood.wealthyhood.ui.confirmationreceipt.ConfirmationReceiptFragment
import com.wealthyhood.wealthyhood.ui.controlcenter.ControlCenterAutomationDescriptionFragment
import com.wealthyhood.wealthyhood.ui.genericerror.GenericErrorFragment
import com.wealthyhood.wealthyhood.ui.loadingdialog.LoadingDialogFragment
import com.wealthyhood.wealthyhood.ui.orderreview.OrderReviewFragment
import com.wealthyhood.wealthyhood.viewholders.TransactionViewHolder

class RecentActivityFragment : Fragment() {

    companion object {

        private const val ARG_ASSET_ID = "RecentActivityFragment.assetID"
        private const val ARG_CURRENT_TICKER_PRICE = "RecentActivityFragment.currentTickerPrice"
        private const val ARG_TRADED_PRICE = "RecentActivityFragment.tradedPrice"
        private const val ARG_TRADED_CURRENCY = "RecentActivityFragment.tradedCurrency"
        private const val ARG_USER_FULL_NAME = "RecentActivityFragment.userFullName"
        private const val ARG_PLAN_PRICE_API_KEY = "RecentActivityFragment.planPriceAPIKey"

        fun newInstance(
            assetID: String?,
            currentTickerPrice: Float?,
            tradedPrice: Float?,
            tradedCurrency: String?,
            userFullName: String?,
            planPriceAPIKey: String?
        ): RecentActivityFragment {
            val arguments = Bundle()

            arguments.putString(ARG_ASSET_ID, assetID)

            currentTickerPrice?.let {
                arguments.putFloat(ARG_CURRENT_TICKER_PRICE, it)
            }

            tradedPrice?.let {
                arguments.putFloat(ARG_TRADED_PRICE, it)
            }

            arguments.putString(ARG_TRADED_CURRENCY, tradedCurrency)
            arguments.putString(ARG_USER_FULL_NAME, userFullName)
            arguments.putString(ARG_PLAN_PRICE_API_KEY, planPriceAPIKey)

            val fragment = RecentActivityFragment()
            fragment.arguments = arguments

            return fragment
        }
    }

    private lateinit var _viewModel: RecentActivityViewModel
    private val viewModel get() = _viewModel

    private lateinit var _binding: FragmentRecentActivityBinding
    private val binding get() = _binding

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        setupFragmentResultListeners()
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        setupViewModel()
        setupBinding(inflater, container)

        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        setupWindowInsetsListener()
        setupNavigationBar()
        setupRecyclerView()

        observeViewModel()
    }

    private fun setupFragmentResultListeners() {
        childFragmentManager.setFragmentResultListener(
            OrderReviewFragment.REQUEST_KEY_SHOW_DESCRIPTION,
            this
        ) { _, bundle ->
            val infoRowID = bundle.getString(OrderReviewFragment.BUNDLE_KEY_INFO_ROW_ID)
            viewModel.handleOnInfoRowInfoButtonClicked(infoRowID)
        }

        childFragmentManager.setFragmentResultListener(
            GenericErrorFragment.REQUEST_KEY_RETRY,
            this
        ) { _, _ ->
            viewModel.handleOnGenericErrorRetryButtonClicked()
        }

        childFragmentManager.setFragmentResultListener(
            OrderReviewFragment.REQUEST_KEY_CONFIRM,
            this
        ) { _, bundle ->
            val orderID = bundle.getString(OrderReviewFragment.BUNDLE_KEY_ITEM_ID)

            viewModel.handleOnCancelButtonClicked(orderID)
        }
    }

    private fun setupViewModel() {
        val assetID = arguments?.getString(ARG_ASSET_ID)
        val currentTickerPrice = arguments?.getFloat(ARG_CURRENT_TICKER_PRICE, -1f)
        val tradedPrice = arguments?.getFloat(ARG_TRADED_PRICE, -1f)
        val tradedCurrency = arguments?.getString(ARG_TRADED_CURRENCY)
        val userFullName = arguments?.getString(ARG_USER_FULL_NAME)
        val planPriceAPIKey = arguments?.getString(ARG_PLAN_PRICE_API_KEY)

        val finalCurrentTickerPrice = if (currentTickerPrice == -1f) null else currentTickerPrice

        val viewModelFactory = RecentActivityViewModelFactory(
            application = requireActivity().application,
            assetID = assetID,
            currentTickerPrice = finalCurrentTickerPrice,
            tradedPrice = tradedPrice,
            tradedCurrency = tradedCurrency,
            userFullName = userFullName,
            planPriceAPIKey = planPriceAPIKey
        )

        val viewModelClass = RecentActivityViewModel::class.java
        _viewModel = ViewModelProvider(this, viewModelFactory)[viewModelClass]
    }

    private fun setupBinding(inflater: LayoutInflater, container: ViewGroup?) {
        _binding = FragmentRecentActivityBinding.inflate(inflater, container, false)
    }

    private fun setupWindowInsetsListener() {
        // https://developer.android.com/develop/ui/views/layout/sw-keyboard
        // https://developer.android.com/develop/ui/views/layout/edge-to-edge

        ViewCompat.setOnApplyWindowInsetsListener(binding.root) { _, windowInsets ->
            val insets = windowInsets.getInsets(WindowInsetsCompat.Type.systemBars())

            binding.navigationBarComponent.updateForTopInset(insets.top)

            binding.recyclerView.updateLayoutParams<ViewGroup.MarginLayoutParams> {
                bottomMargin = insets.bottom
            }

            WindowInsetsCompat.CONSUMED
        }
    }

    private fun setupNavigationBar() {
        binding.navigationBarComponent.setup(
            shouldShowCloseButton = true,
            closeButtonCallback = {
                requireActivity().finish()
            },
            titleText = null,
            titleAlpha = 1f,
            rightButtonProperties = null
        )
    }

    private fun setupRecyclerView() {
        (binding.recyclerView.itemAnimator as? SimpleItemAnimator)?.supportsChangeAnimations = false
        binding.recyclerView.layoutManager = LinearLayoutManager(requireContext())

        val transactionListener = object : TransactionViewHolder.TransactionViewHolderListener {

            override fun onItemClicked(itemID: String?) {
                viewModel.handleOnTransactionClicked(itemID)
            }
        }

        // TODO: Use MVVM

        val userLocale = PreferencesRepository(requireContext()).findUserLocale()

        binding.recyclerView.adapter = AssetDetailsListAdapter(
            userLocale = userLocale,
            headerListener = null,
            topHoldingsListener = null,
            sectionHeaderListener = null,
            lineChartListener = null,
            doubleInfoRowListener = null,
            newsArticleListener = null,
            kidListener = null,
            footerListener = null,
            tagListener = null,
            transactionListener = transactionListener
        )

        binding.recyclerView.addOnScrollListener(object : RecyclerView.OnScrollListener() {

            override fun onScrolled(recyclerView: RecyclerView, dx: Int, dy: Int) {
                super.onScrolled(recyclerView, dx, dy)

                viewModel.handleOnRecyclerViewScrolled(recyclerView)
            }
        })
    }

    private fun observeViewModel() {
        viewModel.navigationTitleText.observe(viewLifecycleOwner) {
            refreshNavigationTitleText(it)
        }

        viewModel.navigationBarElevation.observe(viewLifecycleOwner) {
            refreshNavigationBarElevation(it)
        }

        viewModel.dataItems.observe(viewLifecycleOwner) {
            refreshDataItems(it)
        }

        viewModel.isLoadingIndicatorVisible.observe(viewLifecycleOwner) {
            refreshLoadingIndicatorVisibility(it)
        }

        viewModel.eventRefreshTransactions.observe(viewLifecycleOwner) {
            viewModel.handlePollingResult(it)
        }

        viewModel.eventShowOrHideLoadingDialog.observe(viewLifecycleOwner) {
            it?.let {
                if (it) showLoading()
                else hideLoading()

                viewModel.eventShowOrHideLoadingDialogCompleted()
            }
        }

        viewModel.eventShowConfirmationReceiptDialog.observe(viewLifecycleOwner) {
            it?.let {
                showConfirmationReceiptDialog(it)

                viewModel.eventShowConfirmationReceiptDialogCompleted()
            }
        }

        viewModel.eventShowOrderReviewDialog.observe(viewLifecycleOwner) {
            it?.let {
                showOrderReviewDialog(it)

                viewModel.eventShowOrderReviewDialogCompleted()
            }
        }

        viewModel.eventShowGenericError.observe(viewLifecycleOwner) {
            it?.let {
                showGenericError(it)

                viewModel.eventShowGenericErrorCompleted()
            }
        }

        viewModel.eventShowFXRateDescriptionDialog.observe(viewLifecycleOwner) {
            it?.let {
                showFXRateDescriptionDialog(it)

                viewModel.eventShowFXRateDescriptionDialogCompleted()
            }
        }

        viewModel.eventSetActivityResult.observe(viewLifecycleOwner) {
            it?.let {
                setActivityResult(it)

                viewModel.eventSetActivityResultCompleted()
            }
        }
    }

    private fun refreshNavigationTitleText(text: String?) {
        binding.navigationBarComponent.updateTitleText(text)
    }

    private fun refreshNavigationBarElevation(elevation: Float?) {
        binding.navigationBarComponent.updateElevation(elevation)
    }

    private fun refreshDataItems(dataItems: List<AssetDetailsListAdapter.DataItem>?) {
        (binding.recyclerView.adapter as? AssetDetailsListAdapter)?.submitList(dataItems)
    }

    private fun refreshLoadingIndicatorVisibility(isVisible: Boolean?) {
        val visibility = if (isVisible == true) View.VISIBLE else View.GONE
        binding.circularProgressIndicator.visibility = visibility
    }

    private fun showLoading() {
        val isShowingLoadingDialog =
            parentFragmentManager.findFragmentByTag(LoadingDialogFragment.TAG) != null

        if (isShowingLoadingDialog) return

        val fragment = LoadingDialogFragment.newInstance(
            isWithRoundedCorners = false
        )

        parentFragmentManager.beginTransaction()
            .add(R.id.fragment_container, fragment, LoadingDialogFragment.TAG)
            .commit()
    }

    private fun hideLoading() {
        val loadingDialog =
            parentFragmentManager.findFragmentByTag(LoadingDialogFragment.TAG) ?: return

        parentFragmentManager.beginTransaction()
            .remove(loadingDialog)
            .commit()
    }

    private fun showConfirmationReceiptDialog(screenArgs: ConfirmationReceiptScreenArgs) {
        val fragment = ConfirmationReceiptFragment.newInstance(screenArgs)

        fragment.show(childFragmentManager, "ConfirmationReceiptFragment")
    }

    private fun showOrderReviewDialog(arguments: OrderReviewFragmentArguments) {
        val screenArgs = arguments.convertToOrderReviewScreenArgs()
        val fragment = OrderReviewFragment.newInstance(screenArgs)

        fragment.show(childFragmentManager, "OrderReviewFragment")
    }

    private fun showGenericError(screenArgs: GenericErrorScreenArgs) {
        val fragment = GenericErrorFragment.newInstance(screenArgs)
        fragment.show(childFragmentManager, "GenericErrorFragment")
    }

    private fun showFXRateDescriptionDialog(formattedFXRate: String) {
        val title = resources.getString(R.string.fx_rate_description_title)

        val description = resources.getString(
            R.string.fx_rate_description_text,
            formattedFXRate
        )

        // TODO: Create a generic Fragment to use.
        val fragment = ControlCenterAutomationDescriptionFragment.newInstance(
            title = title,
            description = SpannableString(description),
            shouldShowCloseButton = false,
            closeButtonText = null,
            shouldCreateRebalanceAutomationAfterClosing = false
        )

        fragment.show(childFragmentManager, "ControlCenterAutomationDescriptionFragment")
    }

    private fun setActivityResult(result: Int) {
        requireActivity().setResult(result)
    }
}
