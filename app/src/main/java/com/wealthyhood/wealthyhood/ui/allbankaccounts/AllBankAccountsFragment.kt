package com.wealthyhood.wealthyhood.ui.allbankaccounts

import android.app.Activity
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.activity.OnBackPressedCallback
import androidx.activity.result.contract.ActivityResultContracts
import androidx.core.view.ViewCompat
import androidx.core.view.WindowInsetsCompat
import androidx.core.view.updateLayoutParams
import androidx.fragment.app.Fragment
import androidx.lifecycle.ViewModelProvider
import androidx.recyclerview.widget.DefaultItemAnimator
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.wealthyhood.wealthyhood.R
import com.wealthyhood.wealthyhood.databinding.FragmentAllBankAccountsBinding
import com.wealthyhood.wealthyhood.extensions.animateTitleToAlpha
import com.wealthyhood.wealthyhood.extensions.setup
import com.wealthyhood.wealthyhood.extensions.updateElevation
import com.wealthyhood.wealthyhood.extensions.updateForTopInset
import com.wealthyhood.wealthyhood.extensions.updateTitleText
import com.wealthyhood.wealthyhood.model.GenericMessageSheetScreenArgs
import com.wealthyhood.wealthyhood.ui.genericmessagesheet.GenericMessageSheetFragment
import com.wealthyhood.wealthyhood.ui.linkbankaccount.LinkBankAccountActivity
import com.wealthyhood.wealthyhood.ui.loadingdialog.LoadingDialogFragment
import com.wealthyhood.wealthyhood.ui.myaccount.PaymentMethodOptionsFragment
import com.wealthyhood.wealthyhood.viewholders.ActionViewHolder
import com.wealthyhood.wealthyhood.viewholders.BankAccountWithOptionsViewHolder

class AllBankAccountsFragment : Fragment() {

    companion object {

        fun newInstance(): AllBankAccountsFragment {
            return AllBankAccountsFragment()
        }
    }

    private lateinit var _viewModel: AllBankAccountsViewModel
    private val viewModel get() = _viewModel

    private lateinit var _binding: FragmentAllBankAccountsBinding
    private val binding get() = _binding

    private var isNavigationTitleVisible = false

    private val linkBankAccountLauncher = registerForActivityResult(
        ActivityResultContracts.StartActivityForResult()
    ) { result ->
        if (result.resultCode == Activity.RESULT_OK) {
            viewModel.handleOnNewBankAccountLinked()
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        setupOnBackButtonInterceptor()
        setupFragmentResultListeners()
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        setupViewModel()
        setupBinding(inflater, container)

        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        setupWindowInsetsListener()
        setupNavigationBar()
        setupRecyclerView()

        observeViewModel()
    }

    private fun setupOnBackButtonInterceptor() {
        requireActivity().onBackPressedDispatcher.addCallback(
            this,
            object : OnBackPressedCallback(true) {

                override fun handleOnBackPressed() {
                    viewModel.handleOnBackButtonPressed()
                }
            })
    }

    private fun setupFragmentResultListeners() {
        childFragmentManager.setFragmentResultListener(
            PaymentMethodOptionsFragment.REQUEST_KEY_DELETE,
            this
        ) { _, bundle ->
            val bankAccountID =
                bundle.getString(PaymentMethodOptionsFragment.BUNDLE_KEY_PAYMENT_METHOD_ID)

            viewModel.handleOnDeactivateBankAccountRequest(bankAccountID)
        }

        childFragmentManager.setFragmentResultListener(
            GenericMessageSheetFragment.REQUEST_KEY_DONE,
            this
        ) { _, _ ->
            dismissAllMessageSheetDialogs()
        }
    }

    private fun setupViewModel() {
        val viewModelClass = AllBankAccountsViewModel::class.java
        _viewModel = ViewModelProvider(this)[viewModelClass]
    }

    private fun setupBinding(inflater: LayoutInflater, container: ViewGroup?) {
        _binding = FragmentAllBankAccountsBinding.inflate(inflater, container, false)
    }

    private fun setupWindowInsetsListener() {
        // https://developer.android.com/develop/ui/views/layout/sw-keyboard
        // https://developer.android.com/develop/ui/views/layout/edge-to-edge

        ViewCompat.setOnApplyWindowInsetsListener(binding.root) { _, windowInsets ->
            val insets = windowInsets.getInsets(WindowInsetsCompat.Type.systemBars())

            binding.navigationBarComponent.updateForTopInset(insets.top)

            binding.recyclerView.updateLayoutParams<ViewGroup.MarginLayoutParams> {
                bottomMargin = insets.bottom
            }

            WindowInsetsCompat.CONSUMED
        }
    }

    private fun setupNavigationBar() {
        binding.navigationBarComponent.setup(
            shouldShowCloseButton = true,
            closeButtonCallback = {
                viewModel.handleOnBackButtonPressed()
            },
            titleText = null,
            titleAlpha = 0f,
            rightButtonProperties = null
        )
    }

    private fun setupRecyclerView() {
        binding.recyclerView.layoutManager = LinearLayoutManager(requireContext())

        val itemAnimator = object : DefaultItemAnimator() {

            override fun onAnimationFinished(viewHolder: RecyclerView.ViewHolder) {
                super.onAnimationFinished(viewHolder)

                //viewModel.handleOnRecyclerViewScrolled(binding.recyclerView)
            }
        }

        itemAnimator.supportsChangeAnimations = false

        binding.recyclerView.itemAnimator = itemAnimator

        val bankAccountListener =
            object : BankAccountWithOptionsViewHolder.BankAccountWithOptionsViewHolderListener {

                override fun onOptionsButtonClicked(itemID: String?) {
                    viewModel.handleOnBankAccountOptionsButtonClicked(itemID)
                }
            }

        val actionListener = object : ActionViewHolder.ActionViewHolderListener {

            override fun onItemClicked(itemID: String?) {
                viewModel.handleOnActionClicked(itemID)
            }
        }

        binding.recyclerView.adapter = AllBankAccountsListAdapter(
            bankAccountListener = bankAccountListener,
            actionListener = actionListener
        )

        binding.recyclerView.addOnScrollListener(object : RecyclerView.OnScrollListener() {

            override fun onScrolled(recyclerView: RecyclerView, dx: Int, dy: Int) {
                super.onScrolled(recyclerView, dx, dy)

                viewModel.handleOnRecyclerViewScrolled(recyclerView)
            }
        })
    }

    private fun observeViewModel() {
        viewModel.navigationBarElevation.observe(viewLifecycleOwner) {
            refreshNavigationBarElevation(it)
        }

        viewModel.isNavigationTitleVisible.observe(viewLifecycleOwner) {
            refreshNavigationBarTitleVisibility(it)
        }

        viewModel.navigationTitleText.observe(viewLifecycleOwner) {
            refreshNavigationTitleText(it)
        }

        viewModel.dataItems.observe(viewLifecycleOwner) {
            refreshDataItems(it)
        }

        viewModel.eventShowOrHideLoadingDialog.observe(viewLifecycleOwner) {
            it?.let {
                if (it) showLoading()
                else hideLoading()

                viewModel.eventShowOrHideLoadingDialogCompleted()
            }
        }

        viewModel.eventShowPaymentMethodOptionsDialog.observe(viewLifecycleOwner) {
            it?.let {
                showPaymentMethodOptionsDialog(it)

                viewModel.eventShowPaymentMethodOptionsDialogCompleted()
            }
        }

        viewModel.eventShowAddBankAccountScreen.observe(viewLifecycleOwner) {
            it?.let {
                showAddBankAccountScreen()

                viewModel.eventShowAddBankAccountScreenCompleted()
            }
        }

        viewModel.eventShowGenericMessageSheet.observe(viewLifecycleOwner) {
            it?.let {
                showGenericMessageSheet(it)

                viewModel.eventShowGenericMessageSheetCompleted()
            }
        }

        viewModel.eventCloseScreenWithResult.observe(viewLifecycleOwner) {
            it?.let {
                closeScreenWithResult(it)

                viewModel.eventCloseScreenWithResultCompleted()
            }
        }
    }

    private fun refreshNavigationBarElevation(elevation: Float?) {
        binding.navigationBarComponent.updateElevation(elevation)
    }

    private fun refreshNavigationBarTitleVisibility(isVisible: Boolean?) {
        if (isVisible == null) return
        if (isVisible == isNavigationTitleVisible) return

        isNavigationTitleVisible = isVisible

        val toAlpha = if (isVisible) 1.0f else 0f

        binding.navigationBarComponent.animateTitleToAlpha(toAlpha)
    }

    private fun refreshNavigationTitleText(text: String?) {
        binding.navigationBarComponent.updateTitleText(text)
    }

    private fun refreshDataItems(dataItems: List<AllBankAccountsListAdapter.DataItem>?) {
        (binding.recyclerView.adapter as? AllBankAccountsListAdapter)?.submitList(dataItems)
    }

    private fun showLoading() {
        val isShowingLoadingDialog =
            parentFragmentManager.findFragmentByTag(LoadingDialogFragment.TAG) != null

        if (isShowingLoadingDialog) return

        val fragment = LoadingDialogFragment.newInstance(
            isWithRoundedCorners = false
        )

        parentFragmentManager.beginTransaction()
            .add(R.id.fragment_container, fragment, LoadingDialogFragment.TAG)
            .commit()
    }

    private fun hideLoading() {
        val loadingDialog =
            parentFragmentManager.findFragmentByTag(LoadingDialogFragment.TAG) ?: return

        parentFragmentManager.beginTransaction()
            .remove(loadingDialog)
            .commit()
    }

    private fun showPaymentMethodOptionsDialog(bankAccountID: String) {
        val fragment = PaymentMethodOptionsFragment.newInstance(bankAccountID)

        fragment.show(childFragmentManager, "PaymentMethodOptionsFragment")
    }

    private fun showAddBankAccountScreen() {
        val intent = LinkBankAccountActivity.newIntent(
            context = requireContext()
        )

        linkBankAccountLauncher.launch(intent)
    }

    private fun showGenericMessageSheet(screenArgs: GenericMessageSheetScreenArgs) {
        val fragment = GenericMessageSheetFragment.newInstance(screenArgs)

        fragment.show(childFragmentManager, "GenericMessageSheetFragment")
    }

    private fun closeScreenWithResult(result: Int) {
        requireActivity().setResult(result)
        requireActivity().finish()
    }

    private fun dismissAllMessageSheetDialogs() {
        childFragmentManager.fragments.forEach {
            (it as? GenericMessageSheetFragment)?.dismiss()
        }
    }
}
