package com.wealthyhood.wealthyhood.ui.confirmationreceipt.viewholder

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.annotation.DrawableRes
import androidx.core.view.updatePadding
import androidx.recyclerview.widget.RecyclerView
import com.wealthyhood.wealthyhood.R
import com.wealthyhood.wealthyhood.databinding.ListItemConfirmationReceiptActionBinding

class ConfirmationReceiptActionViewHolder private constructor(
    private val binding: ListItemConfirmationReceiptActionBinding,
    private val listener: ConfirmationReceiptActionViewHolderListener?
) : RecyclerView.ViewHolder(binding.root) {

    interface ConfirmationReceiptActionViewHolderListener {

        fun onItemClicked()
    }

    companion object {

        fun from(
            parent: ViewGroup,
            listener: ConfirmationReceiptActionViewHolderListener?
        ): ConfirmationReceiptActionViewHolder {
            val layoutInflater = LayoutInflater.from(parent.context)

            val binding =
                ListItemConfirmationReceiptActionBinding.inflate(layoutInflater, parent, false)

            return ConfirmationReceiptActionViewHolder(binding, listener)
        }
    }

    init {
        setupButtons()
    }

    fun bind(
        paddingStart: Int?,
        paddingTop: Int?,
        paddingEnd: Int?,
        paddingBottom: Int?,
        @DrawableRes imageDrawableRes: Int,
        titleText: String?
    ) {
        refreshPadding(
            paddingStart = paddingStart,
            paddingTop = paddingTop,
            paddingEnd = paddingEnd,
            paddingBottom = paddingBottom
        )

        refreshImage(imageDrawableRes)
        refreshTitle(titleText)
    }

    fun setupButtons() {
        itemView.setOnClickListener {
            listener?.onItemClicked()
        }
    }

    private fun refreshPadding(
        paddingStart: Int?,
        paddingTop: Int?,
        paddingEnd: Int?,
        paddingBottom: Int?
    ) {
        val spacing0 = itemView.context.resources.getDimensionPixelSize(R.dimen.spacing_0)

        val finalPaddingStart = paddingStart ?: spacing0
        val finalPaddingTop = paddingTop ?: spacing0
        val finalPaddingEnd = paddingEnd ?: spacing0
        val finalPaddingBottom = paddingBottom ?: spacing0

        binding.rootConstraintLayout.updatePadding(
            finalPaddingStart,
            finalPaddingTop,
            finalPaddingEnd,
            finalPaddingBottom
        )
    }

    private fun refreshImage(@DrawableRes drawableRes: Int) {
        binding.iconImageView.setImageResource(drawableRes)
    }

    private fun refreshTitle(text: String?) {
        binding.titleTextView.text = text
    }
}
