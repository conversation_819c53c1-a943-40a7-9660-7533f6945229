package com.wealthyhood.wealthyhood.ui.bankaccountdetails

import android.app.Application
import android.text.SpannableString
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import com.wealthyhood.wealthyhood.R
import com.wealthyhood.wealthyhood.extensions.copyToClipboard
import com.wealthyhood.wealthyhood.extensions.generateInfoRowDataItems
import com.wealthyhood.wealthyhood.model.BankAccountDetailsScreenArgs
import com.wealthyhood.wealthyhood.repository.PreferencesRepository
import com.wealthyhood.wealthyhood.ui.regularbanktransfer.RegularBankTransferListAdapter.DataItem

class BankAccountDetailsViewModel(
    application: Application,
    private val screenArgs: BankAccountDetailsScreenArgs?
) : AndroidViewModel(application) {

    private val context = application

    private val preferencesRepository = PreferencesRepository(application) // TODO: Use DI

    private val bankDetails = preferencesRepository.getBankDetails()

    private val _dataItems = MutableLiveData<List<DataItem>?>()
    val dataItems: LiveData<List<DataItem>?>
        get() = _dataItems

    private val _eventShowToastMessage = MutableLiveData<String?>()
    val eventShowToastMessage: LiveData<String?>
        get() = _eventShowToastMessage

    fun eventShowToastMessageCompleted() {
        _eventShowToastMessage.value = null
    }

    init {
        refreshDataItems()
    }

    fun handleOnInfoRowCopyButtonClicked(itemID: String?) {
        bankDetails?.copyToClipboard(
            context = context,
            itemID = itemID,
            userIBAN = screenArgs?.userIBAN
        )

        _eventShowToastMessage.value = context.getString(R.string.copied_to_clipboard_message_text)
    }

    private fun refreshDataItems() {
        _dataItems.value = generateDataItems()
    }

    private fun generateDataItems(): List<DataItem> {
        val finalAnswer = mutableListOf<DataItem>()

        finalAnswer.add(generateHeaderItem())

        generateInfoRowItems()?.let {
            finalAnswer.addAll(it)
        }

        return finalAnswer
    }

    private fun generateHeaderItem(): DataItem.HeaderItem {
        val spacing16 = context.resources.getDimensionPixelSize(R.dimen.spacing_16)
        val spacing24 = context.resources.getDimensionPixelSize(R.dimen.spacing_24)
        val spacing40 = context.resources.getDimensionPixelSize(R.dimen.spacing_40)

        val screenTitle = context.resources.getString(R.string.account_details_header_title)
        val spannableStringTitle = SpannableString(screenTitle)

        return DataItem.HeaderItem(
            id = "headerItem",
            headerType = null,
            paddingStart = spacing16,
            paddingTop = spacing24,
            paddingEnd = spacing16,
            paddingBottom = spacing40,
            textAppearanceRes = R.style.HeadingsH2Mobile_Primary,
            title = spannableStringTitle
        )
    }

    private fun generateInfoRowItems(): List<DataItem.InfoRowItem>? {
        val dataItems = bankDetails?.generateInfoRowDataItems(
            context = context,
            userIBAN = screenArgs?.userIBAN,
            shouldGenerateAllInfoRows = true
        )

        dataItems?.lastOrNull()?.shouldShowSeparator = false

        return dataItems
    }
}
