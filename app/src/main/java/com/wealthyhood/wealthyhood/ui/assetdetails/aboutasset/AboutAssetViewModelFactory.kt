package com.wealthyhood.wealthyhood.ui.assetdetails.aboutasset

import android.app.Application
import androidx.lifecycle.ViewModel
import androidx.lifecycle.ViewModelProvider
import com.wealthyhood.wealthyhood.model.AboutAssetScreenArgs

@Suppress("UNCHECKED_CAST")
class AboutAssetViewModelFactory(
    private val application: Application,
    private val screenArgs: AboutAssetScreenArgs?
) : ViewModelProvider.Factory {

    override fun <T : ViewModel> create(modelClass: Class<T>): T {
        if (modelClass.isAssignableFrom(AboutAssetViewModel::class.java)) {
            return AboutAssetViewModel(
                application = application,
                screenArgs = screenArgs
            ) as T
        }

        throw IllegalArgumentException("Unknown ViewModel class")
    }
}
