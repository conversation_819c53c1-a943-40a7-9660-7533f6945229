package com.wealthyhood.wealthyhood.domain

import com.wealthyhood.wealthyhood.service.DashboardChartTenor
import com.wealthyhood.wealthyhood.service.Portfolio
import com.wealthyhood.wealthyhood.service.User
import com.wealthyhood.wealthyhood.ui.investments.InvestmentsLoader

data class InvestmentsData(
    val user: User?,
    val portfolioWithReturns: Portfolio?,
    val chartData: Map<String, DashboardChartTenor>?,
    val timestamp: Long,
    val failedAPICalls: List<InvestmentsLoader.FailedAPICall>
)
