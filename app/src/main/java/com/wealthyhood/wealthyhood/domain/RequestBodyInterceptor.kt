package com.wealthyhood.wealthyhood.domain

import io.sentry.Breadcrumb
import io.sentry.Sentry
import io.sentry.SentryLevel
import okhttp3.Interceptor
import okhttp3.RequestBody
import okhttp3.RequestBody.Companion.toRequestBody
import okhttp3.Response
import okio.Buffer

class RequestBodyInterceptor : Interceptor {

    override fun intercept(chain: Interceptor.Chain): Response {
        val originalRequest = chain.request()
        val requestBody = originalRequest.body

        if (requestBody == null) {
            val response = chain.proceed(originalRequest)
            return response
        }

        val isBodyLoggable = isLoggableBody(requestBody)

        if (!isBodyLoggable) {
            Sentry.addBreadcrumb(
                Breadcrumb().apply {
                    message = "Request body is not loggable"
                    level = SentryLevel.ERROR
                }
            )

            val response = chain.proceed(originalRequest)
            return response
        }

        val bodyString = try {
            val buffer = Buffer()

            // Το writeTo κάνει consume το body οπότε μετά θα πρέπει να το ξαναφτιάξουμε.
            requestBody.writeTo(buffer)

            buffer.readUtf8()
        } catch (e: Exception) {
            // Εάν αποτύχει η writeTo τότε το body μπορεί να έχει καταναλωθεί μερικώς οπότε
            // να έχει καταστραφεί. Σε αυτήν την περίπτωση στέλνουμε αυτό ως body
            // για να το δούμε στο Sentry.

            "Error reading body"
        }

        // Εάν δεν είναι null σημαίνει ότι κάναμε consume το body οπότε πρέπει
        // να φτιάξουμε καινούριο.

        val newBody = bodyString.toRequestBody(requestBody.contentType())

        val newRequest = originalRequest.newBuilder()
            .method(originalRequest.method, newBody)
            .build()

        val response = chain.proceed(newRequest)

        if (!response.isSuccessful) {
            Sentry.addBreadcrumb(
                Breadcrumb().apply {
                    message = "Failed request body: $bodyString"
                    level = SentryLevel.ERROR
                }
            )
        }

        return response
    }

    private fun isLoggableBody(body: RequestBody): Boolean {
        return try {
            val contentType = body.contentType()

            val isText = contentType?.subtype?.lowercase()?.run {
                contains("json") || contains("xml") || contains("plain") || contains("form")
            } ?: false

            val length = body.contentLength()

            isText && length in 0..4096 // ασφαλές όριο
        } catch (e: Exception) {
            false
        }
    }
}
