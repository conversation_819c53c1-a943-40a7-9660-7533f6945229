package com.wealthyhood.wealthyhood.domain

import com.wealthyhood.wealthyhood.extensions.getCashForCurrency
import com.wealthyhood.wealthyhood.model.PaymentMethod
import com.wealthyhood.wealthyhood.service.BankAccount
import com.wealthyhood.wealthyhood.service.Portfolio

class FindDefaultBuyPaymentMethodUseCase {

    operator fun invoke(
        willExecuteSingleBuyOrder: Boolean,
        portfolio: Portfolio?,
        paymentMethods: List<PaymentMethod>?,
        currencyISOCode: String?,
        shouldCheckForIBANError: Boolean
    ): PaymentMethod? {
        val cash = portfolio?.getCashForCurrency(currencyISOCode) ?: 0.0

        val giftMethod = findFirstGiftPaymentMethod(paymentMethods)
        val cashMethod = if (cash > 0) findCashPaymentMethod(paymentMethods) else null

        val bankMethod = findFirstBankAccountPaymentMethod(
            paymentMethods = paymentMethods,
            shouldCheckForIBANError = shouldCheckForIBANError
        )

        val firstPaymentMethod = paymentMethods?.firstOrNull()

        if (!willExecuteSingleBuyOrder) {
            return bankMethod
        }

        return giftMethod ?: cashMethod ?: bankMethod ?: firstPaymentMethod
    }

    private fun findFirstGiftPaymentMethod(paymentMethods: List<PaymentMethod>?): PaymentMethod? {
        return paymentMethods?.find { it.type == PaymentMethod.TYPE_GIFT_PAYMENT }
    }

    private fun findCashPaymentMethod(paymentMethods: List<PaymentMethod>?): PaymentMethod? {
        return paymentMethods?.find { it.type == PaymentMethod.TYPE_CASH_ACCOUNT }
    }

    private fun findFirstBankAccountPaymentMethod(
        paymentMethods: List<PaymentMethod>?,
        shouldCheckForIBANError: Boolean
    ): PaymentMethod? {
        val bankAccountPaymentMethods = paymentMethods?.filter {
            it.type == PaymentMethod.TYPE_BANK_ACCOUNT
        }

        // Επιλέγουμε το 1ο bank account που είναι available ή το 1ο not available
        // αν δεν υπάρχει κάποιο available.

        val firstAvailableBankAccount = bankAccountPaymentMethods?.find {
            val isBankAccountNotAvailable = isBankAccountNotAvailable(
                bankAccount = it.bankAccount,
                shouldCheckForIBANError = shouldCheckForIBANError
            )

            !isBankAccountNotAvailable
        }

        return firstAvailableBankAccount ?: bankAccountPaymentMethods?.firstOrNull()
    }

    private fun isBankAccountNotAvailable(
        bankAccount: BankAccount?,
        shouldCheckForIBANError: Boolean
    ): Boolean {
        if (!shouldCheckForIBANError) return false

        return (bankAccount?.isAvailableForDirectDebit != true)
    }
}
