package com.wealthyhood.wealthyhood.domain

import android.content.Context
import android.text.SpannableString
import com.wealthyhood.wealthyhood.R
import com.wealthyhood.wealthyhood.extensions.cleanSpecialFormattingCharacters
import com.wealthyhood.wealthyhood.extensions.convertToListOfStrings
import com.wealthyhood.wealthyhood.extensions.findBoldSubstrings
import com.wealthyhood.wealthyhood.extensions.formatAsPercentage
import com.wealthyhood.wealthyhood.extensions.formatBoldSubstrings
import com.wealthyhood.wealthyhood.extensions.generateFormattedCurrency
import com.wealthyhood.wealthyhood.model.DescriptionTypeEnum
import com.wealthyhood.wealthyhood.service.Plan
import com.wealthyhood.wealthyhood.service.SavingsProductFeeDetails
import java.util.Locale

class GenerateDescriptionItemsUseCase {

    operator fun invoke(
        context: Context,
        currencyISOCode: String?,
        userLocale: Locale,
        descriptionType: DescriptionTypeEnum
    ): List<DescriptionItem> {
        return when (descriptionType) {
            DescriptionTypeEnum.STOCKS -> generateStocksItems(context)

            DescriptionTypeEnum.BONDS -> generateBondsItems(
                context = context,
                currencyISOCode = currencyISOCode,
                userLocale = userLocale
            )

            DescriptionTypeEnum.COMMODITIES -> generateCommoditiesItems(context)
            DescriptionTypeEnum.REAL_ESTATE -> generateRealEstateItems(context)
            DescriptionTypeEnum.TECHNOLOGY -> generateTechnologyItems(context)
            DescriptionTypeEnum.HEALTHCARE -> generateHealthcareItems(context)
            DescriptionTypeEnum.CONSUMER -> generateConsumerItems(context)
            DescriptionTypeEnum.ENERGY -> generateEnergyItems(context)
            DescriptionTypeEnum.COMMUNICATION -> generateCommunicationItems(context)
            DescriptionTypeEnum.FINANCIALS -> generateFinancialsItems(context)
            DescriptionTypeEnum.INDUSTRIALS -> generateIndustrialsItems(context)
            DescriptionTypeEnum.UTILITIES -> generateUtilitiesItems(context)
            DescriptionTypeEnum.MATERIALS -> generateMaterialsItems(context)
            DescriptionTypeEnum.CUSTOM_PORTFOLIO -> generateCustomPortfolioItems(context)
            DescriptionTypeEnum.FUTURE -> generateFutureItems(context)
            DescriptionTypeEnum.LAB_ANNUALISED_RETURN -> generateLabAnnualisedReturnItems(context)
            DescriptionTypeEnum.LAB_RISK -> generateLabRiskItems(context)
            DescriptionTypeEnum.MAXIMUM_DRAWDOWN -> generateMaximumDrawdownItems(context)
            DescriptionTypeEnum.UNINVESTED_SAVED -> generateUninvestedSavedItems(context)
            DescriptionTypeEnum.UNINVESTED_INVESTED -> generateUninvestedInvestedItems(context)
            DescriptionTypeEnum.CLOSE_ACCOUNT -> generateCloseAccountItems(context)
            DescriptionTypeEnum.PORTFOLIO_BUILDER -> generatePortfolioBuilderItems(context)
            DescriptionTypeEnum.SETUP_TARGET_PORTFOLIO -> generateSetupTargetPortfolioItems(context)
            DescriptionTypeEnum.ROBO_ADVISOR -> generateRoboAdvisorItems(context)
            DescriptionTypeEnum.MARKET_CAP -> generateMarketCapItems(context)
            DescriptionTypeEnum.BETA -> generateBetaItems(context)
            DescriptionTypeEnum.P_E_RATIO -> generatePERatioItems(context)
            DescriptionTypeEnum.FORWARD_P_E_RATIO -> generateForwardPERatioItems(context)
            DescriptionTypeEnum.EPS -> generateEPSItems(context)
            DescriptionTypeEnum.DIVIDEND_YIELD -> generateDividendYieldItems(context)
            DescriptionTypeEnum.ANALYST_VIEWS -> generateAnalystViewsItems(context)
            DescriptionTypeEnum.SENTIMENT_SCORE_EU -> generateSentimentScoreEUViewsItems(context)
            DescriptionTypeEnum.SENTIMENT_SCORE_UK -> generateSentimentScoreUKViewsItems(context)
            DescriptionTypeEnum.NEWS_SENTIMENT_SCORE -> generateNewsSentimentScoreViewsItems(context)

            DescriptionTypeEnum.ANALYST_SENTIMENT_SCORE -> generateAnalystSentimentScoreViewsItems(
                context
            )

            DescriptionTypeEnum.PRICE_MOMENTUM_SENTIMENT_SCORE -> generatePriceMomentumSentimentScoreViewsItems(
                context
            )

            DescriptionTypeEnum.SUNDOWN_DIGEST -> generateSundownDigestViewsItems(context)
            DescriptionTypeEnum.AVERAGE_PRICE_TARGET -> generateAveragePriceTargetItems(context)
            DescriptionTypeEnum.BASE_CURRENCY -> generateBaseCurrencyItems(context)
            DescriptionTypeEnum.ETF_METRIC_EXPECTED_RETURN -> generateETFMetricExpectedReturnItems(
                context
            )

            DescriptionTypeEnum.ETF_METRIC_DIVIDEND_YIELD -> generateETFMetricDividendYieldItems(
                context
            )

            DescriptionTypeEnum.INCOME -> generateIncomeItems(context)
            DescriptionTypeEnum.ETF_METRIC_RISK -> generateETFMetricRiskItems(context)

            DescriptionTypeEnum.COUPON -> generateCouponItems(
                context = context,
                currencyISOCode = currencyISOCode,
                userLocale = userLocale
            )

            DescriptionTypeEnum.EXPENSE_RATIO -> generateExpenseRatioItems(context)
            DescriptionTypeEnum.ETF_METRIC_FORWARD_P_E_RATIO -> generateETFMetricForwardPERatioItems(
                context
            )

            DescriptionTypeEnum.YIELD -> generateYieldItems(
                context = context,
                currencyISOCode = currencyISOCode,
                userLocale = userLocale
            )

            DescriptionTypeEnum.TARGET_PORTFOLIO_SCREEN -> generateTargetPortfolioScreenItems(
                context
            )

            DescriptionTypeEnum.DIVERSIFICATION_SCORE -> generateDiversificationScoreItems(context)

            DescriptionTypeEnum.CHANGES_TO_REVIEW -> generateAboutChangesToReviewItems(context)

            DescriptionTypeEnum.MONTHLY_INVESTMENT_INFO -> generateMonthlyInvestmentInfoItems(
                context
            )

            DescriptionTypeEnum.BUY_PORTFOLIO_INFO -> generatePortfolioInfoItems(context)
            else -> emptyList()
        }
    }

    fun generateFeeMatrixItems(
        context: Context,
        feeDetails: SavingsProductFeeDetails?,
        allPlans: List<Plan>
    ): List<DescriptionItem> {
        val finalAnswer = mutableListOf<DescriptionItem>()

        val spacing8 = context.resources.getDimensionPixelSize(R.dimen.spacing_8)
        val spacing16 = context.resources.getDimensionPixelSize(R.dimen.spacing_16)
        val spacing24 = context.resources.getDimensionPixelSize(R.dimen.spacing_24)
        val spacing40 = context.resources.getDimensionPixelSize(R.dimen.spacing_40)

        val section1Title =
            context.resources.getString(R.string.one_day_yield_net_description_section_title_1)

        val section1Text =
            context.resources.getString(R.string.one_day_yield_net_description_section_text_1)

        val section1TitleItem = DescriptionItem.HeaderItem(
            id = "section1TitleItem",
            headerType = null,
            paddingStart = spacing16,
            paddingTop = spacing8,
            paddingEnd = spacing16,
            paddingBottom = null,
            textAppearanceRes = R.style.HeadingsH2Mobile_Primary,
            title = SpannableString(section1Title)
        )

        val section1TextItem = DescriptionItem.HeaderItem(
            id = "section1TextItem",
            headerType = null,
            paddingStart = spacing16,
            paddingTop = spacing24,
            paddingEnd = spacing16,
            paddingBottom = null,
            textAppearanceRes = R.style.BodySM_BaseTextColor,
            title = SpannableString(section1Text)
        )

        finalAnswer.add(section1TitleItem)
        finalAnswer.add(section1TextItem)

        val section2Title =
            context.resources.getString(R.string.one_day_yield_net_description_section_title_2)

        val section2Text =
            context.resources.getString(R.string.one_day_yield_net_description_section_text_2)

        val section2TitleItem = DescriptionItem.HeaderItem(
            id = "section2TitleItem",
            headerType = null,
            paddingStart = spacing16,
            paddingTop = spacing40,
            paddingEnd = spacing16,
            paddingBottom = null,
            textAppearanceRes = R.style.HeadingsH2Mobile_Primary,
            title = SpannableString(section2Title)
        )

        val section2TextItem = DescriptionItem.HeaderItem(
            id = "section2TextItem",
            headerType = null,
            paddingStart = spacing16,
            paddingTop = spacing24,
            paddingEnd = spacing16,
            paddingBottom = null,
            textAppearanceRes = R.style.BodySM_BaseTextColor,
            title = SpannableString(section2Text)
        )

        finalAnswer.add(section2TitleItem)
        finalAnswer.add(section2TextItem)

        val firstRowCellTexts = listOf(
            feeDetails?.planColumnLabel ?: "",
            feeDetails?.fundManagerFeeColumnLabel ?: "",
            feeDetails?.wealthyhoodFeeColumnLabel ?: "",
            feeDetails?.netInterestRateColumnLabel ?: ""
        )

        val secondRowCellTexts = generateCellTexts(
            feeDetailsItem = feeDetails?.feeDetails?.getOrNull(0),
            allPlans = allPlans
        )

        val thirdCellTexts = generateCellTexts(
            feeDetailsItem = feeDetails?.feeDetails?.getOrNull(1),
            allPlans = allPlans
        )

        val fourthCellTexts = generateCellTexts(
            feeDetailsItem = feeDetails?.feeDetails?.getOrNull(2),
            allPlans = allPlans
        )

        val feeMatrixItem = DescriptionItem.FeeMatrixItem(
            id = "feeMatrixItem",
            paddingStart = spacing16,
            paddingTop = spacing40,
            paddingEnd = spacing16,
            paddingBottom = null,
            firstRowCellTexts = firstRowCellTexts,
            secondRowCellTexts = secondRowCellTexts,
            thirdRowCellTexts = thirdCellTexts,
            fourthRowCellTexts = fourthCellTexts
        )

        finalAnswer.add(feeMatrixItem)

        val sectionText3 = context.resources.getString(
            R.string.one_day_yield_net_description_section_text_3,
            feeDetails?.grossInterestRate
        )

        val section3TextItem = DescriptionItem.HeaderItem(
            id = "section3TextItem",
            headerType = null,
            paddingStart = spacing16,
            paddingTop = spacing24,
            paddingEnd = spacing16,
            paddingBottom = null,
            textAppearanceRes = R.style.BodySM_BaseTextColor,
            title = SpannableString(sectionText3)
        )

        finalAnswer.add(section3TextItem)

        return finalAnswer
    }

    fun generateMyAccountFeeMatrixItems(
        context: Context,
        feeDetails: SavingsProductFeeDetails?,
        allPlans: List<Plan>
    ): List<DescriptionItem> {
        val finalAnswer = mutableListOf<DescriptionItem>()

        val spacing8 = context.resources.getDimensionPixelSize(R.dimen.spacing_8)
        val spacing16 = context.resources.getDimensionPixelSize(R.dimen.spacing_16)
        val spacing24 = context.resources.getDimensionPixelSize(R.dimen.spacing_24)
        val spacing40 = context.resources.getDimensionPixelSize(R.dimen.spacing_40)

        val section1Title = context.resources.getString(
            R.string.my_account_mmf_description_section_title_1,
            feeDetails?.netInterestRateOfCurrentPlan
        )

        val section1Text = context.resources.getString(
            R.string.my_account_mmf_description_section_text_1,
            feeDetails?.netInterestRateOfCurrentPlan
        )

        val section1TitleItem = DescriptionItem.HeaderItem(
            id = "section1TitleItem",
            headerType = null,
            paddingStart = spacing16,
            paddingTop = spacing8,
            paddingEnd = spacing16,
            paddingBottom = null,
            textAppearanceRes = R.style.HeadingsH2Mobile_Primary,
            title = SpannableString(section1Title)
        )

        val section1TextItem = DescriptionItem.HeaderItem(
            id = "section1TextItem",
            headerType = null,
            paddingStart = spacing16,
            paddingTop = spacing24,
            paddingEnd = spacing16,
            paddingBottom = null,
            textAppearanceRes = R.style.BodySM_BaseTextColor,
            title = SpannableString(section1Text)
        )

        finalAnswer.add(section1TitleItem)
        finalAnswer.add(section1TextItem)

        val firstRowCellTexts = listOf(
            feeDetails?.planColumnLabel ?: "",
            feeDetails?.fundManagerFeeColumnLabel ?: "",
            feeDetails?.wealthyhoodFeeColumnLabel ?: "",
            feeDetails?.netInterestRateColumnLabel ?: ""
        )

        val secondRowCellTexts = generateCellTexts(
            feeDetailsItem = feeDetails?.feeDetails?.getOrNull(0),
            allPlans = allPlans
        )

        val thirdCellTexts = generateCellTexts(
            feeDetailsItem = feeDetails?.feeDetails?.getOrNull(1),
            allPlans = allPlans
        )

        val fourthCellTexts = generateCellTexts(
            feeDetailsItem = feeDetails?.feeDetails?.getOrNull(2),
            allPlans = allPlans
        )

        val feeMatrixItem = DescriptionItem.FeeMatrixItem(
            id = "feeMatrixItem",
            paddingStart = spacing16,
            paddingTop = spacing40,
            paddingEnd = spacing16,
            paddingBottom = null,
            firstRowCellTexts = firstRowCellTexts,
            secondRowCellTexts = secondRowCellTexts,
            thirdRowCellTexts = thirdCellTexts,
            fourthRowCellTexts = fourthCellTexts
        )

        finalAnswer.add(feeMatrixItem)

        val sectionText2 = context.resources.getString(
            R.string.my_account_mmf_description_section_text_2,
            feeDetails?.grossInterestRate
        )

        val section2TextItem = DescriptionItem.HeaderItem(
            id = "section2TextItem",
            headerType = null,
            paddingStart = spacing16,
            paddingTop = spacing24,
            paddingEnd = spacing16,
            paddingBottom = null,
            textAppearanceRes = R.style.BodySM_BaseTextColor,
            title = SpannableString(sectionText2)
        )

        finalAnswer.add(section2TextItem)

        return finalAnswer
    }

    fun generatePlansFeeMatrixItems(
        context: Context,
        feeDetails: SavingsProductFeeDetails?,
        planPriceAPIKey: String?,
        allPlans: List<Plan>,
        locale: Locale
    ): List<DescriptionItem> {
        val finalAnswer = mutableListOf<DescriptionItem>()

        val spacing8 = context.resources.getDimensionPixelSize(R.dimen.spacing_8)
        val spacing16 = context.resources.getDimensionPixelSize(R.dimen.spacing_16)
        val spacing24 = context.resources.getDimensionPixelSize(R.dimen.spacing_24)
        val spacing40 = context.resources.getDimensionPixelSize(R.dimen.spacing_40)

        val feeDetailsItem = feeDetails?.feeDetails?.find { it.planPriceAPIKey == planPriceAPIKey }

        val netInterestRateValue =
            feeDetailsItem?.netInterestRateValue?.div(100)?.formatAsPercentage(locale = locale)

        val section1Title = context.resources.getString(
            R.string.plans_mmf_description_section_title_1,
            netInterestRateValue
        )

        val section1Text =
            context.resources.getString(R.string.plans_account_mmf_description_section_text_1)

        val section1TitleItem = DescriptionItem.HeaderItem(
            id = "section1TitleItem",
            headerType = null,
            paddingStart = spacing16,
            paddingTop = spacing8,
            paddingEnd = spacing16,
            paddingBottom = null,
            textAppearanceRes = R.style.HeadingsH2Mobile_Primary,
            title = SpannableString(section1Title)
        )

        val section1TextItem = DescriptionItem.HeaderItem(
            id = "section1TextItem",
            headerType = null,
            paddingStart = spacing16,
            paddingTop = spacing24,
            paddingEnd = spacing16,
            paddingBottom = null,
            textAppearanceRes = R.style.BodySM_BaseTextColor,
            title = SpannableString(section1Text)
        )

        finalAnswer.add(section1TitleItem)
        finalAnswer.add(section1TextItem)

        val firstRowCellTexts = listOf(
            feeDetails?.planColumnLabel ?: "",
            feeDetails?.fundManagerFeeColumnLabel ?: "",
            feeDetails?.wealthyhoodFeeColumnLabel ?: "",
            feeDetails?.netInterestRateColumnLabel ?: ""
        )

        val secondRowCellTexts = generateCellTexts(
            feeDetailsItem = feeDetails?.feeDetails?.getOrNull(0),
            allPlans = allPlans
        )

        val thirdCellTexts = generateCellTexts(
            feeDetailsItem = feeDetails?.feeDetails?.getOrNull(1),
            allPlans = allPlans
        )

        val fourthCellTexts = generateCellTexts(
            feeDetailsItem = feeDetails?.feeDetails?.getOrNull(2),
            allPlans = allPlans
        )

        val feeMatrixItem = DescriptionItem.FeeMatrixItem(
            id = "feeMatrixItem",
            paddingStart = spacing16,
            paddingTop = spacing40,
            paddingEnd = spacing16,
            paddingBottom = null,
            firstRowCellTexts = firstRowCellTexts,
            secondRowCellTexts = secondRowCellTexts,
            thirdRowCellTexts = thirdCellTexts,
            fourthRowCellTexts = fourthCellTexts
        )

        finalAnswer.add(feeMatrixItem)

        val sectionText2 = context.resources.getString(
            R.string.plans_account_mmf_description_section_text_2,
            feeDetails?.grossInterestRate
        )

        val section2TextItem = DescriptionItem.HeaderItem(
            id = "section2TextItem",
            headerType = null,
            paddingStart = spacing16,
            paddingTop = spacing24,
            paddingEnd = spacing16,
            paddingBottom = null,
            textAppearanceRes = R.style.BodySM_BaseTextColor,
            title = SpannableString(sectionText2)
        )

        finalAnswer.add(section2TextItem)

        return finalAnswer
    }

    private fun generateCellTexts(
        feeDetailsItem: SavingsProductFeeDetails.FeeDetailsItem?,
        allPlans: List<Plan>
    ): List<String> {
        val planTitle = allPlans.find {
            it.basePlanID == feeDetailsItem?.planPriceAPIKey
        }?.title

        return listOf(
            planTitle ?: "",
            feeDetailsItem?.fundManagerAnnualFeePercentage ?: "",
            feeDetailsItem?.wealthyhoodAnnualFeePercentage ?: "",
            feeDetailsItem?.netInterestRate ?: ""
        )
    }

    private fun generateStocksItems(context: Context): List<DescriptionItem> {
        val finalAnswer = mutableListOf<DescriptionItem>()

        finalAnswer.addAll(
            generateDescriptionItems(
                context,
                title = R.string.stocks_description_title,
                sectionTitle = R.string.stocks_description_first_section_title,
                paragraphs = generateFormattedParagraphs(
                    context = context,
                    strings = listOf(
                        R.string.stocks_description_paragraph_1,
                        R.string.stocks_description_paragraph_2
                    ).convertToListOfStrings(context)
                )
            )
        )

        finalAnswer.addAll(
            generateDescriptionItems(
                context,
                title = null,
                sectionTitle = R.string.stocks_description_second_section_title,
                paragraphs = generateFormattedParagraphs(
                    context = context,
                    strings = listOf(
                        R.string.stocks_description_paragraph_3,
                        R.string.stocks_description_paragraph_4
                    ).convertToListOfStrings(context)
                )
            )
        )

        finalAnswer.addAll(
            generateDescriptionItems(
                context,
                title = null,
                sectionTitle = R.string.stocks_description_third_section_title,
                paragraphs = generateFormattedParagraphs(
                    context = context,
                    strings = listOf(
                        R.string.stocks_description_paragraph_5,
                        R.string.stocks_description_paragraph_6,
                        R.string.stocks_description_paragraph_7,
                        R.string.stocks_description_paragraph_8,
                        R.string.stocks_description_paragraph_9,
                        R.string.capital_at_risk_text_with_spaces
                    ).convertToListOfStrings(context)
                )
            )
        )

        return finalAnswer
    }

    private fun generateBondsItems(
        context: Context,
        currencyISOCode: String?,
        userLocale: Locale
    ): List<DescriptionItem> {
        val finalAnswer = mutableListOf<DescriptionItem>()

        finalAnswer.addAll(
            generateDescriptionItems(
                context,
                title = R.string.bonds_description_title,
                sectionTitle = R.string.bonds_description_first_section_title,
                paragraphs = generateFormattedParagraphs(
                    context = context,
                    strings = listOf(
                        R.string.bonds_description_paragraph_1
                    ).convertToListOfStrings(context)
                )
            )
        )

        val firstAmountText = 5.0.generateFormattedCurrency(
            currencyISOCode = currencyISOCode,
            maximumFractionDigits = 0,
            locale = userLocale
        )

        val secondAmountText = 100.0.generateFormattedCurrency(
            currencyISOCode = currencyISOCode,
            maximumFractionDigits = 0,
            locale = userLocale
        )

        val bondDescriptionParagraph2 = context.resources.getString(
            R.string.bonds_description_paragraph_2,
            secondAmountText,
            secondAmountText,
            firstAmountText,
            secondAmountText
        )

        val bondDescriptionParagraph3 = context.resources.getString(
            R.string.bonds_description_paragraph_3
        )

        finalAnswer.addAll(
            generateDescriptionItems(
                context,
                title = null,
                sectionTitle = R.string.bonds_description_second_section_title,
                paragraphs = generateFormattedParagraphs(
                    context = context,
                    strings = listOf(
                        bondDescriptionParagraph2,
                        bondDescriptionParagraph3
                    )
                )
            )
        )

        finalAnswer.addAll(
            generateDescriptionItems(
                context,
                title = null,
                sectionTitle = R.string.bonds_description_third_section_title,
                paragraphs = generateFormattedParagraphs(
                    context = context,
                    strings = listOf(
                        R.string.bonds_description_paragraph_4,
                        R.string.bonds_description_paragraph_5
                    ).convertToListOfStrings(context)
                )
            )
        )

        finalAnswer.addAll(
            generateDescriptionItems(
                context,
                title = null,
                sectionTitle = R.string.bonds_description_fourth_section_title,
                paragraphs = generateFormattedParagraphs(
                    context = context,
                    strings = listOf(
                        R.string.bonds_description_paragraph_6,
                        R.string.bonds_description_paragraph_7
                    ).convertToListOfStrings(context)
                )
            )
        )

        finalAnswer.addAll(
            generateDescriptionItems(
                context,
                title = null,
                sectionTitle = R.string.bonds_description_fifth_section_title,
                paragraphs = generateFormattedParagraphs(
                    context = context,
                    strings = listOf(
                        R.string.bonds_description_paragraph_8,
                        R.string.bonds_description_paragraph_9,
                        R.string.capital_at_risk_text_with_spaces
                    ).convertToListOfStrings(context)
                )
            )
        )

        return finalAnswer
    }

    private fun generateCommoditiesItems(context: Context): List<DescriptionItem> {
        val finalAnswer = mutableListOf<DescriptionItem>()

        finalAnswer.addAll(
            generateDescriptionItems(
                context,
                title = R.string.commodities_description_title,
                sectionTitle = R.string.commodities_description_first_section_title,
                paragraphs = generateFormattedParagraphs(
                    context = context,
                    strings = listOf(
                        R.string.commodities_description_paragraph_1,
                        R.string.commodities_description_paragraph_2,
                        R.string.commodities_description_paragraph_3
                    ).convertToListOfStrings(context)
                )
            )
        )

        finalAnswer.addAll(
            generateDescriptionItems(
                context,
                title = null,
                sectionTitle = R.string.commodities_description_second_section_title,
                paragraphs = generateFormattedParagraphs(
                    context = context,
                    strings = listOf(
                        R.string.commodities_description_paragraph_4
                    ).convertToListOfStrings(context)
                )
            )
        )

        finalAnswer.addAll(
            generateDescriptionItems(
                context,
                title = null,
                sectionTitle = R.string.commodities_description_third_section_title,
                paragraphs = generateFormattedParagraphs(
                    context = context,
                    strings = listOf(
                        R.string.commodities_description_paragraph_5,
                        R.string.commodities_description_paragraph_6,
                        R.string.commodities_description_paragraph_7,
                        R.string.commodities_description_paragraph_8
                    ).convertToListOfStrings(context)
                )
            )
        )

        finalAnswer.addAll(
            generateDescriptionItems(
                context,
                title = null,
                sectionTitle = R.string.commodities_description_fourth_section_title,
                paragraphs = generateFormattedParagraphs(
                    context = context,
                    strings = listOf(
                        R.string.commodities_description_paragraph_9,
                        R.string.commodities_description_paragraph_10
                    ).convertToListOfStrings(context)
                )
            )
        )

        finalAnswer.addAll(
            generateDescriptionItems(
                context,
                title = null,
                sectionTitle = R.string.commodities_description_fifth_section_title,
                paragraphs = generateFormattedParagraphs(
                    context = context,
                    strings = listOf(
                        R.string.commodities_description_paragraph_11,
                        R.string.commodities_description_paragraph_12,
                        R.string.capital_at_risk_text_with_spaces
                    ).convertToListOfStrings(context)
                )
            )
        )

        return finalAnswer
    }

    private fun generateRealEstateItems(context: Context): List<DescriptionItem> {
        val finalAnswer = mutableListOf<DescriptionItem>()

        finalAnswer.addAll(
            generateDescriptionItems(
                context,
                title = R.string.real_estate_description_title,
                sectionTitle = R.string.real_estate_description_first_section_title,
                paragraphs = generateFormattedParagraphs(
                    context = context,
                    strings = listOf(
                        R.string.real_estate_description_paragraph_1,
                        R.string.real_estate_description_paragraph_2,
                        R.string.real_estate_description_paragraph_3
                    ).convertToListOfStrings(context)
                )
            )
        )

        finalAnswer.addAll(
            generateDescriptionItems(
                context,
                title = null,
                sectionTitle = R.string.real_estate_description_second_section_title,
                paragraphs = generateFormattedParagraphs(
                    context = context,
                    strings = listOf(
                        R.string.real_estate_description_paragraph_4,
                        R.string.real_estate_description_paragraph_5,
                        R.string.real_estate_description_paragraph_6,
                        R.string.real_estate_description_paragraph_7
                    ).convertToListOfStrings(context)
                )
            )
        )

        finalAnswer.addAll(
            generateDescriptionItems(
                context,
                title = null,
                sectionTitle = R.string.real_estate_description_third_section_title,
                paragraphs = generateFormattedParagraphs(
                    context = context,
                    strings = listOf(
                        R.string.real_estate_description_paragraph_8,
                        R.string.real_estate_description_paragraph_9
                    ).convertToListOfStrings(context)
                )
            )
        )

        finalAnswer.addAll(
            generateDescriptionItems(
                context,
                title = null,
                sectionTitle = R.string.real_estate_description_fourth_section_title,
                paragraphs = generateFormattedParagraphs(
                    context = context,
                    strings = listOf(
                        R.string.real_estate_description_paragraph_10,
                        R.string.real_estate_description_paragraph_11,
                        R.string.capital_at_risk_text_with_spaces
                    ).convertToListOfStrings(context)
                )
            )
        )

        return finalAnswer
    }

    private fun generateTechnologyItems(context: Context): List<DescriptionItem> {
        val finalAnswer = mutableListOf<DescriptionItem>()

        finalAnswer.addAll(
            generateDescriptionItems(
                context,
                title = R.string.technology_description_title,
                sectionTitle = R.string.technology_description_first_section_title,
                paragraphs = generateFormattedParagraphs(
                    context = context,
                    strings = listOf(
                        R.string.technology_description_paragraph_1,
                        R.string.technology_description_paragraph_2,
                        R.string.technology_description_paragraph_3
                    ).convertToListOfStrings(context)
                )
            )
        )

        finalAnswer.addAll(
            generateDescriptionItems(
                context,
                title = null,
                sectionTitle = R.string.technology_description_second_section_title,
                paragraphs = generateFormattedParagraphs(
                    context = context,
                    strings = listOf(
                        R.string.technology_description_paragraph_4,
                        R.string.technology_description_paragraph_5,
                        R.string.technology_description_paragraph_6,
                        R.string.technology_description_paragraph_7
                    ).convertToListOfStrings(context)
                )
            )
        )

        finalAnswer.addAll(
            generateDescriptionItems(
                context,
                title = null,
                sectionTitle = R.string.technology_description_third_section_title,
                paragraphs = generateFormattedParagraphs(
                    context = context,
                    strings = listOf(
                        R.string.technology_description_paragraph_8,
                        R.string.capital_at_risk_text_with_spaces
                    ).convertToListOfStrings(context)
                )
            )
        )

        return finalAnswer
    }

    private fun generateHealthcareItems(context: Context): List<DescriptionItem> {
        val finalAnswer = mutableListOf<DescriptionItem>()

        finalAnswer.addAll(
            generateDescriptionItems(
                context,
                title = R.string.healthcare_description_title,
                sectionTitle = R.string.healthcare_description_first_section_title,
                paragraphs = generateFormattedParagraphs(
                    context = context,
                    strings = listOf(
                        R.string.healthcare_description_paragraph_1
                    ).convertToListOfStrings(context)
                )
            )
        )

        finalAnswer.addAll(
            generateDescriptionItems(
                context,
                title = null,
                sectionTitle = R.string.healthcare_description_second_section_title,
                paragraphs = generateFormattedParagraphs(
                    context = context,
                    strings = listOf(
                        R.string.healthcare_description_paragraph_2,
                        R.string.healthcare_description_paragraph_3,
                        R.string.healthcare_description_paragraph_4,
                        R.string.healthcare_description_paragraph_5
                    ).convertToListOfStrings(context)
                )
            )
        )

        finalAnswer.addAll(
            generateDescriptionItems(
                context,
                title = null,
                sectionTitle = R.string.healthcare_description_third_section_title,
                paragraphs = generateFormattedParagraphs(
                    context = context,
                    strings = listOf(
                        R.string.healthcare_description_paragraph_6,
                        R.string.capital_at_risk_text_with_spaces
                    ).convertToListOfStrings(context)
                )
            )
        )

        return finalAnswer
    }

    private fun generateConsumerItems(context: Context): List<DescriptionItem> {
        val finalAnswer = mutableListOf<DescriptionItem>()

        finalAnswer.addAll(
            generateDescriptionItems(
                context,
                title = R.string.consumer_description_title,
                sectionTitle = R.string.consumer_description_first_section_title,
                paragraphs = generateFormattedParagraphs(
                    context = context,
                    strings = listOf(
                        R.string.consumer_description_paragraph_1,
                        R.string.consumer_description_paragraph_2
                    ).convertToListOfStrings(context)
                )
            )
        )

        finalAnswer.addAll(
            generateDescriptionItems(
                context,
                title = null,
                sectionTitle = R.string.consumer_description_second_section_title,
                paragraphs = generateFormattedParagraphs(
                    context = context,
                    strings = listOf(
                        R.string.consumer_description_paragraph_3,
                        R.string.consumer_description_paragraph_4,
                        R.string.consumer_description_paragraph_5
                    ).convertToListOfStrings(context)
                )
            )
        )

        finalAnswer.addAll(
            generateDescriptionItems(
                context,
                title = null,
                sectionTitle = R.string.consumer_description_third_section_title,
                paragraphs = generateFormattedParagraphs(
                    context = context,
                    strings = listOf(
                        R.string.consumer_description_paragraph_6
                    ).convertToListOfStrings(context)
                )
            )
        )

        finalAnswer.addAll(
            generateDescriptionItems(
                context,
                title = null,
                sectionTitle = R.string.consumer_description_fourth_section_title,
                paragraphs = generateFormattedParagraphs(
                    context = context,
                    strings = listOf(
                        R.string.consumer_description_paragraph_7,
                        R.string.consumer_description_paragraph_8,
                        R.string.consumer_description_paragraph_9
                    ).convertToListOfStrings(context)
                )
            )
        )

        finalAnswer.addAll(
            generateDescriptionItems(
                context,
                title = null,
                sectionTitle = R.string.consumer_description_fifth_section_title,
                paragraphs = generateFormattedParagraphs(
                    context = context,
                    strings = listOf(
                        R.string.consumer_description_paragraph_10,
                        R.string.capital_at_risk_text_with_spaces
                    ).convertToListOfStrings(context)
                )
            )
        )

        return finalAnswer
    }

    private fun generateEnergyItems(context: Context): List<DescriptionItem> {
        val finalAnswer = mutableListOf<DescriptionItem>()

        finalAnswer.addAll(
            generateDescriptionItems(
                context,
                title = R.string.energy_description_title,
                sectionTitle = R.string.energy_description_first_section_title,
                paragraphs = generateFormattedParagraphs(
                    context = context,
                    strings = listOf(
                        R.string.energy_description_paragraph_1,
                        R.string.energy_description_paragraph_2
                    ).convertToListOfStrings(context)
                )
            )
        )

        finalAnswer.addAll(
            generateDescriptionItems(
                context,
                title = null,
                sectionTitle = R.string.energy_description_second_section_title,
                paragraphs = generateFormattedParagraphs(
                    context = context,
                    strings = listOf(
                        R.string.energy_description_paragraph_3,
                        R.string.energy_description_paragraph_4,
                        R.string.energy_description_paragraph_5,
                        R.string.energy_description_paragraph_6
                    ).convertToListOfStrings(context)
                )
            )
        )

        finalAnswer.addAll(
            generateDescriptionItems(
                context,
                title = null,
                sectionTitle = R.string.energy_description_third_section_title,
                paragraphs = generateFormattedParagraphs(
                    context = context,
                    strings = listOf(
                        R.string.energy_description_paragraph_7,
                        R.string.capital_at_risk_text_with_spaces
                    ).convertToListOfStrings(context)
                )
            )
        )

        return finalAnswer
    }

    private fun generateCommunicationItems(context: Context): List<DescriptionItem> {
        val finalAnswer = mutableListOf<DescriptionItem>()

        finalAnswer.addAll(
            generateDescriptionItems(
                context,
                title = R.string.communication_description_title,
                sectionTitle = R.string.communication_description_first_section_title,
                paragraphs = generateFormattedParagraphs(
                    context = context,
                    strings = listOf(
                        R.string.communication_description_paragraph_1,
                        R.string.communication_description_paragraph_2
                    ).convertToListOfStrings(context)
                )
            )
        )

        finalAnswer.addAll(
            generateDescriptionItems(
                context,
                title = null,
                sectionTitle = R.string.communication_description_second_section_title,
                paragraphs = generateFormattedParagraphs(
                    context = context,
                    strings = listOf(
                        R.string.communication_description_paragraph_3,
                        R.string.communication_description_paragraph_4,
                        R.string.communication_description_paragraph_5,
                        R.string.communication_description_paragraph_6
                    ).convertToListOfStrings(context)
                )
            )
        )

        finalAnswer.addAll(
            generateDescriptionItems(
                context,
                title = null,
                sectionTitle = R.string.communication_description_third_section_title,
                paragraphs = generateFormattedParagraphs(
                    context = context,
                    strings = listOf(
                        R.string.communication_description_paragraph_7,
                        R.string.capital_at_risk_text_with_spaces
                    ).convertToListOfStrings(context)
                )
            )
        )

        return finalAnswer
    }

    private fun generateFinancialsItems(context: Context): List<DescriptionItem> {
        val finalAnswer = mutableListOf<DescriptionItem>()

        finalAnswer.addAll(
            generateDescriptionItems(
                context,
                title = R.string.financials_description_title,
                sectionTitle = R.string.financials_description_first_section_title,
                paragraphs = generateFormattedParagraphs(
                    context = context,
                    strings = listOf(
                        R.string.financials_description_paragraph_1
                    ).convertToListOfStrings(context)
                )
            )
        )

        finalAnswer.addAll(
            generateDescriptionItems(
                context,
                title = null,
                sectionTitle = R.string.financials_description_second_section_title,
                paragraphs = generateFormattedParagraphs(
                    context = context,
                    strings = listOf(
                        R.string.financials_description_paragraph_2,
                        R.string.financials_description_paragraph_3,
                        R.string.financials_description_paragraph_4
                    ).convertToListOfStrings(context)
                )
            )
        )

        finalAnswer.addAll(
            generateDescriptionItems(
                context,
                title = null,
                sectionTitle = R.string.financials_description_third_section_title,
                paragraphs = generateFormattedParagraphs(
                    context = context,
                    strings = listOf(
                        R.string.financials_description_paragraph_5,
                        R.string.capital_at_risk_text_with_spaces
                    ).convertToListOfStrings(context)
                )
            )
        )

        return finalAnswer
    }

    private fun generateIndustrialsItems(context: Context): List<DescriptionItem> {
        val finalAnswer = mutableListOf<DescriptionItem>()

        finalAnswer.addAll(
            generateDescriptionItems(
                context,
                title = R.string.industrials_description_title,
                sectionTitle = R.string.industrials_description_first_section_title,
                paragraphs = generateFormattedParagraphs(
                    context = context,
                    strings = listOf(
                        R.string.industrials_description_paragraph_1
                    ).convertToListOfStrings(context)
                )
            )
        )

        finalAnswer.addAll(
            generateDescriptionItems(
                context,
                title = null,
                sectionTitle = R.string.industrials_description_second_section_title,
                paragraphs = generateFormattedParagraphs(
                    context = context,
                    strings = listOf(
                        R.string.industrials_description_paragraph_2,
                        R.string.industrials_description_paragraph_3,
                        R.string.industrials_description_paragraph_4
                    ).convertToListOfStrings(context)
                )
            )
        )

        finalAnswer.addAll(
            generateDescriptionItems(
                context,
                title = null,
                sectionTitle = R.string.industrials_description_third_section_title,
                paragraphs = generateFormattedParagraphs(
                    context = context,
                    strings = listOf(
                        R.string.industrials_description_paragraph_5,
                        R.string.capital_at_risk_text_with_spaces
                    ).convertToListOfStrings(context)
                )
            )
        )

        return finalAnswer
    }

    private fun generateUtilitiesItems(context: Context): List<DescriptionItem> {
        val finalAnswer = mutableListOf<DescriptionItem>()

        finalAnswer.addAll(
            generateDescriptionItems(
                context,
                title = R.string.utilities_description_title,
                sectionTitle = R.string.utilities_description_first_section_title,
                paragraphs = generateFormattedParagraphs(
                    context = context,
                    strings = listOf(
                        R.string.utilities_description_paragraph_1
                    ).convertToListOfStrings(context)
                )
            )
        )

        finalAnswer.addAll(
            generateDescriptionItems(
                context,
                title = null,
                sectionTitle = R.string.utilities_description_second_section_title,
                paragraphs = generateFormattedParagraphs(
                    context = context,
                    strings = listOf(
                        R.string.utilities_description_paragraph_2,
                        R.string.utilities_description_paragraph_3,
                        R.string.utilities_description_paragraph_4
                    ).convertToListOfStrings(context)
                )
            )
        )

        finalAnswer.addAll(
            generateDescriptionItems(
                context,
                title = null,
                sectionTitle = R.string.utilities_description_third_section_title,
                paragraphs = generateFormattedParagraphs(
                    context = context,
                    strings = listOf(
                        R.string.utilities_description_paragraph_5,
                        R.string.capital_at_risk_text_with_spaces
                    ).convertToListOfStrings(context)
                )
            )
        )

        return finalAnswer
    }

    private fun generateMaterialsItems(context: Context): List<DescriptionItem> {
        val finalAnswer = mutableListOf<DescriptionItem>()

        finalAnswer.addAll(
            generateDescriptionItems(
                context,
                title = R.string.materials_description_title,
                sectionTitle = R.string.materials_description_first_section_title,
                paragraphs = generateFormattedParagraphs(
                    context = context,
                    strings = listOf(
                        R.string.materials_description_paragraph_1
                    ).convertToListOfStrings(context)
                )
            )
        )

        finalAnswer.addAll(
            generateDescriptionItems(
                context,
                title = null,
                sectionTitle = R.string.materials_description_second_section_title,
                paragraphs = generateFormattedParagraphs(
                    context = context,
                    strings = listOf(
                        R.string.materials_description_paragraph_2,
                        R.string.materials_description_paragraph_3,
                        R.string.materials_description_paragraph_4
                    ).convertToListOfStrings(context)
                )
            )
        )

        finalAnswer.addAll(
            generateDescriptionItems(
                context,
                title = null,
                sectionTitle = R.string.materials_description_third_section_title,
                paragraphs = generateFormattedParagraphs(
                    context = context,
                    strings = listOf(
                        R.string.materials_description_paragraph_5,
                        R.string.capital_at_risk_text_with_spaces
                    ).convertToListOfStrings(context)
                )
            )
        )

        return finalAnswer
    }

    private fun generateCustomPortfolioItems(context: Context): List<DescriptionItem> {
        val finalAnswer = mutableListOf<DescriptionItem>()

        finalAnswer.addAll(
            generateDescriptionItems(
                context,
                title = R.string.custom_portfolio_description_title,
                marginHeight = context.resources.getDimensionPixelSize(R.dimen.spacing_16),
                sectionTitle = null,
                paragraphs = generateFormattedParagraphs(
                    context = context,
                    strings = listOf(
                        R.string.custom_portfolio_description_first_paragraph,
                        R.string.custom_portfolio_description_second_paragraph,
                        R.string.custom_portfolio_description_third_paragraph
                    ).convertToListOfStrings(context)
                )
            )
        )

        finalAnswer.addAll(
            generateDescriptionItems(
                context,
                title = null,
                sectionTitle = R.string.custom_portfolio_description_second_section_title,
                paragraphs = generateFormattedParagraphs(
                    context = context,
                    strings = listOf(
                        R.string.custom_portfolio_description_fourth_paragraph,
                        R.string.custom_portfolio_description_fifth_paragraph,
                        R.string.custom_portfolio_description_sixth_paragraph,
                        R.string.custom_portfolio_description_seventh_paragraph
                    ).convertToListOfStrings(context)
                )
            )
        )

        finalAnswer.addAll(
            generateDescriptionItems(
                context,
                title = null,
                sectionTitle = R.string.custom_portfolio_description_third_section_title,
                paragraphs = generateFormattedParagraphs(
                    context = context,
                    strings = listOf(
                        R.string.custom_portfolio_description_eighth_paragraph,
                        R.string.custom_portfolio_description_ninth_paragraph,
                        R.string.custom_portfolio_description_tenth_paragraph
                    ).convertToListOfStrings(context)
                )
            )
        )

        finalAnswer.addAll(
            generateDescriptionItems(
                context,
                title = null,
                sectionTitle = null,
                paragraphs = generateFormattedParagraphs(
                    context = context,
                    strings = listOf(
                        R.string.custom_portfolio_description_important_message
                    ).convertToListOfStrings(context)
                )
            )
        )

        finalAnswer.add(
            DescriptionItem.MarginItem(
                height = context.resources.getDimensionPixelSize(R.dimen.spacing_24)
            )
        )

        return finalAnswer
    }

    private fun generateLabAnnualisedReturnItems(context: Context): List<DescriptionItem> {
        val paragraphs = generateFormattedParagraphs(
            context = context,
            strings = listOf(
                R.string.lab_annualised_return_description_first_paragraph,
                R.string.lab_annualised_return_description_second_paragraph,
                R.string.lab_annualised_return_description_third_paragraph,
                R.string.lab_annualised_return_description_fourth_paragraph
            ).convertToListOfStrings(context)
        )

        return generateDescriptionItems(
            context = context,
            title = R.string.lab_annualised_return_description_title,
            sectionTitle = R.string.lab_annualised_return_description_section_title,
            paragraphs = paragraphs
        )
    }

    private fun generateFutureItems(context: Context): List<DescriptionItem> {
        val paragraphs = generateFormattedParagraphs(
            context = context,
            strings = listOf(
                R.string.future_description_paragraph_1,
                R.string.future_description_paragraph_2,
                R.string.future_description_paragraph_3,
                R.string.future_description_paragraph_4,
                R.string.future_description_paragraph_5,
                R.string.future_description_paragraph_6,
                R.string.future_description_paragraph_7
            ).convertToListOfStrings(context)
        )

        return generateDescriptionItems(
            context = context,
            title = R.string.future_description_title,
            sectionTitle = null,
            paragraphs = paragraphs
        )
    }

    private fun generateUninvestedSavedItems(context: Context): List<DescriptionItem> {
        val paragraphs = generateFormattedParagraphs(
            context = context,
            strings = listOf(
                R.string.uninvested_saved_first_paragraph
            ).convertToListOfStrings(context)
        )

        return generateDescriptionItems(
            context = context,
            title = R.string.uninvested_saved_description_title,
            sectionTitle = null,
            paragraphs = paragraphs
        )
    }

    private fun generateUninvestedInvestedItems(context: Context): List<DescriptionItem> {
        val paragraphs = generateFormattedParagraphs(
            context = context,
            strings = listOf(
                R.string.uninvested_invested_first_paragraph,
                R.string.uninvested_invested_second_paragraph,
                R.string.uninvested_invested_third_paragraph,
                R.string.uninvested_invested_fourth_paragraph
            ).convertToListOfStrings(context)
        )

        return generateDescriptionItems(
            context = context,
            title = R.string.uninvested_invested_description_title,
            sectionTitle = null,
            paragraphs = paragraphs
        )
    }

    private fun generateCloseAccountItems(context: Context): List<DescriptionItem> {
        val paragraphs = generateFormattedParagraphs(
            context = context,
            strings = listOf(
                R.string.close_account_first_paragraph
            ).convertToListOfStrings(context)
        )

        return generateDescriptionItems(
            context = context,
            title = null,
            sectionTitle = null,
            paragraphs = paragraphs
        )
    }

    private fun generateMaximumDrawdownItems(context: Context): List<DescriptionItem> {
        val paragraphs = generateFormattedParagraphs(
            context = context,
            strings = listOf(
                R.string.maximum_drawdown_description_first_paragraph,
                R.string.maximum_drawdown_description_second_paragraph,
                R.string.maximum_drawdown_description_third_paragraph,
                R.string.maximum_drawdown_description_fourth_paragraph,
                R.string.maximum_drawdown_description_fifth_paragraph
            ).convertToListOfStrings(context)
        )

        return generateDescriptionItems(
            context = context,
            title = R.string.maximum_drawdown_description_title,
            sectionTitle = R.string.maximum_drawdown_description_section_title,
            paragraphs = paragraphs
        )
    }

    private fun generateLabRiskItems(context: Context): List<DescriptionItem> {
        val paragraphs = generateFormattedParagraphs(
            context = context,
            strings = listOf(
                R.string.lab_risk_description_first_paragraph,
                R.string.lab_risk_description_second_paragraph,
                R.string.lab_risk_description_third_paragraph,
                R.string.lab_risk_description_fourth_paragraph
            ).convertToListOfStrings(context)
        )

        return generateDescriptionItems(
            context = context,
            title = R.string.lab_risk_description_title,
            sectionTitle = R.string.lab_risk_description_section_title,
            paragraphs = paragraphs
        )
    }

    private fun generateFormattedParagraphs(
        context: Context,
        strings: List<String>
    ): List<SpannableString> {
        return strings.map { string ->
            val boldSubstrings = string.findBoldSubstrings()
            val finalString = string.cleanSpecialFormattingCharacters()

            finalString.formatBoldSubstrings(context, boldSubstrings)
        }
    }

    private fun generateDescriptionItems(
        context: Context,
        title: Int?,
        marginHeight: Int = 0,
        titleTextSize: Float? = null,
        sectionTitle: Int?,
        paragraphs: List<SpannableString>,
        paragraphsTextSize: Float = 14f
    ): List<DescriptionItem> {
        val finalAnswer = mutableListOf<DescriptionItem>()

        val resources = context.resources

        title?.let {
            finalAnswer.add(
                DescriptionItem.TitleItem(
                    id = "titleItem",
                    title = resources.getString(it),
                    textSize = titleTextSize
                )
            )
        }

        if (marginHeight != 0) {
            finalAnswer.add(
                DescriptionItem.MarginItem(
                    height = marginHeight
                )
            )
        }

        sectionTitle?.let {
            finalAnswer.add(
                DescriptionItem.SectionTitleItem(
                    id = "sectionTitleItem",
                    title = resources.getString(it)
                )
            )
        }

        paragraphs.forEachIndexed { index, paragraph ->
            finalAnswer.add(
                DescriptionItem.ParagraphItem(
                    id = "paragraphItem_$index",
                    text = SpannableString(paragraph),
                    textSize = paragraphsTextSize
                )
            )
        }

        return finalAnswer
    }

    private fun generatePortfolioBuilderItems(context: Context): List<DescriptionItem> {
        val finalAnswer = mutableListOf<DescriptionItem>()

        finalAnswer.addAll(
            generateDescriptionItems(
                context,
                title = R.string.portfolio_builder_pick_your_template_about_info,
                titleTextSize = 16f,
                sectionTitle = R.string.portfolio_builder_description_section_title_1,
                paragraphs = generateFormattedParagraphs(
                    context = context,
                    strings = listOf(
                        R.string.portfolio_builder_description_paragraph_1,
                        R.string.portfolio_builder_description_paragraph_2
                    ).convertToListOfStrings(context)
                ),
                paragraphsTextSize = 14f
            )
        )

        finalAnswer.addAll(
            generateDescriptionItems(
                context,
                title = null,
                sectionTitle = R.string.portfolio_builder_description_section_title_2,
                paragraphs = generateFormattedParagraphs(
                    context = context,
                    strings = listOf(
                        R.string.portfolio_builder_description_paragraph_3,
                        R.string.portfolio_builder_description_paragraph_4,
                        R.string.portfolio_builder_description_paragraph_5,
                        R.string.portfolio_builder_description_paragraph_6
                    ).convertToListOfStrings(context)
                )
            )
        )

        finalAnswer.addAll(
            generateDescriptionItems(
                context,
                title = null,
                sectionTitle = R.string.portfolio_builder_description_section_title_3,
                paragraphs = generateFormattedParagraphs(
                    context = context,
                    strings = listOf(
                        R.string.portfolio_builder_description_paragraph_7,
                        R.string.portfolio_builder_description_paragraph_8
                    ).convertToListOfStrings(context)
                )
            )
        )

        finalAnswer.addAll(
            generateDescriptionItems(
                context,
                title = null,
                sectionTitle = R.string.portfolio_builder_description_section_title_4,
                paragraphs = generateFormattedParagraphs(
                    context = context,
                    strings = listOf(
                        R.string.portfolio_builder_description_paragraph_9,
                        R.string.portfolio_builder_description_paragraph_10,
                        R.string.portfolio_builder_description_paragraph_11
                    ).convertToListOfStrings(context)
                )
            )
        )

        finalAnswer.addAll(
            generateDescriptionItems(
                context,
                title = null,
                sectionTitle = R.string.portfolio_builder_description_section_title_5,
                paragraphs = generateFormattedParagraphs(
                    context = context,
                    strings = listOf(
                        R.string.portfolio_builder_description_paragraph_12
                    ).convertToListOfStrings(context)
                )
            )
        )

        finalAnswer.add(
            DescriptionItem.MarginItem(
                height = context.resources.getDimensionPixelSize(R.dimen.spacing_24)
            )
        )

        return finalAnswer
    }

    private fun generateSetupTargetPortfolioItems(context: Context): List<DescriptionItem> {
        val finalAnswer = mutableListOf<DescriptionItem>()

        finalAnswer.addAll(
            generateDescriptionItems(
                context,
                title = R.string.setup_target_portfolio_description_title,
                titleTextSize = 16f,
                sectionTitle = R.string.setup_target_portfolio_description_section_title_1,
                paragraphs = generateFormattedParagraphs(
                    context = context,
                    strings = listOf(
                        R.string.setup_target_portfolio_description_paragraph_1,
                        R.string.setup_target_portfolio_description_paragraph_2,
                        R.string.setup_target_portfolio_description_paragraph_3
                    ).convertToListOfStrings(context)
                ),
                paragraphsTextSize = 14f
            )
        )

        finalAnswer.addAll(
            generateDescriptionItems(
                context,
                title = null,
                sectionTitle = R.string.setup_target_portfolio_description_section_title_2,
                paragraphs = generateFormattedParagraphs(
                    context = context,
                    strings = listOf(
                        R.string.setup_target_portfolio_description_paragraph_4,
                        R.string.setup_target_portfolio_description_paragraph_5,
                        R.string.setup_target_portfolio_description_paragraph_6
                    ).convertToListOfStrings(context)
                )
            )
        )

        finalAnswer.addAll(
            generateDescriptionItems(
                context,
                title = null,
                sectionTitle = R.string.setup_target_portfolio_description_section_title_3,
                paragraphs = generateFormattedParagraphs(
                    context = context,
                    strings = listOf(
                        R.string.setup_target_portfolio_description_paragraph_7,
                        R.string.setup_target_portfolio_description_paragraph_8,
                        R.string.setup_target_portfolio_description_paragraph_9,
                        R.string.setup_target_portfolio_description_paragraph_10,
                        R.string.setup_target_portfolio_description_paragraph_11
                    ).convertToListOfStrings(context)
                )
            )
        )

        finalAnswer.add(
            DescriptionItem.MarginItem(
                height = context.resources.getDimensionPixelSize(R.dimen.spacing_24)
            )
        )

        return finalAnswer
    }

    private fun generateRoboAdvisorItems(context: Context): List<DescriptionItem> {
        val finalAnswer = mutableListOf<DescriptionItem>()

        finalAnswer.addAll(
            generateDescriptionItems(
                context,
                title = R.string.robo_advisor_description_title,
                titleTextSize = 16f,
                sectionTitle = R.string.robo_advisor_section_title_1,
                paragraphs = generateFormattedParagraphs(
                    context = context,
                    strings = listOf(
                        R.string.robo_advisor_description_paragraph_1,
                        R.string.robo_advisor_description_paragraph_2
                    ).convertToListOfStrings(context)
                ),
                paragraphsTextSize = 14f
            )
        )

        finalAnswer.addAll(
            generateDescriptionItems(
                context,
                title = null,
                titleTextSize = 16f,
                sectionTitle = R.string.robo_advisor_section_title_2,
                paragraphs = generateFormattedParagraphs(
                    context = context,
                    strings = listOf(
                        R.string.robo_advisor_description_paragraph_3
                    ).convertToListOfStrings(context)
                ),
                paragraphsTextSize = 14f
            )
        )

        finalAnswer.addAll(
            generateDescriptionItems(
                context,
                title = null,
                titleTextSize = 16f,
                sectionTitle = R.string.robo_advisor_section_title_3,
                paragraphs = generateFormattedParagraphs(
                    context = context,
                    strings = listOf(
                        R.string.robo_advisor_description_paragraph_4,
                        R.string.robo_advisor_description_paragraph_5,
                        R.string.robo_advisor_description_paragraph_6,
                        R.string.robo_advisor_description_paragraph_7,
                        R.string.robo_advisor_description_paragraph_8
                    ).convertToListOfStrings(context)
                ),
                paragraphsTextSize = 14f
            )
        )

        finalAnswer.addAll(
            generateDescriptionItems(
                context,
                title = null,
                titleTextSize = 16f,
                sectionTitle = R.string.robo_advisor_section_title_4,
                paragraphs = generateFormattedParagraphs(
                    context = context,
                    strings = listOf(
                        R.string.robo_advisor_description_paragraph_9
                    ).convertToListOfStrings(context)
                ),
                paragraphsTextSize = 14f
            )
        )

        finalAnswer.addAll(
            generateDescriptionItems(
                context,
                title = null,
                titleTextSize = 16f,
                sectionTitle = R.string.robo_advisor_section_title_5,
                paragraphs = generateFormattedParagraphs(
                    context = context,
                    strings = listOf(
                        R.string.robo_advisor_description_paragraph_10
                    ).convertToListOfStrings(context)
                ),
                paragraphsTextSize = 14f
            )
        )

        finalAnswer.addAll(
            generateDescriptionItems(
                context,
                title = null,
                titleTextSize = 16f,
                sectionTitle = R.string.robo_advisor_section_title_6,
                paragraphs = generateFormattedParagraphs(
                    context = context,
                    strings = listOf(
                        R.string.robo_advisor_description_paragraph_11
                    ).convertToListOfStrings(context)
                ),
                paragraphsTextSize = 14f
            )
        )

        finalAnswer.add(
            DescriptionItem.MarginItem(
                height = context.resources.getDimensionPixelSize(R.dimen.spacing_24)
            )
        )

        return finalAnswer
    }

    private fun generateMarketCapItems(context: Context): List<DescriptionItem> {
        val finalAnswer = mutableListOf<DescriptionItem>()

        finalAnswer.addAll(
            generateDescriptionItems(
                context,
                title = R.string.market_cap_description_title,
                titleTextSize = 18f,
                sectionTitle = R.string.market_cap_description_section_title_1,
                paragraphs = generateFormattedParagraphs(
                    context = context,
                    strings = listOf(
                        R.string.market_cap_description_paragraph_1,
                        R.string.market_cap_description_paragraph_2
                    ).convertToListOfStrings(context)
                )
            )
        )

        finalAnswer.addAll(
            generateDescriptionItems(
                context,
                title = null,
                sectionTitle = R.string.market_cap_description_section_title_2,
                paragraphs = generateFormattedParagraphs(
                    context = context,
                    strings = listOf(
                        R.string.market_cap_description_paragraph_3
                    ).convertToListOfStrings(context)
                )
            )
        )

        finalAnswer.addAll(
            generateDescriptionItems(
                context,
                title = null,
                sectionTitle = R.string.market_cap_description_section_title_3,
                paragraphs = generateFormattedParagraphs(
                    context = context,
                    strings = listOf(
                        R.string.market_cap_description_paragraph_4,
                        R.string.market_cap_description_paragraph_5,
                        R.string.market_cap_description_paragraph_6
                    ).convertToListOfStrings(context)
                )
            )
        )

        return finalAnswer
    }

    private fun generateBetaItems(context: Context): List<DescriptionItem> {
        val finalAnswer = mutableListOf<DescriptionItem>()

        finalAnswer.addAll(
            generateDescriptionItems(
                context,
                title = R.string.beta_description_title,
                titleTextSize = 18f,
                sectionTitle = R.string.beta_description_section_title_1,
                paragraphs = generateFormattedParagraphs(
                    context = context,
                    strings = listOf(
                        R.string.beta_description_paragraph_1,
                        R.string.beta_description_paragraph_2
                    ).convertToListOfStrings(context)
                )
            )
        )

        finalAnswer.addAll(
            generateDescriptionItems(
                context,
                title = null,
                sectionTitle = R.string.beta_description_section_title_2,
                paragraphs = generateFormattedParagraphs(
                    context = context,
                    strings = listOf(
                        R.string.beta_description_paragraph_3
                    ).convertToListOfStrings(context)
                )
            )
        )

        finalAnswer.addAll(
            generateDescriptionItems(
                context,
                title = null,
                sectionTitle = R.string.beta_description_section_title_3,
                paragraphs = generateFormattedParagraphs(
                    context = context,
                    strings = listOf(
                        R.string.beta_description_paragraph_4,
                        R.string.beta_description_paragraph_5
                    ).convertToListOfStrings(context)
                )
            )
        )

        return finalAnswer
    }

    private fun generatePERatioItems(context: Context): List<DescriptionItem> {
        val finalAnswer = mutableListOf<DescriptionItem>()

        finalAnswer.addAll(
            generateDescriptionItems(
                context,
                title = R.string.p_e_ratio_description_title,
                titleTextSize = 18f,
                sectionTitle = R.string.p_e_ratio_description_section_title_1,
                paragraphs = generateFormattedParagraphs(
                    context = context,
                    strings = listOf(
                        R.string.p_e_ratio_description_paragraph_1,
                        R.string.p_e_ratio_description_paragraph_2
                    ).convertToListOfStrings(context)
                )
            )
        )

        finalAnswer.addAll(
            generateDescriptionItems(
                context,
                title = null,
                sectionTitle = R.string.p_e_ratio_description_section_title_2,
                paragraphs = generateFormattedParagraphs(
                    context = context,
                    strings = listOf(
                        R.string.p_e_ratio_description_paragraph_3
                    ).convertToListOfStrings(context)
                )
            )
        )

        finalAnswer.addAll(
            generateDescriptionItems(
                context,
                title = null,
                sectionTitle = R.string.p_e_ratio_description_section_title_3,
                paragraphs = generateFormattedParagraphs(
                    context = context,
                    strings = listOf(
                        R.string.p_e_ratio_description_paragraph_4,
                        R.string.p_e_ratio_description_paragraph_5
                    ).convertToListOfStrings(context)
                )
            )
        )

        return finalAnswer
    }

    private fun generateForwardPERatioItems(context: Context): List<DescriptionItem> {
        val finalAnswer = mutableListOf<DescriptionItem>()

        finalAnswer.addAll(
            generateDescriptionItems(
                context,
                title = R.string.forward_p_e_ratio_description_title,
                titleTextSize = 18f,
                sectionTitle = R.string.forward_p_e_ratio_description_section_title_1,
                paragraphs = generateFormattedParagraphs(
                    context = context,
                    strings = listOf(
                        R.string.forward_p_e_ratio_description_paragraph_1,
                        R.string.forward_p_e_ratio_description_paragraph_2
                    ).convertToListOfStrings(context)
                )
            )
        )

        finalAnswer.addAll(
            generateDescriptionItems(
                context,
                title = null,
                sectionTitle = R.string.forward_p_e_ratio_description_section_title_2,
                paragraphs = generateFormattedParagraphs(
                    context = context,
                    strings = listOf(
                        R.string.forward_p_e_ratio_description_paragraph_3
                    ).convertToListOfStrings(context)
                )
            )
        )

        finalAnswer.addAll(
            generateDescriptionItems(
                context,
                title = null,
                sectionTitle = R.string.forward_p_e_ratio_description_section_title_3,
                paragraphs = generateFormattedParagraphs(
                    context = context,
                    strings = listOf(
                        R.string.forward_p_e_ratio_description_paragraph_4,
                        R.string.forward_p_e_ratio_description_paragraph_5
                    ).convertToListOfStrings(context)
                )
            )
        )

        return finalAnswer
    }

    private fun generateEPSItems(context: Context): List<DescriptionItem> {
        val finalAnswer = mutableListOf<DescriptionItem>()

        finalAnswer.addAll(
            generateDescriptionItems(
                context,
                title = R.string.eps_description_title,
                titleTextSize = 18f,
                sectionTitle = R.string.eps_description_section_title_1,
                paragraphs = generateFormattedParagraphs(
                    context = context,
                    strings = listOf(
                        R.string.eps_description_paragraph_1,
                        R.string.eps_description_paragraph_2
                    ).convertToListOfStrings(context)
                )
            )
        )

        finalAnswer.addAll(
            generateDescriptionItems(
                context,
                title = null,
                sectionTitle = R.string.eps_description_section_title_2,
                paragraphs = generateFormattedParagraphs(
                    context = context,
                    strings = listOf(
                        R.string.eps_description_paragraph_3
                    ).convertToListOfStrings(context)
                )
            )
        )

        finalAnswer.addAll(
            generateDescriptionItems(
                context,
                title = null,
                sectionTitle = R.string.eps_description_section_title_3,
                paragraphs = generateFormattedParagraphs(
                    context = context,
                    strings = listOf(
                        R.string.eps_description_paragraph_4,
                        R.string.eps_description_paragraph_5
                    ).convertToListOfStrings(context)
                )
            )
        )

        return finalAnswer
    }

    private fun generateDividendYieldItems(context: Context): List<DescriptionItem> {
        val finalAnswer = mutableListOf<DescriptionItem>()

        finalAnswer.addAll(
            generateDescriptionItems(
                context,
                title = R.string.dividend_yield_description_title,
                titleTextSize = 18f,
                sectionTitle = R.string.dividend_yield_description_section_title_1,
                paragraphs = generateFormattedParagraphs(
                    context = context,
                    strings = listOf(
                        R.string.dividend_yield_description_paragraph_1,
                        R.string.dividend_yield_description_paragraph_2
                    ).convertToListOfStrings(context)
                )
            )
        )

        finalAnswer.addAll(
            generateDescriptionItems(
                context,
                title = null,
                sectionTitle = R.string.dividend_yield_description_section_title_2,
                paragraphs = generateFormattedParagraphs(
                    context = context,
                    strings = listOf(
                        R.string.dividend_yield_description_paragraph_3
                    ).convertToListOfStrings(context)
                )
            )
        )

        finalAnswer.addAll(
            generateDescriptionItems(
                context,
                title = null,
                sectionTitle = R.string.dividend_yield_description_section_title_3,
                paragraphs = generateFormattedParagraphs(
                    context = context,
                    strings = listOf(
                        R.string.dividend_yield_description_paragraph_4,
                        R.string.dividend_yield_description_paragraph_5
                    ).convertToListOfStrings(context)
                )
            )
        )

        return finalAnswer
    }

    private fun generateAnalystViewsItems(context: Context): List<DescriptionItem> {
        val finalAnswer = mutableListOf<DescriptionItem>()

        finalAnswer.addAll(
            generateDescriptionItems(
                context,
                title = R.string.analyst_views_description_title,
                titleTextSize = 18f,
                sectionTitle = null,
                paragraphs = generateFormattedParagraphs(
                    context = context,
                    strings = listOf(
                        R.string.analyst_views_description_paragraph_1,
                        R.string.analyst_views_description_paragraph_2,
                        R.string.analyst_views_description_paragraph_3
                    ).convertToListOfStrings(context)
                )
            )
        )

        finalAnswer.addAll(
            generateDescriptionItems(
                context,
                title = null,
                sectionTitle = R.string.analyst_views_description_section_title_1,
                paragraphs = generateFormattedParagraphs(
                    context = context,
                    strings = listOf(
                        R.string.analyst_views_description_paragraph_4,
                        R.string.analyst_views_description_paragraph_5,
                        R.string.analyst_views_description_paragraph_6
                    ).convertToListOfStrings(context)
                )
            )
        )

        finalAnswer.addAll(
            generateDescriptionItems(
                context,
                title = null,
                sectionTitle = R.string.analyst_views_description_section_title_2,
                paragraphs = generateFormattedParagraphs(
                    context = context,
                    strings = listOf(
                        R.string.analyst_views_description_paragraph_7,
                        R.string.analyst_views_description_paragraph_8
                    ).convertToListOfStrings(context)
                )
            )
        )

        return finalAnswer
    }

    private fun generateSentimentScoreEUViewsItems(context: Context): List<DescriptionItem> {
        val finalAnswer = mutableListOf<DescriptionItem>()

        finalAnswer.addAll(
            generateDescriptionItems(
                context,
                title = R.string.wealthyhood_sentiment_score_eu_description_title_text,
                titleTextSize = 18f,
                sectionTitle = null,
                paragraphs = generateFormattedParagraphs(
                    context = context,
                    strings = listOf(
                        R.string.wealthyhood_sentiment_score_eu_description_text_1,
                        R.string.wealthyhood_sentiment_score_eu_description_text_2,
                        R.string.wealthyhood_sentiment_score_eu_description_text_3,
                        R.string.wealthyhood_sentiment_score_eu_description_text_4,
                        R.string.wealthyhood_sentiment_score_eu_description_text_5
                    ).convertToListOfStrings(context)
                )
            )
        )

        return finalAnswer
    }

    private fun generateSentimentScoreUKViewsItems(context: Context): List<DescriptionItem> {
        val finalAnswer = mutableListOf<DescriptionItem>()

        finalAnswer.addAll(
            generateDescriptionItems(
                context,
                title = R.string.wealthyhood_sentiment_score_uk_description_title_text,
                titleTextSize = 18f,
                sectionTitle = null,
                paragraphs = generateFormattedParagraphs(
                    context = context,
                    strings = listOf(
                        R.string.wealthyhood_sentiment_score_uk_description_text_1,
                        R.string.wealthyhood_sentiment_score_uk_description_text_2,
                        R.string.wealthyhood_sentiment_score_uk_description_text_3,
                        R.string.wealthyhood_sentiment_score_uk_description_text_4,
                        R.string.wealthyhood_sentiment_score_uk_description_text_5
                    ).convertToListOfStrings(context)
                )
            )
        )

        return finalAnswer
    }

    private fun generateNewsSentimentScoreViewsItems(context: Context): List<DescriptionItem> {
        val finalAnswer = mutableListOf<DescriptionItem>()

        finalAnswer.addAll(
            generateDescriptionItems(
                context,
                title = R.string.news_sentiment_score_description_title_text,
                titleTextSize = 18f,
                sectionTitle = null,
                paragraphs = generateFormattedParagraphs(
                    context = context,
                    strings = listOf(
                        R.string.news_sentiment_score_description_text_1,
                        R.string.news_sentiment_score_description_text_2,
                        R.string.news_sentiment_score_description_text_3,
                        R.string.news_sentiment_score_description_text_4
                    ).convertToListOfStrings(context)
                )
            )
        )

        return finalAnswer
    }

    private fun generateAnalystSentimentScoreViewsItems(context: Context): List<DescriptionItem> {
        val finalAnswer = mutableListOf<DescriptionItem>()

        finalAnswer.addAll(
            generateDescriptionItems(
                context,
                title = R.string.analyst_sentiment_score_description_title_text,
                titleTextSize = 18f,
                sectionTitle = null,
                paragraphs = generateFormattedParagraphs(
                    context = context,
                    strings = listOf(
                        R.string.analyst_sentiment_score_description_text_1,
                        R.string.analyst_sentiment_score_description_text_2,
                        R.string.analyst_sentiment_score_description_text_3
                    ).convertToListOfStrings(context)
                )
            )
        )

        return finalAnswer
    }

    private fun generatePriceMomentumSentimentScoreViewsItems(context: Context): List<DescriptionItem> {
        val finalAnswer = mutableListOf<DescriptionItem>()

        finalAnswer.addAll(
            generateDescriptionItems(
                context,
                title = R.string.price_momentum_sentiment_score_description_title_text,
                titleTextSize = 18f,
                sectionTitle = null,
                paragraphs = generateFormattedParagraphs(
                    context = context,
                    strings = listOf(
                        R.string.price_momentum_sentiment_score_description_text_1,
                        R.string.price_momentum_sentiment_score_description_text_2,
                        R.string.price_momentum_sentiment_score_description_text_3,
                        R.string.price_momentum_sentiment_score_description_text_4,
                        R.string.price_momentum_sentiment_score_description_text_5
                    ).convertToListOfStrings(context)
                )
            )
        )

        return finalAnswer
    }

    private fun generateSundownDigestViewsItems(context: Context): List<DescriptionItem> {
        val finalAnswer = mutableListOf<DescriptionItem>()

        finalAnswer.addAll(
            generateDescriptionItems(
                context,
                title = R.string.sundown_digest_description_title_text,
                titleTextSize = 18f,
                sectionTitle = null,
                paragraphs = generateFormattedParagraphs(
                    context = context,
                    strings = listOf(
                        R.string.sundown_digest_description_text_1,
                        R.string.sundown_digest_description_text_2,
                        R.string.sundown_digest_description_text_3,
                        R.string.sundown_digest_description_text_4
                    ).convertToListOfStrings(context)
                )
            )
        )

        return finalAnswer
    }

    private fun generateAveragePriceTargetItems(context: Context): List<DescriptionItem> {
        val finalAnswer = mutableListOf<DescriptionItem>()

        finalAnswer.addAll(
            generateDescriptionItems(
                context,
                title = R.string.average_price_target_description_title,
                titleTextSize = 18f,
                sectionTitle = null,
                paragraphs = generateFormattedParagraphs(
                    context = context,
                    strings = listOf(
                        R.string.average_price_target_description_paragraph_1,
                        R.string.average_price_target_description_paragraph_2
                    ).convertToListOfStrings(context)
                )
            )
        )

        finalAnswer.addAll(
            generateDescriptionItems(
                context,
                title = null,
                sectionTitle = R.string.average_price_target_description_section_title_1,
                paragraphs = generateFormattedParagraphs(
                    context = context,
                    strings = listOf(
                        R.string.average_price_target_description_paragraph_3,
                        R.string.average_price_target_description_paragraph_4
                    ).convertToListOfStrings(context)
                )
            )
        )

        finalAnswer.addAll(
            generateDescriptionItems(
                context,
                title = null,
                sectionTitle = R.string.average_price_target_description_section_title_2,
                paragraphs = generateFormattedParagraphs(
                    context = context,
                    strings = listOf(
                        R.string.average_price_target_description_paragraph_5,
                        R.string.average_price_target_description_paragraph_6
                    ).convertToListOfStrings(context)
                )
            )
        )

        return finalAnswer
    }

    private fun generateBaseCurrencyItems(context: Context): List<DescriptionItem> {
        val finalAnswer = mutableListOf<DescriptionItem>()

        finalAnswer.addAll(
            generateDescriptionItems(
                context,
                title = R.string.base_currency_description_title,
                titleTextSize = 18f,
                sectionTitle = R.string.base_currency_description_section_title_1,
                paragraphs = generateFormattedParagraphs(
                    context = context,
                    strings = listOf(
                        R.string.base_currency_description_paragraph_1,
                        R.string.base_currency_description_paragraph_2
                    ).convertToListOfStrings(context)
                )
            )
        )

        return finalAnswer
    }

    private fun generateETFMetricExpectedReturnItems(context: Context): List<DescriptionItem> {
        val finalAnswer = mutableListOf<DescriptionItem>()

        finalAnswer.addAll(
            generateDescriptionItems(
                context,
                title = R.string.etf_metric_expected_return_description_title,
                titleTextSize = 18f,
                sectionTitle = R.string.etf_metric_expected_return_description_section_title_1,
                paragraphs = generateFormattedParagraphs(
                    context = context,
                    strings = listOf(
                        R.string.etf_metric_expected_return_description_paragraph_1,
                        R.string.etf_metric_expected_return_description_paragraph_2,
                        R.string.etf_metric_expected_return_description_paragraph_3,
                        R.string.etf_metric_expected_return_description_paragraph_4
                    ).convertToListOfStrings(context)
                )
            )
        )

        return finalAnswer
    }

    private fun generateETFMetricDividendYieldItems(context: Context): List<DescriptionItem> {
        val finalAnswer = mutableListOf<DescriptionItem>()

        finalAnswer.addAll(
            generateDescriptionItems(
                context,
                title = R.string.etf_metric_dividend_yield_description_title,
                titleTextSize = 18f,
                sectionTitle = R.string.etf_metric_dividend_yield_description_section_title_1,
                paragraphs = generateFormattedParagraphs(
                    context = context,
                    strings = listOf(
                        R.string.etf_metric_dividend_yield_description_paragraph_1,
                        R.string.etf_metric_dividend_yield_description_paragraph_2,
                        R.string.etf_metric_dividend_yield_description_paragraph_3
                    ).convertToListOfStrings(context)
                )
            )
        )

        return finalAnswer
    }

    private fun generateIncomeItems(context: Context): List<DescriptionItem> {
        val finalAnswer = mutableListOf<DescriptionItem>()

        finalAnswer.addAll(
            generateDescriptionItems(
                context,
                title = R.string.income_description_title,
                titleTextSize = 18f,
                sectionTitle = R.string.income_description_section_title_1,
                paragraphs = generateFormattedParagraphs(
                    context = context,
                    strings = listOf(
                        R.string.income_description_paragraph_1,
                        R.string.income_description_paragraph_2,
                        R.string.income_description_paragraph_3
                    ).convertToListOfStrings(context)
                )
            )
        )

        return finalAnswer
    }

    private fun generateETFMetricRiskItems(context: Context): List<DescriptionItem> {
        val finalAnswer = mutableListOf<DescriptionItem>()

        finalAnswer.addAll(
            generateDescriptionItems(
                context,
                title = R.string.etf_metric_risk_description_title,
                titleTextSize = 18f,
                sectionTitle = R.string.etf_metric_risk_description_section_title_1,
                paragraphs = generateFormattedParagraphs(
                    context = context,
                    strings = listOf(
                        R.string.etf_metric_risk_description_paragraph_1,
                        R.string.etf_metric_risk_description_paragraph_2,
                        R.string.etf_metric_risk_description_paragraph_3,
                        R.string.etf_metric_risk_description_paragraph_4,
                        R.string.etf_metric_risk_description_paragraph_5
                    ).convertToListOfStrings(context)
                )
            )
        )

        return finalAnswer
    }

    private fun generateCouponItems(
        context: Context,
        currencyISOCode: String?,
        userLocale: Locale
    ): List<DescriptionItem> {
        val finalAnswer = mutableListOf<DescriptionItem>()

        val firstAmountText = 5.0.generateFormattedCurrency(
            currencyISOCode = currencyISOCode,
            maximumFractionDigits = 0,
            locale = userLocale
        )

        val secondAmountText = 100.0.generateFormattedCurrency(
            currencyISOCode = currencyISOCode,
            maximumFractionDigits = 0,
            locale = userLocale
        )

        val couponDescriptionParagraph1 = context.resources.getString(
            R.string.coupon_description_paragraph_1
        )

        val couponDescriptionParagraph2 = context.resources.getString(
            R.string.coupon_description_paragraph_2,
            secondAmountText,
            firstAmountText,
            secondAmountText
        )

        finalAnswer.addAll(
            generateDescriptionItems(
                context,
                title = R.string.coupon_description_title,
                titleTextSize = 18f,
                sectionTitle = R.string.coupon_description_section_title_1,
                paragraphs = generateFormattedParagraphs(
                    context = context,
                    strings = listOf(
                        couponDescriptionParagraph1,
                        couponDescriptionParagraph2
                    )
                )
            )
        )

        return finalAnswer
    }

    private fun generateExpenseRatioItems(context: Context): List<DescriptionItem> {
        val finalAnswer = mutableListOf<DescriptionItem>()

        finalAnswer.addAll(
            generateDescriptionItems(
                context,
                title = R.string.expense_ratio_description_title,
                titleTextSize = 18f,
                sectionTitle = R.string.expense_ratio_description_section_title_1,
                paragraphs = generateFormattedParagraphs(
                    context = context,
                    strings = listOf(
                        R.string.expense_ratio_description_paragraph_1,
                        R.string.expense_ratio_description_paragraph_2
                    ).convertToListOfStrings(context)
                )
            )
        )

        return finalAnswer
    }

    private fun generateETFMetricForwardPERatioItems(context: Context): List<DescriptionItem> {
        val finalAnswer = mutableListOf<DescriptionItem>()

        finalAnswer.addAll(
            generateDescriptionItems(
                context,
                title = R.string.etf_metric_forward_p_e_ratio_description_title,
                titleTextSize = 18f,
                sectionTitle = R.string.etf_metric_forward_p_e_ratio_description_section_title_1,
                paragraphs = generateFormattedParagraphs(
                    context = context,
                    strings = listOf(
                        R.string.etf_metric_forward_p_e_ratio_description_paragraph_1,
                        R.string.etf_metric_forward_p_e_ratio_description_paragraph_2,
                        R.string.etf_metric_forward_p_e_ratio_description_paragraph_3
                    ).convertToListOfStrings(context)
                )
            )
        )

        return finalAnswer
    }

    private fun generateYieldItems(
        context: Context,
        currencyISOCode: String?,
        userLocale: Locale
    ): List<DescriptionItem> {
        val finalAnswer = mutableListOf<DescriptionItem>()

        val amountText5 = 5.0.generateFormattedCurrency(
            currencyISOCode = currencyISOCode,
            maximumFractionDigits = 0,
            locale = userLocale
        )

        val amountText90 = 90.0.generateFormattedCurrency(
            currencyISOCode = currencyISOCode,
            maximumFractionDigits = 0,
            locale = userLocale
        )

        val amountText100 = 100.0.generateFormattedCurrency(
            currencyISOCode = currencyISOCode,
            maximumFractionDigits = 0,
            locale = userLocale
        )

        val amountText110 = 110.0.generateFormattedCurrency(
            currencyISOCode = currencyISOCode,
            maximumFractionDigits = 0,
            locale = userLocale
        )

        val paragraph1 = context.resources.getString(
            R.string.yield_description_paragraph_1
        )

        val paragraph2 = context.resources.getString(
            R.string.yield_description_paragraph_2
        )

        val paragraph3 = context.resources.getString(
            R.string.yield_description_paragraph_3
        )

        val paragraph4 = context.resources.getString(
            R.string.yield_description_paragraph_4,
            amountText100,
            amountText5,
            amountText110
        )

        val paragraph5 = context.resources.getString(
            R.string.yield_description_paragraph_5,
            amountText90
        )

        finalAnswer.addAll(
            generateDescriptionItems(
                context,
                title = R.string.yield_description_title,
                titleTextSize = 18f,
                sectionTitle = R.string.yield_description_section_title_1,
                paragraphs = generateFormattedParagraphs(
                    context = context,
                    strings = listOf(
                        paragraph1,
                        paragraph2,
                        paragraph3,
                        paragraph4,
                        paragraph5
                    )
                )
            )
        )

        return finalAnswer
    }

    private fun generateTargetPortfolioScreenItems(context: Context): List<DescriptionItem> {
        val finalAnswer = mutableListOf<DescriptionItem>()

        finalAnswer.addAll(
            generateDescriptionItems(
                context,
                title = R.string.target_portfolio_screen_description_title,
                titleTextSize = 18f,
                sectionTitle = R.string.target_portfolio_screen_description_section_title_1,
                paragraphs = generateFormattedParagraphs(
                    context = context,
                    strings = listOf(
                        R.string.target_portfolio_screen_description_paragraph_1,
                        R.string.target_portfolio_screen_description_paragraph_2,
                        R.string.target_portfolio_screen_description_paragraph_3
                    ).convertToListOfStrings(context)
                )
            )
        )

        finalAnswer.addAll(
            generateDescriptionItems(
                context,
                title = null,
                titleTextSize = 18f,
                sectionTitle = R.string.target_portfolio_screen_description_section_title_2,
                paragraphs = generateFormattedParagraphs(
                    context = context,
                    strings = listOf(
                        R.string.target_portfolio_screen_description_paragraph_4,
                        R.string.target_portfolio_screen_description_paragraph_5,
                        R.string.target_portfolio_screen_description_paragraph_6
                    ).convertToListOfStrings(context)
                )
            )
        )

        finalAnswer.addAll(
            generateDescriptionItems(
                context,
                title = null,
                titleTextSize = 18f,
                sectionTitle = R.string.target_portfolio_screen_description_section_title_3,
                paragraphs = generateFormattedParagraphs(
                    context = context,
                    strings = listOf(
                        R.string.target_portfolio_screen_description_paragraph_7,
                        R.string.target_portfolio_screen_description_paragraph_8,
                        R.string.target_portfolio_screen_description_paragraph_9,
                        R.string.target_portfolio_screen_description_paragraph_10,
                        R.string.target_portfolio_screen_description_paragraph_11
                    ).convertToListOfStrings(context)
                )
            )
        )

        return finalAnswer
    }

    private fun generateDiversificationScoreItems(context: Context): List<DescriptionItem> {
        val finalAnswer = mutableListOf<DescriptionItem>()

        finalAnswer.addAll(
            generateDescriptionItems(
                context,
                title = R.string.diversification_score_description_title,
                titleTextSize = 18f,
                sectionTitle = R.string.diversification_score_description_section_title_1,
                paragraphs = generateFormattedParagraphs(
                    context = context,
                    strings = listOf(
                        R.string.diversification_score_description_paragraph_1
                    ).convertToListOfStrings(context)
                )
            )
        )

        finalAnswer.addAll(
            generateDescriptionItems(
                context,
                title = null,
                titleTextSize = 18f,
                sectionTitle = R.string.diversification_score_description_section_title_2,
                paragraphs = generateFormattedParagraphs(
                    context = context,
                    strings = listOf(
                        R.string.diversification_score_description_paragraph_2,
                        R.string.diversification_score_description_paragraph_3,
                        R.string.diversification_score_description_paragraph_4
                    ).convertToListOfStrings(context)
                )
            )
        )

        finalAnswer.addAll(
            generateDescriptionItems(
                context,
                title = null,
                titleTextSize = 18f,
                sectionTitle = R.string.diversification_score_description_section_title_3,
                paragraphs = generateFormattedParagraphs(
                    context = context,
                    strings = listOf(
                        R.string.diversification_score_description_paragraph_5,
                        R.string.diversification_score_description_paragraph_6
                    ).convertToListOfStrings(context)
                )
            )
        )

        return finalAnswer
    }

    private fun generateAboutChangesToReviewItems(context: Context): List<DescriptionItem> {
        val finalAnswer = mutableListOf<DescriptionItem>()

        finalAnswer.addAll(
            generateDescriptionItems(
                context,
                title = R.string.about_changes_to_review_description_title,
                titleTextSize = 18f,
                sectionTitle = null,
                paragraphs = generateFormattedParagraphs(
                    context = context,
                    strings = listOf(
                        R.string.about_changes_to_review_description_paragraph_1,
                        R.string.about_changes_to_review_description_paragraph_2,
                        R.string.about_changes_to_review_description_paragraph_3,
                        R.string.about_changes_to_review_description_paragraph_4,
                        R.string.about_changes_to_review_description_paragraph_5,
                        R.string.about_changes_to_review_description_paragraph_6
                    ).convertToListOfStrings(context)
                )
            )
        )

        finalAnswer.addAll(
            generateDescriptionItems(
                context,
                title = null,
                titleTextSize = 18f,
                sectionTitle = R.string.about_changes_to_review_description_section_title_1,
                paragraphs = generateFormattedParagraphs(
                    context = context,
                    strings = listOf(
                        R.string.about_changes_to_review_description_paragraph_7,
                        R.string.about_changes_to_review_description_paragraph_8
                    ).convertToListOfStrings(context)
                )
            )
        )

        return finalAnswer
    }

    private fun generateMonthlyInvestmentInfoItems(context: Context): List<DescriptionItem> {
        val finalAnswer = mutableListOf<DescriptionItem>()

        finalAnswer.addAll(
            generateDescriptionItems(
                context,
                title = R.string.set_up_a_monthly_investment_description_section_title_1,
                titleTextSize = 18f,
                sectionTitle = null,
                paragraphs = generateFormattedParagraphs(
                    context = context,
                    strings = listOf(
                        R.string.set_up_a_monthly_investment_description_paragraph_1
                    ).convertToListOfStrings(context)
                )
            )
        )

        finalAnswer.addAll(
            generateDescriptionItems(
                context,
                title = null,
                titleTextSize = 18f,
                sectionTitle = R.string.set_up_a_monthly_investment_description_section_title_2,
                paragraphs = generateFormattedParagraphs(
                    context = context,
                    strings = listOf(
                        R.string.set_up_a_monthly_investment_description_paragraph_2,
                        R.string.set_up_a_monthly_investment_description_paragraph_3
                    ).convertToListOfStrings(context)
                )
            )
        )

        finalAnswer.addAll(
            generateDescriptionItems(
                context,
                title = null,
                titleTextSize = 18f,
                sectionTitle = R.string.set_up_a_monthly_investment_description_section_title_3,
                paragraphs = generateFormattedParagraphs(
                    context = context,
                    strings = listOf(
                        R.string.set_up_a_monthly_investment_description_paragraph_4,
                        R.string.set_up_a_monthly_investment_description_paragraph_5,
                        R.string.set_up_a_monthly_investment_description_paragraph_6
                    ).convertToListOfStrings(context)
                )
            )
        )

        finalAnswer.addAll(
            generateDescriptionItems(
                context,
                title = null,
                titleTextSize = 18f,
                sectionTitle = R.string.set_up_a_monthly_investment_description_section_title_4,
                paragraphs = generateFormattedParagraphs(
                    context = context,
                    strings = listOf(
                        R.string.set_up_a_monthly_investment_description_paragraph_7
                    ).convertToListOfStrings(context)
                )
            )
        )

        return finalAnswer
    }

    private fun generatePortfolioInfoItems(context: Context): List<DescriptionItem> {
        val finalAnswer = mutableListOf<DescriptionItem>()

        finalAnswer.addAll(
            generateDescriptionItems(
                context,
                title = R.string.buy_target_portfolio_section_title_1,
                titleTextSize = 18f,
                sectionTitle = null,
                paragraphs = generateFormattedParagraphs(
                    context = context,
                    strings = listOf(
                        R.string.buy_target_portfolio_section_paragraph_1,
                        R.string.buy_target_portfolio_section_paragraph_2,
                        R.string.buy_target_portfolio_section_paragraph_3,
                        R.string.buy_target_portfolio_section_paragraph_4
                    ).convertToListOfStrings(context)
                )
            )
        )

        return finalAnswer
    }
}
