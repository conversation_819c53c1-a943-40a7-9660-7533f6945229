package com.wealthyhood.wealthyhood.domain

import com.wealthyhood.wealthyhood.database.InvestmentProduct
import com.wealthyhood.wealthyhood.service.BankAccount
import com.wealthyhood.wealthyhood.service.Portfolio
import com.wealthyhood.wealthyhood.service.SavingsProduct
import com.wealthyhood.wealthyhood.service.SavingsProductFeeDetails
import com.wealthyhood.wealthyhood.service.Transaction
import com.wealthyhood.wealthyhood.service.TransactionActivityResponse
import com.wealthyhood.wealthyhood.service.TrueLayerProvider
import com.wealthyhood.wealthyhood.service.User
import com.wealthyhood.wealthyhood.ui.myaccount.AccountsLoader

data class AccountsData(
    val user: User?,
    val portfolios: List<Portfolio>?,
    val incomingCashFlows: List<Transaction>?,
    val transactions: List<TransactionActivityResponse>?,
    val investmentProducts: List<InvestmentProduct>?,
    val trueLayerProviders: List<TrueLayerProvider>?,
    val bankAccounts: List<BankAccount>?,
    val savingsProducts: List<SavingsProduct>?,
    val savingsProductFeeDetails: SavingsProductFeeDetails?,
    val savingsProductActivity: List<TransactionActivityResponse>?,
    val timestamp: Long,
    val failedAPICalls: List<AccountsLoader.Companion.APICall>
)
