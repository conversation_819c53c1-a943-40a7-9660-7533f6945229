package com.wealthyhood.wealthyhood.domain

import com.google.gson.Gson
import com.wealthyhood.wealthyhood.database.InvestmentProduct
import com.wealthyhood.wealthyhood.database.WealthyhoodDatabase
import com.wealthyhood.wealthyhood.extensions.convertToListOfPortfolios
import com.wealthyhood.wealthyhood.extensions.convertToListOfTransactions
import com.wealthyhood.wealthyhood.repository.PreferencesRepository
import com.wealthyhood.wealthyhood.service.Portfolio
import com.wealthyhood.wealthyhood.service.Transaction
import java.util.Date

class CacheHelper {

    companion object {

        private const val MAX_DIFF = 15 * 60 * 1000L

        fun getValidCachedTransactions(preferencesRepository: PreferencesRepository): List<Transaction>? {
            val cacheData = preferencesRepository.getTransactionsCacheData() ?: return null

            val currentTimestamp = Date().time
            val diff = currentTimestamp - cacheData.timestamp

            if (diff > MAX_DIFF) {
                return null
            }

            val lastTransactionCreatedAt = preferencesRepository.getLastTransactionCreatedAt()

            if (lastTransactionCreatedAt > cacheData.timestamp) {
                return null
            }

            return cacheData.data.convertToListOfTransactions()
        }

        fun cacheTransactions(
            preferencesRepository: PreferencesRepository,
            transactions: List<Transaction>?
        ) {
            val jsonString = transactions?.let {
                Gson().toJson(transactions)
            }

            val cacheData = jsonString?.let { data ->
                CacheData(
                    type = CacheDataTypeEnum.TRANSACTIONS,
                    parameters = null,
                    timestamp = Date().time,
                    data = data
                )
            }

            preferencesRepository.putTransactionsCacheData(cacheData)
        }

        suspend fun getValidCachedInvestmentProducts(database: WealthyhoodDatabase): List<InvestmentProduct>? {
            val products = database.investmentProductDAO.getAllInvestmentProducts()

            return products.ifEmpty { null }
        }

        suspend fun cacheInvestmentProducts(
            database: WealthyhoodDatabase,
            investmentProducts: List<InvestmentProduct>?
        ) {
            val dbProducts = investmentProducts ?: emptyList()

            database.investmentProductDAO.insertInvestmentProducts(dbProducts)
        }

        fun getValidCachedPortfolios(
            preferencesRepository: PreferencesRepository,
            parameters: String?
        ): List<Portfolio>? {
            val cache = preferencesRepository.getPortfoliosCache() ?: return null

            val cacheData = cache.findLast {
                it.parameters == parameters
            } ?: return null

            val currentTimestamp = Date().time
            val diff = currentTimestamp - cacheData.timestamp

            if (diff > MAX_DIFF) {
                return null
            }

            return cacheData.data.convertToListOfPortfolios()
        }

        fun cachePortfolios(
            preferencesRepository: PreferencesRepository,
            parameters: String?,
            portfolios: List<Portfolio>?
        ) {
            val cache =
                (preferencesRepository.getPortfoliosCache() ?: emptyList()).toMutableList()

            val existingCacheData = cache.findLast {
                it.parameters == parameters
            }

            existingCacheData?.let {
                cache.remove(it)
            }

            if (portfolios == null) {
                preferencesRepository.putPortfoliosCache(cache)

                return
            }

            val cacheData = CacheData(
                type = CacheDataTypeEnum.PORTFOLIOS,
                parameters = parameters,
                timestamp = Date().time,
                data = Gson().toJson(portfolios)
            )

            cache.add(cacheData)

            preferencesRepository.putPortfoliosCache(cache)
        }

        fun clearCache(preferencesRepository: PreferencesRepository) {
            // Τα investment products δε θέλουμε να τα κάνουμε clear.

            clearTransactionsCache(preferencesRepository)
            clearPortfoliosCache(preferencesRepository)
        }

        fun clearTransactionsCache(preferencesRepository: PreferencesRepository) {
            preferencesRepository.putTransactionsCacheData(null)
        }

        fun clearPortfoliosCache(preferencesRepository: PreferencesRepository) {
            preferencesRepository.putPortfoliosCache(null)
        }
    }
}
