package com.wealthyhood.wealthyhood.domain

import com.wealthyhood.wealthyhood.service.BannerPrompt
import com.wealthyhood.wealthyhood.service.GetDailySummariesResponse
import com.wealthyhood.wealthyhood.service.SavingsProductFeeDetails
import com.wealthyhood.wealthyhood.service.Transaction
import com.wealthyhood.wealthyhood.service.User
import com.wealthyhood.wealthyhood.ui.dailybrief.DailyBriefLoader

data class DailyBriefData(
    val user: User?,
    val savingsProductFeeDetails: SavingsProductFeeDetails?,
    val sortedBannerPrompts: List<BannerPrompt>?,
    val dailySummaries: GetDailySummariesResponse?,
    val pendingTransactions: List<Transaction>?,
    val timestamp: Long,
    val failedAPICalls: List<DailyBriefLoader.FailedAPICall>
)
