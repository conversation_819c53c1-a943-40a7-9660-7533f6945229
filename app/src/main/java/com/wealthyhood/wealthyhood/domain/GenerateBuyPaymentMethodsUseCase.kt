package com.wealthyhood.wealthyhood.domain

import com.wealthyhood.wealthyhood.extensions.generateFormattedCurrency
import com.wealthyhood.wealthyhood.extensions.getCashForCurrency
import com.wealthyhood.wealthyhood.model.PaymentMethod
import com.wealthyhood.wealthyhood.service.BankAccount
import com.wealthyhood.wealthyhood.service.Gift
import com.wealthyhood.wealthyhood.service.Portfolio
import java.util.Locale

class GenerateBuyPaymentMethodsUseCase {

    operator fun invoke(
        isUserUK: <PERSON>olean,
        willExecuteSingleBuyOrder: Boolean,
        portfolio: Portfolio?,
        gifts: List<Gift>?,
        bankAccounts: List<BankAccount>?,
        currencyISOCode: String?,
        userLocale: Locale
    ): List<PaymentMethod> {
        val finalAnswer = mutableListOf<PaymentMethod>()

        if (willExecuteSingleBuyOrder) {
            finalAnswer.addAll(
                generateGiftPaymentMethods(gifts = gifts, userLocale = userLocale)
            )

            finalAnswer.add(
                generateCashPaymentMethod(
                    portfolio = portfolio,
                    currencyISOCode = currencyISOCode,
                    userLocale = userLocale
                )
            )
        }

        // Οι EU users δεν έχουν 1-step investment

        if (isUserUK || !willExecuteSingleBuyOrder) {
            generateBankAccountPaymentMethods(bankAccounts)?.let {
                finalAnswer.addAll(it)
            }
        }

        return finalAnswer
    }

    fun generateAddMoneyPaymentMethods(
        isUserUK: Boolean,
        willExecuteSingleBuyOrder: Boolean,
        portfolio: Portfolio?,
        bankAccounts: List<BankAccount>?,
        currencyISOCode: String?,
        userLocale: Locale
    ): List<PaymentMethod> {
        val finalAnswer = mutableListOf<PaymentMethod>()

        if (willExecuteSingleBuyOrder) {
            finalAnswer.add(
                generateCashPaymentMethod(
                    portfolio = portfolio,
                    currencyISOCode = currencyISOCode,
                    userLocale = userLocale
                )
            )
        }

        // Οι EU users δεν έχουν 1-step investment

        if (isUserUK || !willExecuteSingleBuyOrder) {
            generateBankAccountPaymentMethods(bankAccounts)?.let {
                finalAnswer.addAll(it)
            }
        }

        return finalAnswer
    }

    private fun generateGiftPaymentMethods(
        gifts: List<Gift>?,
        userLocale: Locale
    ): List<PaymentMethod> {
        val finalAnswer = mutableListOf<PaymentMethod>()

        gifts?.forEach { gift ->
            val giftID = gift.id ?: return@forEach

            val formattedBalance =
                gift.consideration?.amount?.div(100)?.generateFormattedCurrency(
                    currencyISOCode = gift.consideration.currency,
                    maximumFractionDigits = 0,
                    locale = userLocale
                )

            val paymentMethod = PaymentMethod(
                id = giftID,
                type = PaymentMethod.TYPE_GIFT_PAYMENT,
                formattedBalance = formattedBalance,
                bankAccount = null
            )

            finalAnswer.add(paymentMethod)
        }

        return finalAnswer
    }

    private fun generateCashPaymentMethod(
        portfolio: Portfolio?,
        currencyISOCode: String?,
        userLocale: Locale
    ): PaymentMethod {
        val cash = portfolio?.getCashForCurrency(currencyISOCode)

        val formattedBalance = cash?.generateFormattedCurrency(
            currencyISOCode = currencyISOCode,
            locale = userLocale
        )

        return PaymentMethod(
            id = PaymentMethod.TYPE_CASH_ACCOUNT,
            type = PaymentMethod.TYPE_CASH_ACCOUNT,
            formattedBalance = formattedBalance,
            bankAccount = null
        )
    }

    private fun generateBankAccountPaymentMethods(bankAccounts: List<BankAccount>?): List<PaymentMethod>? {
        return bankAccounts?.map { bankAccount ->
            PaymentMethod(
                id = bankAccount.id,
                type = PaymentMethod.TYPE_BANK_ACCOUNT,
                formattedBalance = null,
                bankAccount = bankAccount
            )
        }
    }
}
