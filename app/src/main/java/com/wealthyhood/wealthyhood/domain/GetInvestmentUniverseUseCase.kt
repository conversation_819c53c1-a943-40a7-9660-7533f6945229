package com.wealthyhood.wealthyhood.domain

import android.content.Context
import com.wealthyhood.wealthyhood.common.UniverseSingleton
import com.wealthyhood.wealthyhood.database.WealthyhoodDatabase
import com.wealthyhood.wealthyhood.extensions.generateAssetsFromKeys
import com.wealthyhood.wealthyhood.model.DomainResult
import com.wealthyhood.wealthyhood.repository.PreferencesRepository
import com.wealthyhood.wealthyhood.repository.Repository
import com.wealthyhood.wealthyhood.service.GetInvestmentUniverseResponse
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext

class GetInvestmentUniverseUseCase(
    private val context: Context,
    private val repository: Repository,
    private val preferencesRepository: PreferencesRepository
) {

    suspend fun invoke(
        companyEntity: String?,
        country: String?,
        email: String?,
        referralCode: String?
    ): DomainResult<GetInvestmentUniverseResponse>? {
        return when (val result = repository.getInvestmentUniverse(
            companyEntity = companyEntity,
            country = country,
            email = email,
            referralCode = referralCode
        )) {
            is DomainResult.Success -> {
                val response = result.body

                saveDataToDatabase(
                    context = context,
                    response = response
                )

                withContext(Dispatchers.IO) {
                    preferencesRepository.setUpInvestmentUniverse(response)
                }

                withContext(Dispatchers.Main) {
                    result
                }
            }

            else -> {
                result
            }
        }
    }

    private suspend fun saveDataToDatabase(
        context: Context,
        response: GetInvestmentUniverseResponse?
    ) {
        val database = WealthyhoodDatabase.getInstance(context.applicationContext)
        database.assetDAO.deleteAll()

        val assets = response?.investmentUniverse?.assets

        assets?.generateAssetsFromKeys()?.let { assetEntries ->
            database.assetDAO.insertAll(assetEntries)
        }

        UniverseSingleton.resetAssets()
    }
}
