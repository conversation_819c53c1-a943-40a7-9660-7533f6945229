package com.wealthyhood.wealthyhood.domain

import android.content.Context
import com.wealthyhood.wealthyhood.R
import com.wealthyhood.wealthyhood.extensions.formatAsPercentage
import com.wealthyhood.wealthyhood.service.AssetClass
import java.util.Locale

data class AssetClassBreakdownItem(
    val assetClass: AssetClass,
    val allocation: Double
) {

    companion object {

        fun AssetClassBreakdownItem.generateTitle(context: Context): String? {
            return if (assetClass.id == "equities") {
                context.resources.getString(R.string.equities_label)
            } else assetClass.fieldName
        }

        fun AssetClassBreakdownItem.generateFormattedAllocation(locale: Locale): String? {
            return allocation.formatAsPercentage(
                minimumFractionDigits = 0,
                locale = locale
            )
        }
    }
}
