package com.wealthyhood.wealthyhood.domain

import com.wealthyhood.wealthyhood.database.InvestmentProduct
import com.wealthyhood.wealthyhood.database.WealthyhoodDatabase
import com.wealthyhood.wealthyhood.extensions.convertToDatabaseInvestmentProducts
import com.wealthyhood.wealthyhood.model.DomainResult
import com.wealthyhood.wealthyhood.model.NetworkResource
import com.wealthyhood.wealthyhood.repository.PreferencesRepository
import com.wealthyhood.wealthyhood.repository.Repository
import java.util.Date

class GetInvestmentProductsUseCase(
    private val preferencesRepository: PreferencesRepository,
    private val repository: Repository,
    private val database: WealthyhoodDatabase
) {

    suspend operator fun invoke(
        accessToken: String,
        idToken: String,
        shouldGetFromCache: Boolean
    ): NetworkResource<List<InvestmentProduct>> {
        if (shouldGetFromCache) {
            CacheHelper.getValidCachedInvestmentProducts(database)?.let {
                return NetworkResource.Success(it)
            }
        }

        val result = repository.getInvestmentProducts(
            accessToken = accessToken,
            idToken = idToken,
            shouldPopulateTicker = true
        )

        when (result) {
            is DomainResult.Success -> {
                val investmentProducts = result.body?.convertToDatabaseInvestmentProducts()

                CacheHelper.cacheInvestmentProducts(
                    database = database,
                    investmentProducts = investmentProducts
                )

                preferencesRepository.putInvestmentProductsUpdatedAt(Date().time)

                return NetworkResource.Success(investmentProducts)
            }

            is DomainResult.Error -> {
                return NetworkResource.Failure(result.throwable?.localizedMessage)
            }

            else -> {
                return NetworkResource.Failure(null)
            }
        }
    }
}
