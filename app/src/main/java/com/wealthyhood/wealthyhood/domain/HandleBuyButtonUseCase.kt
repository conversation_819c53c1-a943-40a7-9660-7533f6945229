package com.wealthyhood.wealthyhood.domain

import com.wealthyhood.wealthyhood.common.parseSuccessResponse
import com.wealthyhood.wealthyhood.extensions.getCashForCurrency
import com.wealthyhood.wealthyhood.model.DomainResult
import com.wealthyhood.wealthyhood.model.NetworkResource
import com.wealthyhood.wealthyhood.repository.AuthRepository
import com.wealthyhood.wealthyhood.repository.PreferencesRepository
import com.wealthyhood.wealthyhood.repository.Repository
import com.wealthyhood.wealthyhood.service.ApiResponse
import com.wealthyhood.wealthyhood.service.Gift
import com.wealthyhood.wealthyhood.service.Portfolio
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.launch

class HandleBuyButtonUseCase(
    private val currencyISOCode: String?,
    private val preferencesRepository: PreferencesRepository,
    private val authRepository: AuthRepository,
    private val repository: Repository,
    private val getPortfoliosUseCase: GetPortfoliosCoroutinesUseCase,
    private val viewModelScope: CoroutineScope,
    private val showLoadingCallback: (() -> Unit)?,
    private val hideLoadingCallback: (() -> Unit)?,
    private val navigateToBuyCallback: ((extras: String?) -> Unit)?,
    private val showTopUpOptionsCallback: (() -> Unit)?
) {

    operator fun invoke(navigateToBuyExtras: String?, shouldCheckForGifts: Boolean) {
        val userCompanyEntity = preferencesRepository.getUserCompanyEntity()

        if (userCompanyEntity != "WEALTHYHOOD_EUROPE") {
            navigateToBuyCallback?.invoke(navigateToBuyExtras)

            return
        }

        val cachedPortfolios = CacheHelper.getValidCachedPortfolios(
            preferencesRepository = preferencesRepository,
            parameters = null
        )

        val shouldInvokeLoader = shouldInvokeLoader(cachedPortfolios)
        if (shouldInvokeLoader) showLoadingCallback?.invoke()

        getCredentialsCall { _, accessToken, idToken ->
            if (accessToken == null || idToken == null) {
                // TODO: Handle the failure

                if (shouldInvokeLoader) hideLoadingCallback?.invoke()

                return@getCredentialsCall
            }

            viewModelScope.launch {
                val shouldShowTopUpOptions = shouldShowTopUpOptionsOnBuy(
                    accessToken = accessToken,
                    idToken = idToken,
                    cachedPortfolios = cachedPortfolios,
                    shouldCheckForGifts = shouldCheckForGifts
                )

                if (shouldInvokeLoader) hideLoadingCallback?.invoke()

                if (!shouldShowTopUpOptions) {
                    navigateToBuyCallback?.invoke(navigateToBuyExtras)

                    return@launch
                }

                showTopUpOptionsCallback?.invoke()
            }
        }
    }

    private suspend fun shouldShowTopUpOptionsOnBuy(
        accessToken: String,
        idToken: String,
        cachedPortfolios: List<Portfolio>?,
        shouldCheckForGifts: Boolean
    ): Boolean {
        val portfolios = if (cachedPortfolios != null) cachedPortfolios else {
            val portfoliosResult = getPortfoliosCall(
                accessToken = accessToken,
                idToken = idToken
            )

            parseSuccessResponse(portfoliosResult)
        }

        val portfolio = portfolios?.find { it.isReal == true }
        if (!shouldGetGifts(portfolio)) return false

        if (!shouldCheckForGifts) {
            return true
        }

        val giftsResult = getGiftsCall(
            accessToken = accessToken,
            idToken = idToken
        )

        val gifts = parseSuccessResponse(giftsResult)

        return gifts?.data.isNullOrEmpty()
    }

    private fun getCredentialsCall(callback: ((succeeded: Boolean, accessToken: String?, idToken: String?) -> Unit)?) {
        authRepository.loadAuth0Credentials {
            when (it) {
                is NetworkResource.Success -> {
                    val accessToken = "Bearer ${it.data?.accessToken}"
                    val idToken = "Bearer ${it.data?.idToken}"

                    callback?.invoke(true, accessToken, idToken)
                }

                is NetworkResource.Failure -> {
                    callback?.invoke(false, null, null)
                }
            }
        }
    }

    private suspend fun getPortfoliosCall(
        accessToken: String,
        idToken: String
    ): NetworkResource<List<Portfolio>> {
        // Έχουμε ήδη πάρει τα portfolios από την cache και σε περίπτωση που δεν υπήρχαν τότε μόνο
        // ερχόμαστε εδώ. Οπότε δε χρειάζεται να ελέγξουμε κανά την cache.

        return getPortfoliosUseCase.invoke(
            accessToken = accessToken,
            idToken = idToken,
            parameters = null,
            shouldGetFromCache = false
        )
    }

    private suspend fun getGiftsCall(
        accessToken: String,
        idToken: String
    ): DomainResult<ApiResponse<List<Gift>>>? {
        return repository.getGifts(
            accessToken = accessToken,
            idToken = idToken,
            used = false,
            hasViewedAppModal = null
        )
    }

    private fun shouldInvokeLoader(cachedPortfolios: List<Portfolio>?): Boolean {
        // Το loader θέλουμε να τον εμφανίσουμε μόνο εάν κάνουμε API call
        // (είτε για τα portoflios είτε για τα gifts αργότερα).

        // FIXME: Also show the loader if there is a need to refresh the tokens.

        if (cachedPortfolios == null) return true

        val portfolio = cachedPortfolios.find { it.isReal == true }
        return shouldGetGifts(portfolio)
    }

    private fun shouldGetGifts(portfolio: Portfolio?): Boolean {
        val cash = portfolio?.getCashForCurrency(currencyISOCode) ?: 0.0
        return (cash <= 0.0)
    }
}
