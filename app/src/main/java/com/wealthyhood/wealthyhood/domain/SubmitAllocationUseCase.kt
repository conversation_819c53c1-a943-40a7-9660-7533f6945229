package com.wealthyhood.wealthyhood.domain

import com.wealthyhood.wealthyhood.extensions.saveEssentialPortfolioInfo
import com.wealthyhood.wealthyhood.model.NetworkResource
import com.wealthyhood.wealthyhood.repository.MyAccountRepository
import com.wealthyhood.wealthyhood.repository.PreferencesRepository
import com.wealthyhood.wealthyhood.service.Portfolio
import com.wealthyhood.wealthyhood.service.SubmitAllocationBodyParams
import java.util.Date

@Deprecated("Create and use SubmitAllocationUseCaseUseCase instead")
class SubmitAllocationUseCase(
    private val preferencesRepository: PreferencesRepository,
    private val myAccountRepository: MyAccountRepository
) {

    operator fun invoke(
        accessToken: String,
        idToken: String,
        portfolioID: String?,
        allocation: SubmitAllocationBodyParams,
        callback: (networkResource: NetworkResource<Portfolio>) -> Unit
    ) {
        myAccountRepository.submitAllocation(
            accessToken = accessToken,
            idToken = idToken,
            portfolioID = portfolioID,
            allocation = allocation
        ) {
            when (it) {
                is NetworkResource.Success -> {
                    val previousHasTargetAllocationSetup =
                        preferencesRepository.getPortfolioHasTargetAllocation()

                    it.data?.saveEssentialPortfolioInfo(preferencesRepository)

                    if (!previousHasTargetAllocationSetup) {
                        // Στην περίπτωση του CoroutinesUseCase δε χρειάζεται να γίνει αυτό γιατί
                        // εκείνο καλείται μόνο από το lab που κάνει μόνο update το allocation.
                        // Αυτός είναι και ο λόγος που δεν έφτιαξα CoroutinesUseCase.

                        preferencesRepository.putTargetAllocationCreatedAt(Date().time)
                    }

                    callback(NetworkResource.Success(it.data))
                }

                is NetworkResource.Failure -> {
                    callback(NetworkResource.Failure(it.message))
                }
            }
        }
    }
}
