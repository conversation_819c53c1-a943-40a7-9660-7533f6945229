package com.wealthyhood.wealthyhood.database

import androidx.room.ColumnInfo
import androidx.room.Entity
import androidx.room.PrimaryKey

@Entity(tableName = "asset_table")
data class Asset(
    @PrimaryKey
    val id: String,

    @ColumnInfo(name = "aggregated_submission")
    val aggregatedSubmission: Boolean?,

    @ColumnInfo("category")
    val category: String?,

    @ColumnInfo("kid_uri")
    val kidURI: String?,

    @ColumnInfo("simple_name")
    val title: String?,

    @ColumnInfo("advanced_name")
    val subtitle: String?,

    @ColumnInfo("short_description")
    val shortDescription: String?,

    @ColumnInfo("about")
    val about: String?,

    @ColumnInfo("icon")
    val icon: String?,

    @ColumnInfo("asset_class")
    val assetClassID: String?,

    @ColumnInfo("sector")
    val sectorID: String?,

    @ColumnInfo("bond_category")
    val bondCategory: String?,

    @ColumnInfo("isin")
    val isin: String?,

    @ColumnInfo("income")
    val income: String?,

    @ColumnInfo("base_currency")
    val baseCurrency: String?,

    @ColumnInfo("provider")
    val provider: String?,

    @ColumnInfo("stats_url_mapping")
    val statsURLMapping: String?,

    @ColumnInfo("formal_ticker")
    val formalTicker: String?,

    @ColumnInfo("ticker_with_currency")
    val tickerWithCurrency: String?,

    @ColumnInfo("formal_exchange")
    val formalExchange: String?,

    @ColumnInfo("sorting")
    val order: Double?,

    @ColumnInfo("similar_companies")
    val similarCompanies: List<String>?,

    @ColumnInfo("search_terms")
    val searchTerms: List<String>?,

    @ColumnInfo("typos")
    val typos: List<String>?,

    @ColumnInfo("deprecated")
    val deprecated: Boolean?,

    // Το id του αντίστοιχου non-deprecated asset
    @ColumnInfo("deprecated_by")
    val deprecatedBy: String?,

    @ColumnInfo("provider_logo")
    val providerLogo: String? = null,

    @ColumnInfo("asset_class_breakdown")
    val assetClassBreakdown: String? = null
)
