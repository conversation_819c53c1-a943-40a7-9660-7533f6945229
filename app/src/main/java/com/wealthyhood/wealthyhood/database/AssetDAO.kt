package com.wealthyhood.wealthyhood.database

import androidx.room.Dao
import androidx.room.Insert
import androidx.room.OnConflictStrategy
import androidx.room.Query

@Dao
interface AssetDAO {

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertAll(assets: List<Asset>)

    @Query("DELETE FROM asset_table")
    suspend fun deleteAll()

    @Query("SELECT * FROM asset_table")
    suspend fun getAll(): List<Asset>

    @Query("SELECT * FROM asset_table WHERE deprecated is NULL OR deprecated = 0")
    suspend fun getNonDeprecated(): List<Asset>

    @Query("SELECT * FROM asset_table WHERE id = :assetID")
    suspend fun getAsset(assetID: String): Asset?

    @Query("SELECT * FROM asset_table WHERE isin = :isinID")
    suspend fun getAssetWithISIN(isinID: String): Asset?

    @Query("SELECT * FROM asset_table WHERE id in (:ids)")
    suspend fun getAssetsByIDs(ids: List<String>): List<Asset>
}
