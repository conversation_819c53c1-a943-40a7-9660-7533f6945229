package com.wealthyhood.wealthyhood.database

import androidx.room.migration.Migration
import androidx.sqlite.db.SupportSQLiteDatabase

val MIGRATION_1_2 = object : Migration(1, 2) {

    override fun migrate(db: SupportSQLiteDatabase) {
        db.execSQL("ALTER TABLE asset_table ADD COLUMN provider_logo TEXT")
    }
}

val MIGRATION_2_3 = object : Migration(2, 3) {
    override fun migrate(db: SupportSQLiteDatabase) {
        db.execSQL(
            """
            CREATE TABLE IF NOT EXISTS `investment_product_table` (
                `id` TEXT NOT NULL,
                `isin` TEXT,
                `common_id` TEXT,
                `traded_price` REAL,
                `traded_currency` TEXT,
                `current_ticker_currency` TEXT,
                `current_ticker_price` REAL,
                `current_ticker_monthly_return_percentage` REAL,
                PRIMARY KEY(`id`)
            )
            """.trimIndent()
        )
    }
}

val MIGRATION_3_4 = object : Migration(3, 4) {
    override fun migrate(db: SupportSQLiteDatabase) {
        db.execSQL("ALTER TABLE asset_table ADD COLUMN asset_class_breakdown TEXT")
    }
}

val MIGRATION_4_5 = object : Migration(4, 5) {
    override fun migrate(db: SupportSQLiteDatabase) {
        db.execSQL("ALTER TABLE asset_table ADD COLUMN kid_uri TEXT")
    }
}
