package com.wealthyhood.wealthyhood.database

import android.content.Context
import androidx.room.Database
import androidx.room.Room
import androidx.room.RoomDatabase
import androidx.room.TypeConverters

@Database(entities = [Asset::class, InvestmentProduct::class], version = 5, exportSchema = false)
@TypeConverters(Converters::class)
abstract class WealthyhoodDatabase : RoomDatabase() {

    abstract val assetDAO: AssetDAO
    abstract val investmentProductDAO: InvestmentProductDAO

    companion object {
        @Volatile
        private var INSTANCE: WealthyhoodDatabase? = null

        fun getInstance(context: Context): WealthyhoodDatabase {
            synchronized(this) {
                var instance = INSTANCE

                if (instance == null) {
                    instance = Room.databaseBuilder(
                        context.applicationContext,
                        WealthyhoodDatabase::class.java,
                        "wealthyhood_database"
                    )
                        .fallbackToDestructiveMigration()
                        .addMigrations(MIGRATION_1_2)
                        .addMigrations(MIGRATION_2_3)
                        .addMigrations(MIGRATION_3_4)
                        .addMigrations(MIGRATION_4_5)
                        .build()

                    INSTANCE = instance
                }

                return instance
            }
        }
    }
}
