package com.wealthyhood.wealthyhood.database

import androidx.room.Dao
import androidx.room.Insert
import androidx.room.OnConflictStrategy
import androidx.room.Query
import androidx.room.Update

@Dao
interface InvestmentProductDAO {

    @Query("SELECT * FROM investment_product_table")
    suspend fun getAllInvestmentProducts(): List<InvestmentProduct>

    @Query("SELECT * FROM investment_product_table WHERE common_id in (:ids)")
    suspend fun getInvestmentProducts(ids: List<String>): List<InvestmentProduct>

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertInvestmentProducts(products: List<InvestmentProduct>)

    @Update
    suspend fun updateInvestmentProducts(products: List<InvestmentProduct>): Int
}
