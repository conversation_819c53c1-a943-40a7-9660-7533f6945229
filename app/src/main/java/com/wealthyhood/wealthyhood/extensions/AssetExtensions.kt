package com.wealthyhood.wealthyhood.extensions

import android.content.Context
import androidx.annotation.DrawableRes
import com.google.gson.Gson
import com.google.gson.JsonObject
import com.google.gson.reflect.TypeToken
import com.wealthyhood.wealthyhood.database.Asset
import com.wealthyhood.wealthyhood.domain.AssetClassBreakdownItem
import com.wealthyhood.wealthyhood.domain.AssetClassBreakdownItem.Companion.generateFormattedAllocation
import com.wealthyhood.wealthyhood.domain.AssetClassBreakdownItem.Companion.generateTitle
import com.wealthyhood.wealthyhood.service.AssetClass
import com.wealthyhood.wealthyhood.service.BondCategory
import com.wealthyhood.wealthyhood.service.ETFProvider
import com.wealthyhood.wealthyhood.service.Portfolio
import com.wealthyhood.wealthyhood.service.Sector
import java.util.Locale

fun JsonObject.generateAssetsFromKeys(): List<Asset>? {
    return keySet()?.mapNotNull { assetID ->
        val assetJsonObject = try {
            get(assetID)?.asJsonObject
        } catch (e: IllegalStateException) {
            e.printStackTrace()
            null
        } ?: return@mapNotNull null

        assetJsonObject.convertToAsset(assetID)
    }
}

fun JsonObject.convertToAsset(assetID: String): Asset? {
    val gson = Gson()

    return try {
        val aggregatedSubmission = get("aggregatedSubmission")?.asBoolean
        val category = get("category")?.asString
        val kidURI = get("kid")?.asString
        val title = get("simpleName")?.asString
        val subtitle = get("advancedName")?.asString
        val shortDescription = get("shortDescription")?.asString
        val about = get("about")?.asString
        val icon = get("icon")?.asString
        val assetClassID = get("assetClass")?.asString
        val sectorID = get("sector")?.asString
        val bondCategory = get("bondCategory")?.asString
        val isin = get("isin")?.asString
        val income = get("income")?.asString
        val baseCurrency = get("baseCurrency")?.asString
        val provider = get("provider")?.asString
        val statsURLMapping = get("statsUrlMapping")?.asString
        val formalTicker = get("formalTicker")?.asString
        val tickerWithCurrency = get("tickerWithCurrency")?.asString
        val formalExchange = get("formalExchange")?.asString
        val order = get("sorting")?.asDouble
        val similarCompanies = get("similarCompanies")?.asJsonArray?.map { it.asString }
        val searchTerms = get("searchTerms")?.asJsonArray?.map { it.asString }
        val typos = get("typos")?.asJsonArray?.map { it.asString }
        val deprecated = get("deprecated")?.asBoolean
        val deprecatedBy = get("deprecatedBy")?.asString
        val providerLogo = get("providerLogo")?.asString

        val assetClassBreakdown = get("assetClassBreakdown")?.asJsonObject

        val assetClassBreakdownJSONString = assetClassBreakdown?.let {
            gson.toJson(it)
        }

        Asset(
            id = assetID,
            aggregatedSubmission = aggregatedSubmission,
            category = category,
            kidURI = kidURI,
            title = title,
            subtitle = subtitle,
            shortDescription = shortDescription,
            about = about,
            icon = icon,
            assetClassID = assetClassID,
            sectorID = sectorID,
            bondCategory = bondCategory,
            isin = isin,
            income = income,
            baseCurrency = baseCurrency,
            provider = provider,
            statsURLMapping = statsURLMapping,
            formalTicker = formalTicker,
            tickerWithCurrency = tickerWithCurrency,
            formalExchange = formalExchange,
            order = order,
            similarCompanies = similarCompanies,
            searchTerms = searchTerms,
            typos = typos,
            deprecated = deprecated,
            deprecatedBy = deprecatedBy,
            providerLogo = providerLogo,
            assetClassBreakdown = assetClassBreakdownJSONString
        )
    } catch (e: Exception) {
        null
    }
}

@DrawableRes
fun Asset.getIconDrawableRes(context: Context): Int? {
    return getFileName()?.let {
        context.getDrawableRes(it)
    }
}

fun Asset.generateProviderLogoURI(etfProviders: List<ETFProvider>?): String? {
    val provider = etfProviders?.find { it.id == provider }
    return provider?.icon?.generateProviderLogoURI()
}

private fun Asset.getFileName(): String? {
    if (isStock()) {
        // Εάν το stock είναι deprecated θα πρέπει να πάρουμε το id του αντίστοιχου
        // non-deprecated (που είναι το πεδίο deprecatedBy)

        return this.deprecatedBy ?: this.id
    }

    return null
}

fun Asset.isStock(): Boolean {
    return (category == "stock")
}

fun Asset.hasSector(sectors: List<Sector>?): Boolean {
    val foundSector = sectors?.find { it.id == sectorID }

    return (sectorID.isNullOrBlank() || foundSector == null).not()
}

fun Asset.hasBondCategory(bondCategories: List<BondCategory>?): Boolean {
    val foundBondCategory = bondCategories?.find { it.id == bondCategory }

    return (bondCategory.isNullOrBlank() || foundBondCategory == null).not()
}

fun Asset.hasParentGroup(sectors: List<Sector>?, bondCategories: List<BondCategory>?): Boolean {
    return hasSector(sectors) || hasBondCategory(bondCategories)
}

fun Asset.findHoldingForAsset(portfolio: Portfolio?): Portfolio.Holding? {
    return portfolio?.holdings?.find { it.asset?.commonID == id }
}

fun Asset.generateAssetClassBreakdownItems(assetClasses: List<AssetClass>?): List<AssetClassBreakdownItem>? {
    val jsonString = assetClassBreakdown ?: return null

    val itemType = object : TypeToken<Map<String, Double>>() {}.type

    val map: Map<String, Double> = try {
        Gson().fromJson(jsonString, itemType)
    } catch (e: Exception) {
        return null
    }

    val finalAnswer = mutableListOf<AssetClassBreakdownItem>()

    map.forEach { (key, value) ->
        val assetClass = assetClasses?.find { assetClass ->
            assetClass.id == key
        } ?: return@forEach

        val breakDownItem = AssetClassBreakdownItem(
            assetClass = assetClass,
            allocation = value
        )

        finalAnswer.add(breakDownItem)
    }

    return if (finalAnswer.isEmpty()) null else finalAnswer
}

fun Asset.generateAssetClassBreakdownText(
    context: Context,
    locale: Locale,
    assetClasses: List<AssetClass>?
): String? {
    val assetClassBreakdownItems = generateAssetClassBreakdownItems(assetClasses)

    val parts = mutableListOf<String>()

    assetClassBreakdownItems?.forEach { item ->
        val classTitle = item.generateTitle(context)
        val classAllocation = item.generateFormattedAllocation(locale)

        parts.add("$classAllocation $classTitle")
    }

    return if (parts.isEmpty()) null else parts.joinToString(" - ")
}
