package com.wealthyhood.wealthyhood.extensions

import android.content.Context
import android.text.SpannableString
import androidx.annotation.ColorInt
import androidx.annotation.ColorRes
import androidx.annotation.DrawableRes
import androidx.core.content.ContextCompat
import com.wealthyhood.wealthyhood.R
import com.wealthyhood.wealthyhood.database.Asset
import com.wealthyhood.wealthyhood.model.ConfirmationReceiptScreenArgs
import com.wealthyhood.wealthyhood.model.ConfirmationReceiptTypeEnum
import com.wealthyhood.wealthyhood.service.ETFProvider
import com.wealthyhood.wealthyhood.service.Reward
import com.wealthyhood.wealthyhood.ui.myaccount.transactions.TransactionsHelper
import java.util.Locale

@ColorRes
fun Reward.generateStatusTextColorRes(): Int? {
    val statusEnum = status?.rawValue?.convertToStatusEnum()
    return statusEnum?.generateStatusTextColorRes()
}

fun Reward.generateFormattedUpdatedAt(stringPattern: String): String? {
    return updatedAt?.generateFormattedDate(
        dateStringPattern = "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'",
        formattedStringPattern = stringPattern
    )
}

fun Reward.generateFormattedAmount(userLocale: Locale): String? {
    val amount = this.displayAmount ?: return null
    val realAmount = amount / 100f

    return realAmount.generateFormattedCurrency(
        currencyISOCode = consideration?.currency,
        locale = userLocale
    )
}

@DrawableRes
fun Reward.generateStatusIconRes(): Int? {
    return when (status) {
        TransactionsHelper.TransactionStatus.Settled -> R.drawable.ic_reward
        TransactionsHelper.TransactionStatus.Pending -> R.drawable.ic_update
        TransactionsHelper.TransactionStatus.PendingDeposit -> R.drawable.ic_update
        TransactionsHelper.TransactionStatus.PendingGift -> R.drawable.ic_update
        TransactionsHelper.TransactionStatus.Cancelled -> R.drawable.ic_cancel_filled
        TransactionsHelper.TransactionStatus.Rejected -> R.drawable.ic_cancel_filled
        null -> null
    }
}

@Deprecated("Use the new generateStatusIconColorV2")
@ColorInt
fun Reward.generateStatusIconColor(context: Context): Int? {
    val colorRes = when (status) {
        TransactionsHelper.TransactionStatus.Settled -> R.color.wealthyhood_burple
        TransactionsHelper.TransactionStatus.Pending -> R.color.system_alerts_pending_color
        TransactionsHelper.TransactionStatus.PendingDeposit -> R.color.system_alerts_pending_color
        TransactionsHelper.TransactionStatus.PendingGift -> R.color.system_alerts_pending_color
        TransactionsHelper.TransactionStatus.Cancelled -> R.color.system_alerts_danger_color // TODO: Is this correct?
        TransactionsHelper.TransactionStatus.Rejected -> R.color.system_alerts_danger_color // TODO: Is this correct?
        null -> null
    }

    return colorRes?.let {
        ContextCompat.getColor(context, it)
    }
}

@ColorRes
fun Reward.generateStatusIconColorRes(): Int {
    return when (status) {
        TransactionsHelper.TransactionStatus.Settled -> R.color.green_30

        TransactionsHelper.TransactionStatus.Pending,
        TransactionsHelper.TransactionStatus.PendingDeposit,
        TransactionsHelper.TransactionStatus.PendingGift -> R.color.yellow_30

        TransactionsHelper.TransactionStatus.Cancelled,
        TransactionsHelper.TransactionStatus.Rejected -> R.color.red_30

        null -> R.color.grey_50
    }
}

fun Reward.generateStatusText(context: Context): String? {
    val resources = context.resources

    return when (status) {
        TransactionsHelper.TransactionStatus.Settled -> resources.getString(R.string.completed)
        TransactionsHelper.TransactionStatus.Pending -> resources.getString(R.string.pending)
        TransactionsHelper.TransactionStatus.Cancelled -> resources.getString(R.string.cancelled)
        TransactionsHelper.TransactionStatus.Rejected -> resources.getString(R.string.rejected)
        else -> null
    }
}

fun Reward.generateTitle(allAssets: List<Asset>?): String? {
    return allAssets?.find { it.isin == isin }?.title
}

fun Reward.generateIconDrawableRes(
    context: Context,
    assets: List<Asset>?
): Int? {
    val asset = assets?.find { it.isin == isin } ?: return null

    if (asset.isStock()) {
        return asset.getIconDrawableRes(context)
    }

    return null
}

fun Reward.generateIconURI(assets: List<Asset>?, etfProviders: List<ETFProvider>?): String? {
    val asset = assets?.find { it.isin == isin } ?: return null

    if (asset.isStock()) return null

    return asset.generateProviderLogoURI(etfProviders)
}

fun Reward.generateFormattedUnitPrice(userLocale: Locale): String? {
    val amount = unitPrice?.amount ?: return null
    val currency = unitPrice.currency ?: return null

    return amount.generateFormattedCurrency(
        currencyISOCode = currency,
        locale = userLocale
    )
}

fun Reward.generateRewardReceiptDialogArguments(
    context: Context,
    isUserEU: Boolean,
    userFullName: String?,
    currentAsset: Asset?,
    userLocale: Locale,
    etfProviders: List<ETFProvider>?
): ConfirmationReceiptScreenArgs {
    val title = context.resources.getString(
        R.string.confirmation_receipt_title,
        currentAsset?.tickerWithCurrency,
        currentAsset?.title
    )

    val firmRes = if (!isUserEU) {
        R.string.receipt_firm
    } else R.string.receipt_firm_eu

    val firm = context.resources.getString(firmRes)

    return ConfirmationReceiptScreenArgs(
        receiptType = ConfirmationReceiptTypeEnum.REWARD,
        orderID = id,
        displayOrderID = displayUserFriendlyID,
        title = title,
        iconDrawableRes = currentAsset?.getIconDrawableRes(context),
        iconURI = currentAsset?.generateProviderLogoURI(etfProviders),
        shouldShowFirstInfoColumnOnly = false,
        amount = generateFormattedAmount(userLocale = userLocale),
        date = generateFormattedLocalisedTime("dd MMM yy"),
        time = generateFormattedLocalisedTime("hh:mm:ss"),
        shares = quantity?.toString(),
        perShare = generateFormattedUnitPrice(userLocale = userLocale),
        commission = null,
        fxRate = null,
        sideDescription = context.resources.getString(R.string.reward),
        isin = currentAsset?.isin,
        firm = firm,
        color = generateStatusIconColor(context), //FIXME: Fix this
        userFullName = userFullName
    )
}

fun Reward.generateFormattedLocalisedTime(stringPattern: String): String? {
    val utcDateString = displayDate ?: return null

    return utcDateString.convertUTCToLocal(
        utcPattern = "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'",
        localPattern = stringPattern
    )
}

fun List<Reward>.generateClaimRewardsMessage(context: Context): SpannableString {
    val pendingRewardsCount = size

    val resources = context.resources

    val title = if (pendingRewardsCount == 1) {
        resources.getString(R.string.claim_reward_message_single)
    } else {
        resources.getString(
            R.string.claim_reward_message_plural,
            size.toString()
        )
    }

    val coloredSubstrings = title.findBoldSubstrings()
    val finalTitle = title.cleanSpecialFormattingCharacters()

    return finalTitle.formatSubstringsWithColor(
        context = context,
        substrings = coloredSubstrings,
        colorRes = R.color.wealthyhood_burple
    )
}

fun List<Reward>.generateClaimRewardButtonTitle(context: Context): String {
    val pendingRewardsCount = size

    val resources = context.resources

    return if (pendingRewardsCount == 1) {
        resources.getString(R.string.claim_reward_primary_button_title_single)
    } else {
        resources.getString(R.string.claim_reward_primary_button_title_plural)
    }
}
