package com.wealthyhood.wealthyhood.extensions

import com.wealthyhood.wealthyhood.service.ExchangeRate
import java.util.Locale

fun ExchangeRate.generateFXRate(
    currencyISOCode: String?,
    userLocale: Locale,
    rateSeparator: String
): String? {
    val parts = mutableListOf<String>()

    val formattedBaseRate = 1.0.generateFormattedCurrency(
        currencyISOCode = currencyISOCode,
        maximumFractionDigits = 0,
        locale = userLocale
    ) ?: return null

    val formattedRate = rate?.generateFormattedCurrency(
        currencyISOCode = currency,
        maximumFractionDigits = 3,
        shouldRemoveTrailingZeros = false,
        locale = userLocale
    ) ?: return null

    parts.add(formattedBaseRate)
    parts.add(formattedRate)

    return parts.joinToString(rateSeparator)
}
