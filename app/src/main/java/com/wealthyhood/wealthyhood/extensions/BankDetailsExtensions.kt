package com.wealthyhood.wealthyhood.extensions

import android.content.Context
import com.wealthyhood.wealthyhood.R
import com.wealthyhood.wealthyhood.service.GetInvestmentUniverseResponse.BankDetails
import com.wealthyhood.wealthyhood.ui.regularbanktransfer.RegularBankTransferListAdapter.DataItem

private const val ACCOUNT_NAME_ITEM_ID = "accountNameInfoRowItem"
private const val IBAN_ITEM_ID = "ibanInfoRowItem"
private const val BIC_SWIFT_ITEM_ID = "bicSwiftInfoRowItem"
private const val BANK_NAME_ITEM_ID = "bankNameInfoRowItem"
private const val BANK_ADDRESS_ITEM_ID = "bankAddressInfoRowItem"
private const val BENEFICIARY_BANK_ADDRESS_ITEM_ID = "beneficiaryBankAddressInfoRowItem"

fun BankDetails.generateInfoRowDataItems(
    context: Context,
    userIBAN: String?,
    shouldGenerateAllInfoRows: Boolean
): List<DataItem.InfoRowItem> {
    val finalAnswer = mutableListOf<DataItem.InfoRowItem>()

    val spacing16 = context.resources.getDimensionPixelSize(R.dimen.spacing_16)

    val accountNameLabelText = context.getString(R.string.account_name_label)
    val ibanLabelText = context.getString(R.string.iban_label)
    val bicSwiftLabelText = context.getString(R.string.bic_swift_label)
    val bankNameLabelText = context.getString(R.string.bank_name_label)
    val bankAddressLabelText = context.getString(R.string.bank_address_label)
    val beneficiaryBankAddressLabelText = context.getString(R.string.beneficiary_bank_address_label)

    val accountNameItem = DataItem.InfoRowItem(
        id = ACCOUNT_NAME_ITEM_ID,
        paddingStart = spacing16,
        paddingTop = spacing16,
        paddingEnd = spacing16,
        paddingBottom = null,
        titleText = accountNameLabelText,
        subtitleText = accountName,
        shouldShowSeparator = true
    )

    val ibanItem = DataItem.InfoRowItem(
        id = IBAN_ITEM_ID,
        paddingStart = spacing16,
        paddingTop = spacing16,
        paddingEnd = spacing16,
        paddingBottom = null,
        titleText = ibanLabelText,
        subtitleText = userIBAN,
        shouldShowSeparator = true
    )

    val bicSwiftItem = DataItem.InfoRowItem(
        id = BIC_SWIFT_ITEM_ID,
        paddingStart = spacing16,
        paddingTop = spacing16,
        paddingEnd = spacing16,
        paddingBottom = null,
        titleText = bicSwiftLabelText,
        subtitleText = bank?.bic,
        shouldShowSeparator = true
    )

    val bankNameItem = DataItem.InfoRowItem(
        id = BANK_NAME_ITEM_ID,
        paddingStart = spacing16,
        paddingTop = spacing16,
        paddingEnd = spacing16,
        paddingBottom = null,
        titleText = bankNameLabelText,
        subtitleText = bank?.name,
        shouldShowSeparator = true
    )

    val bankAddressItem = DataItem.InfoRowItem(
        id = BANK_ADDRESS_ITEM_ID,
        paddingStart = spacing16,
        paddingTop = spacing16,
        paddingEnd = spacing16,
        paddingBottom = null,
        titleText = bankAddressLabelText,
        subtitleText = bank?.fullAddress,
        shouldShowSeparator = true
    )

    val beneficiaryBankAddressItem = DataItem.InfoRowItem(
        id = BENEFICIARY_BANK_ADDRESS_ITEM_ID,
        paddingStart = spacing16,
        paddingTop = spacing16,
        paddingEnd = spacing16,
        paddingBottom = null,
        titleText = beneficiaryBankAddressLabelText,
        subtitleText = beneficiaryFullAddress,
        shouldShowSeparator = true
    )

    finalAnswer.add(accountNameItem)

    if (shouldGenerateAllInfoRows) {
        finalAnswer.add(beneficiaryBankAddressItem)
    }

    finalAnswer.add(ibanItem)
    finalAnswer.add(bicSwiftItem)
    finalAnswer.add(bankNameItem)
    finalAnswer.add(bankAddressItem)

    return finalAnswer
}

fun BankDetails.copyToClipboard(context: Context, itemID: String?, userIBAN: String?) {
    val label: String?
    val text: String?

    when (itemID) {
        ACCOUNT_NAME_ITEM_ID -> {
            label = context.getString(R.string.account_name_label)
            text = accountName
        }

        IBAN_ITEM_ID -> {
            label = context.getString(R.string.iban_label)
            text = userIBAN
        }

        BIC_SWIFT_ITEM_ID -> {
            label = context.getString(R.string.bic_swift_label)
            text = bank?.bic
        }

        BANK_NAME_ITEM_ID -> {
            label = context.getString(R.string.bank_name_label)
            text = bank?.name
        }

        BANK_ADDRESS_ITEM_ID -> {
            label = context.getString(R.string.bank_address_label)
            text = bank?.fullAddress
        }

        BENEFICIARY_BANK_ADDRESS_ITEM_ID -> {
            label = context.getString(R.string.beneficiary_bank_address_label)
            text = beneficiaryFullAddress
        }

        else -> {
            label = null
            text = null
        }
    }

    if (label == null || text == null) return

    text.copyTextToClipboard(
        context = context,
        label = label
    )
}
