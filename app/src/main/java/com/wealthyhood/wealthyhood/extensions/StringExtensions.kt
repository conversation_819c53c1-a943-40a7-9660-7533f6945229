package com.wealthyhood.wealthyhood.extensions

import android.content.ClipData
import android.content.ClipboardManager
import android.content.Context
import android.graphics.Color
import android.text.SpannableString
import android.text.TextUtils
import android.text.style.ClickableSpan
import androidx.annotation.ColorRes
import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import com.wealthyhood.wealthyhood.domain.Choice
import com.wealthyhood.wealthyhood.domain.VirtualTreeItem
import com.wealthyhood.wealthyhood.model.AboutAssetScreenArgs
import com.wealthyhood.wealthyhood.model.AccountDetailsScreenArgs
import com.wealthyhood.wealthyhood.model.ActionsSheetScreenArgs
import com.wealthyhood.wealthyhood.model.AddMoneyScreenArgs
import com.wealthyhood.wealthyhood.model.AllocationOptionsScreenArgs
import com.wealthyhood.wealthyhood.model.AppRatingSheetScreenArgs
import com.wealthyhood.wealthyhood.model.AssetClassesScreenArgs
import com.wealthyhood.wealthyhood.model.AssetNewsArticlesScreenArgs
import com.wealthyhood.wealthyhood.model.BankAccountDetailsScreenArgs
import com.wealthyhood.wealthyhood.model.BankTransferScreenArgs
import com.wealthyhood.wealthyhood.model.BanksSheetScreenArgs
import com.wealthyhood.wealthyhood.model.BannerDescriptionScreenArguments
import com.wealthyhood.wealthyhood.model.ConfirmationReceiptScreenArgs
import com.wealthyhood.wealthyhood.model.ControlCenterRepeatingInvestmentScreenArgs
import com.wealthyhood.wealthyhood.model.DayPickerScreenArgs
import com.wealthyhood.wealthyhood.model.DepositScreenArgs
import com.wealthyhood.wealthyhood.model.DescriptionScreenArgs
import com.wealthyhood.wealthyhood.model.DirectDebitScheduleScreenArgs
import com.wealthyhood.wealthyhood.model.FilteredTransactionsScreenArguments
import com.wealthyhood.wealthyhood.model.GenericErrorScreenArgs
import com.wealthyhood.wealthyhood.model.GenericMessageSheetScreenArgs
import com.wealthyhood.wealthyhood.model.GeographyScreenArgs
import com.wealthyhood.wealthyhood.model.GuideScreenArgs
import com.wealthyhood.wealthyhood.model.HelpCenterFAQScreenArgs
import com.wealthyhood.wealthyhood.model.InfoRow
import com.wealthyhood.wealthyhood.model.KYCIncompleteScreenArgs
import com.wealthyhood.wealthyhood.model.LinkBankAccountLoadingScreenArgs
import com.wealthyhood.wealthyhood.model.OrderReviewFooterProperties
import com.wealthyhood.wealthyhood.model.OrderReviewScreenArgs
import com.wealthyhood.wealthyhood.model.OrdersScreenArgs
import com.wealthyhood.wealthyhood.model.PaymentMethod
import com.wealthyhood.wealthyhood.model.PlanActivationCompletedScreenArgs
import com.wealthyhood.wealthyhood.model.PortfolioBuilderScreenArgs
import com.wealthyhood.wealthyhood.model.PortfolioBuilderStartPickTemplateScreenArgs
import com.wealthyhood.wealthyhood.model.PortfolioCreationChoicesScreenArgs
import com.wealthyhood.wealthyhood.model.PortfolioCreationChoicesSheetScreenArgs
import com.wealthyhood.wealthyhood.model.PortfolioScratchpadScreenArgs
import com.wealthyhood.wealthyhood.model.PortfolioTemplateReadyScreenArgs
import com.wealthyhood.wealthyhood.model.PortfolioTemplateWaitingScreenArgs
import com.wealthyhood.wealthyhood.model.PortfolioWeightingScreenArgs
import com.wealthyhood.wealthyhood.model.ReferOptionsScreenArgs
import com.wealthyhood.wealthyhood.model.ReferScreenArgs
import com.wealthyhood.wealthyhood.model.RegularBankTransferScreenArgs
import com.wealthyhood.wealthyhood.model.RevealRewardDialogScreenArgs
import com.wealthyhood.wealthyhood.model.RewardDialogScreenArgs
import com.wealthyhood.wealthyhood.model.RoboAdvisorScreenArgs
import com.wealthyhood.wealthyhood.model.SavingsProductDetailsScreenArgs
import com.wealthyhood.wealthyhood.model.ScheduleOptionsScreenArgs
import com.wealthyhood.wealthyhood.model.ScheduleSheetScreenArgs
import com.wealthyhood.wealthyhood.model.SectorsScreenArgs
import com.wealthyhood.wealthyhood.model.SetupDirectDebitScreenArgs
import com.wealthyhood.wealthyhood.model.SundownDigestScreenArgs
import com.wealthyhood.wealthyhood.model.TopPerformersScreenArgs
import com.wealthyhood.wealthyhood.model.TopUpOptionsScreenArgs
import com.wealthyhood.wealthyhood.model.TransactionsScreenState
import com.wealthyhood.wealthyhood.model.UpdateRecurringMessageScreenArgs
import com.wealthyhood.wealthyhood.model.VerificationInfo
import com.wealthyhood.wealthyhood.model.WealthyHubArticleScreenArgs
import com.wealthyhood.wealthyhood.model.WealthyHubStory
import com.wealthyhood.wealthyhood.model.WithdrawMoneyScreenArgs
import com.wealthyhood.wealthyhood.service.Allocation
import com.wealthyhood.wealthyhood.service.Automation
import com.wealthyhood.wealthyhood.service.BankAccount
import com.wealthyhood.wealthyhood.service.DashboardChartTenorElement
import com.wealthyhood.wealthyhood.service.Mandate
import com.wealthyhood.wealthyhood.service.Order
import com.wealthyhood.wealthyhood.service.Portfolio
import com.wealthyhood.wealthyhood.service.Reward
import com.wealthyhood.wealthyhood.service.SocketPricing
import com.wealthyhood.wealthyhood.service.Transaction
import com.wealthyhood.wealthyhood.service.TransactionPreview
import com.wealthyhood.wealthyhood.service.TrueLayerProvider
import com.wealthyhood.wealthyhood.service.User
import com.wealthyhood.wealthyhood.service.Wallet
import io.sentry.Sentry
import io.sentry.SentryLevel
import java.text.SimpleDateFormat
import java.util.Currency
import java.util.Date
import java.util.Locale
import java.util.TimeZone

fun String.generateProviderLogoURI(): String {
    return "https://landing-page-assets.wealthyhood.cloud/provider-logos/$this"
}

fun String.copyTextToClipboard(context: Context, label: String) {
    (context.getSystemService(Context.CLIPBOARD_SERVICE) as? ClipboardManager)?.let { clipboard ->
        val clip = ClipData.newPlainText(label, this)
        clipboard.setPrimaryClip(clip)
    }
}

fun String.convertCurrencyCodeToSymbol(): String? {
    return try {
        val currency = Currency.getInstance(this)
        currency.symbol
    } catch (e: Exception) {
        Sentry.captureMessage(
            "String.convertCurrencyCodeToSymbol: invalid currency code: $this",
            SentryLevel.ERROR
        )

        null
    }
}

fun String.generateKIDLink(): String {
    val prefix = "https://kids.wealthyhood.dev/"
    val suffix = "_kid.pdf"

    return "$prefix$this$suffix"
}

fun List<String>.searchInList(searchText: String?): Boolean {
    if (searchText == null) return false

    forEach { item ->
        if (item.contains(other = searchText, ignoreCase = true)) {
            return true
        }
    }

    return false
}

fun String?.isNull(): Boolean = this == null

fun String.extractCapitalizedInitialLetters(): String? {
    val letters = mutableListOf<String>()

    val parts = split(" ")

    parts.forEach { part ->
        part.firstOrNull()?.uppercase()?.let {
            letters.add(it)
        }
    }

    return if (letters.isEmpty()) null else letters.joinToString("")
}

fun String.convertToColor(): Int? {
    return try {
        Color.parseColor(this)
    } catch (e: Exception) {
        e.printStackTrace()
        Sentry.captureException(e)

        null
    }
}

fun String.cleanSpecialFormattingCharacters(): String {
    var finalString = replace("#b#", "")

    finalString = finalString.replace("#bb#", "")
    finalString = finalString.replace("#v#", "")
    finalString = finalString.replace("#vv#", "")
    finalString = finalString.replace("#l#", "")

    return finalString.replace("#ll#", "")
}

// TODO: Rename this to findMarkedSubstrings
fun String.findBoldSubstrings(): List<String> {
    // https://stackoverflow.com/questions/6109882/regex-match-all-characters-between-two-strings

    val regex = Regex("(?s)(?<=#b#).*?(?=#bb#)")
    val matches = regex.findAll(this)

    return matches.map {
        it.value
    }.toList()
}

// TODO: We can reuse String.findBoldSubstrings and pass the special character.
fun String.findLinkSubstrings(): List<String> {
    // https://stackoverflow.com/questions/6109882/regex-match-all-characters-between-two-strings

    val regex = Regex("(?s)(?<=#l#).*?(?=#ll#)")
    val matches = regex.findAll(this)

    return matches.map {
        it.value
    }.toList()
}

// TODO: We can reuse String.findBoldSubstrings and pass the special character.
fun String.findVerticallyCenteredSubstrings(): List<String> {
    // https://stackoverflow.com/questions/6109882/regex-match-all-characters-between-two-strings

    val regex = Regex("(?s)(?<=#v#).*?(?=#vv#)")
    val matches = regex.findAll(this)

    return matches.map {
        it.value
    }.toList()
}

@Deprecated("Use SpannableString.formatSubstringsAsBold")
fun String.formatBoldSubstrings(
    context: Context,
    substrings: List<String>
): SpannableString {
    val finalAnswer = SpannableString(this)

    // For this to work, the substrings must be ordered: the first substring must be
    // the first to be found in the string, the second substring must be the second to be found
    // in the string etc.

    var startSearchingFromIndex = 0

    substrings.forEach { substring ->
        startSearchingFromIndex = finalAnswer.formatStringAsBold(
            context = context,
            string = substring,
            startSearchingFromIndex = startSearchingFromIndex
        )
    }

    return finalAnswer
}

// TODO: Create the SpannableString.formatSubstringsWithColor just like
//  SpannableString.formatSubstringsAsLinks and deprecate it.
fun String.formatSubstringsWithColor(
    context: Context,
    substrings: List<String>,
    @ColorRes colorRes: Int
): SpannableString {
    val finalAnswer = SpannableString(this)

    // For this to work, the substrings must be ordered: the first substring must be
    // the first to be found in the string, the second substring must be the second to be found
    // in the string etc.

    var startSearchingFromIndex = 0

    substrings.forEach { substring ->
        startSearchingFromIndex = finalAnswer.formatStringWithColor(
            context = context,
            string = substring,
            startSearchingFromIndex = startSearchingFromIndex,
            colorRes = colorRes
        )
    }

    return finalAnswer
}

@Deprecated("Use SpannableString.formatSubstringsAsLinks")
fun String.formatSubstringsAsLinks(
    substrings: List<String>,
    clickableSpans: List<ClickableSpan>
): SpannableString {
    val finalAnswer = SpannableString(this)

    // For this to work, the substrings must be ordered: the first substring must be
    // the first to be found in the string, the second substring must be the second to be found
    // in the string etc.

    var startSearchingFromIndex = 0

    substrings.forEachIndexed { index, substring ->
        val clickableSpan = clickableSpans.getOrNull(index) ?: return@forEachIndexed

        startSearchingFromIndex = finalAnswer.formatStringAsLink(
            string = substring,
            startSearchingFromIndex = startSearchingFromIndex,
            clickableSpan = clickableSpan
        )
    }

    return finalAnswer
}

fun String.isValidMail(): Boolean {
    if (TextUtils.isEmpty(this)) return false

    // trim string to exclude leading and trailing whitespaces,
    // they will be trimmed during email submission
    return android.util.Patterns.EMAIL_ADDRESS.matcher(this.trim()).matches()
}

fun String.toListOfStrings(): List<String>? {
    val itemType = object : TypeToken<List<String>>() {}.type

    return try {
        Gson().fromJson(this, itemType)
    } catch (e: Exception) {
        e.printStackTrace()
        null
    }
}

fun String.toListOfSocketPricing(): List<SocketPricing>? {
    val itemType = object : TypeToken<List<SocketPricing>>() {}.type

    return try {
        Gson().fromJson(this, itemType)
    } catch (e: Exception) {
        e.printStackTrace()
        null
    }
}

@Deprecated("Use the new optimized generateDateUsingPatternV2")
fun String.generateDateUsingPattern(
    pattern: String,
    locale: Locale? = null,
    timeZone: TimeZone? = null
): Date? {
    // We use Locale.US because we use this function to turn a server string to a date.
    val finalLocale = locale ?: Locale.US

    val formatter = try {
        SimpleDateFormat(pattern, finalLocale)
    } catch (e: Exception) {
        e.printStackTrace()

        null
    }

    timeZone?.let {
        formatter?.timeZone = it
    }

    return try {
        formatter?.parse(this)
    } catch (e: Exception) {
        e.printStackTrace()

        null
    }
}

fun String.generateDateUsingPatternV2(formatter: SimpleDateFormat?): Date? {
    return formatter?.parseOrNull(this)
}

fun String.generateDateFormatter(
    locale: Locale? = null,
    timeZone: TimeZone? = null
): SimpleDateFormat? {
    // We use Locale.US because we use this function to turn a server string to a date.
    val finalLocale = locale ?: Locale.US

    val formatter = try {
        SimpleDateFormat(this, finalLocale)
    } catch (e: Exception) {
        e.printStackTrace()

        null
    }

    timeZone?.let {
        formatter?.timeZone = it
    }

    return formatter
}

fun String.convertToListOfWealthyHubStories(): List<WealthyHubStory>? {
    val itemType = object : TypeToken<List<WealthyHubStory>>() {}.type

    return try {
        Gson().fromJson(this, itemType)
    } catch (e: Exception) {
        e.printStackTrace()

        null
    }
}

fun String.convertToListOfChoices(): List<Choice>? {
    val itemType = object : TypeToken<List<Choice>>() {}.type

    return try {
        Gson().fromJson(this, itemType)
    } catch (e: Exception) {
        e.printStackTrace()

        null
    }
}

fun String.toMapOfAssetWeights(): Map<String, Int>? {
    val itemType = object : TypeToken<Map<String, Int>>() {}.type

    return try {
        Gson().fromJson(this, itemType)
    } catch (e: Exception) {
        e.printStackTrace()

        null
    }
}

fun String.convertToListOfPaymentMethods(): List<PaymentMethod>? {
    val itemType = object : TypeToken<List<PaymentMethod>>() {}.type

    return try {
        Gson().fromJson(this, itemType)
    } catch (e: Exception) {
        e.printStackTrace()

        null
    }
}

fun String.convertToListOfInfoRows(): List<InfoRow>? {
    val itemType = object : TypeToken<List<InfoRow>>() {}.type

    return try {
        Gson().fromJson(this, itemType)
    } catch (e: Exception) {
        e.printStackTrace()

        null
    }
}

fun String.convertToTrueLayerProvider(): TrueLayerProvider? {
    return try {
        Gson().fromJson(this, TrueLayerProvider::class.java)
    } catch (e: Exception) {
        e.printStackTrace()

        null
    }
}

fun String.convertToAddMoneyScreenArgs(): AddMoneyScreenArgs? {
    return try {
        Gson().fromJson(this, AddMoneyScreenArgs::class.java)
    } catch (e: Exception) {
        e.printStackTrace()

        null
    }
}

fun String.convertToRevealRewardsScreenArgs(): RevealRewardDialogScreenArgs? {
    return try {
        Gson().fromJson(this, RevealRewardDialogScreenArgs::class.java)
    } catch (e: Exception) {
        e.printStackTrace()

        null
    }
}

fun String.convertToRewardsScreenArgs(): RewardDialogScreenArgs? {
    return try {
        Gson().fromJson(this, RewardDialogScreenArgs::class.java)
    } catch (e: Exception) {
        e.printStackTrace()

        null
    }
}

fun String.convertToAboutAssetScreenArgs(): AboutAssetScreenArgs? {
    return try {
        Gson().fromJson(this, AboutAssetScreenArgs::class.java)
    } catch (e: Exception) {
        e.printStackTrace()

        null
    }
}

fun String.convertToOrdersScreenArgs(): OrdersScreenArgs? {
    return try {
        Gson().fromJson(this, OrdersScreenArgs::class.java)
    } catch (e: Exception) {
        e.printStackTrace()

        null
    }
}

fun String.convertToOrderReviewScreenArgs(): OrderReviewScreenArgs? {
    return try {
        Gson().fromJson(this, OrderReviewScreenArgs::class.java)
    } catch (e: Exception) {
        e.printStackTrace()

        null
    }
}

fun String.convertToConfirmationReceiptScreenArgs(): ConfirmationReceiptScreenArgs? {
    return try {
        Gson().fromJson(this, ConfirmationReceiptScreenArgs::class.java)
    } catch (e: Exception) {
        e.printStackTrace()

        null
    }
}

fun String.convertToFilteredTransactionsScreenArgs(): FilteredTransactionsScreenArguments? {
    return try {
        Gson().fromJson(this, FilteredTransactionsScreenArguments::class.java)
    } catch (e: Exception) {
        e.printStackTrace()

        null
    }
}

fun String.convertToGuideScreenArgs(): GuideScreenArgs? {
    return try {
        Gson().fromJson(this, GuideScreenArgs::class.java)
    } catch (e: Exception) {
        e.printStackTrace()

        null
    }
}

fun String.convertToTopPerformersScreenArgs(): TopPerformersScreenArgs? {
    return try {
        Gson().fromJson(this, TopPerformersScreenArgs::class.java)
    } catch (e: Exception) {
        e.printStackTrace()

        null
    }
}

fun String.convertToSundownDigestScreenArgs(): SundownDigestScreenArgs? {
    return try {
        Gson().fromJson(this, SundownDigestScreenArgs::class.java)
    } catch (e: Exception) {
        e.printStackTrace()

        null
    }
}

fun String.convertToKYCIncompleteScreenArgs(): KYCIncompleteScreenArgs? {
    return try {
        Gson().fromJson(this, KYCIncompleteScreenArgs::class.java)
    } catch (e: Exception) {
        e.printStackTrace()

        null
    }
}

fun String.convertToAccountDetailsScreenArgs(): AccountDetailsScreenArgs? {
    return try {
        Gson().fromJson(this, AccountDetailsScreenArgs::class.java)
    } catch (e: Exception) {
        e.printStackTrace()

        null
    }
}

fun String.convertToReferScreenArgs(): ReferScreenArgs? {
    return try {
        Gson().fromJson(this, ReferScreenArgs::class.java)
    } catch (e: Exception) {
        e.printStackTrace()

        null
    }
}

fun String.convertToReferOptionsScreenArgs(): ReferOptionsScreenArgs? {
    return try {
        Gson().fromJson(this, ReferOptionsScreenArgs::class.java)
    } catch (e: Exception) {
        e.printStackTrace()

        null
    }
}

fun String.convertToWealthyHubArticleScreenArgs(): WealthyHubArticleScreenArgs? {
    return try {
        Gson().fromJson(this, WealthyHubArticleScreenArgs::class.java)
    } catch (e: Exception) {
        e.printStackTrace()

        null
    }
}

fun String.convertToHelpCenterFAQScreenArgs(): HelpCenterFAQScreenArgs? {
    return try {
        Gson().fromJson(this, HelpCenterFAQScreenArgs::class.java)
    } catch (e: Exception) {
        e.printStackTrace()

        null
    }
}

fun String.convertToDirectDebitScheduleScreenArgs(): DirectDebitScheduleScreenArgs? {
    return try {
        Gson().fromJson(this, DirectDebitScheduleScreenArgs::class.java)
    } catch (e: Exception) {
        e.printStackTrace()

        null
    }
}

fun String.convertToScheduleOptionsScreenArgs(): ScheduleOptionsScreenArgs? {
    return try {
        Gson().fromJson(this, ScheduleOptionsScreenArgs::class.java)
    } catch (e: Exception) {
        e.printStackTrace()

        null
    }
}

fun String.convertToAllocationOptionsScreenArgs(): AllocationOptionsScreenArgs? {
    return try {
        Gson().fromJson(this, AllocationOptionsScreenArgs::class.java)
    } catch (e: Exception) {
        e.printStackTrace()

        null
    }
}

fun String.convertToTopUpOptionsScreenArgs(): TopUpOptionsScreenArgs? {
    return try {
        Gson().fromJson(this, TopUpOptionsScreenArgs::class.java)
    } catch (e: Exception) {
        e.printStackTrace()

        null
    }
}

fun String.convertToUpdateRecurringMessageScreenArgs(): UpdateRecurringMessageScreenArgs? {
    return try {
        Gson().fromJson(this, UpdateRecurringMessageScreenArgs::class.java)
    } catch (e: Exception) {
        e.printStackTrace()

        null
    }
}

fun String.convertToSetupDirectDebitScreenArgs(): SetupDirectDebitScreenArgs? {
    return try {
        Gson().fromJson(this, SetupDirectDebitScreenArgs::class.java)
    } catch (e: Exception) {
        e.printStackTrace()

        null
    }
}

fun String.convertToControlCenterRepeatingInvestmentScreenArgs(): ControlCenterRepeatingInvestmentScreenArgs? {
    return try {
        Gson().fromJson(this, ControlCenterRepeatingInvestmentScreenArgs::class.java)
    } catch (e: Exception) {
        e.printStackTrace()

        null
    }
}

fun String.convertToPortfolioCreationChoicesScreenArgs(): PortfolioCreationChoicesScreenArgs? {
    return try {
        Gson().fromJson(this, PortfolioCreationChoicesScreenArgs::class.java)
    } catch (e: Exception) {
        e.printStackTrace()

        null
    }
}

fun String.convertToPortfolioScratchpadScreenArgs(): PortfolioScratchpadScreenArgs? {
    return try {
        Gson().fromJson(this, PortfolioScratchpadScreenArgs::class.java)
    } catch (e: Exception) {
        e.printStackTrace()

        null
    }
}

fun String.convertToPortfolioCreationChoicesSheetScreenArgs(): PortfolioCreationChoicesSheetScreenArgs? {
    return try {
        Gson().fromJson(this, PortfolioCreationChoicesSheetScreenArgs::class.java)
    } catch (e: Exception) {
        e.printStackTrace()

        null
    }
}

fun String.convertToBanksSheetScreenArgs(): BanksSheetScreenArgs? {
    return try {
        Gson().fromJson(this, BanksSheetScreenArgs::class.java)
    } catch (e: Exception) {
        e.printStackTrace()

        null
    }
}

fun String.convertToDepositScreenArgs(): DepositScreenArgs? {
    return try {
        Gson().fromJson(this, DepositScreenArgs::class.java)
    } catch (e: Exception) {
        e.printStackTrace()

        null
    }
}

fun String.convertToRoboAdvisorScreenArgs(): RoboAdvisorScreenArgs? {
    return try {
        Gson().fromJson(this, RoboAdvisorScreenArgs::class.java)
    } catch (e: Exception) {
        e.printStackTrace()

        null
    }
}

fun String.convertToPortfolioWeightingScreenArgs(): PortfolioWeightingScreenArgs? {
    return try {
        Gson().fromJson(this, PortfolioWeightingScreenArgs::class.java)
    } catch (e: Exception) {
        e.printStackTrace()

        null
    }
}

fun String.convertToPortfolioBuilderStartPickTemplateScreenArgs(): PortfolioBuilderStartPickTemplateScreenArgs? {
    return try {
        Gson().fromJson(this, PortfolioBuilderStartPickTemplateScreenArgs::class.java)
    } catch (e: Exception) {
        e.printStackTrace()

        null
    }
}

fun String.convertToAssetClassesScreenArgs(): AssetClassesScreenArgs? {
    return try {
        Gson().fromJson(this, AssetClassesScreenArgs::class.java)
    } catch (e: Exception) {
        e.printStackTrace()

        null
    }
}

fun String.convertToGeographyScreenArgs(): GeographyScreenArgs? {
    return try {
        Gson().fromJson(this, GeographyScreenArgs::class.java)
    } catch (e: Exception) {
        e.printStackTrace()

        null
    }
}

fun String.convertToSectorsScreenArgs(): SectorsScreenArgs? {
    return try {
        Gson().fromJson(this, SectorsScreenArgs::class.java)
    } catch (e: Exception) {
        e.printStackTrace()

        null
    }
}

fun String.convertToPortfolioBuilderScreenArgs(): PortfolioBuilderScreenArgs? {
    return try {
        Gson().fromJson(this, PortfolioBuilderScreenArgs::class.java)
    } catch (e: Exception) {
        e.printStackTrace()

        null
    }
}

fun String.convertToPortfolioTemplateWaitingScreenArgs(): PortfolioTemplateWaitingScreenArgs? {
    return try {
        Gson().fromJson(this, PortfolioTemplateWaitingScreenArgs::class.java)
    } catch (e: Exception) {
        e.printStackTrace()

        null
    }
}

fun String.convertToPortfolioTemplateReadyScreenArgs(): PortfolioTemplateReadyScreenArgs? {
    return try {
        Gson().fromJson(this, PortfolioTemplateReadyScreenArgs::class.java)
    } catch (e: Exception) {
        e.printStackTrace()

        null
    }
}

fun String.convertToLinkBankAccountLoadingScreenArgs(): LinkBankAccountLoadingScreenArgs? {
    return try {
        Gson().fromJson(this, LinkBankAccountLoadingScreenArgs::class.java)
    } catch (e: Exception) {
        e.printStackTrace()

        null
    }
}

fun String.convertToBankTransferScreenArgs(): BankTransferScreenArgs? {
    return try {
        Gson().fromJson(this, BankTransferScreenArgs::class.java)
    } catch (e: Exception) {
        e.printStackTrace()

        null
    }
}

fun String.convertToRegularBankTransferScreenArgs(): RegularBankTransferScreenArgs? {
    return try {
        Gson().fromJson(this, RegularBankTransferScreenArgs::class.java)
    } catch (e: Exception) {
        e.printStackTrace()

        null
    }
}

fun String.convertToBankAccountDetailsScreenArgs(): BankAccountDetailsScreenArgs? {
    return try {
        Gson().fromJson(this, BankAccountDetailsScreenArgs::class.java)
    } catch (e: Exception) {
        e.printStackTrace()

        null
    }
}

fun String.convertToSavingsProductDetailsScreenArgs(): SavingsProductDetailsScreenArgs? {
    return try {
        Gson().fromJson(this, SavingsProductDetailsScreenArgs::class.java)
    } catch (e: Exception) {
        e.printStackTrace()

        null
    }
}

fun String.convertToWithdrawMoneyScreenArgs(): WithdrawMoneyScreenArgs? {
    return try {
        Gson().fromJson(this, WithdrawMoneyScreenArgs::class.java)
    } catch (e: Exception) {
        e.printStackTrace()

        null
    }
}

fun String.convertToBannerDescriptionScreenArgs(): BannerDescriptionScreenArguments? {
    return try {
        Gson().fromJson(this, BannerDescriptionScreenArguments::class.java)
    } catch (e: Exception) {
        e.printStackTrace()

        null
    }
}

fun String.convertToActionSheetScreenArgs(): ActionsSheetScreenArgs? {
    return try {
        Gson().fromJson(this, ActionsSheetScreenArgs::class.java)
    } catch (e: Exception) {
        e.printStackTrace()

        null
    }
}

fun String.convertToScheduleSheetScreenArgs(): ScheduleSheetScreenArgs? {
    return try {
        Gson().fromJson(this, ScheduleSheetScreenArgs::class.java)
    } catch (e: Exception) {
        e.printStackTrace()

        null
    }
}

fun String.convertToAppRatingSheetScreenArgs(): AppRatingSheetScreenArgs? {
    return try {
        Gson().fromJson(this, AppRatingSheetScreenArgs::class.java)
    } catch (e: Exception) {
        e.printStackTrace()

        null
    }
}

fun String.convertToGenericErrorScreenArgs(): GenericErrorScreenArgs? {
    return try {
        Gson().fromJson(this, GenericErrorScreenArgs::class.java)
    } catch (e: Exception) {
        e.printStackTrace()

        null
    }
}

fun String.convertToDayPickerScreenArgs(): DayPickerScreenArgs? {
    return try {
        Gson().fromJson(this, DayPickerScreenArgs::class.java)
    } catch (e: Exception) {
        e.printStackTrace()

        null
    }
}

fun String.convertToAssetNewsArticlesScreenArgs(): AssetNewsArticlesScreenArgs? {
    return try {
        Gson().fromJson(this, AssetNewsArticlesScreenArgs::class.java)
    } catch (e: Exception) {
        e.printStackTrace()

        null
    }
}

fun String.convertToGenericMessageSheetScreenArgs(): GenericMessageSheetScreenArgs? {
    return try {
        Gson().fromJson(this, GenericMessageSheetScreenArgs::class.java)
    } catch (e: Exception) {
        e.printStackTrace()

        null
    }
}

fun String.convertToDescriptionScreenArgs(): DescriptionScreenArgs? {
    return try {
        Gson().fromJson(this, DescriptionScreenArgs::class.java)
    } catch (e: Exception) {
        e.printStackTrace()

        null
    }
}

fun String.convertToPaymentMethod(): com.wealthyhood.wealthyhood.service.PaymentMethod? {
    return try {
        Gson().fromJson(this, com.wealthyhood.wealthyhood.service.PaymentMethod::class.java)
    } catch (e: Exception) {
        e.printStackTrace()

        null
    }
}

fun String.convertToPendingDeposit(): Transaction.PendingDeposit? {
    return try {
        Gson().fromJson(this, Transaction.PendingDeposit::class.java)
    } catch (e: Exception) {
        e.printStackTrace()

        null
    }
}

fun String.convertToOrderReviewFooterProperties(): OrderReviewFooterProperties? {
    return try {
        Gson().fromJson(this, OrderReviewFooterProperties::class.java)
    } catch (e: Exception) {
        e.printStackTrace()

        null
    }
}

fun String.convertToTransaction(): Transaction? {
    return try {
        Gson().fromJson(this, Transaction::class.java)
    } catch (e: Exception) {
        e.printStackTrace()

        null
    }
}

fun String.convertToAutomation(): Automation? {
    return try {
        Gson().fromJson(this, Automation::class.java)
    } catch (e: Exception) {
        e.printStackTrace()

        null
    }
}

fun String.convertToReward(): Reward? {
    return try {
        Gson().fromJson(this, Reward::class.java)
    } catch (e: Exception) {
        e.printStackTrace()

        null
    }
}

fun String.convertToOrder(): Order? {
    return try {
        Gson().fromJson(this, Order::class.java)
    } catch (e: Exception) {
        e.printStackTrace()

        null
    }
}

fun String.convertToListOfTransactions(): List<Transaction>? {
    val itemType = object : TypeToken<List<Transaction>>() {}.type

    return try {
        Gson().fromJson(this, itemType)
    } catch (e: Exception) {
        e.printStackTrace()

        null
    }
}

fun String.convertToListOfPortfolios(): List<Portfolio>? {
    val itemType = object : TypeToken<List<Portfolio>>() {}.type

    return try {
        Gson().fromJson(this, itemType)
    } catch (e: Exception) {
        e.printStackTrace()

        null
    }
}

fun String.convertToUser(): User? {
    return try {
        Gson().fromJson(this, User::class.java)
    } catch (e: Exception) {
        e.printStackTrace()

        null
    }
}

fun String.convertToBankAccount(): BankAccount? {
    return try {
        Gson().fromJson(this, BankAccount::class.java)
    } catch (e: Exception) {
        Sentry.captureException(e)

        null
    }
}

fun String.convertToMandate(): Mandate? {
    return try {
        Gson().fromJson(this, Mandate::class.java)
    } catch (e: Exception) {
        Sentry.captureException(e)

        null
    }
}

fun String.convertToWallet(): Wallet? {
    return try {
        Gson().fromJson(this, Wallet::class.java)
    } catch (e: Exception) {
        Sentry.captureException(e)

        null
    }
}

fun String.convertToVerificationInfo(): VerificationInfo? {
    return try {
        Gson().fromJson(this, VerificationInfo::class.java)
    } catch (e: Exception) {
        e.printStackTrace()

        null
    }
}

fun String.convertToTransactionPreview(): TransactionPreview? {
    return try {
        Gson().fromJson(this, TransactionPreview::class.java)
    } catch (e: Exception) {
        e.printStackTrace()

        null
    }
}

fun String.convertToPlanActivationCompletedScreenArgs(): PlanActivationCompletedScreenArgs? {
    return try {
        Gson().fromJson(this, PlanActivationCompletedScreenArgs::class.java)
    } catch (e: Exception) {
        e.printStackTrace()

        null
    }
}

fun String.convertToAllocation(): Allocation? {
    return try {
        Gson().fromJson(this, Allocation::class.java)
    } catch (e: Exception) {
        e.printStackTrace()

        null
    }
}

fun String.convertToListOfVirtualTreeItems(): List<VirtualTreeItem>? {
    val itemType = object : TypeToken<List<VirtualTreeItem>>() {}.type

    return try {
        Gson().fromJson(this, itemType)
    } catch (e: Exception) {
        e.printStackTrace()

        null
    }
}

fun String.convertToListOfDashboardChartElements(): List<DashboardChartTenorElement>? {
    val itemType = object : TypeToken<List<DashboardChartTenorElement>>() {}.type

    return try {
        Gson().fromJson(this, itemType)
    } catch (e: Exception) {
        e.printStackTrace()

        null
    }
}

fun String.generateFormattedDate(
    dateStringPattern: String,
    formattedStringPattern: String
): String? {
    return generateDateUsingPattern(dateStringPattern)
        ?.generateStringUsingPattern(
            pattern = formattedStringPattern
        )
}

fun String.isMyAccountPendingScreenState() =
    this == TransactionsScreenState.MyAccountPending.rawValue

fun String.isSavingsProductActivityScreenState(): Boolean {
    return this == TransactionsScreenState.SavingsProductActivity.rawValue
}
