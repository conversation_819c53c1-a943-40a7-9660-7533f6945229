package com.wealthyhood.wealthyhood.extensions

import android.content.Context
import androidx.annotation.ColorInt
import androidx.annotation.ColorRes
import androidx.annotation.DrawableRes
import androidx.core.content.ContextCompat
import com.google.gson.Gson
import com.wealthyhood.wealthyhood.R
import com.wealthyhood.wealthyhood.common.ScheduleStepBuilder
import com.wealthyhood.wealthyhood.common.Utils.generateScheduleStep3SubtitleText
import com.wealthyhood.wealthyhood.database.Asset
import com.wealthyhood.wealthyhood.domain.InfoRowEnum
import com.wealthyhood.wealthyhood.domain.generateBuyExecutionWindowInfoRow
import com.wealthyhood.wealthyhood.domain.generateExecutionWindowInfoRows
import com.wealthyhood.wealthyhood.domain.generateSellExecutionWindowInfoRow
import com.wealthyhood.wealthyhood.model.ConfirmationReceiptScreenArgs
import com.wealthyhood.wealthyhood.model.ConfirmationReceiptTypeEnum
import com.wealthyhood.wealthyhood.model.InfoRow
import com.wealthyhood.wealthyhood.model.MandateStatus
import com.wealthyhood.wealthyhood.model.OrderReviewFooterProperties
import com.wealthyhood.wealthyhood.model.ScheduleStep
import com.wealthyhood.wealthyhood.service.BankAccount
import com.wealthyhood.wealthyhood.service.ETFProvider
import com.wealthyhood.wealthyhood.service.Transaction
import com.wealthyhood.wealthyhood.service.TrueLayerProvider
import com.wealthyhood.wealthyhood.ui.myaccount.transactions.TransactionsHelper
import java.util.Locale

fun List<Transaction>.excludeAutomatedTransactions(): List<Transaction> {
    return filter {
        it.linkedAutomation == null
    }
}

fun List<Transaction>.excludePendingDepositTransactionsWithCancelledOrRejectedDeposit(): List<Transaction> {
    return filter {
        if (it.displayStatus != TransactionsHelper.TransactionStatus.PendingDeposit.rawValue) {
            return@filter true
        }

        val validPendingDepositStatuses = listOf(
            TransactionsHelper.TransactionStatus.Pending.rawValue,
            TransactionsHelper.TransactionStatus.Settled.rawValue
        )

        if (validPendingDepositStatuses.contains(it.getPendingDepositAsObject()?.displayStatus)) {
            return@filter true
        }

        false
    }
}

// Excludes the deposit transactions that are linked to asset transactions.
// These are created during an 1-step portfolio or ETF buy.
fun List<Transaction>.excludeDependentDepositTransactions(): List<Transaction> {
    val pendingDepositIDs = mutableListOf<String>()

    forEach { transaction ->
        transaction.getPendingDepositAsObject()?.id?.let { pendingDepositID ->
            pendingDepositIDs.add(pendingDepositID)
        }
    }

    return filter { transaction ->
        !pendingDepositIDs.contains(transaction.id)
    }
}

private fun List<Transaction>.filterOutValidTransactions(
    validCategories: List<String>?,
    validStatuses: List<String>?
): List<Transaction> {
    return filter { transaction ->
        validCategories?.let { validCategories ->
            if (validCategories.contains(transaction.category).not()) {
                return@filter false
            }
        }

        validStatuses?.let { validStatuses ->
            if (validStatuses.contains(transaction.displayStatus).not()) {
                return@filter false
            }
        }

        true
    }
}

fun List<Transaction>.computePendingTransactionsCount(): Int {
    val validCategories = mutableListOf(
        TransactionsHelper.TransactionCategory.Withdrawal.rawValue,
        TransactionsHelper.TransactionCategory.Deposit.rawValue,
        TransactionsHelper.TransactionCategory.ETF.rawValue,
        TransactionsHelper.TransactionCategory.Rebalance.rawValue
    )

    val validStatuses = mutableListOf(
        TransactionsHelper.TransactionStatus.Pending.rawValue,
        TransactionsHelper.TransactionStatus.PendingDeposit.rawValue,
        TransactionsHelper.TransactionStatus.PendingGift.rawValue
    )

    val finalTransactions = excludeDependentDepositTransactions()
        .excludePendingDepositTransactionsWithCancelledOrRejectedDeposit()
        .excludeAutomatedTransactions()
        .filterOutValidTransactions(
            validCategories = validCategories,
            validStatuses = validStatuses
        )

    return finalTransactions.size
}

// Εδώ τα assets θα πρέπει να είναι όλα τα assets (ακόμη και τα deprecated).
@DrawableRes
fun Transaction.generateOrderReviewIconRes(
    context: Context,
    assets: List<Asset>?
): Int? {
    return when (category) {
        TransactionsHelper.TransactionCategory.Withdrawal.rawValue -> R.drawable.ic_minus_24
        TransactionsHelper.TransactionCategory.Deposit.rawValue -> R.drawable.ic_plus_24
        TransactionsHelper.TransactionCategory.ETF.rawValue -> {
            // TODO: Simplify it
            if (orders?.size == 1) {
                val asset = assets?.find {
                    it.isin == orders[0].isin
                }

                if (asset?.isStock() == true) {
                    asset.getIconDrawableRes(context)
                } else null
            } else {
                0 // TODO: Use the real data
            }
        }

        TransactionsHelper.TransactionCategory.Rebalance.rawValue -> 0 // TODO: Use the real data
        else -> null
    }
}

// Εδώ τα assets θα πρέπει να είναι όλα τα assets (ακόμη και τα deprecated).
fun Transaction.generateOrderIconURI(
    assets: List<Asset>?,
    etfProviders: List<ETFProvider>?
): String? {
    if (category != TransactionsHelper.TransactionCategory.ETF.rawValue) {
        return null
    }

    if (orders?.size != 1) {
        return null
    }

    val asset = assets?.find {
        it.isin == orders[0].isin
    }

    return asset?.generateProviderLogoURI(etfProviders)
}

fun Transaction.generateWalletTransactionInfoRows(
    context: Context,
    bankAccount: BankAccount?,
    trueLayerProviders: List<TrueLayerProvider>?,
    userLocale: Locale
): List<InfoRow> {
    val resources = context.resources
    val defaultColor = ContextCompat.getColor(context, R.color.wealthyhood_dark_blue)

    val finalAnswer = mutableListOf<InfoRow>()

    val amountInfoRow = InfoRow(
        id = "amountInfoRow",
        label = resources.getString(R.string.amount),
        shouldShowInfoButton = false,
        value = generateFormattedDisplayAmount(userLocale = userLocale, cashFlowSign = null),
        secondaryValue = null,
        shouldShowSwitch = false,
        isSwitchOn = false,
        switchUpdateTimestamp = null,
        valueTextColor = defaultColor,
        hasSeparator = true
    )

    displayAmount?.let { finalAnswer.add(amountInfoRow) }

    bankAccount?.generateDescription()?.let { accountDescription ->
        val label = when (category) {
            TransactionsHelper.TransactionCategory.Withdrawal.rawValue -> {
                resources.getString(R.string.to_account)
            }

            TransactionsHelper.TransactionCategory.Deposit.rawValue -> {
                resources.getString(R.string.from_account)
            }

            else -> null
        } ?: return@let

        val accountInfoRow = InfoRow(
            id = "accountInfoRow",
            label = label,
            shouldShowInfoButton = false,
            value = accountDescription,
            secondaryValue = null,
            shouldShowSwitch = false,
            isSwitchOn = false,
            switchUpdateTimestamp = null,
            valueTextColor = defaultColor,
            hasSeparator = true
        )

        finalAnswer.add(accountInfoRow)
    }

    trueLayerProviders?.find { it.id == bankAccount?.truelayerProviderId }?.name?.let { bankName ->
        val label = when (category) {
            TransactionsHelper.TransactionCategory.Withdrawal.rawValue -> {
                resources.getString(R.string.to_bank)
            }

            TransactionsHelper.TransactionCategory.Deposit.rawValue -> {
                resources.getString(R.string.from_bank)
            }

            else -> null
        } ?: return@let

        val bankInfoRow = InfoRow(
            id = "bankInfoRow",
            label = label,
            shouldShowInfoButton = false,
            value = bankName,
            secondaryValue = null,
            shouldShowSwitch = false,
            isSwitchOn = false,
            switchUpdateTimestamp = null,
            valueTextColor = defaultColor,
            hasSeparator = true
        )

        finalAnswer.add(bankInfoRow)
    }

    val createdInfoRow = InfoRow(
        id = "createdInfoRow",
        label = resources.getString(R.string.created),
        shouldShowInfoButton = false,
        value = generateFormattedDisplayDate(
            stringPattern = "dd MMM yy • HH:mm"
        ),
        secondaryValue = null,
        shouldShowSwitch = false,
        isSwitchOn = false,
        switchUpdateTimestamp = null,
        valueTextColor = defaultColor,
        hasSeparator = true
    )

    finalAnswer.add(createdInfoRow)

    val statusInfoRow = InfoRow(
        id = "statusInfoRow",
        label = resources.getString(R.string.status),
        shouldShowInfoButton = false,
        value = generateStatusText(context),
        secondaryValue = null,
        shouldShowSwitch = false,
        isSwitchOn = false,
        switchUpdateTimestamp = null,
        valueTextColor = generateReceiptStatusTextColor(context),
        hasSeparator = false
    )

    finalAnswer.add(statusInfoRow)

    return finalAnswer
}

fun Transaction.generateFormattedDisplayDate(stringPattern: String): String? {
    return displayDate?.generateFormattedDate(
        dateStringPattern = "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'",
        formattedStringPattern = stringPattern
    )
}

fun Transaction.generateFormattedChargeMonth(stringPattern: String): String? {
    return chargeMonth?.generateFormattedDate(
        dateStringPattern = "yyyy-MM",
        formattedStringPattern = stringPattern
    )
}

fun Transaction.generateFormattedDisplayAmount(userLocale: Locale, cashFlowSign: Int?): String? {
    val amount = displayAmount ?: return null
    val realAmount = amount / 100f

    val formattedAmount = realAmount.generateFormattedCurrency(
        currencyISOCode = consideration?.currency,
        locale = userLocale
    )

    return if (cashFlowSign == null) formattedAmount
    else if (cashFlowSign > 0) "+$formattedAmount"
    else "-$formattedAmount"
}

@DrawableRes
fun Transaction.generateStatusIconRes(): Int? {
    return when (displayStatus) {
        TransactionsHelper.TransactionStatus.Settled.rawValue -> R.drawable.ic_check_circle_filled
        TransactionsHelper.TransactionStatus.Pending.rawValue -> R.drawable.ic_update
        TransactionsHelper.TransactionStatus.PendingDeposit.rawValue -> R.drawable.ic_update
        TransactionsHelper.TransactionStatus.PendingGift.rawValue -> R.drawable.ic_update
        TransactionsHelper.TransactionStatus.Cancelled.rawValue, TransactionsHelper.TransactionStatus.Rejected.rawValue -> R.drawable.ic_cancel_filled
        else -> null
    }
}

fun Transaction.isPending(): Boolean {
    val pendingStatuses = listOf(
        TransactionsHelper.TransactionStatus.Pending.rawValue,
        TransactionsHelper.TransactionStatus.PendingDeposit.rawValue,
        TransactionsHelper.TransactionStatus.PendingGift.rawValue
    )

    return pendingStatuses.contains(displayStatus)
}

@ColorRes
fun Transaction.generateStatusIconColorRes(): Int? {
    val statusEnum = displayStatus?.convertToStatusEnum()
    return statusEnum?.generateStatusIconColorRes()
}

@ColorInt
fun Transaction.generateStatusIconColor(context: Context): Int {
    // New colors from AssetDetails task. We should also use them in MyAccount and Transactions.

    val statusEnum = displayStatus?.convertToStatusEnum()
    val color = statusEnum?.generateStatusIconColor(context)

    return color ?: ContextCompat.getColor(context, R.color.primary)
}

fun Transaction.generateStatusText(context: Context): String? {
    val statusEnum = displayStatus?.convertToStatusEnum()
    return statusEnum?.generateStatusText(context)
}

@ColorRes
fun Transaction.generateStatusTextColorRes(): Int? {
    val statusEnum = displayStatus?.convertToStatusEnum()
    return statusEnum?.generateStatusTextColorRes()
}

@ColorInt
private fun Transaction.generateReceiptStatusTextColor(context: Context): Int? {
    val statusEnum = displayStatus?.convertToStatusEnum()
    return statusEnum?.generateReceiptStatusTextColor(context)
}

fun Transaction.generateOrderTypeText(context: Context): String? {
    val resources = context.resources

    return when (portfolioTransactionCategory) {
        "buy" -> resources.getString(R.string.buy)
        "sell" -> resources.getString(R.string.sell)
        else -> null
    }
}

@ColorRes
fun Transaction.generateOrderTypeTextColorRes(): Int {
    if (displayStatus == TransactionsHelper.TransactionStatus.Cancelled.rawValue || displayStatus == TransactionsHelper.TransactionStatus.Rejected.rawValue) {
        return R.color.base
    }

    return when (portfolioTransactionCategory) {
        "buy" -> R.color.green_50
        "sell" -> R.color.red_50
        else -> R.color.base
    }
}

fun Transaction.generateOrderReviewFooterProperties(context: Context): OrderReviewFooterProperties {
    val resources = context.resources

    val buttonBackgroundColor = ContextCompat.getColor(context, R.color.system_alerts_danger_color)

    val hasOrdersButton =
        if (category == TransactionsHelper.TransactionCategory.Rebalance.rawValue) {
            (displayStatus == TransactionsHelper.TransactionStatus.Settled.rawValue)
        } else false

    return OrderReviewFooterProperties(
        topPadding = resources.getDimensionPixelSize(R.dimen.spacing_40),
        hasOrdersButton = hasOrdersButton,
        hasButton = isCancellable,
        buttonTitle = resources.getString(R.string.cancel_order),
        buttonBackgroundColor = buttonBackgroundColor,
        buttonElevationRes = null,
        hasDisclaimer = false,
        disclaimerText = null,
        isDisclaimerCentered = false
    )
}

fun Transaction.generatePortfolioReceiptTitle(context: Context): String {
    val resources = context.resources

    val title = when (portfolioTransactionCategory) {
        "buy" -> resources.getString(R.string.my_portfolio_buy)
        "sell" -> resources.getString(R.string.my_portfolio_sell)
        else -> resources.getString(R.string.my_portfolio_rebalance)
    }

    return title
}

fun Transaction.generateRebalanceReceiptInfoRows(
    context: Context,
    userLocale: Locale
): List<InfoRow> {
    val resources = context.resources
    val defaultColor = ContextCompat.getColor(context, R.color.wealthyhood_dark_blue)

    val infoRows = mutableListOf<InfoRow>()

    generateFormattedDisplayDayOfMonthText(context)?.let { dayOfMonthText ->
        val repeatingInfoRow = InfoRow(
            id = "repeatingInfoRow",
            label = resources.getString(R.string.repeating_label),
            shouldShowInfoButton = false,
            value = dayOfMonthText,
            secondaryValue = null,
            shouldShowSwitch = false,
            isSwitchOn = false,
            switchUpdateTimestamp = null,
            valueTextColor = defaultColor,
            hasSeparator = true
        )

        infoRows.add(repeatingInfoRow)
    }

    val commissionCurrencyCode = fees?.generateCommissionCurrencyCode()

    fees?.generateFormattedTotalCommission(
        userLocale = userLocale,
        currencyISOCode = commissionCurrencyCode
    )?.let {
        val commissionRow = InfoRow(
            id = InfoRowEnum.PORTFOLIO_COMMISSION.rawValue,
            label = resources.getString(R.string.commission),
            shouldShowInfoButton = true,
            value = it,
            secondaryValue = null,
            shouldShowSwitch = false,
            isSwitchOn = false,
            switchUpdateTimestamp = null,
            valueTextColor = defaultColor,
            hasSeparator = true
        )

        infoRows.add(commissionRow)
    }

    if (isPending()) {
        val executionWindowInfoRows = generateExecutionWindowInfoRows(
            context = context,
            valueTextColor = defaultColor,
            shouldAllowSmartExecutionOption = false,
            isSmartExecutionSwitchOn = false
        )

        infoRows.addAll(executionWindowInfoRows)

        generateSellExecutionWindowInfoRow(
            context = context,
            valueTextColor = defaultColor
        )?.let {
            infoRows.add(it)
        }

        generateBuyExecutionWindowInfoRow(
            context = context,
            valueTextColor = defaultColor
        )?.let {
            infoRows.add(it)
        }
    } else {
        val dateInfoRow = InfoRow(
            id = "dateInfoRow",
            label = resources.getString(R.string.date),
            shouldShowInfoButton = false,
            value = generateFormattedDisplayDate("dd MMM yy"),
            secondaryValue = null,
            shouldShowSwitch = false,
            isSwitchOn = false,
            switchUpdateTimestamp = null,
            valueTextColor = defaultColor,
            hasSeparator = true
        )

        infoRows.add(dateInfoRow)
    }

    val statusInfoRow = InfoRow(
        id = "statusInfoRow",
        label = resources.getString(R.string.status),
        shouldShowInfoButton = false,
        value = generateStatusText(context),
        secondaryValue = null,
        shouldShowSwitch = false,
        isSwitchOn = false,
        switchUpdateTimestamp = null,
        valueTextColor = generateStatusIconColor(context), // TODO: Rename the function
        hasSeparator = false
    )

    infoRows.add(statusInfoRow)

    return infoRows
}

fun Transaction.generateFormattedDisplayDayOfMonthText(context: Context): String? {
    val dayOfMonth = linkedAutomation?.let {
        Gson().toJson(it).convertToAutomation()?.dayOfMonth
    }

    return dayOfMonth?.generateInfoRowRepeatingText(resources = context.resources)
}

fun Transaction.generateExecutionProgressDetails(context: Context): Pair<String, String>? {
    // Επιστρέφουμε ένα object της μορφής: {"first": "Partially executed", "second": "3/28 orders"}

    val label = executionProgress?.label ?: return null
    val matched = executionProgress.matched ?: return null
    val total = executionProgress.total ?: return null

    val executedOrdersText = context.resources.getString(
        R.string.partially_executed_orders,
        matched.toString(),
        total.toString()
    )

    return Pair(label, executedOrdersText)
}

fun Transaction.generateScheduleSteps(
    context: Context,
    generateForMMFTransaction: Boolean,
    repeatingText: String?,
    isUserEU: Boolean
): List<ScheduleStep> {
    val finalAnswer = mutableListOf<ScheduleStep>()

    val mandate = getLinkedAutomationAsObject()?.getMandateAsObject()
    val pendingDeposit = getPendingDepositAsObject()

    val isBankStepCompleted = (pendingDeposit?.isDirectDebitPaymentCollected == true)

    val isMandateStepCompleted =
        mandate?.status == MandateStatus.Active.rawValue || isBankStepCompleted

    // Step 1

    val mandateStepTitle = context.resources.getString(R.string.schedule_step_1_title_text)

    val mandateStepSubtitle: String = if (generateForMMFTransaction) {
        context.resources.getString(R.string.schedule_step_1_mmf_subtitle_text)
    } else {
        context.resources.getString(R.string.schedule_step_1_subtitle_text)
    }

    val mandateStep = ScheduleStepBuilder.generateMandateStep(
        context = context,
        pendingDeposit = pendingDeposit,
        title = mandateStepTitle,
        subtitle = mandateStepSubtitle,
        isCompleted = isMandateStepCompleted
    )

    mandateStep?.let {
        finalAnswer.add(mandateStep)
    }

    // Step 2

    val bankStepTitle = context.resources.getString(R.string.schedule_step_2_title_text)

    val bankStepSubtitle = if (mandateStep != null) {
        context.resources.getString(R.string.schedule_step_2_subtitle_text)
    } else repeatingText

    val bankStep = ScheduleStepBuilder.generateBankStep(
        context = context,
        title = bankStepTitle,
        subtitle = bankStepSubtitle,
        isCompleted = isBankStepCompleted
    )

    finalAnswer.add(bankStep)

    // Step 3

    val directDebitProgressStepSubtitle = generateScheduleStep3SubtitleText(
        context = context,
        isUserEU = isUserEU
    )

    val directDebitProgress = pendingDeposit?.directDebitProgressPercentage ?: 0f

    val directDebitProgressStep = ScheduleStepBuilder.generateDirectDebitProgressStep(
        context = context,
        subtitle = directDebitProgressStepSubtitle,
        progress = directDebitProgress
    )

    finalAnswer.add(directDebitProgressStep)

    // Step 4

    val isMoneyReceivedStepCompleted = (pendingDeposit?.moneyReceived == true)

    val moneyReceivedStepTitle = context.resources.getString(R.string.schedule_step_4_title_text)

    val moneyReceivedStepSubtitle =
        context.resources.getString(R.string.schedule_step_4_subtitle_text)

    val moneyReceivedStep = ScheduleStepBuilder.generateMoneyReceivedStep(
        context = context,
        title = moneyReceivedStepTitle,
        subtitle = moneyReceivedStepSubtitle,
        isCompleted = isMoneyReceivedStepCompleted
    )

    finalAnswer.add(moneyReceivedStep)

    // Step 5

    val orderPlacedStepTitle: String
    val orderPlacedStepSubtitle: String

    if (generateForMMFTransaction) {
        orderPlacedStepTitle = context.resources.getString(R.string.schedule_step_5_mmf_title_text)

        orderPlacedStepSubtitle = context.resources.getString(
            R.string.schedule_step_5_mmf_subtitle_text
        )
    } else {
        orderPlacedStepTitle = context.resources.getString(R.string.schedule_step_5_title_text)

        orderPlacedStepSubtitle = context.resources.getString(
            R.string.schedule_step_5_subtitle_text
        )
    }

    val orderPlacedStep = ScheduleStepBuilder.generateOrderPlacedStep(
        title = orderPlacedStepTitle,
        subtitle = orderPlacedStepSubtitle
    )

    finalAnswer.add(orderPlacedStep)

    return finalAnswer
}

fun Transaction.generateDividendReceiptScreenArguments(
    context: Context,
    userFullName: String?,
    userCompanyEntity: String?,
    currentAsset: Asset?,
    userLocale: Locale,
    etfProviders: List<ETFProvider>?
): ConfirmationReceiptScreenArgs {
    val title = context.resources.getString(
        R.string.confirmation_receipt_title,
        currentAsset?.tickerWithCurrency,
        currentAsset?.title
    )

    val iconDrawableRes = currentAsset?.getIconDrawableRes(context)
    val iconURI = currentAsset?.generateProviderLogoURI(etfProviders)

    val amount = generateFormattedDisplayAmount(
        userLocale = userLocale,
        cashFlowSign = null
    )

    val date = generateFormattedDisplayDate(
        stringPattern = "dd MMM yy"
    )

    val time = generateFormattedDisplayDate(
        stringPattern = "hh:mm:ss"
    )

    val stringRes = if (userCompanyEntity != "WEALTHYHOOD_EUROPE") {
        R.string.receipt_firm
    } else R.string.receipt_firm_eu

    val sideDescription = context.resources.getString(R.string.dividend)
    val isin = currentAsset?.isin
    val firm = context.resources.getString(stringRes)
    val color = ContextCompat.getColor(context, R.color.wealthyhood_burple)

    return ConfirmationReceiptScreenArgs(
        receiptType = ConfirmationReceiptTypeEnum.DIVIDEND,
        orderID = null,
        displayOrderID = null,
        title = title,
        iconDrawableRes = iconDrawableRes,
        iconURI = iconURI,
        shouldShowFirstInfoColumnOnly = true,
        amount = amount,
        date = date,
        time = time,
        shares = null,
        perShare = null,
        commission = null,
        fxRate = null,
        sideDescription = sideDescription,
        isin = isin,
        firm = firm,
        color = color,
        userFullName = userFullName
    )
}
