package com.wealthyhood.wealthyhood.extensions

import android.content.Context
import android.content.Intent
import com.wealthyhood.wealthyhood.model.ScreenEnum
import com.wealthyhood.wealthyhood.ui.authentication.getstarted.GetStartedActivity
import com.wealthyhood.wealthyhood.ui.authentication.pin.PinActivity
import com.wealthyhood.wealthyhood.ui.authentication.pinsetup.PinSetupActivity
import com.wealthyhood.wealthyhood.ui.authentication.setupbiometrics.BiometricsSetupActivity
import com.wealthyhood.wealthyhood.ui.closingaccount.ClosingAccountActivity
import com.wealthyhood.wealthyhood.ui.forceupdate.ForceUpdateActivity
import com.wealthyhood.wealthyhood.ui.kycincomplete.KYCIncompleteActivity
import com.wealthyhood.wealthyhood.ui.main.MainActivity
import com.wealthyhood.wealthyhood.ui.notificationspermission.NotificationsPermissionActivity
import com.wealthyhood.wealthyhood.ui.plans.PlansActivity
import com.wealthyhood.wealthyhood.ui.prefetchwaiting.PreFetchWaitingActivity
import com.wealthyhood.wealthyhood.ui.referralcode.ReferralCodeActivity
import com.wealthyhood.wealthyhood.ui.verification.successful.VerificationSuccessfulActivity
import com.wealthyhood.wealthyhood.ui.verificationpending.VerificationPendingActivity
import com.wealthyhood.wealthyhood.ui.waitinglist.WaitingListActivity
import com.wealthyhood.wealthyhood.ui.wealthybitessignup.WealthyBitesSignUpActivity
import com.wealthyhood.wealthyhood.ui.welcome.WelcomeActivity
import com.wealthyhood.wealthyhood.ui.welcomewithreferral.WelcomeWithReferralActivity

fun ScreenEnum.generateIntent(context: Context): Intent? {
    return when (this) {
        ScreenEnum.ForceUpdate -> ForceUpdateActivity.newIntent(context)
        ScreenEnum.GetStarted -> GetStartedActivity.newIntent(context)
        ScreenEnum.NotificationsPermission -> NotificationsPermissionActivity.newIntent(
            context = context
        )

        ScreenEnum.Pin -> PinActivity.newIntent(
            context = context,
            shouldUseScreenChooser = true
        )

        ScreenEnum.PinSetup -> PinSetupActivity.newIntent(
            context = context,
            pin = null
        )

        ScreenEnum.BiometricsSetup -> BiometricsSetupActivity.newIntent(
            context = context,
            shouldUseScreenChooser = true
        )

        ScreenEnum.Plans -> PlansActivity.newIntent(
            context = context,
            shouldShowCloseButton = false,
            selectedPriceAPIKey = null
        )

        ScreenEnum.Welcome -> WelcomeActivity.newIntent(context)
        ScreenEnum.RedeemReferralCode -> ReferralCodeActivity.newIntent(context)
        ScreenEnum.WelcomeWithReferral -> WelcomeWithReferralActivity.newIntent(context)
        ScreenEnum.Dashboard -> MainActivity.newIntent(context)
        ScreenEnum.ClosingAccount -> ClosingAccountActivity.newIntent(context)
        ScreenEnum.Undefined -> null
        ScreenEnum.VerificationCompleted -> VerificationSuccessfulActivity.newIntent(context)
        ScreenEnum.VerificationPending -> VerificationPendingActivity.newIntent(context)
        ScreenEnum.WealthyBitesSignUp -> WealthyBitesSignUpActivity.newIntent(context)
        ScreenEnum.PreFetchWaiting -> PreFetchWaitingActivity.newIntent(context)

        is ScreenEnum.KYCIncomplete -> KYCIncompleteActivity.newIntent(
            context = context,
            screenArgs = this.args
        )

        is ScreenEnum.WaitingList -> WaitingListActivity.newIntent(context)
    }
}
