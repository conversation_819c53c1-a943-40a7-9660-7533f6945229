package com.wealthyhood.wealthyhood.extensions

import android.content.Context
import androidx.annotation.ColorInt
import androidx.annotation.ColorRes
import androidx.core.content.ContextCompat
import com.wealthyhood.wealthyhood.R
import com.wealthyhood.wealthyhood.database.Asset
import com.wealthyhood.wealthyhood.domain.AssetCategoryEnum
import com.wealthyhood.wealthyhood.domain.InfoRowEnum
import com.wealthyhood.wealthyhood.domain.generateFormattedExecutionWindow
import com.wealthyhood.wealthyhood.model.ConfirmationReceiptScreenArgs
import com.wealthyhood.wealthyhood.model.ConfirmationReceiptTypeEnum
import com.wealthyhood.wealthyhood.model.InfoRow
import com.wealthyhood.wealthyhood.model.OrderReviewFooterProperties
import com.wealthyhood.wealthyhood.model.StatusEnum
import com.wealthyhood.wealthyhood.service.ETFProvider
import com.wealthyhood.wealthyhood.service.Order
import com.wealthyhood.wealthyhood.service.Transaction
import com.wealthyhood.wealthyhood.service.TransactionPreview
import com.wealthyhood.wealthyhood.ui.myaccount.transactions.TransactionsHelper
import java.util.Locale

private fun Order.generateTotalInfoRow(
    context: Context,
    currencyISOCode: String?,
    userLocale: Locale,
    @ColorInt defaultColor: Int
): InfoRow? {
    if (displayAmount == null) return null

    val amountRes = if (side == "Buy") {
        R.string.amount
    } else {
        R.string.amount_est
    }

    return InfoRow(
        id = "totalInfoRow",
        label = context.resources.getString(amountRes),
        shouldShowInfoButton = false,
        value = generateFormattedDisplayAmount(
            currencyISOCode = currencyISOCode,
            userLocale = userLocale,
            cashFlowSign = null
        ),
        secondaryValue = null,
        shouldShowSwitch = false,
        isSwitchOn = false,
        switchUpdateTimestamp = null,
        valueTextColor = defaultColor,
        hasSeparator = true
    )
}

private fun generateCashbackInfoRow(
    context: Context,
    @ColorInt defaultColor: Int,
    shouldShowCashback: Boolean,
    transactionPreview: TransactionPreview?,
    currencyISOCode: String?,
    userLocale: Locale
): InfoRow? {
    if (!shouldShowCashback) return null
    val cashback = transactionPreview?.cashback ?: return null

    val value = cashback.generateFormattedCurrency(
        currencyISOCode = currencyISOCode,
        locale = userLocale
    )

    return InfoRow(
        id = InfoRowEnum.CASHBACK.rawValue,
        label = context.resources.getString(R.string.cashback_label),
        shouldShowInfoButton = true,
        value = value,
        secondaryValue = null,
        shouldShowSwitch = false,
        isSwitchOn = false,
        switchUpdateTimestamp = null,
        valueTextColor = defaultColor,
        hasSeparator = true
    )
}

private fun Order.generateNumberOfSharesInfoRow(
    context: Context,
    @ColorInt defaultColor: Int,
    userLocale: Locale
): InfoRow? {
    if (displayQuantity == null) return null

    val sharesRes = if (side == "Buy") {
        R.string.shares_est
    } else {
        R.string.shares
    }

    return InfoRow(
        id = "sharesInfoRow",
        label = context.resources.getString(sharesRes),
        shouldShowInfoButton = false,
        value = generateFormattedDisplayQuantity(userLocale),
        secondaryValue = null,
        shouldShowSwitch = false,
        isSwitchOn = false,
        switchUpdateTimestamp = null,
        valueTextColor = defaultColor,
        hasSeparator = true
    )
}

private fun Order.generateCommissionInfoRow(
    context: Context,
    @ColorInt defaultColor: Int,
    userLocale: Locale
): InfoRow? {
    val value = generateFormattedCommission(userLocale) ?: return null

    val infoRowID = if (assetCategory == AssetCategoryEnum.STOCK.rawValue) {
        InfoRowEnum.STOCK_COMMISSION.rawValue
    } else {
        if (side == "Buy") {
            InfoRowEnum.ETF_BUY_COMMISSION.rawValue
        } else {
            InfoRowEnum.ETF_SELL_COMMISSION.rawValue
        }
    }

    return InfoRow(
        id = infoRowID,
        label = context.resources.getString(R.string.commission),
        shouldShowInfoButton = true,
        value = value,
        secondaryValue = null,
        shouldShowSwitch = false,
        isSwitchOn = false,
        switchUpdateTimestamp = null,
        valueTextColor = defaultColor,
        hasSeparator = true
    )
}

private fun Order.generateFXRateInfoRow(
    context: Context,
    currencyISOCode: String?,
    userLocale: Locale,
    @ColorInt defaultColor: Int,
    transactionPreview: TransactionPreview?
): InfoRow? {
    val orderFXRate = displayExchangeRate?.generateFXRate(
        currencyISOCode = currencyISOCode,
        userLocale = userLocale,
        rateSeparator = " ≈ "
    )

    val previewFXRate = transactionPreview?.foreignCurrencyRates?.generateFXRate(
        userCurrencyISOCode = currencyISOCode,
        userLocale = userLocale
    )

    val finalFXRate = orderFXRate ?: previewFXRate ?: return null

    return InfoRow(
        id = InfoRowEnum.FX_RATE.rawValue,
        label = context.resources.getString(R.string.fx_rate_info_row_label),
        shouldShowInfoButton = true,
        value = finalFXRate,
        secondaryValue = null,
        shouldShowSwitch = false,
        isSwitchOn = false,
        switchUpdateTimestamp = null,
        valueTextColor = defaultColor,
        hasSeparator = true
    )
}

private fun Order.generateLatestPriceInfoRow(
    context: Context,
    @ColorInt defaultColor: Int,
    tradedPrice: Float?,
    tradedCurrency: String?,
    userLocale: Locale
): InfoRow? {
    val cancelStatuses = listOf(
        TransactionsHelper.OrderStatus.Cancelled.rawValue,
        TransactionsHelper.OrderStatus.Rejected.rawValue
    )

    if (cancelStatuses.contains(status)) return null
    if (tradedPrice == null) return null

    val value = tradedPrice.generateFormattedCurrency(
        currencyISOCode = tradedCurrency,
        locale = userLocale
    )

    return InfoRow(
        id = "latestPriceInfoRow",
        label = context.resources.getString(R.string.latest_price),
        shouldShowInfoButton = false,
        value = value,
        secondaryValue = null,
        shouldShowSwitch = false,
        isSwitchOn = false,
        switchUpdateTimestamp = null,
        valueTextColor = defaultColor,
        hasSeparator = true
    )
}

private fun Order.generateDateInfoRow(
    context: Context,
    @ColorInt defaultColor: Int
): InfoRow? {
    val cancelStatuses = listOf(
        TransactionsHelper.OrderStatus.Cancelled.rawValue,
        TransactionsHelper.OrderStatus.Rejected.rawValue
    )

    if (!cancelStatuses.contains(status)) return null
    val formattedDate = generateFormattedDisplayDate("dd MMM yy") ?: return null

    return InfoRow(
        id = "dateInfoRow",
        label = context.resources.getString(R.string.date),
        shouldShowInfoButton = false,
        value = formattedDate,
        secondaryValue = null,
        shouldShowSwitch = false,
        isSwitchOn = false,
        switchUpdateTimestamp = null,
        valueTextColor = defaultColor,
        hasSeparator = true
    )
}

fun Order.getStatusEnum(): StatusEnum? {
    return if (isMatched == true) StatusEnum.SETTLED
    else status?.convertToStatusEnum()
}

private fun Order.generateStatusInfoRow(context: Context): InfoRow {
    val statusEnum = getStatusEnum()

    val value = statusEnum?.generateStatusText(context)
    val valueTextColor = statusEnum?.generateReceiptStatusTextColor(context)

    return InfoRow(
        id = "statusInfoRow",
        label = context.resources.getString(R.string.status),
        shouldShowInfoButton = false,
        value = value,
        secondaryValue = null,
        shouldShowSwitch = false,
        isSwitchOn = false,
        switchUpdateTimestamp = null,
        valueTextColor = valueTextColor,
        hasSeparator = false
    )
}

fun Order.generateInfoRows(
    context: Context,
    tradedPrice: Float?,
    tradedCurrency: String?,
    currencyISOCode: String?,
    userLocale: Locale,
    transactionPreview: TransactionPreview? = null,
    shouldShowCashback: Boolean
): List<InfoRow> {
    // FIXME: Σε ένα AssetTransaction, το Transaction με το μοναδικό Order που κουβαλάει έχουν
    //  διαφορετικά status και display date. Μήπως πρέπει να φτιάξουμε ξεχωριστό function όπως
    //  κάναμε και με το TransactionsHelper.generateOrderTransaction();

    val finalAnswer = mutableListOf<InfoRow>()

    val defaultColor = ContextCompat.getColor(context, R.color.wealthyhood_dark_blue)

    val totalInfoRow = generateTotalInfoRow(
        context = context,
        currencyISOCode = currencyISOCode,
        userLocale = userLocale,
        defaultColor = defaultColor
    )

    val numberOfSharesInfoRow = generateNumberOfSharesInfoRow(
        context = context,
        defaultColor = defaultColor,
        userLocale = userLocale
    )

    if (side == "Buy") {
        totalInfoRow?.let { finalAnswer.add(it) }
        numberOfSharesInfoRow?.let { finalAnswer.add(numberOfSharesInfoRow) }
    } else {
        numberOfSharesInfoRow?.let { finalAnswer.add(numberOfSharesInfoRow) }
        totalInfoRow?.let { finalAnswer.add(it) }
    }

    generateCashbackInfoRow(
        context = context,
        defaultColor = defaultColor,
        shouldShowCashback = shouldShowCashback,
        transactionPreview = transactionPreview,
        currencyISOCode = currencyISOCode,
        userLocale = userLocale
    )?.let {
        finalAnswer.add(it)
    }

    generateCommissionInfoRow(
        context = context,
        defaultColor = defaultColor,
        userLocale = userLocale
    )?.let {
        finalAnswer.add(it)
    }

    generateFXRateInfoRow(
        context = context,
        currencyISOCode = currencyISOCode,
        userLocale = userLocale,
        defaultColor = defaultColor,
        transactionPreview = transactionPreview
    )?.let {
        finalAnswer.add(it)
    }

    generateLatestPriceInfoRow(
        context = context,
        defaultColor = defaultColor,
        tradedPrice = tradedPrice,
        tradedCurrency = tradedCurrency,
        userLocale = userLocale
    )?.let {
        finalAnswer.add(it)
    }

    generateExecutionWindowInfoRow(
        context = context,
        valueTextColor = defaultColor,
    )?.let {
        finalAnswer.add(it)
    }

    generateDateInfoRow(
        context = context,
        defaultColor = defaultColor
    )?.let {
        finalAnswer.add(it)
    }

    val statusInfoRow = generateStatusInfoRow(
        context = context
    )

    finalAnswer.add(statusInfoRow)

    return finalAnswer
}

private fun Order.generateExecutionWindowInfoRow(
    context: Context,
    @ColorInt valueTextColor: Int
): InfoRow? {
    val cancelStatuses = listOf(
        TransactionsHelper.OrderStatus.Cancelled.rawValue,
        TransactionsHelper.OrderStatus.Rejected.rawValue
    )

    if (cancelStatuses.contains(status)) return null

    val formattedExecutionWindow = generateFormattedExecutionWindow(context) ?: return null

    val infoRowID: String
    val label: String

    if (assetCategory == AssetCategoryEnum.STOCK.rawValue) {
        infoRowID = InfoRowEnum.STOCKS_EXECUTION_WINDOW.rawValue
        label = context.resources.getString(R.string.stocks_trading_window_label)
    } else {
        infoRowID = InfoRowEnum.ETFS_EXECUTION_WINDOW.rawValue
        label = context.resources.getString(R.string.etfs_trading_window_label)
    }

    return InfoRow(
        id = infoRowID,
        label = label,
        shouldShowInfoButton = true,
        value = formattedExecutionWindow,
        secondaryValue = null,
        shouldShowSwitch = false,
        isSwitchOn = false,
        switchUpdateTimestamp = null,
        valueTextColor = valueTextColor,
        hasSeparator = true
    )
}

private fun Order.generateFormattedExecutionWindow(context: Context): String? {
    val executionWindowData = executionWindow ?: return null

    return executionWindowData.generateFormattedExecutionWindow(
        context = context,
        hasExecutionStarted = (hasExecutionStarted == true)
    )
}

fun Order.generateFormattedDisplayDate(stringPattern: String): String? {
    return displayDate?.generateFormattedDate(
        dateStringPattern = "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'",
        formattedStringPattern = stringPattern
    )
}

fun Order.generateFormattedDisplayAmount(
    currencyISOCode: String?,
    userLocale: Locale,
    cashFlowSign: Int?
): String? {
    val amount = displayAmount ?: return null
    val realAmount = amount / 100f

    val finalCurrencyISOCode = consideration?.currency ?: currencyISOCode

    val formattedAmount = realAmount.generateFormattedCurrency(
        currencyISOCode = finalCurrencyISOCode,
        locale = userLocale
    )

    return if (cashFlowSign == null) formattedAmount
    else if (cashFlowSign > 0) "+$formattedAmount"
    else "-$formattedAmount"
}

fun Order.generateFormattedLocalisedTime(stringPattern: String): String? {
    return displayDate?.convertUTCToLocal(
        utcPattern = "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'",
        localPattern = stringPattern
    )
}

fun Order.generateOrderReviewFooterProperties(context: Context): OrderReviewFooterProperties {
    val resources = context.resources

    val buttonBackgroundColor = ContextCompat.getColor(context, R.color.system_alerts_danger_color)

    return OrderReviewFooterProperties(
        topPadding = resources.getDimensionPixelSize(R.dimen.spacing_40),
        hasOrdersButton = false,
        hasButton = (isCancellable == true),
        buttonTitle = resources.getString(R.string.cancel_order),
        buttonBackgroundColor = buttonBackgroundColor,
        buttonElevationRes = null,
        hasDisclaimer = false,
        disclaimerText = null,
        isDisclaimerCentered = false
    )
}

fun Order.generateOrderTypeText(context: Context): String? {
    val resources = context.resources

    return when (side) {
        "Buy" -> resources.getString(R.string.buy) // TODO: Capitalized?
        "Sell" -> resources.getString(R.string.sell) // TODO: Capitalized?
        else -> null
    }
}

@ColorRes
fun Order.generateSideColor(): Int {
    return if (side == "Buy") {
        R.color.system_alerts_success_color
    } else {
        R.color.system_alerts_danger_color
    }
}

@Deprecated("Use the new generateTypeTextColor function below")
@ColorInt
fun Order.generateOrderTypeTextColor(context: Context, parentTransaction: Transaction): Int {
    // TODO: Confirm that this function returns the desired color

    val cancelStatuses = listOf(
        TransactionsHelper.OrderStatus.Cancelled.rawValue,
        TransactionsHelper.OrderStatus.Rejected.rawValue
    )

    if (cancelStatuses.contains(status)) {
        return ContextCompat.getColor(context, R.color.wealthyhood_dark_blue_60)
    }

    return when (side) {
        "Buy" -> ContextCompat.getColor(context, R.color.system_alerts_success_color)
        "Sell" -> ContextCompat.getColor(context, R.color.system_alerts_danger_color)
        else -> ContextCompat.getColor(context, R.color.wealthyhood_dark_blue_60)
    }
}

@ColorRes
fun Order.generateTypeTextColorRes(statusEnum: StatusEnum?): Int {
    if (statusEnum == null ||
        statusEnum == StatusEnum.CANCELLED ||
        statusEnum == StatusEnum.REJECTED
    ) {
        return R.color.base
    }

    return when (side) {
        "Buy" -> R.color.green_50
        "Sell" -> R.color.red_50
        else -> R.color.base
    }
}

@ColorInt
fun Order.generateTypeTextColor(context: Context, statusEnum: StatusEnum?): Int {
    val colorRes = generateTypeTextColorRes(statusEnum)
    return ContextCompat.getColor(context, colorRes)
}

fun Order.generateFormattedDisplayQuantity(userLocale: Locale): String? {
    if (displayQuantity == null) return null

    return displayQuantity.generateFormattedNumber(
        minimumFractionDigits = 4,
        maximumFractionDigits = 4,
        locale = userLocale
    )
}

fun Order.generateTitle(allAssets: List<Asset>?): String? {
    return allAssets?.find { it.isin == isin }?.title
}

fun Order.generateReviewTitle(
    context: Context,
    assetTitle: String?
): String? {
    if (assetTitle == null) return null

    val titleRes = if (side == "Buy") {
        R.string.buy_with_etf_name
    } else {
        R.string.sell_with_etf_name
    }

    return context.resources.getString(titleRes, assetTitle)
}

fun Order.generateETFOrderReceiptDialogArguments(
    context: Context,
    currentAsset: Asset?,
    isUserEU: Boolean,
    userFullName: String?,
    currencyISOCode: String?,
    userLocale: Locale,
    etfProviders: List<ETFProvider>?
): ConfirmationReceiptScreenArgs {
    val title = context.resources.getString(
        R.string.confirmation_receipt_title,
        currentAsset?.tickerWithCurrency,
        currentAsset?.title
    )

    val colorRes = generateSideColor()
    val color = ContextCompat.getColor(context, colorRes)

    val commission = generateFormattedCommission(userLocale = userLocale)

    val fxRate = displayExchangeRate?.generateFXRate(
        currencyISOCode = currencyISOCode,
        userLocale = userLocale,
        rateSeparator = " = "
    )

    val firmRes = if (!isUserEU) {
        R.string.receipt_firm
    } else R.string.receipt_firm_eu

    val firm = context.resources.getString(firmRes)

    return ConfirmationReceiptScreenArgs(
        receiptType = ConfirmationReceiptTypeEnum.ORDER,
        orderID = id,
        displayOrderID = displayUserFriendlyID,
        title = title,
        iconDrawableRes = currentAsset?.getIconDrawableRes(context),
        iconURI = currentAsset?.generateProviderLogoURI(etfProviders),
        shouldShowFirstInfoColumnOnly = false,
        amount = generateFormattedDisplayAmount(
            currencyISOCode = currencyISOCode,
            userLocale = userLocale,
            cashFlowSign = null
        ),
        date = generateFormattedLocalisedTime("dd MMM yyyy"),
        time = generateFormattedLocalisedTime("HH:mm:ss"),
        shares = generateFormattedDisplayQuantity(userLocale),
        perShare = generateFormattedUnitPrice(userLocale = userLocale),
        commission = commission,
        fxRate = fxRate,
        sideDescription = side,
        isin = isin,
        firm = firm,
        color = color,
        userFullName = userFullName
    )
}

private fun Order.generateFormattedCommission(userLocale: Locale): String? {
    estimatedRealTimeCommission?.let {
        val currencyISOCode = consideration?.currency

        return estimatedRealTimeCommission.generateFormattedCurrency(
            currencyISOCode = currencyISOCode,
            locale = userLocale
        )
    }

    if (fees == null) {
        return generateFormattedZeroCommission(userLocale)
    }

    val currencyISOCode = fees.generateCommissionCurrencyCode()

    return fees.generateFormattedTotalCommission(
        userLocale = userLocale,
        currencyISOCode = currencyISOCode
    )
}

private fun Order.generateFormattedZeroCommission(userLocale: Locale): String? {
    val currencyISOCode = consideration?.currency

    return 0.0.generateFormattedCurrency(
        currencyISOCode = currencyISOCode,
        locale = userLocale
    )
}
