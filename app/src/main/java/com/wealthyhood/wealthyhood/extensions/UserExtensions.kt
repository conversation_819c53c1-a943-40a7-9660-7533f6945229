package com.wealthyhood.wealthyhood.extensions

import android.content.Context
import androidx.annotation.DrawableRes
import com.auth0.android.result.Credentials
import com.wealthyhood.wealthyhood.R
import com.wealthyhood.wealthyhood.domain.PlanBannerProperties
import com.wealthyhood.wealthyhood.model.BasePlan
import com.wealthyhood.wealthyhood.model.LabStatusEnum
import com.wealthyhood.wealthyhood.model.PortfolioConversionStatusEnum
import com.wealthyhood.wealthyhood.model.VerificationStatusEnum
import com.wealthyhood.wealthyhood.repository.AuthRepository
import com.wealthyhood.wealthyhood.repository.PlansRepository
import com.wealthyhood.wealthyhood.repository.PreferencesRepository
import com.wealthyhood.wealthyhood.service.Plan
import com.wealthyhood.wealthyhood.service.Portfolio
import com.wealthyhood.wealthyhood.service.User
import io.sentry.Sentry
import java.util.TimeZone

fun User.saveEssentialUserInfo(preferencesRepository: PreferencesRepository): Boolean {
    if (id == null || email == null) {
        return false
    }

    // Το currency δεν είναι mandatory γιατί θα είναι null μέχρι ο χρήστης
    // να επιλέξει residency country.

    preferencesRepository.putUserID(id)
    preferencesRepository.putUserEmail(email)
    preferencesRepository.putUserCurrency(currency)
    preferencesRepository.putUserResidencyCountry(residencyCountry)
    preferencesRepository.putUserIBAN(getWalletAsObject()?.iban)
    preferencesRepository.putUserCompanyEntity(companyEntity)

    val isRealtimeETFExecutionEnabled = (isRealtimeETFExecutionEnabled == true)
    preferencesRepository.putUserIsRealtimeETFExecutionEnabled(isRealtimeETFExecutionEnabled)

    val isRoboAdvisorEnabled = (isRoboAdvisorEnabled == true)
    preferencesRepository.putUserIsRoboAdvisorEnabled(isRoboAdvisorEnabled)

    preferencesRepository.putLastLoginMail(email)

    // Από τη στιγμή που δει το invested dashboard, δεν υπάρχει περίπτωση να γυρίσει στο uninvested.

    val shouldShowInvestedDashboard = shouldShowInvestedDashboard()

    if (shouldShowInvestedDashboard) {
        preferencesRepository.putShouldShowInvestedDashboardWithoutChecking(true)
    }

    return true
}

fun User.isEU(): Boolean {
    return (companyEntity == "WEALTHYHOOD_EUROPE")
}

fun User.isUK(): Boolean {
    return (companyEntity == "WEALTHYHOOD_UK")
}

fun User.saveLoginInfo(
    credentials: Credentials,
    preferencesRepository: PreferencesRepository,
    authRepository: AuthRepository
): Boolean {
    // Παίρνουμε το lastLoginMail πριν καλέσουμε την saveEssentialUserInfo
    // που θα βάλει το καινούριο.

    val previousUserMail = preferencesRepository.getLastLoginMail()

    val essentialUserInfoSaved = saveEssentialUserInfo(preferencesRepository)

    if (!essentialUserInfoSaved) {
        Sentry.captureMessage("saveEssentialUserInfo failed: one of the required fields is null")

        return false
    }

    val credentialsSaved = authRepository.saveAuth0Credentials(credentials)

    if (!credentialsSaved) {
        Sentry.captureMessage("Credentials could not be saved after successful login")

        return false
    }

    // Όταν αλλάζει ο χρήστης θέλουμε να κάνουμε reset το PIN και τα notification preferences

    if (previousUserMail != email) {
        preferencesRepository.deletePreferencesRelatedToUserSwitching()
    }

    return true
}

fun User?.isNull() = this == null

fun User.shouldShowInvestedDashboard(): Boolean {
    val status = portfolioConversionStatus ?: return false

    val investedStatuses = listOf(
        PortfolioConversionStatusEnum.IN_PROGRESS.rawValue,
        PortfolioConversionStatusEnum.FULLY_WITHDRAWN.rawValue,
        PortfolioConversionStatusEnum.COMPLETED.rawValue
    )

    return investedStatuses.contains(status)
}

fun User.generateFullNameAbbreviation(): String? {
    return generateFullName()?.extractCapitalizedInitialLetters()
}

fun User.generateFullName(): String? {
    val parts = mutableListOf<String>()

    firstName?.let {
        parts.add(it)
    }

    lastName?.let {
        parts.add(it)
    }

    return if (parts.isEmpty()) null else parts.joinToString(" ")
}

fun User.convertDateOfBirthToUTCLong(): Long? {
    return dateOfBirth?.generateDateUsingPattern(
        pattern = "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'",
        timeZone = TimeZone.getTimeZone("UTC")
    )?.time
}

fun User.convertCanSendGiftUntilToUTCLong(): Long? {
    return canSendGiftUntil?.generateDateUsingPattern(
        pattern = "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'",
        timeZone = TimeZone.getTimeZone("UTC")
    )?.time
}

// TODO: Use this extension throughout the app.
fun User.isVerified(portfolio: Portfolio?): Boolean {
    portfolio?.let {
        if (it.getOwnerID() != id) {
            // TODO: Delete this check
            return false
        }

        return kycStatus == "passed" && portfolio.wealthKernel?.portfolioID != null
    }

    return false
}

fun User.isVerifying(portfolio: Portfolio?): Boolean {
    portfolio?.let {
        if (it.getOwnerID() != id) {
            // TODO: Delete this check
            return false
        }

        return (kycStatus == "pending" && hasSubmittedRequiredInfo == true && hasAcceptedTerms == true) ||
                (kycStatus == "passed" && !portfolio.hasSubmittedPortfolioToBroker())
    }

    return false
}

fun User.getVerificationStatus(portfolio: Portfolio?): VerificationStatusEnum {
    return if (isVerified(portfolio)) {
        VerificationStatusEnum.VERIFIED
    } else if (kycStatus == "failed") {
        VerificationStatusEnum.VERIFICATION_FAILED
    } else if (isVerifying(portfolio)) {
        VerificationStatusEnum.VERIFYING
    } else {
        VerificationStatusEnum.UNVERIFIED
    }
}

fun User.getLabStatus(): LabStatusEnum {
    return when (portfolioConversionStatus) {
        PortfolioConversionStatusEnum.IN_PROGRESS.rawValue -> {
            LabStatusEnum.PROCESSING_FIRST_INVESTMENT
        }

        PortfolioConversionStatusEnum.COMPLETED.rawValue -> {
            LabStatusEnum.INVESTED
        }

        else -> {
            LabStatusEnum.UNINVESTED
        }
    }
}

fun User.generatePlanBannerProperties(
    context: Context,
    plansRepository: PlansRepository
): PlanBannerProperties? {
    val priceAPIKey = subscription?.price ?: return null

    val plan = plansRepository.getAllPlans().find {
        it.keyName == priceAPIKey
    } ?: return null

    val planConfig = plansRepository.getAllBasePlanConfigs(context).find {
        it.basePlanID == plan.basePlanID
    } ?: return null

    val resources = context.resources

    val creationFormattedDate = subscription.createdAt?.generateFormattedDate(
        dateStringPattern = "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'",
        formattedStringPattern = "dd MMM yyyy"
    )

    val expirationFormattedDate = subscription.expiration?.date?.generateFormattedDate(
        dateStringPattern = "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'",
        formattedStringPattern = "dd MMM yyyy"
    )

    val renewalFormattedDate = subscription.nextChargeAt?.generateFormattedDate(
        dateStringPattern = "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'",
        formattedStringPattern = "dd MMM yyyy"
    )

    val subtitle: String?
    var buttonTitle: String?

    if (priceAPIKey == Plan.BASIC_PLAN_PRICE_API_KEY) {
        buttonTitle = resources.getString(R.string.billing_plan_upgrade_button_title)

        subtitle = creationFormattedDate?.let {
            resources.getString(
                R.string.billing_beginner_plan_subtitle,
                it
            )
        }
    } else if (plan.isLifetime()) {
        buttonTitle = null

        subtitle = resources.getString(R.string.lifetime_label)
    } else if (expirationFormattedDate != null) {
        buttonTitle = resources.getString(R.string.billing_plan_renew_button_title)

        subtitle = resources.getString(
            R.string.billing_plus_plan_expires_subtitle,
            expirationFormattedDate
        )
    } else {
        buttonTitle = null

        subtitle = renewalFormattedDate?.let {
            resources.getString(
                R.string.billing_plus_plan_renews_subtitle,
                it
            )
        }
    }

    if (subscription.category == "DirectDebitSubscription") {
        // The user should not be able to renew his plan in this case.
        buttonTitle = null
    }

    // FIXME: Does not work for annual plans

    val monthlyPriceAPIKey = when (priceAPIKey) {
        Plan.LIFETIME_PLUS_PLAN_PRICE_API_KEY -> {
            Plan.PLUS_PLAN_PRICE_API_KEY
        }

        Plan.LIFETIME_GOLD_PLAN_PRICE_API_KEY -> {
            Plan.GOLD_PLAN_PRICE_API_KEY
        }

        Plan.SWEAT_COIN_LIFETIME_GOLD_PLAN_PRICE_API_KEY -> {
            Plan.GOLD_PLAN_PRICE_API_KEY
        }

        else -> priceAPIKey
    }

    val monthlyPlan = plansRepository.getAllPlans().find {
        it.keyName == monthlyPriceAPIKey
    } ?: return null

    @DrawableRes val iconDrawableRes = when (plan.basePlanID) {
        BasePlan.BASIC_PLAN_ID -> R.drawable.basic_plan_icon
        BasePlan.PLUS_PLAN_ID -> R.drawable.plus_plan_icon
        else -> R.drawable.gold_plan_icon
    }

    return PlanBannerProperties(
        backgroundDrawableRes = planConfig.backgroundDrawableRes,
        iconDrawableRes = iconDrawableRes,
        title = monthlyPlan.title ?: "", // FIXME: Check this
        subtitle = subtitle,
        buttonTitle = buttonTitle
    )
}
