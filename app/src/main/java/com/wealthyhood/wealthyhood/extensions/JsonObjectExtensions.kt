package com.wealthyhood.wealthyhood.extensions

import com.google.gson.Gson
import com.google.gson.JsonObject
import com.google.gson.reflect.TypeToken
import java.util.Locale

fun JsonObject.getString(key: String): String? {
    return try {
        get(key)?.asString
    } catch (e: ClassCastException) {
        e.printStackTrace()
        null
    } catch (e: IllegalStateException) {
        e.printStackTrace()
        null
    }
}

fun JsonObject.getInt(key: String): Int? {
    return try {
        get(key)?.asInt
    } catch (e: ClassCastException) {
        e.printStackTrace()
        null
    } catch (e: IllegalStateException) {
        e.printStackTrace()
        null
    }
}

fun JsonObject.generateFXRate(userCurrencyISOCode: String?, userLocale: Locale): String? {
    val parts = mutableListOf<String>()

    keySet()?.forEach { currencyISOCode ->
        val amount = try {
            get(currencyISOCode)?.asDouble
        } catch (e: IllegalStateException) {
            e.printStackTrace()
            null
        } ?: return@forEach

        val formattedAmount = amount.generateFormattedCurrency(
            currencyISOCode = currencyISOCode,
            maximumFractionDigits = 3,
            shouldRemoveTrailingZeros = false,
            locale = userLocale
        ) ?: return@forEach

        parts.add(formattedAmount)
    }

    return if (parts.isEmpty()) null else {
        1.0.generateFormattedCurrency(
            currencyISOCode = userCurrencyISOCode,
            maximumFractionDigits = 0,
            locale = userLocale
        )?.let {
            parts.add(0, it)
        }

        parts.joinToString(" ≈ ")
    }
}

fun JsonObject.generateMap(): Map<String, Float>? {
    val itemType = object : TypeToken<Map<String, Float>>() {}.type

    return try {
        val jsonString = Gson().toJson(this)
        Gson().fromJson(jsonString, itemType)
    } catch (e: Exception) {
        null
    }
}
