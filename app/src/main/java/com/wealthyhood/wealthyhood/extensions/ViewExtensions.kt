package com.wealthyhood.wealthyhood.extensions

import android.animation.AnimatorSet
import android.animation.ObjectAnimator
import android.content.res.Resources
import android.graphics.Rect
import android.view.Gravity
import android.view.MotionEvent
import android.view.View
import android.view.animation.LinearInterpolator
import android.widget.FrameLayout
import android.widget.TextView
import androidx.core.content.ContextCompat
import androidx.core.view.ViewCompat
import androidx.core.view.WindowInsetsCompat
import com.google.android.material.snackbar.Snackbar
import com.wealthyhood.wealthyhood.R
import com.wealthyhood.wealthyhood.common.SafeClickListener

// Kotlin extension function για ImageView
fun View.bellShakeAnimation(duration: Long = 1000) {
    // Translation animation values
    val translationValues = floatArrayOf(-15f, 15f, -15f, 15f, -9f, 9f, -6f, 6f, 0f)

    // Rotation animation values (σε degrees)
    val rotationValues = floatArrayOf(-5f, 5f, -5f, 5f, -3f, 3f, -2f, 2f, 0f)

    // Create translation animation
    val translationAnimator =
        ObjectAnimator.ofFloat(this, "translationX", *translationValues).apply {
            this.duration = duration
            interpolator = LinearInterpolator()
        }

    // Create rotation animation
    val rotationAnimator = ObjectAnimator.ofFloat(this, "rotation", *rotationValues).apply {
        this.duration = duration
        interpolator = LinearInterpolator()
    }

    // Combine animations
    val animatorSet = AnimatorSet().apply {
        playTogether(translationAnimator, rotationAnimator)
    }

    animatorSet.start()
}

fun View.animateToAlpha(alpha: Float) {
    animate()
        .alpha(alpha)
        .setDuration(100)
        .setInterpolator(android.view.animation.AccelerateDecelerateInterpolator())
        .start()
}

fun View.setCenter(centerX: Float, centerY: Float) {
    val offsetX = centerX - (width / 2f)
    val offsetY = centerY - (height / 2f)

    x = offsetX
    y = offsetY
}

fun View.setCenterY(centerY: Float) {
    val offsetY = centerY - (height / 2f)

    y = offsetY
}

fun View.isUnderCoordinates(event: MotionEvent): Boolean {
    val out = IntArray(2)
    getLocationOnScreen(out)

    val x = out[0]
    val y = out[1]
    val w = width
    val h = height

    if (event.rawX < x || event.rawX > x + w || event.rawY < y || event.rawY > y + h) {
        return false
    }

    return true
}

fun View?.isOnScreen(): Boolean {
    if (this == null) {
        return false
    }

    if (!isShown) {
        return false
    }

    val actualPosition = Rect()
    getGlobalVisibleRect(actualPosition)

    val screenWidth = Resources.getSystem().displayMetrics.widthPixels
    val screenHeight = Resources.getSystem().displayMetrics.heightPixels

    val screen = Rect(0, 0, screenWidth, screenHeight)

    return actualPosition.intersect(screen)
}

fun View.showCustomSnack(message: String?) {
    if (message == null) return

    val snack = Snackbar.make(this, message, Snackbar.LENGTH_LONG)

    val snackView = snack.view

    val params = snackView.layoutParams as? FrameLayout.LayoutParams ?: kotlin.run {
        // TODO: Something changed in the underlying implementation of SnackBar.
        //  Inform the server.
        return
    }

    val textView =
        snackView.findViewById<View>(com.google.android.material.R.id.snackbar_text) as? TextView
            ?: kotlin.run {
                // TODO: Something changed in the underlying implementation of SnackBar.
                //  Inform the server.

                return
            }

    params.gravity = Gravity.TOP

    val marginInDp = context.resources.getDimensionPixelSize(R.dimen.spacing_16)

    val windowInsets = ViewCompat.getRootWindowInsets(this)
    val insets = windowInsets?.getInsets(WindowInsetsCompat.Type.systemBars())
    val topInset = insets?.top ?: 0
    val leftInset = insets?.left ?: 0
    val rightInset = insets?.right ?: 0

    params.setMargins(marginInDp + leftInset, marginInDp + topInset, marginInDp + rightInset, 0)

    snackView.layoutParams = params

    textView.gravity = Gravity.CENTER
    textView.textAlignment = View.TEXT_ALIGNMENT_GRAVITY

    snackView.background =
        ContextCompat.getDrawable(context, R.drawable.corner_background)

    snack.show()
}

fun View.isInvisible(isInvisible: Boolean) {
    this.visibility = if (isInvisible) View.INVISIBLE else View.VISIBLE
}

fun View.setOnSafeClickListener(
    onSafeClick: (View) -> Unit
) {
    setOnClickListener(SafeClickListener { v ->
        onSafeClick(v)
    })
}
