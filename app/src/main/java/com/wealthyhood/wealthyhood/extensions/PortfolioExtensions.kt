package com.wealthyhood.wealthyhood.extensions

import com.wealthyhood.wealthyhood.database.Asset
import com.wealthyhood.wealthyhood.repository.PreferencesRepository
import com.wealthyhood.wealthyhood.service.AssetClass
import com.wealthyhood.wealthyhood.service.Portfolio
import com.wealthyhood.wealthyhood.ui.allocationoptions.AllocationOptionsViewModel
import kotlin.math.abs

fun Portfolio.saveEssentialPortfolioInfo(preferencesRepository: PreferencesRepository): Boolean {
    val hasTargetPortfolio = (isTargetAllocationSetup == true)

    preferencesRepository.putPortfolioHasTargetAllocation(hasTargetPortfolio)

    return true
}

fun List<Portfolio>.saveRealPortfolioEssentialInfo(preferencesRepository: PreferencesRepository) {
    val realPortfolio = find { portfolio ->
        portfolio.isReal == true
    }

    realPortfolio?.saveEssentialPortfolioInfo(preferencesRepository)
}

fun Portfolio?.isNull() = this == null

fun Portfolio.getCashForCurrency(currencyISOCode: String?): Double? {
    if (currencyISOCode == null) return null

    return try {
        cash?.get(currencyISOCode)?.asJsonObject?.get("available")?.asDouble
    } catch (e: Exception) {
        null
    }
}

fun Portfolio.getOwnerID(): String? {

    getOwnerAsString()?.let {
        return it
    }

    getOwnerAsUser()?.let {
        return it.id
    }

    return null
}

fun Portfolio.hasSubmittedPortfolioToBroker(): Boolean {
    return this.isSubmittedToBroker == true
}

fun Portfolio.computeTotalAssetClassValue(
    assetClassID: String?,
    allAssetClasses: List<AssetClass>?
): Double {
    var finalAnswer = 0.0

    allAssetClasses?.findPortfolioHoldingsForAssetClass(assetClassID, this)
        ?.forEach { holding ->
            finalAnswer += holding.computeTotalHoldingValue()
        }

    return finalAnswer
}

fun Portfolio.computeTotalAssetTypeValue(
    category: String?,
    allAssets: List<Asset>?
): Double {
    val holdingsForAssetType =
        findPortfolioHoldingsForAssetType(category, allAssets)

    var finalAnswer = 0.0

    holdingsForAssetType?.forEach { holding ->
        finalAnswer += holding.computeTotalHoldingValue()
    }

    return finalAnswer
}

fun Portfolio.findPortfolioHoldingsForAssetType(
    category: String?,
    allAssets: List<Asset>?
): List<Portfolio.Holding>? {
    if (category == null) return null
    val holdings = holdings ?: return null

    val userAssets = holdings.getUserAssetsFromHoldings(allAssets)

    val userAssetIdsForCategory = userAssets?.filter { it.category == category }?.map { it.id }

    return holdings.filter { userAssetIdsForCategory?.contains(it.asset?.commonID) == true }
}

fun Portfolio.computeTotalPortfolioValue(): Double {
    var finalAnswer = 0.0

    this.holdings?.forEach { holding ->
        finalAnswer += holding.computeTotalHoldingValue()
    }

    return finalAnswer
}

fun Portfolio.weightForAssetType(
    assetTypeCategory: String?,
    allAssets: List<Asset>?
): Double {
    val totalPortfolioValue = computeTotalPortfolioValue()
    val totalAssetTypeValue = computeTotalAssetTypeValue(assetTypeCategory, allAssets)

    return try {
        ((totalAssetTypeValue / totalPortfolioValue) * 100.0)
    } catch (e: Exception) {
        // TODO: Log this error state
        0.0
    }
}

fun Portfolio.hasQualifyingBuyCompleted(assetID: String?): Boolean {
    return this.holdings?.find { holding ->
        holding.asset?.commonID == assetID
    } == null
}

fun Portfolio.hasQualifyingSellCompleted(
    assetID: String?,
    amount: Float?
): Boolean {
    val assetQuantity = amount?.toDouble()?.toScaled(digits = 4)

    return this.holdings?.find { holding ->
        val formattedQuantity = holding.quantity
        holding.asset?.commonID == assetID && formattedQuantity == assetQuantity
    } != null
}

fun Portfolio.hasChangesToRealPortfolioFromTarget(): Boolean {
    val holdingsSize = holdings?.size ?: 0
    val initialHoldingsSize = initialHoldingsAllocation?.size ?: 0

    return abs(holdingsSize - initialHoldingsSize) != 0
}

fun Portfolio.shouldShowDotState(preferencesRepository: PreferencesRepository): Boolean {
    return preferencesRepository.getIfHasQualifyingTradeCompleted() && hasChangesToRealPortfolioFromTarget()
}

fun Portfolio?.shouldEnableBuyTargetPortfolioSwitch(): Boolean {
    val hasTargetAllocation = (this?.isTargetAllocationSetup == true)
    val hasHoldings = this?.holdings.isNullOrEmpty().not()

    if (!hasTargetAllocation || !hasHoldings) {
        return false
    }

    return true
}

fun Portfolio?.generateBuyTargetPortfolioSwitchDefaultValue(): Boolean {
    val hasHoldings = this?.holdings.isNullOrEmpty().not()

    if (!hasHoldings) {
        return true
    }

    return false
}

fun Portfolio?.generateDefaultAllocationMethodID(): String {
    val hasHoldings = this?.holdings.isNullOrEmpty().not()

    if (!hasHoldings) {
        return AllocationOptionsViewModel.TARGET_PORTFOLIO_OPTION_ID
    }

    return AllocationOptionsViewModel.MY_INVESTMENTS_OPTION_ID
}
