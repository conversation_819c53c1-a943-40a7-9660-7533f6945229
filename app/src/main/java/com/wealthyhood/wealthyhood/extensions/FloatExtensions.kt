package com.wealthyhood.wealthyhood.extensions

import io.sentry.Sentry
import java.math.BigDecimal
import java.math.RoundingMode
import java.text.NumberFormat
import java.util.Currency
import java.util.Locale
import kotlin.math.ln
import kotlin.math.pow

fun Float.generateFormattedCurrency(
    currencyISOCode: String?,
    minimumFractionDigits: Int? = null,
    maximumFractionDigits: Int = 2,
    shouldRemoveTrailingZeros: Boolean = false,
    locale: Locale
): String? {
    val numberFormat = NumberFormat.getCurrencyInstance(locale)

    var finalMaximumFractionDigits = maximumFractionDigits

    if (shouldRemoveTrailingZeros) {
        // If all fraction digits are zeros, adjust the maximumFractionDigits to exclude them

        val numberInString = this.toScaledString(digits = maximumFractionDigits)

        // FIXME: change the function depending on the locale of the number
        numberInString.split(".").getOrNull(1)?.let { decimals ->
            val decimalsWithoutZeros = decimals.trimEnd { it == '0' }

            if (decimalsWithoutZeros.isEmpty()) {
                finalMaximumFractionDigits = 0
            }
        }
    }

    if (minimumFractionDigits != null) {
        numberFormat.minimumFractionDigits = minimumFractionDigits
    }

    numberFormat.maximumFractionDigits = finalMaximumFractionDigits

    val currency = try {
        Currency.getInstance(currencyISOCode)
    } catch (e: Exception) {
        Sentry.captureException(e)

        null
    } ?: return null

    numberFormat.currency = currency

    val formattedCurrency = try {
        numberFormat.format(this)
    } catch (e: Exception) {
        e.printStackTrace()

        null
    }

    // FIXME: Is there any other way to prevent formatting like US$500?
    return formattedCurrency?.replace("US", "")
}

fun Float.toExactDouble(): Double? {
    return try {
        this.toString().toDouble()
    } catch (e: Exception) {
        e.printStackTrace()

        null
    }
}

fun Float.generateFormattedNumber(
    minimumFractionDigits: Int = 2,
    maximumFractionDigits: Int = 2,
    locale: Locale
): String? {
    val numberFormat = NumberFormat.getNumberInstance(locale)

    numberFormat.minimumFractionDigits = minimumFractionDigits
    numberFormat.maximumFractionDigits = maximumFractionDigits

    return try {
        numberFormat.format(this)
    } catch (e: Exception) {
        e.printStackTrace()

        null
    }
}

fun Float.toScaled(digits: Int = 4, roundingMode: RoundingMode = RoundingMode.HALF_EVEN): Float {
    return BigDecimal.valueOf(this.toDouble()).setScale(digits, roundingMode).toFloat()
}

fun Float.toScaledString(
    digits: Int = 4,
    roundingMode: RoundingMode = RoundingMode.HALF_EVEN
): String {
    // There is a difference between calling Float.toScaled.toString and Float.toScaledString():
    // For example, for 0.3110, the former will result in "0.311" while the later
    // will result in "0.3110". This is because the former calls toFloat()
    // immediately after the scaling.
    return BigDecimal.valueOf(this.toDouble()).setScale(digits, roundingMode).toString()
}

fun Float.formatNumber(
    locale: Locale,
    minimumFractionDigits: Int = 0,
    maximumFractionDigits: Int = 1
): String {
    // This formatter removes the trailing zeros
    val numberFormat = NumberFormat.getNumberInstance(locale)

    numberFormat.minimumFractionDigits = minimumFractionDigits
    numberFormat.maximumFractionDigits = maximumFractionDigits

    if (this < 1000) return numberFormat.format(this)

    val exp = (ln(this) / ln(1000.0)).toInt()

    val value = this / 1000.0.pow(exp.toDouble())

    val formattedValue = numberFormat.format(value)

    return String.format("%s%c", formattedValue, "kMGTPE"[exp - 1])
}

fun Float?.isNull(): Boolean = this == null
