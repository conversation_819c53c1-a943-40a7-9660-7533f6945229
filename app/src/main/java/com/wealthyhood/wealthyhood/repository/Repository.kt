package com.wealthyhood.wealthyhood.repository

import com.wealthyhood.wealthyhood.BuildConfig
import com.wealthyhood.wealthyhood.common.responseToDomainResult
import com.wealthyhood.wealthyhood.model.DayChoice
import com.wealthyhood.wealthyhood.model.DomainResult
import com.wealthyhood.wealthyhood.service.AddMoneyWithCashParameters
import com.wealthyhood.wealthyhood.service.Allocation
import com.wealthyhood.wealthyhood.service.ApiResponse
import com.wealthyhood.wealthyhood.service.AssetNewsSection
import com.wealthyhood.wealthyhood.service.AssetRecentActivityItem
import com.wealthyhood.wealthyhood.service.Automation
import com.wealthyhood.wealthyhood.service.AvailableHolding
import com.wealthyhood.wealthyhood.service.CompleteStripeForSubscriptionParams
import com.wealthyhood.wealthyhood.service.CompleteStripeParams
import com.wealthyhood.wealthyhood.service.CreateAutomationBodyParams
import com.wealthyhood.wealthyhood.service.CreateMandateBodyParams
import com.wealthyhood.wealthyhood.service.DashboardChartTenor
import com.wealthyhood.wealthyhood.service.DeactivateBankAccountResponse
import com.wealthyhood.wealthyhood.service.EtfData
import com.wealthyhood.wealthyhood.service.EuropeProvider
import com.wealthyhood.wealthyhood.service.GenerateAccountStatementResponse
import com.wealthyhood.wealthyhood.service.GenerateReferralCodeResponse
import com.wealthyhood.wealthyhood.service.GenerateTradeConfirmationResponse
import com.wealthyhood.wealthyhood.service.GenericService
import com.wealthyhood.wealthyhood.service.GetAnalystInsightArticleResponse
import com.wealthyhood.wealthyhood.service.GetAssetRestrictionResponse
import com.wealthyhood.wealthyhood.service.GetDailySummariesResponse
import com.wealthyhood.wealthyhood.service.GetDashboardChartDataQueryParams
import com.wealthyhood.wealthyhood.service.GetEmploymentConfigResponse
import com.wealthyhood.wealthyhood.service.GetForceUpdateDataResponse
import com.wealthyhood.wealthyhood.service.GetHelpCenterContentResponse
import com.wealthyhood.wealthyhood.service.GetInvestmentDetailsResponse
import com.wealthyhood.wealthyhood.service.GetInvestmentProductPriceByTenorResponse
import com.wealthyhood.wealthyhood.service.GetInvestmentUniverseResponse
import com.wealthyhood.wealthyhood.service.GetLatestMatchedOrderResponse
import com.wealthyhood.wealthyhood.service.GetLearningGuideResponse
import com.wealthyhood.wealthyhood.service.GetLearningGuidesResponse
import com.wealthyhood.wealthyhood.service.GetLinkedBankAccountsResponse
import com.wealthyhood.wealthyhood.service.GetPastPerformance
import com.wealthyhood.wealthyhood.service.GetPastPerformanceQueryParams
import com.wealthyhood.wealthyhood.service.GetPendingOrdersResponse
import com.wealthyhood.wealthyhood.service.GetPortfoliosQueryParams
import com.wealthyhood.wealthyhood.service.GetPromptsResponse
import com.wealthyhood.wealthyhood.service.GetProvidersResponse
import com.wealthyhood.wealthyhood.service.GetRestrictedHoldingsResponse
import com.wealthyhood.wealthyhood.service.GetSavingsProductDetailsResponse
import com.wealthyhood.wealthyhood.service.GetUserQueryParams
import com.wealthyhood.wealthyhood.service.GetWealthyHubArticlesResponse
import com.wealthyhood.wealthyhood.service.GetWhitelistedMailsResponse
import com.wealthyhood.wealthyhood.service.Gift
import com.wealthyhood.wealthyhood.service.InitiateEuropeBankLinkingParams
import com.wealthyhood.wealthyhood.service.InitiateEuropeBankLinkingResponse
import com.wealthyhood.wealthyhood.service.InitiateKYCResponse
import com.wealthyhood.wealthyhood.service.InitiateStripeForSubscriptionParams
import com.wealthyhood.wealthyhood.service.InitiateStripeForSubscriptionResponse
import com.wealthyhood.wealthyhood.service.InitiateStripeResponse
import com.wealthyhood.wealthyhood.service.InvestmentProduct
import com.wealthyhood.wealthyhood.service.InviteUserBodyParams
import com.wealthyhood.wealthyhood.service.JoinWaitingListParams
import com.wealthyhood.wealthyhood.service.LinkEuropeBankParams
import com.wealthyhood.wealthyhood.service.MakeDepositParameters
import com.wealthyhood.wealthyhood.service.MakeDepositResponse
import com.wealthyhood.wealthyhood.service.Mandate
import com.wealthyhood.wealthyhood.service.NotificationSettings
import com.wealthyhood.wealthyhood.service.Order
import com.wealthyhood.wealthyhood.service.PaymentMethod
import com.wealthyhood.wealthyhood.service.Portfolio
import com.wealthyhood.wealthyhood.service.RateAppParams
import com.wealthyhood.wealthyhood.service.RegisterDeviceTokenParams
import com.wealthyhood.wealthyhood.service.SavingsProduct
import com.wealthyhood.wealthyhood.service.SavingsProductFeeDetails
import com.wealthyhood.wealthyhood.service.SetSelectCountryBodyParams
import com.wealthyhood.wealthyhood.service.SubmitAllocationBodyParams
import com.wealthyhood.wealthyhood.service.SubmitEmploymentInfoBody
import com.wealthyhood.wealthyhood.service.SubmitHasAcceptedTermsParams
import com.wealthyhood.wealthyhood.service.Subscription
import com.wealthyhood.wealthyhood.service.SyncKYCResponse
import com.wealthyhood.wealthyhood.service.SyncedTransaction
import com.wealthyhood.wealthyhood.service.Transaction
import com.wealthyhood.wealthyhood.service.TransactionActivityResponse
import com.wealthyhood.wealthyhood.service.TrueLayerService
import com.wealthyhood.wealthyhood.service.UpdateDeviceNotificationSettingsParams
import com.wealthyhood.wealthyhood.service.UpdateNotificationSettingParams
import com.wealthyhood.wealthyhood.service.UpdatePaymentMethodForSubscriptionBody
import com.wealthyhood.wealthyhood.service.UpdateUserBodyParams
import com.wealthyhood.wealthyhood.service.User
import com.wealthyhood.wealthyhood.service.WealthyhoodService
import com.wealthyhood.wealthyhood.service.WithdrawMoneyParams
import okhttp3.ResponseBody

class Repository {

    fun generateDayChoices(): List<DayChoice> {
        // FIXME: Use localized strings

        val finalAnswer = mutableListOf<DayChoice>()

        val firstChoiceTexts = listOf(
            "1st", "2nd", "3rd"
        )

        firstChoiceTexts.forEachIndexed { index, text ->
            val choice = DayChoice(
                id = "day$index",
                text = text,
                value = index + 1
            )

            finalAnswer.add(choice)
        }

        repeat(25) { index ->
            val finalIndex = index + 3

            val choice = DayChoice(
                id = "day$finalIndex",
                text = "${finalIndex + 1}th",
                value = finalIndex + 1
            )

            finalAnswer.add(choice)
        }

        val choice = DayChoice(
            id = "lastDay",
            text = "Last",
            value = -1
        )

        finalAnswer.add(choice)

        return finalAnswer
    }

    suspend fun setSelectedCountry(
        accessToken: String,
        idToken: String,
        body: SetSelectCountryBodyParams?
    ): DomainResult<ResponseBody>? {
        return responseToDomainResult {
            WealthyhoodService.instance.setSelectedCountry(
                authorization = accessToken,
                identity = idToken,
                body = body
            )
        }
    }

    suspend fun getPortfolioWithReturnsByTenor(
        accessToken: String,
        idToken: String,
        portfolioID: String?,
    ): DomainResult<Portfolio>? {
        return responseToDomainResult {
            WealthyhoodService.instance.getPortfolioWithReturnsByTenor(
                authorization = accessToken,
                identity = idToken,
                portfolioID = portfolioID
            )
        }
    }

    suspend fun getInvestmentProducts(
        accessToken: String,
        idToken: String,
        shouldPopulateTicker: Boolean?
    ): DomainResult<List<InvestmentProduct>>? {
        return responseToDomainResult {
            WealthyhoodService.instance.getInvestmentProducts(
                authorization = accessToken,
                identity = idToken,
                shouldPopulateTicker = shouldPopulateTicker
            )
        }
    }

    suspend fun getInvestmentDetails(
        accessToken: String,
        idToken: String,
        assetID: String?
    ): DomainResult<GetInvestmentDetailsResponse>? {
        return responseToDomainResult {
            WealthyhoodService.instance.getInvestmentDetails(
                authorization = accessToken,
                identity = idToken,
                assetID = assetID
            )
        }
    }

    suspend fun getAssetRecentActivity(
        accessToken: String,
        idToken: String,
        assetID: String?,
        limit: Int?
    ): DomainResult<List<AssetRecentActivityItem>>? {
        return responseToDomainResult {
            WealthyhoodService.instance.getAssetRecentActivity(
                authorization = accessToken,
                identity = idToken,
                assetID = assetID,
                limit = limit
            )
        }
    }

    suspend fun getInvestmentProductEtfData(
        accessToken: String,
        idToken: String,
        assetID: String?
    ): DomainResult<EtfData>? {
        return responseToDomainResult {
            WealthyhoodService.instance.getInvestmentProductEtfData(
                authorization = accessToken,
                identity = idToken,
                assetID = assetID
            )
        }
    }

    suspend fun getInvestmentProductPricesByTenor(
        accessToken: String,
        idToken: String,
        assetID: String?
    ): DomainResult<GetInvestmentProductPriceByTenorResponse>? {
        return responseToDomainResult {
            WealthyhoodService.instance.getInvestmentProductPricesByTenor(
                authorization = accessToken,
                identity = idToken,
                assetID = assetID
            )
        }
    }

    suspend fun getAssetRestriction(
        accessToken: String,
        idToken: String,
        assetId: String?,
        portfolioID: String?,
    ): DomainResult<GetAssetRestrictionResponse>? {
        return responseToDomainResult {
            WealthyhoodService.instance.getAssetRestriction(
                authorization = accessToken,
                identity = idToken,
                portfolioID = portfolioID,
                assetId = assetId
            )
        }
    }

    suspend fun getRestrictedHoldings(
        accessToken: String,
        idToken: String,
        portfolioID: String?
    ): DomainResult<GetRestrictedHoldingsResponse>? {
        return responseToDomainResult {
            WealthyhoodService.instance.getRestrictedHoldings(
                authorization = accessToken,
                identity = idToken,
                portfolioID = portfolioID
            )
        }
    }

    suspend fun getInvestmentUniverse(
        companyEntity: String?,
        country: String?,
        email: String?,
        referralCode: String?
    ): DomainResult<GetInvestmentUniverseResponse>? {
        return responseToDomainResult {
            GenericService.instance.getInvestmentUniverse(
                companyEntity = companyEntity,
                country = country,
                email = email,
                referralCode = referralCode
            )
        }
    }

    suspend fun getWhitelistedMails(): DomainResult<GetWhitelistedMailsResponse>? {
        return responseToDomainResult {
            GenericService.instance.getWhitelistedMails()
        }
    }

    suspend fun getPendingRebalances(
        accessToken: String,
        idToken: String
    ): DomainResult<List<Transaction>>? {
        return responseToDomainResult {
            WealthyhoodService.instance.getPendingRebalances(
                authorization = accessToken,
                identity = idToken
            )
        }
    }

    suspend fun cancelTransaction(
        accessToken: String,
        idToken: String,
        transactionID: String
    ): DomainResult<Void>? {
        return responseToDomainResult {
            WealthyhoodService.instance.cancelTransaction(
                authorization = accessToken,
                identity = idToken,
                transactionID = transactionID
            )
        }
    }

    suspend fun cancelOrder(
        accessToken: String,
        idToken: String,
        orderID: String
    ): DomainResult<Order>? {
        return responseToDomainResult {
            WealthyhoodService.instance.cancelOrder(
                authorization = accessToken,
                identity = idToken,
                orderID = orderID
            )
        }
    }

    suspend fun getAllTrueLayerProviders(
    ): DomainResult<GetProvidersResponse>? {
        // Στο currency θέλουμε παντα GBP: δεν έχουμε open banking στην ΕΕ για την ώρα.

        return responseToDomainResult {
            TrueLayerService.instance.getProviders(
                currency = "GBP",
                authFlowType = "redirect",
                accountType = "sort_code_account_number",
                clientID = BuildConfig.TRUE_LAYER_API_CLIENT_ID
            )
        }
    }

    suspend fun submitHasAcceptedTerms(
        accessToken: String,
        idToken: String,
        hasAcceptedTerms: Boolean?
    ): DomainResult<ResponseBody>? {
        val body = SubmitHasAcceptedTermsParams(
            hasAcceptedTerms = hasAcceptedTerms
        )

        return responseToDomainResult {
            WealthyhoodService.instance.submitHasAcceptedTerms(
                authorization = accessToken,
                identity = idToken,
                body = body
            )
        }
    }

    suspend fun getIncomingCashFlows(
        accessToken: String,
        idToken: String,
        portfolioID: String?,
        populate: String?
    ): DomainResult<List<Transaction>>? {
        return responseToDomainResult {
            WealthyhoodService.instance.getIncomingCashFlows(
                authorization = accessToken,
                identity = idToken,
                portfolioID = portfolioID,
                populate = populate
            )
        }
    }

    suspend fun getSavingsProductActivity(
        accessToken: String,
        idToken: String,
        savingsProductID: String?,
        limit: Int? = null
    ): DomainResult<List<TransactionActivityResponse>>? {
        return responseToDomainResult {
            WealthyhoodService.instance.getSavingsProductActivity(
                authorization = accessToken,
                identity = idToken,
                savingsProductID = savingsProductID,
                limit = limit
            )
        }
    }

    suspend fun getPendingTransactions(
        accessToken: String,
        idToken: String,
        portfolioID: String?,
        populate: String?
    ): DomainResult<List<Transaction>>? {
        return responseToDomainResult {
            WealthyhoodService.instance.getPendingTransactions(
                authorization = accessToken,
                identity = idToken,
                portfolioID = portfolioID,
                populate = populate
            )
        }
    }

    suspend fun getEmploymentConfig(
        accessToken: String,
        idToken: String
    ): DomainResult<GetEmploymentConfigResponse>? {
        return responseToDomainResult {
            WealthyhoodService.instance.getEmploymentConfig(
                authorization = accessToken,
                identity = idToken
            )
        }
    }

    suspend fun submitEmploymentInfo(
        accessToken: String,
        idToken: String,
        annualIncome: String?,
        sourcesOfWealth: List<String>?,
        employmentStatus: String?,
        industry: String?
    ): DomainResult<Void>? {
        val body = SubmitEmploymentInfoBody(
            annualIncome = annualIncome,
            sourcesOfWealth = sourcesOfWealth,
            employmentStatus = employmentStatus,
            industry = industry
        )

        return responseToDomainResult {
            WealthyhoodService.instance.submitEmploymentInfo(
                authorization = accessToken,
                identity = idToken,
                body = body
            )
        }
    }

    suspend fun getUser(
        accessToken: String,
        idToken: String,
        queryParams: GetUserQueryParams? = null
    ): DomainResult<User>? {
        return responseToDomainResult {
            WealthyhoodService.instance.getUserCoroutines(
                authorization = accessToken,
                identity = idToken,
                populate = queryParams?.populate
            )
        }
    }

    suspend fun getBannerPrompts(
        accessToken: String,
        idToken: String,
        type: String?
    ): DomainResult<GetPromptsResponse>? {
        return responseToDomainResult {
            WealthyhoodService.instance.getPromptsCoroutines(
                authorization = accessToken,
                identity = idToken,
                type = type
            )
        }
    }

    suspend fun getTransactions(
        accessToken: String,
        idToken: String,
        portfolioID: String?,
        category: String?
    ): DomainResult<List<Transaction>>? {
        return responseToDomainResult {
            WealthyhoodService.instance.getTransactionsCoroutines(
                authorization = accessToken,
                identity = idToken,
                portfolioID = portfolioID,
                category = category,
                trueLayerStatus = listOf("executed"),
                sort = "-createdAt",
                status = listOf(
                    "PendingDeposit",
                    "PendingGift",
                    "Pending",
                    "Settled",
                    "Cancelled",
                    "Rejected"
                ),
                populate = "bankAccount,orders,subscription"
            )
        }
    }

    suspend fun getDashboardChartDataByTenor(
        accessToken: String,
        idToken: String,
        queryParams: GetDashboardChartDataQueryParams? = null
    ): DomainResult<Map<String, DashboardChartTenor>>? {
        return responseToDomainResult {
            WealthyhoodService.instance.getDashboardChartDataByTenor(
                authorization = accessToken,
                identity = idToken,
                portfolioID = queryParams?.portfolioID
            )
        }
    }

    suspend fun getPortfolios(
        accessToken: String,
        idToken: String,
        parameters: GetPortfoliosQueryParams? = null
    ): DomainResult<List<Portfolio>>? {
        return responseToDomainResult {
            WealthyhoodService.instance.getPortfoliosCoroutines(
                authorization = accessToken,
                identity = idToken,
                mode = parameters?.mode
            )
        }
    }

    suspend fun getPastPerformance(
        accessToken: String,
        idToken: String,
        queryParams: GetPastPerformanceQueryParams? = null
    ): DomainResult<GetPastPerformance>? {
        return responseToDomainResult {
            WealthyhoodService.instance.getPastPerformanceCoroutines(
                authorization = accessToken,
                identity = idToken,
                // TODO: statistics wrapper of api does not support this param
                // It may be added in case a performance issue occurs (previously used)
                // weeklyResample = queryParams?.weeklyResample,
                initial = queryParams?.initial,
                options = queryParams?.options
            )
        }
    }

    suspend fun getGifts(
        accessToken: String,
        idToken: String,
        used: Boolean,
        hasViewedAppModal: Boolean?
    ): DomainResult<ApiResponse<List<Gift>>>? {
        return responseToDomainResult {
            WealthyhoodService.instance.getGiftsCoroutines(
                authorization = accessToken,
                identity = idToken,
                used = used,
                hasViewedAppModal = hasViewedAppModal
            )
        }
    }

    suspend fun getMandates(
        accessToken: String,
        idToken: String,
        category: String?
    ): DomainResult<ApiResponse<List<Mandate>>>? {
        return responseToDomainResult {
            WealthyhoodService.instance.getMandatesCoroutines(
                authorization = accessToken,
                identity = idToken,
                category = category
            )
        }
    }

    suspend fun getAutomations(
        accessToken: String,
        idToken: String,
        category: String?
    ): DomainResult<ApiResponse<List<Automation>>>? {
        return responseToDomainResult {
            WealthyhoodService.instance.getAutomationsCoroutines(
                authorization = accessToken,
                identity = idToken,
                category = category
            )
        }
    }

    suspend fun getAvailableHoldings(
        accessToken: String,
        idToken: String,
        portfolioID: String?
    ): DomainResult<List<AvailableHolding>>? {
        return responseToDomainResult {
            WealthyhoodService.instance.getAvailableHoldings(
                authorization = accessToken,
                identity = idToken,
                portfolioID = portfolioID
            )
        }
    }

    suspend fun renewSubscription(
        accessToken: String,
        idToken: String,
        subscriptionID: String?
    ): DomainResult<Subscription>? {
        return responseToDomainResult {
            WealthyhoodService.instance.renewSubscription(
                authorization = accessToken,
                identity = idToken,
                subscriptionID = subscriptionID
            )
        }
    }

    suspend fun getLinkedBankAccounts(
        accessToken: String,
        idToken: String,
        userID: String?
    ): DomainResult<GetLinkedBankAccountsResponse>? {
        return responseToDomainResult {
            WealthyhoodService.instance.getLinkedBankAccountsCoroutines(
                authorization = accessToken,
                identity = idToken,
                externalUserID = userID
            )
        }
    }

    suspend fun deactivateBankAccount(
        accessToken: String,
        idToken: String,
        bankAccountID: String?
    ): DomainResult<DeactivateBankAccountResponse>? {
        return responseToDomainResult {
            WealthyhoodService.instance.deactivateBankAccount(
                authorization = accessToken,
                identity = idToken,
                bankAccountID = bankAccountID
            )
        }
    }

    suspend fun getBilling(
        accessToken: String,
        idToken: String,
        limit: Int?
    ): DomainResult<List<Transaction>>? {
        return responseToDomainResult {
            WealthyhoodService.instance.getBilling(
                authorization = accessToken,
                identity = idToken,
                limit = limit
            )
        }
    }

    suspend fun getPaymentMethods(
        accessToken: String,
        idToken: String
    ): DomainResult<ApiResponse<List<PaymentMethod>>>? {
        return responseToDomainResult {
            WealthyhoodService.instance.getPaymentMethods(
                authorization = accessToken,
                identity = idToken
            )
        }
    }

    suspend fun initiateStripe(
        accessToken: String,
        idToken: String
    ): DomainResult<InitiateStripeResponse>? {
        return responseToDomainResult {
            WealthyhoodService.instance.initiateStripe(
                authorization = accessToken,
                identity = idToken
            )
        }
    }

    suspend fun completeStripe(
        accessToken: String,
        idToken: String,
        setupIntentID: String?
    ): DomainResult<PaymentMethod>? {
        val body = CompleteStripeParams(
            setupIntentID = setupIntentID
        )

        return responseToDomainResult {
            WealthyhoodService.instance.completeStripe(
                authorization = accessToken,
                identity = idToken,
                body = body
            )
        }
    }

    suspend fun initiateStripeForSubscription(
        accessToken: String,
        idToken: String,
        priceAPIKey: String?,
        paymentMethodStripeID: String?
    ): DomainResult<InitiateStripeForSubscriptionResponse>? {
        val body = InitiateStripeForSubscriptionParams(
            priceAPIKey = priceAPIKey,
            paymentMethodStripeID = paymentMethodStripeID
        )

        return responseToDomainResult {
            WealthyhoodService.instance.initiateStripeForSubscription(
                authorization = accessToken,
                identity = idToken,
                body = body
            )
        }
    }

    suspend fun completeStripeForSubscription(
        accessToken: String,
        idToken: String,
        paymentIntentID: String?
    ): DomainResult<Subscription>? {
        val body = CompleteStripeForSubscriptionParams(
            paymentIntentID = paymentIntentID
        )

        return responseToDomainResult {
            WealthyhoodService.instance.completeStripeForSubscription(
                authorization = accessToken,
                identity = idToken,
                body = body
            )
        }
    }

    suspend fun updateUser(
        accessToken: String,
        idToken: String,
        userID: String?,
        body: UpdateUserBodyParams? = null
    ): DomainResult<ResponseBody>? {
        return responseToDomainResult {
            WealthyhoodService.instance.updateUser(
                authorization = accessToken,
                identity = idToken,
                userID = userID,
                body = body
            )
        }
    }

    suspend fun createAutomation(
        accessToken: String,
        idToken: String,
        category: String,
        mandateID: String?,
        savingsProductID: String?,
        formattedAmount: String?,
        allocationMethod: String?,
        postponeActivation: Boolean?,
        dayOfMonth: Int?
    ): DomainResult<ResponseBody>? {
        val body = CreateAutomationBodyParams(
            category = category,
            frequency = "monthly",
            mandateID = mandateID,
            savingsProductID = savingsProductID,
            formattedAmount = formattedAmount,
            postponeActivation = postponeActivation,
            dayOfMonth = dayOfMonth
        )

        return responseToDomainResult {
            WealthyhoodService.instance.createAutomationCoroutines(
                authorization = accessToken,
                identity = idToken,
                allocationMethod = allocationMethod,
                body = body
            )
        }
    }

    suspend fun createMandate(
        accessToken: String,
        idToken: String,
        category: String?,
        bankAccountID: String?
    ): DomainResult<Mandate>? {
        val bodyParams = CreateMandateBodyParams(
            bankAccountID = bankAccountID,
            category = category
        )

        return responseToDomainResult {
            WealthyhoodService.instance.createMandateCoroutines(
                authorization = accessToken,
                identity = idToken,
                body = bodyParams
            )
        }
    }

    suspend fun markReferralCodeScreenAsViewed(
        accessToken: String,
        idToken: String
    ): DomainResult<ResponseBody>? {
        return responseToDomainResult {
            WealthyhoodService.instance.markReferralCodeScreenAsViewed(
                authorization = accessToken,
                identity = idToken
            )
        }
    }

    suspend fun updatePaymentMethodForSubscription(
        accessToken: String,
        idToken: String,
        paymentMethodStripeID: String?
    ): DomainResult<Subscription>? {
        val body = UpdatePaymentMethodForSubscriptionBody(
            paymentMethodStripeID = paymentMethodStripeID
        )

        return responseToDomainResult {
            WealthyhoodService.instance.updatePaymentMethodForSubscription(
                authorization = accessToken,
                identity = idToken,
                body = body
            )
        }
    }

    suspend fun getPendingOrders(
        accessToken: String,
        idToken: String
    ): DomainResult<GetPendingOrdersResponse>? {
        return responseToDomainResult {
            WealthyhoodService.instance.getPendingOrders(
                authorization = accessToken,
                identity = idToken
            )
        }
    }

    suspend fun makeDepositForSavings(
        accessToken: String,
        idToken: String,
        amount: Float,
        bankAccountID: String
    ): DomainResult<MakeDepositResponse>? {
        val parameters = MakeDepositParameters(
            amount = amount,
            bankID = null,
            bankAccountID = bankAccountID,
            depositAndInvest = null
        )

        return responseToDomainResult {
            WealthyhoodService.instance.makeDepositForSavings(
                authorization = accessToken,
                identity = idToken,
                parameters = parameters
            )
        }
    }

    suspend fun addMoneyWithCash(
        accessToken: String,
        idToken: String,
        portfolioID: String?,
        amount: Float,
        savingsProductID: String?
    ): DomainResult<Transaction>? {
        val parameters = AddMoneyWithCashParameters(
            amount = amount,
            savingsProductID = savingsProductID
        )

        return responseToDomainResult {
            WealthyhoodService.instance.addMoneyWithCash(
                authorization = accessToken,
                identity = idToken,
                portfolioID = portfolioID,
                parameters = parameters
            )
        }
    }

    suspend fun withdrawMoney(
        accessToken: String,
        idToken: String,
        portfolioID: String?,
        amount: Float,
        savingsProductID: String?
    ): DomainResult<Transaction>? {
        val parameters = WithdrawMoneyParams(
            amount = amount,
            savingsProductID = savingsProductID
        )

        return responseToDomainResult {
            WealthyhoodService.instance.withdrawMoney(
                authorization = accessToken,
                identity = idToken,
                portfolioID = portfolioID,
                parameters = parameters
            )
        }
    }

    suspend fun getSavingsProducts(
        accessToken: String,
        idToken: String
    ): DomainResult<List<SavingsProduct>>? {
        return responseToDomainResult {
            WealthyhoodService.instance.getSavingsProducts(
                authorization = accessToken,
                identity = idToken,
            )
        }
    }

    suspend fun getSavingsProductDetails(
        accessToken: String,
        idToken: String,
        savingsProductID: String?
    ): DomainResult<GetSavingsProductDetailsResponse>? {
        return responseToDomainResult {
            WealthyhoodService.instance.getSavingsProductDetails(
                authorization = accessToken,
                identity = idToken,
                savingsProductID = savingsProductID
            )
        }
    }

    suspend fun getSavingsProductFeeDetails(
        accessToken: String,
        idToken: String,
        savingsProductID: String?
    ): DomainResult<SavingsProductFeeDetails>? {
        return responseToDomainResult {
            WealthyhoodService.instance.getSavingsProductFeeDetails(
                authorization = accessToken,
                identity = idToken,
                savingsProductID = savingsProductID
            )
        }
    }

    suspend fun getAssetNews(
        accessToken: String,
        idToken: String,
        assetID: String?,
        limit: Int?
    ): DomainResult<List<AssetNewsSection>>? {
        return responseToDomainResult {
            WealthyhoodService.instance.getAssetNews(
                authorization = accessToken,
                identity = idToken,
                assetID = assetID,
                limit = limit
            )
        }
    }

    suspend fun subscribeToWealthyBites(
        accessToken: String,
        idToken: String,
        didSubscribe: Boolean
    ): DomainResult<ResponseBody>? {
        return responseToDomainResult {
            WealthyhoodService.instance.subscribeToWealthyBites(
                authorization = accessToken,
                identity = idToken,
                didSubscribe = didSubscribe
            )
        }
    }

    suspend fun getEuropeProviders(
        accessToken: String,
        idToken: String,
        scope: String?
    ): DomainResult<List<EuropeProvider>>? {
        return responseToDomainResult {
            WealthyhoodService.instance.getEuropeProviders(
                authorization = accessToken,
                identity = idToken,
                scope = scope
            )
        }
    }

    suspend fun initiateEuropeBankLinking(
        accessToken: String,
        idToken: String,
        bankID: String?
    ): DomainResult<InitiateEuropeBankLinkingResponse>? {
        val parameters = InitiateEuropeBankLinkingParams(
            bankID = bankID
        )

        return responseToDomainResult {
            WealthyhoodService.instance.initiateEuropeBankLinking(
                authorization = accessToken,
                identity = idToken,
                parameters = parameters
            )
        }
    }

    suspend fun linkEuropeBank(
        accessToken: String,
        idToken: String,
        userID: String?,
        reference: String?
    ): DomainResult<ResponseBody>? {
        val parameters = LinkEuropeBankParams(
            userID = userID,
            reference = reference
        )

        return responseToDomainResult {
            WealthyhoodService.instance.linkEuropeBank(
                authorization = accessToken,
                identity = idToken,
                parameters = parameters
            )
        }
    }

    suspend fun getOptimalAllocation(
        accessToken: String,
        idToken: String,
        roboAdvisorRiskLevel: String?
    ): DomainResult<Allocation>? {
        return responseToDomainResult {
            WealthyhoodService.instance.getOptimalAllocationCoroutines(
                authorization = accessToken,
                identity = idToken,
                roboAdvisorRiskLevel = roboAdvisorRiskLevel
            )
        }
    }

    suspend fun syncSaltedge(
        accessToken: String,
        idToken: String,
        saltedgeCustomID: String?
    ): DomainResult<List<SyncedTransaction>>? {
        return responseToDomainResult {
            WealthyhoodService.instance.syncSaltedge(
                authorization = accessToken,
                identity = idToken,
                saltedgeCustomID = saltedgeCustomID
            )
        }
    }

    suspend fun rateApp(
        accessToken: String,
        idToken: String,
        appRatingID: String?,
        starRating: Int?,
        feedback: String?
    ): DomainResult<ResponseBody>? {
        val parameters = RateAppParams(
            starRating = starRating,
            feedback = feedback
        )

        return responseToDomainResult {
            WealthyhoodService.instance.rateApp(
                authorization = accessToken,
                identity = idToken,
                appRatingID = appRatingID,
                parameters = parameters
            )
        }
    }

    suspend fun generateAccountStatement(
        accessToken: String,
        idToken: String,
        startDate: String?,
        endDate: String?
    ): DomainResult<GenerateAccountStatementResponse>? {
        return responseToDomainResult {
            WealthyhoodService.instance.generateAccountStatement(
                authorization = accessToken,
                identity = idToken,
                startDate = startDate,
                endDate = endDate
            )
        }
    }

    suspend fun getHelpCenterContent(
        accessToken: String,
        idToken: String
    ): DomainResult<GetHelpCenterContentResponse>? {
        return responseToDomainResult {
            WealthyhoodService.instance.getHelpCenterContent(
                authorization = accessToken,
                identity = idToken
            )
        }
    }

    suspend fun getAnalystInsightArticle(
        accessToken: String,
        idToken: String,
        articleID: String?
    ): DomainResult<GetAnalystInsightArticleResponse>? {
        return responseToDomainResult {
            WealthyhoodService.instance.getAnalystInsightArticle(
                authorization = accessToken,
                identity = idToken,
                articleID = articleID
            )
        }
    }

    suspend fun getForceUpdateData(): DomainResult<GetForceUpdateDataResponse>? {
        return responseToDomainResult {
            GenericService.instance.getForceUpdateData()
        }
    }

    suspend fun getWealthyHubArticles(
        accessToken: String,
        idToken: String,
        type: String,
        page: Int?,
        pageSize: Int?
    ): DomainResult<GetWealthyHubArticlesResponse>? {
        return responseToDomainResult {
            WealthyhoodService.instance.getWealthyHubArticlesCoroutines(
                authorization = accessToken,
                identity = idToken,
                type = type,
                page = page,
                pageSize = pageSize
            )
        }
    }

    suspend fun initiateKYC(
        accessToken: String,
        idToken: String
    ): DomainResult<InitiateKYCResponse>? {
        return responseToDomainResult {
            WealthyhoodService.instance.initiateKYC(
                authorization = accessToken,
                identity = idToken
            )
        }
    }

    suspend fun syncKYC(
        accessToken: String,
        idToken: String
    ): DomainResult<SyncKYCResponse>? {
        return responseToDomainResult {
            WealthyhoodService.instance.syncKYC(
                authorization = accessToken,
                identity = idToken
            )
        }
    }

    suspend fun inviteUser(
        accessToken: String,
        idToken: String,
        userMail: String
    ): DomainResult<ResponseBody>? {
        val bodyParams = InviteUserBodyParams(
            userMail = userMail
        )

        return responseToDomainResult {
            WealthyhoodService.instance.inviteUser(
                authorization = accessToken,
                identity = idToken,
                body = bodyParams
            )
        }
    }

    suspend fun generateReferralCode(
        accessToken: String,
        idToken: String
    ): DomainResult<GenerateReferralCodeResponse>? {
        return responseToDomainResult {
            WealthyhoodService.instance.generateReferralCode(
                authorization = accessToken,
                identity = idToken
            )
        }
    }

    suspend fun getNotificationSettings(
        accessToken: String,
        idToken: String
    ): DomainResult<NotificationSettings>? {
        return responseToDomainResult {
            WealthyhoodService.instance.getNotificationSettings(
                authorization = accessToken,
                identity = idToken
            )
        }
    }

    suspend fun updateNotificationSetting(
        accessToken: String,
        idToken: String,
        notificationID: String,
        isActive: Boolean
    ): DomainResult<NotificationSettings>? {
        val params = UpdateNotificationSettingParams(
            id = notificationID,
            active = isActive
        )

        return responseToDomainResult {
            WealthyhoodService.instance.updateNotificationSetting(
                authorization = accessToken,
                identity = idToken,
                parameters = params
            )
        }
    }

    suspend fun updateDeviceNotificationSettings(
        accessToken: String,
        idToken: String,
        areDeviceNotificationsActive: Boolean
    ): DomainResult<ResponseBody>? {
        val params = UpdateDeviceNotificationSettingsParams(
            areDeviceNotificationsActive = areDeviceNotificationsActive
        )

        return responseToDomainResult {
            WealthyhoodService.instance.updateDeviceNotificationSettings(
                authorization = accessToken,
                identity = idToken,
                parameters = params
            )
        }
    }

    suspend fun getLearningGuides(
        accessToken: String,
        idToken: String
    ): DomainResult<GetLearningGuidesResponse>? {
        return responseToDomainResult {
            WealthyhoodService.instance.getLearningGuides(
                authorization = accessToken,
                identity = idToken
            )
        }
    }

    suspend fun getLearningGuideBySlug(
        accessToken: String,
        idToken: String,
        guideSlug: String?
    ): DomainResult<GetLearningGuideResponse>? {
        return responseToDomainResult {
            WealthyhoodService.instance.getLearningGuideBySlug(
                authorization = accessToken,
                identity = idToken,
                guideSlug = guideSlug
            )
        }
    }

    suspend fun getLearningGuideByID(
        accessToken: String,
        idToken: String,
        guideID: String?
    ): DomainResult<GetLearningGuideResponse>? {
        return responseToDomainResult {
            WealthyhoodService.instance.getLearningGuideByID(
                authorization = accessToken,
                identity = idToken,
                guideID = guideID
            )
        }
    }

    suspend fun getLatestMatchedOrder(
        accessToken: String,
        idToken: String
    ): DomainResult<GetLatestMatchedOrderResponse>? {
        return responseToDomainResult {
            WealthyhoodService.instance.getLatestMatchedOrder(
                authorization = accessToken,
                identity = idToken
            )
        }
    }

    suspend fun registerDeviceToken(
        accessToken: String,
        idToken: String,
        deviceToken: String
    ): DomainResult<ResponseBody>? {
        val parameters = RegisterDeviceTokenParams(
            deviceToken = deviceToken
        )

        return responseToDomainResult {
            WealthyhoodService.instance.registerDeviceToken(
                authorization = accessToken,
                identity = idToken,
                parameters = parameters
            )
        }
    }

    suspend fun getDailySummaries(
        accessToken: String,
        idToken: String
    ): DomainResult<GetDailySummariesResponse>? {
        return responseToDomainResult {
            WealthyhoodService.instance.getDailySummaries(
                authorization = accessToken,
                identity = idToken
            )
        }
    }

    suspend fun getCashActivity(
        accessToken: String,
        idToken: String,
        limit: Int? = null
    ): DomainResult<List<TransactionActivityResponse>>? {
        return responseToDomainResult {
            WealthyhoodService.instance.getCashActivity(
                authorization = accessToken,
                identity = idToken,
                limit = limit
            )
        }
    }

    suspend fun getInvestmentActivity(
        accessToken: String,
        idToken: String,
        limit: Int? = null
    ): DomainResult<List<TransactionActivityResponse>>? {
        return responseToDomainResult {
            WealthyhoodService.instance.getInvestmentActivity(
                authorization = accessToken,
                identity = idToken,
                limit = limit
            )
        }
    }

    suspend fun submitAllocationCoroutines(
        accessToken: String,
        idToken: String,
        portfolioID: String?,
        allocation: SubmitAllocationBodyParams // TODO: Rename it to body
    ): DomainResult<ResponseBody>? {
        return responseToDomainResult {
            WealthyhoodService.instance.submitAllocationCoroutines(
                authorization = accessToken,
                identity = idToken,
                portfolioID = portfolioID,
                body = allocation
            )
        }
    }

    suspend fun joinWaitingList(
        accessToken: String,
        idToken: String,
        body: JoinWaitingListParams?
    ): DomainResult<ResponseBody>? {
        return responseToDomainResult {
            WealthyhoodService.instance.joinWaitingList(
                authorization = accessToken,
                identity = idToken,
                body = body
            )
        }
    }

    suspend fun generateOrderTradeConfirmation(
        accessToken: String,
        idToken: String,
        orderID: String?
    ): DomainResult<GenerateTradeConfirmationResponse>? {
        return responseToDomainResult {
            WealthyhoodService.instance.generateOrderTradeConfirmation(
                authorization = accessToken,
                identity = idToken,
                orderID = orderID
            )
        }
    }

    suspend fun generateRewardTradeConfirmation(
        accessToken: String,
        idToken: String,
        orderID: String?
    ): DomainResult<GenerateTradeConfirmationResponse>? {
        return responseToDomainResult {
            WealthyhoodService.instance.generateRewardTradeConfirmation(
                authorization = accessToken,
                identity = idToken,
                orderID = orderID
            )
        }
    }
}
