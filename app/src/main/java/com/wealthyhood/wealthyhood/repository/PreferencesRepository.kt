package com.wealthyhood.wealthyhood.repository

import android.content.Context
import android.content.SharedPreferences
import androidx.security.crypto.EncryptedSharedPreferences
import androidx.security.crypto.MasterKeys
import com.google.gson.Gson
import com.google.gson.JsonObject
import com.google.gson.reflect.TypeToken
import com.wealthyhood.wealthyhood.domain.AccountsData
import com.wealthyhood.wealthyhood.domain.CacheData
import com.wealthyhood.wealthyhood.domain.DailyBriefData
import com.wealthyhood.wealthyhood.domain.DashboardData
import com.wealthyhood.wealthyhood.domain.InvestmentsData
import com.wealthyhood.wealthyhood.domain.ScreenChooserData
import com.wealthyhood.wealthyhood.domain.WealthyHubData
import com.wealthyhood.wealthyhood.extensions.convertToFullDate
import com.wealthyhood.wealthyhood.extensions.convertUTCToLocal
import com.wealthyhood.wealthyhood.model.BannerCloseInfo
import com.wealthyhood.wealthyhood.model.FCMNotificationData
import com.wealthyhood.wealthyhood.model.StatementPeriodsSector
import com.wealthyhood.wealthyhood.service.DepositsConfig
import com.wealthyhood.wealthyhood.service.GetInvestmentUniverseResponse
import com.wealthyhood.wealthyhood.service.GetWhitelistedMailsResponse
import com.wealthyhood.wealthyhood.service.LegalLinks
import com.wealthyhood.wealthyhood.service.Plan
import com.wealthyhood.wealthyhood.service.Portfolio
import com.wealthyhood.wealthyhood.service.SupportedCountry
import com.wealthyhood.wealthyhood.service.TaxResidencyConfig
import com.wealthyhood.wealthyhood.service.Transaction
import com.wealthyhood.wealthyhood.service.TransactionPreview
import io.sentry.Sentry

class PreferencesRepository(private val context: Context) {

    companion object {

        private const val FILE_NAME = "wealthyPrefs"

        private const val KEY_DID_SEND_INSTALL_EVENT = "didSendInstallEvent"
        private const val KEY_ARE_BIOMETRICS_ENABLED = "areBiometricsEnabled"
        private const val KEY_ARE_NOTIFICATIONS_ENABLED = "areNotificationsEnabled"
        private const val KEY_DID_ASK_FOR_NOTIFICATIONS = "didAskForNotifications"
        private const val KEY_DID_SEE_BLACK_FRIDAY_SCREEN = "didSeeBlackFridayScreen"
        private const val KEY_PIN = "pin"
        private const val KEY_GOOGLE_USER_ID = "googleUserID"
        private const val KEY_LAST_LOGIN_MAIL = "lastLoginMail"
        private const val KEY_USER_ID = "userID"
        private const val KEY_USER_CURRENCY = "userCurrency"
        private const val KEY_USER_RESIDENCY_COUNTRY = "userResidencyCountry"
        private const val KEY_USER_IBAN = "userIBAN"
        private const val KEY_USER_COMPANY_ENTITY = "userCompanyEntity"

        private const val KEY_USER_IS_REALTIME_ETF_EXECUTION_ENABLED =
            "userIsRealtimeETFExecutionEnabled"

        private const val KEY_USER_EMAIL = "userEmail"
        private const val KEY_USER_REFERRAL_CODE = "userReferralCode"
        private const val KEY_USER_IS_ROBO_ADVISOR_ENABLED = "userIsRoboAdvisorEnabled"
        private const val KEY_PORTFOLIO_HAS_TARGET_ALLOCATION = "portfolioHasTargetAllocation"
        private const val KEY_SELECTED_TAB = "selectedTab"
        private const val KEY_PORTFOLIO = "portfolio"
        private const val KEY_PORTFOLIO_ID = "portfolioID"
        private const val KEY_DEEP_LINK_TRANSACTION_PREVIEW = "deepLinkTransactionPreview"
        private const val KEY_DEEP_LINK_ALLOCATION_METHOD = "deepLinkAllocationMethod"
        private const val KEY_DEEP_LINK_TRANSACTION = "deepLinkTransaction"
        private const val KEY_DEEP_LINK_ACTION = "deepLinkAction"
        private const val KEY_DEEP_LINK_ETF_ID = "deepLinkETFID"
        private const val KEY_DEEP_LINK_ETF_NAME = "deepLinkETFName"
        private const val KEY_DEEP_LINK_CAN_UNLOCK_FREE_SHARE = "deepLinkCanUnlockFreeShare"
        private const val KEY_DEEP_LINK_SHOULD_SHOW_SET_UP_MONTHLY =
            "deepLinkShouldShowSetUpMonthlyScreen"
        private const val KEY_DEEP_LINK_BUY_AMOUNT = "deepLinkBuyAmount"

        // This one is used when linking a new bank account
        private const val KEY_DEEP_LINK_BANK_ICON_URI = "deepLinkBankIconURI"

        // This one is used when making a new order
        private const val KEY_ORDER_DEEP_LINK_BANK_LOGO_URI = "orderDeepLinkBankLogoURI"

        private const val KEY_FREE_SHARE_DISCLAIMER_VIEWED = "freeShareDisclaimerViewed"
        private const val KEY_DID_SEND_VERIFY_EVENT = "didSendVerifyEvent"
        private const val KEY_DID_SEND_LINK_BANK_ACCOUNT_EVENT = "didSendLinkBankAccountEvent"
        private const val KEY_DID_SEND_CREATE_FIRST_DEPOSIT_EVENT = "didSendCreateFirstDepositEvent"
        private const val KEY_DID_SEND_CREATE_FIRST_INVESTMENT_EVENT =
            "didSendCreateFirstInvestmentEvent"

        private const val KEY_SAVINGS_UNIVERSE = "savingsUniverse"
        private const val KEY_SAVINGS_CONFIG = "savingsConfig"
        private const val KEY_ASSET_CLASSES = "assetClasses"
        private const val KEY_GEOGRAPHIES = "geographies"
        private const val KEY_SECTORS = "sectors"
        private const val KEY_BOND_CATEGORIES = "bondCategories"
        private const val KEY_ETF_PROVIDERS = "etfProviders"
        private const val KEY_ASSETS = "assets"
        private const val KEY_PRICING = "pricing"
        private const val KEY_DISCOVER_CONFIGURATION = "discoverConfiguration"
        private const val KEY_SUPPORTED_COUNTRIES = "supportedCountries"
        private const val KEY_UNIVERSE_BANK_DETAILS = "universeBankDetails"
        private const val KEY_LOCALES = "locales"
        private const val KEY_LEGAL_DOCUMENTS = "legalDocuments"
        private const val KEY_LEGAL_PAGES = "legalPages"
        private const val KEY_TAX_RESIDENCY_CONFIG = "taxResidencyConfig"
        private const val KEY_DEPOSITS_CONFIG = "universeDepositsConfig"
        private const val KEY_STATEMENT_PERIOD_SECTORS = "universeStatementPeriodSectors"

        private const val KEY_WHITELISTED_MAILS = "whitelistedMails"

        private const val KEY_WEALTHYHOOD_DIVIDENDS = "wealthyhoodDividends"
        private const val KEY_CASHBACKS = "cashbacks"
        private const val KEY_FEES = "fees"
        private const val KEY_UNIVERSE_PLANS = "universePlans"
        private const val KEY_REWARD_SETTINGS = "rewardSettings"
        private const val KEY_EXECUTION_WINDOWS = "executionWindows"

        private const val KEY_LAST_TRANSACTION_CREATED_AT = "lastTransactionCreatedAt"
        private const val KEY_INVESTMENT_PRODUCTS_UPDATED_AT = "investmentProductsUpdatedAt"
        private const val KEY_INCOMING_CASH_FLOWS_UPDATED_AT = "incomingCashFlowsUpdatedAt"
        private const val KEY_LAST_AUTOMATION_CHANGE_MADE_AT = "lastAutomationChangeMadeAt"
        private const val KEY_TARGET_ALLOCATION_CREATED_AT = "targetAllocationCreatedAt"

        private const val KEY_LAST_VERIFICATION_STEP_COMPLETED_AT =
            "lastVerificationStepCompletedAt"
        private const val KEY_DID_SEE_PORTFOLIO_BUY_SCHEDULING_OPTIONS =
            "didSeePortfolioBuySchedulingOptions"
        private const val KEY_DID_SEE_ADD_MONEY_SCHEDULING_OPTIONS =
            "didSeeAddMoneySchedulingOptions"
        private const val KEY_DID_SEE_AUTOMATED_REBALANCING_DESCRIPTION =
            "didSeeAutomatedRebalancingDescription"

        private const val KEY_VIEWED_INTERCOM_CAROUSELS = "viewedIntercomCarousels"
        private const val KEY_TRANSACTIONS_CACHE = "transactionsCache"
        private const val KEY_INVESTMENT_PRODUCTS_CACHE = "investmentProductsCache"
        private const val KEY_INVESTED_DASHBOARD_DATA = "investedDashboardData"
        private const val KEY_INVESTMENTS_DATA = "investmentsData"
        private const val KEY_DAILY_BRIEF_DATA = "dailyBriefData"
        private const val KEY_ACCOUNTS_DATA = "accountsData"
        private const val KEY_WEALTHY_HUB_DATA = "wealthyHubData"
        private const val KEY_SCREEN_CHOOSER_DATA = "screenChooserData"
        private const val KEY_PORTFOLIOS_CACHE = "portfoliosCache"
        private const val KEY_BANNER_CLOSE_INFO = "bannerCloseInfo"
        private const val KEY_DASHBOARD_ASSET_GROUPING_TYPE = "dashboardAssetGroupingType"
        private const val KEY_HAS_COMPLETED_A_QUALIFYING_TRADE = "hasCompletedAQualifyingTrade"
        private const val KEY_TARGET_ASSET_GROUPING_TYPE = "targetAssetGroupingType"

        private const val KEY_SHOULD_SHOW_INVESTED_DASHBOARD_WITHOUT_CHECKING =
            "shouldShowInvestedDashboardWithoutChecking"

        private const val KEY_FCM_NOTIFICATION_DATA = "fcmNotificationData"
    }

    private val keyGenParameterSpec = MasterKeys.AES256_GCM_SPEC
    private val mainKeyAlias = MasterKeys.getOrCreate(keyGenParameterSpec)

    private var didDeleteExistingPreferencesFile = false
    private lateinit var sharedPreferences: SharedPreferences

    init {
        initializeSharedPreferences(false)
    }

    private fun initializeSharedPreferences(reportSuccessToSentry: Boolean) {
        initializeEncryptedSharedPreferences()?.let {
            sharedPreferences = it

            if (reportSuccessToSentry) {
                Sentry.captureMessage("Recreated encrypted shared preferences")
            }

            return
        }

        if (didDeleteExistingPreferencesFile) {
            // We deleted the old preferences file and we failed to create a new
            // encrypted one. Fallback to normal preferences.
            sharedPreferences = initializeUnencryptedSharedPreferences()

            if (reportSuccessToSentry) {
                Sentry.captureMessage("Fallback to un-encrypted shared preferences")
            }

            return
        }

        // Delete the old preferences file. We can't decrypted. Create a new one.

        deleteSharedPreferences()
        initializeSharedPreferences(true)
    }

    private fun initializeEncryptedSharedPreferences(): SharedPreferences? {
        return try {
            EncryptedSharedPreferences.create(
                FILE_NAME,
                mainKeyAlias,
                context.applicationContext,
                EncryptedSharedPreferences.PrefKeyEncryptionScheme.AES256_SIV,
                EncryptedSharedPreferences.PrefValueEncryptionScheme.AES256_GCM
            )
        } catch (e: Exception) {
            Sentry.captureMessage("Failed to initialize encrypted shared preferences")
            null
        }
    }

    private fun initializeUnencryptedSharedPreferences(): SharedPreferences {
        return context.getSharedPreferences(FILE_NAME, Context.MODE_PRIVATE)
    }

    private fun deleteSharedPreferences() {
        context.deleteSharedPreferences(FILE_NAME)

        didDeleteExistingPreferencesFile = true
    }

    fun deletePreferencesRelatedToUserSwitching() {
        with(sharedPreferences.edit()) {
            remove(KEY_PIN)
            remove(KEY_DID_ASK_FOR_NOTIFICATIONS)

            apply()
        }
    }

    fun deleteLoginSessionPreferences() {
        with(sharedPreferences.edit()) {
            remove(KEY_USER_ID)
            remove(KEY_USER_CURRENCY)
            remove(KEY_USER_RESIDENCY_COUNTRY)
            remove(KEY_USER_IBAN)
            remove(KEY_USER_COMPANY_ENTITY)
            remove(KEY_USER_IS_REALTIME_ETF_EXECUTION_ENABLED)
            remove(KEY_GOOGLE_USER_ID)
            remove(KEY_USER_EMAIL)
            remove(KEY_USER_REFERRAL_CODE)
            remove(KEY_USER_IS_ROBO_ADVISOR_ENABLED)
            remove(KEY_PORTFOLIO_HAS_TARGET_ALLOCATION)
            remove(KEY_SELECTED_TAB)
            remove(KEY_PORTFOLIO)
            remove(KEY_DEEP_LINK_TRANSACTION_PREVIEW)
            remove(KEY_DEEP_LINK_ALLOCATION_METHOD)
            remove(KEY_DEEP_LINK_TRANSACTION)
            remove(KEY_DEEP_LINK_ACTION)
            remove(KEY_DEEP_LINK_ETF_ID)
            remove(KEY_DEEP_LINK_ETF_NAME)
            remove(KEY_DEEP_LINK_CAN_UNLOCK_FREE_SHARE)
            remove(KEY_ORDER_DEEP_LINK_BANK_LOGO_URI)
            remove(KEY_FREE_SHARE_DISCLAIMER_VIEWED)
            remove(KEY_DID_SEE_BLACK_FRIDAY_SCREEN)
            remove(KEY_TRANSACTIONS_CACHE)
            remove(KEY_INVESTMENT_PRODUCTS_CACHE)
            remove(KEY_INVESTED_DASHBOARD_DATA)
            remove(KEY_INVESTMENTS_DATA)
            remove(KEY_DAILY_BRIEF_DATA)
            remove(KEY_ACCOUNTS_DATA)
            remove(KEY_WEALTHY_HUB_DATA)
            remove(KEY_SCREEN_CHOOSER_DATA)
            remove(KEY_PORTFOLIOS_CACHE)
            remove(KEY_BANNER_CLOSE_INFO)
            remove(KEY_PORTFOLIO_ID)
            remove(KEY_DASHBOARD_ASSET_GROUPING_TYPE)
            remove(KEY_TARGET_ASSET_GROUPING_TYPE)
            remove(KEY_SHOULD_SHOW_INVESTED_DASHBOARD_WITHOUT_CHECKING)
            remove(KEY_FCM_NOTIFICATION_DATA)
            remove(KEY_DID_SEE_PORTFOLIO_BUY_SCHEDULING_OPTIONS)
            remove(KEY_DID_SEE_ADD_MONEY_SCHEDULING_OPTIONS)

            // Clear because investment universe is per user
            remove(KEY_SAVINGS_UNIVERSE)
            remove(KEY_SAVINGS_CONFIG)
            remove(KEY_ASSET_CLASSES)
            remove(KEY_ASSETS)
            remove(KEY_GEOGRAPHIES)
            remove(KEY_SECTORS)
            remove(KEY_BOND_CATEGORIES)
            remove(KEY_ETF_PROVIDERS)
            remove(KEY_WEALTHYHOOD_DIVIDENDS)
            remove(KEY_CASHBACKS)
            remove(KEY_FEES)
            remove(KEY_UNIVERSE_PLANS)
            remove(KEY_REWARD_SETTINGS)
            remove(KEY_EXECUTION_WINDOWS)
            remove(KEY_PRICING)
            remove(KEY_DISCOVER_CONFIGURATION)
            remove(KEY_SUPPORTED_COUNTRIES)
            remove(KEY_UNIVERSE_BANK_DETAILS)
            remove(KEY_LOCALES)
            remove(KEY_LEGAL_DOCUMENTS)
            remove(KEY_LEGAL_PAGES)
            remove(KEY_TAX_RESIDENCY_CONFIG)
            remove(KEY_DEPOSITS_CONFIG)
            remove(KEY_STATEMENT_PERIOD_SECTORS)
            remove(KEY_HAS_COMPLETED_A_QUALIFYING_TRADE)
            remove(KEY_DEEP_LINK_SHOULD_SHOW_SET_UP_MONTHLY)
            remove(KEY_DEEP_LINK_BUY_AMOUNT)

            // KEY_WHITELISTED_MAILS should NOT be removed.
            // The user should be able to logout and login with another whitelisted mail.

            apply()
        }
    }

    fun getDidSendInstallEvent(): Boolean {
        return sharedPreferences.getBoolean(KEY_DID_SEND_INSTALL_EVENT, false)
    }

    fun putDidSendInstallEvent(didSend: Boolean) {
        with(sharedPreferences.edit()) {
            putBoolean(KEY_DID_SEND_INSTALL_EVENT, didSend)
            apply()
        }
    }

    fun getAreBiometricsEnabled(): Boolean {
        return sharedPreferences.getBoolean(KEY_ARE_BIOMETRICS_ENABLED, false)
    }

    fun putAreBiometricsEnabled(areEnabled: Boolean) {
        with(sharedPreferences.edit()) {
            putBoolean(KEY_ARE_BIOMETRICS_ENABLED, areEnabled)
            apply()
        }
    }

    fun areNotificationsEnabledKeyExists(): Boolean {
        return sharedPreferences.contains(KEY_ARE_NOTIFICATIONS_ENABLED)
    }

    fun getAreNotificationsEnabled(): Boolean {
        return sharedPreferences.getBoolean(KEY_ARE_NOTIFICATIONS_ENABLED, false)
    }

    fun putAreNotificationsEnabled(areEnabled: Boolean) {
        with(sharedPreferences.edit()) {
            putBoolean(KEY_ARE_NOTIFICATIONS_ENABLED, areEnabled)
            apply()
        }
    }

    fun getDidAskForNotifications(): Boolean {
        return sharedPreferences.getBoolean(KEY_DID_ASK_FOR_NOTIFICATIONS, false)
    }

    fun putDidAskForNotifications(didAsk: Boolean) {
        with(sharedPreferences.edit()) {
            putBoolean(KEY_DID_ASK_FOR_NOTIFICATIONS, didAsk)
            apply()
        }
    }

    fun getDidSeeBlackFridayScreen(): Boolean {
        return sharedPreferences.getBoolean(KEY_DID_SEE_BLACK_FRIDAY_SCREEN, false)
    }

    fun putDidSeeBlackFridayScreen(didSee: Boolean) {
        with(sharedPreferences.edit()) {
            putBoolean(KEY_DID_SEE_BLACK_FRIDAY_SCREEN, didSee)
            apply()
        }
    }

    fun getPin(): String? {
        return sharedPreferences.getString(KEY_PIN, null)
    }

    fun putPin(pin: String?) {
        with(sharedPreferences.edit()) {
            putString(KEY_PIN, pin)
            apply()
        }
    }

    fun getGoogleUserID(): String? {
        return sharedPreferences.getString(KEY_GOOGLE_USER_ID, null)
    }

    fun putGoogleUserID(googleUserID: String?) {
        with(sharedPreferences.edit()) {
            putString(KEY_GOOGLE_USER_ID, googleUserID)
            apply()
        }
    }

    fun getLastLoginMail(): String? {
        return sharedPreferences.getString(KEY_LAST_LOGIN_MAIL, null)
    }

    fun putLastLoginMail(email: String?) {
        with(sharedPreferences.edit()) {
            putString(KEY_LAST_LOGIN_MAIL, email)
            apply()
        }
    }

    fun getUserID(): String? {
        return sharedPreferences.getString(KEY_USER_ID, null)
    }

    fun putUserID(userID: String?) {
        with(sharedPreferences.edit()) {
            putString(KEY_USER_ID, userID)
            apply()
        }
    }

    fun getUserCurrency(): String? {
        return sharedPreferences.getString(KEY_USER_CURRENCY, null)
    }

    fun putUserCurrency(userCurrency: String?) {
        with(sharedPreferences.edit()) {
            putString(KEY_USER_CURRENCY, userCurrency)
            apply()
        }
    }

    fun getUserResidencyCountry(): String? {
        return sharedPreferences.getString(KEY_USER_RESIDENCY_COUNTRY, null)
    }

    fun putUserResidencyCountry(residencyCountry: String?) {
        with(sharedPreferences.edit()) {
            putString(KEY_USER_RESIDENCY_COUNTRY, residencyCountry)
            apply()
        }
    }

    fun getUserIBAN(): String? {
        return sharedPreferences.getString(KEY_USER_IBAN, null)
    }

    fun putUserIBAN(iban: String?) {
        with(sharedPreferences.edit()) {
            putString(KEY_USER_IBAN, iban)
            apply()
        }
    }

    fun getUserCompanyEntity(): String? {
        return sharedPreferences.getString(KEY_USER_COMPANY_ENTITY, null)
    }

    fun putUserCompanyEntity(companyEntity: String?) {
        with(sharedPreferences.edit()) {
            putString(KEY_USER_COMPANY_ENTITY, companyEntity)
            apply()
        }
    }

    fun getUserIsRealtimeETFExecutionEnabled(): Boolean {
        return sharedPreferences.getBoolean(KEY_USER_IS_REALTIME_ETF_EXECUTION_ENABLED, false)
    }

    fun putUserIsRealtimeETFExecutionEnabled(value: Boolean) {
        with(sharedPreferences.edit()) {
            putBoolean(KEY_USER_IS_REALTIME_ETF_EXECUTION_ENABLED, value)
            apply()
        }
    }

    fun getUserEmail(): String? {
        return sharedPreferences.getString(KEY_USER_EMAIL, null)
    }

    fun putUserEmail(email: String?) {
        with(sharedPreferences.edit()) {
            putString(KEY_USER_EMAIL, email)
            apply()
        }
    }

    fun getUserReferralCode(): String? {
        return sharedPreferences.getString(KEY_USER_REFERRAL_CODE, null)
    }

    fun putUserReferralCode(email: String?) {
        with(sharedPreferences.edit()) {
            putString(KEY_USER_REFERRAL_CODE, email)
            apply()
        }
    }

    fun getUserIsRoboAdvisorEnabled(): Boolean {
        return sharedPreferences.getBoolean(KEY_USER_IS_ROBO_ADVISOR_ENABLED, false)
    }

    fun putUserIsRoboAdvisorEnabled(value: Boolean) {
        with(sharedPreferences.edit()) {
            putBoolean(KEY_USER_IS_ROBO_ADVISOR_ENABLED, value)
            apply()
        }
    }

    fun getPortfolioHasTargetAllocation(): Boolean {
        return sharedPreferences.getBoolean(KEY_PORTFOLIO_HAS_TARGET_ALLOCATION, false)
    }

    fun putPortfolioHasTargetAllocation(value: Boolean) {
        with(sharedPreferences.edit()) {
            putBoolean(KEY_PORTFOLIO_HAS_TARGET_ALLOCATION, value)
            apply()
        }
    }

    fun getMainSelectedTabIndex(): Int {
        return sharedPreferences.getInt(KEY_SELECTED_TAB, 0)
    }

    fun putMainSelectedTabIndex(selectedTab: Int?) {
        with(sharedPreferences.edit()) {
            putInt(KEY_SELECTED_TAB, selectedTab ?: 0)
            apply()
        }
    }

    fun getDeepLinkAction(): String? {
        return sharedPreferences.getString(KEY_DEEP_LINK_ACTION, null)
    }

    fun putDeepLinkAction(deepLinkAction: String?) {
        with(sharedPreferences.edit()) {
            putString(KEY_DEEP_LINK_ACTION, deepLinkAction)
            apply()
        }
    }

    fun getDeepLinkETFID(): String? {
        return sharedPreferences.getString(KEY_DEEP_LINK_ETF_ID, null)
    }

    fun putDeepLinkETFID(etfID: String?) {
        with(sharedPreferences.edit()) {
            putString(KEY_DEEP_LINK_ETF_ID, etfID)
            apply()
        }
    }

    fun getDeepLinkETFName(): String? {
        return sharedPreferences.getString(KEY_DEEP_LINK_ETF_NAME, null)
    }

    fun putDeepLinkETFName(etfName: String?) {
        with(sharedPreferences.edit()) {
            putString(KEY_DEEP_LINK_ETF_NAME, etfName)
            apply()
        }
    }

    fun getDeepLinkCanUnlockFreeShare(): Boolean {
        return sharedPreferences.getBoolean(KEY_DEEP_LINK_CAN_UNLOCK_FREE_SHARE, false)
    }

    fun putDeepLinkCanUnlockFreeShare(canUnlockFreeShare: Boolean) {
        with(sharedPreferences.edit()) {
            putBoolean(KEY_DEEP_LINK_CAN_UNLOCK_FREE_SHARE, canUnlockFreeShare)
            apply()
        }
    }

    fun getDeepLinkBankIconURI(): String? {
        return sharedPreferences.getString(KEY_DEEP_LINK_BANK_ICON_URI, null)
    }

    fun putDeepLinkBankIconURI(bankIconURI: String?) {
        with(sharedPreferences.edit()) {
            putString(KEY_DEEP_LINK_BANK_ICON_URI, bankIconURI)
            apply()
        }
    }

    fun getDeepLinkOrderBankLogoURI(): String? {
        return sharedPreferences.getString(KEY_ORDER_DEEP_LINK_BANK_LOGO_URI, null)
    }

    fun putDeepLinkOrderBankLogoURI(bankLogoURI: String?) {
        with(sharedPreferences.edit()) {
            putString(KEY_ORDER_DEEP_LINK_BANK_LOGO_URI, bankLogoURI)
            apply()
        }
    }

    fun getHasViewedFreeShareDisclaimer(): Boolean {
        return sharedPreferences.getBoolean(KEY_FREE_SHARE_DISCLAIMER_VIEWED, false)
    }

    fun putHasViewedFreeShareDisclaimer(hasViewed: Boolean) {
        with(sharedPreferences.edit()) {
            putBoolean(KEY_FREE_SHARE_DISCLAIMER_VIEWED, hasViewed)
            apply()
        }
    }

    fun getDeepLinkTransactionPreview(): TransactionPreview? {
        val jsonString =
            sharedPreferences.getString(KEY_DEEP_LINK_TRANSACTION_PREVIEW, null) ?: return null

        return try {
            Gson().fromJson(jsonString, TransactionPreview::class.java)
        } catch (e: Exception) {
            null
        }
    }

    fun putDeepLinkTransactionPreview(transactionPreview: TransactionPreview?) {
        with(sharedPreferences.edit()) {
            val transactionPreviewJSONString = transactionPreview?.let {
                Gson().toJson(it)
            }

            putString(KEY_DEEP_LINK_TRANSACTION_PREVIEW, transactionPreviewJSONString)
            commit()
        }
    }

    fun getDeepLinkTransaction(): Transaction? {
        val jsonString =
            sharedPreferences.getString(KEY_DEEP_LINK_TRANSACTION, null) ?: return null

        return try {
            Gson().fromJson(jsonString, Transaction::class.java)
        } catch (e: Exception) {
            null
        }
    }

    fun putDeepLinkTransaction(transaction: Transaction?) {
        with(sharedPreferences.edit()) {
            val jsonString = transaction?.let {
                Gson().toJson(it)
            }

            putString(KEY_DEEP_LINK_TRANSACTION, jsonString)
            commit()
        }
    }

    fun getDeepLinkAllocationMethod(): String? {
        return sharedPreferences.getString(KEY_DEEP_LINK_ALLOCATION_METHOD, null)
    }

    fun putDeepLinkAllocationMethod(allocationMethod: String?) {
        with(sharedPreferences.edit()) {
            putString(KEY_DEEP_LINK_ALLOCATION_METHOD, allocationMethod)
            apply()
        }
    }

    fun getPortfolio(): Portfolio? {
        val jsonString = sharedPreferences.getString(KEY_PORTFOLIO, null) ?: return null

        return try {
            Gson().fromJson(jsonString, Portfolio::class.java)
        } catch (e: Exception) {
            null
        }
    }

    fun putPortfolio(portfolio: Portfolio?) {
        // TODO: Do we need to save the whole portfolio just for the personal personalisationPreferences?
        with(sharedPreferences.edit()) {
            val portfolioJSONString = portfolio?.let {
                Gson().toJson(it)
            }

            putString(KEY_PORTFOLIO, portfolioJSONString)
            commit()
        }
    }

    fun getPortfolioID(): String? {
        return sharedPreferences.getString(KEY_PORTFOLIO_ID, null)
    }

    fun putPortfolioID(portfolioID: String?) {
        // TODO: Do we need to save the whole portfolio just for the personal personalisationPreferences?
        with(sharedPreferences.edit()) {
            putString(KEY_PORTFOLIO_ID, portfolioID)
            commit()
        }
    }

    fun getDidSendVerifyEvent(): Boolean {
        return sharedPreferences.getBoolean(KEY_DID_SEND_VERIFY_EVENT, false)
    }

    fun putDidSendVerifyEvent(didSend: Boolean) {
        with(sharedPreferences.edit()) {
            putBoolean(KEY_DID_SEND_VERIFY_EVENT, didSend)
            apply()
        }
    }

    fun getDidSendLinkBankAccountEvent(): Boolean {
        return sharedPreferences.getBoolean(KEY_DID_SEND_LINK_BANK_ACCOUNT_EVENT, false)
    }

    fun putDidSendLinkBankAccountEvent(didSend: Boolean) {
        with(sharedPreferences.edit()) {
            putBoolean(KEY_DID_SEND_LINK_BANK_ACCOUNT_EVENT, didSend)
            apply()
        }
    }

    fun getDidSendCreateFirstDepositEvent(): Boolean {
        return sharedPreferences.getBoolean(KEY_DID_SEND_CREATE_FIRST_DEPOSIT_EVENT, false)
    }

    fun putDidSendCreateFirstDepositEvent(didSend: Boolean) {
        with(sharedPreferences.edit()) {
            putBoolean(KEY_DID_SEND_CREATE_FIRST_DEPOSIT_EVENT, didSend)
            apply()
        }
    }

    fun getDidSendCreateFirstInvestmentEvent(): Boolean {
        return sharedPreferences.getBoolean(KEY_DID_SEND_CREATE_FIRST_INVESTMENT_EVENT, false)
    }

    fun putDidSendCreateFirstInvestmentEvent(didSend: Boolean) {
        with(sharedPreferences.edit()) {
            putBoolean(KEY_DID_SEND_CREATE_FIRST_INVESTMENT_EVENT, didSend)
            apply()
        }
    }

    fun setUpInvestmentUniverse(result: GetInvestmentUniverseResponse?) {
        putSavingsUniverse(result?.savingsUniverse)
        putSavingsConfig(result?.savingsConfig)
        putAssetClasses(result?.investmentUniverse?.assetClasses)
        putGeographies(result?.investmentUniverse?.geographies)
        putSectors(result?.investmentUniverse?.sectors)
        putBondCategories(result?.investmentUniverse?.bondCategories)
        putETFProviders(result?.investmentUniverse?.providers)

        putWealthyhoodDividends(result?.wealthyhoodDividends)
        putCashbacks(result?.cashbacks)
        putFees(result?.fees)
        putUniversePlans(result?.plans)
        putRewardSettings(result?.rewardSettings)
        putExecutionWindows(result?.executionWindows)
        putLocales(result?.locales)
        putLegalDocuments(result?.legalDocuments)
        putLegalPages(result?.legalPages)
        putTaxResidencyConfig(result?.taxResidency)
        putDepositsConfig(result?.deposits)
        putStatementPeriodSectors(result?.statementPeriods)

        putPricing(result?.pricing)

        putDiscoverConfiguration(result?.discoverConfiguration)
        putSupportedCountries(result?.supportedCountries)

        putBankDetails(result?.bankDetails)
    }

    fun getSavingsUniverse(): JsonObject? {
        val jsonString =
            sharedPreferences.getString(KEY_SAVINGS_UNIVERSE, null) ?: return null

        return try {
            Gson().fromJson(jsonString, JsonObject::class.java)
        } catch (e: Exception) {
            null
        }
    }

    private fun putSavingsUniverse(savingsUniverse: JsonObject?) {
        with(sharedPreferences.edit()) {
            val jsonString = savingsUniverse?.let {
                Gson().toJson(it)
            }

            putString(KEY_SAVINGS_UNIVERSE, jsonString)
            apply()
        }
    }

    fun getSavingsConfig(): GetInvestmentUniverseResponse.SavingsConfig? {
        val jsonString =
            sharedPreferences.getString(KEY_SAVINGS_CONFIG, null) ?: return null

        return try {
            Gson().fromJson(jsonString, GetInvestmentUniverseResponse.SavingsConfig::class.java)
        } catch (e: Exception) {
            null
        }
    }

    private fun putSavingsConfig(savingsConfig: GetInvestmentUniverseResponse.SavingsConfig?) {
        with(sharedPreferences.edit()) {
            val jsonString = savingsConfig?.let {
                Gson().toJson(it)
            }

            putString(KEY_SAVINGS_CONFIG, jsonString)
            apply()
        }
    }

    fun getAssetClasses(): JsonObject? {
        val jsonString = sharedPreferences.getString(KEY_ASSET_CLASSES, null) ?: return null

        return try {
            Gson().fromJson(jsonString, JsonObject::class.java)
        } catch (e: Exception) {
            null
        }
    }

    private fun putAssetClasses(assetClasses: JsonObject?) {
        with(sharedPreferences.edit()) {
            val jsonString = assetClasses?.let {
                Gson().toJson(it)
            }

            putString(KEY_ASSET_CLASSES, jsonString)
            apply()
        }
    }

    fun getGeographies(): JsonObject? {
        val jsonString = sharedPreferences.getString(KEY_GEOGRAPHIES, null) ?: return null

        return try {
            Gson().fromJson(jsonString, JsonObject::class.java)
        } catch (e: Exception) {
            null
        }
    }

    private fun putGeographies(geographies: JsonObject?) {
        with(sharedPreferences.edit()) {
            val jsonString = geographies?.let {
                Gson().toJson(it)
            }

            putString(KEY_GEOGRAPHIES, jsonString)
            apply()
        }
    }

    fun getSectors(): JsonObject? {
        val jsonString = sharedPreferences.getString(KEY_SECTORS, null) ?: return null

        return try {
            Gson().fromJson(jsonString, JsonObject::class.java)
        } catch (e: Exception) {
            null
        }
    }

    private fun putSectors(sectors: JsonObject?) {
        with(sharedPreferences.edit()) {
            val jsonString = sectors?.let {
                Gson().toJson(it)
            }

            putString(KEY_SECTORS, jsonString)
            apply()
        }
    }

    fun getBondCategories(): JsonObject? {
        val jsonString = sharedPreferences.getString(KEY_BOND_CATEGORIES, null) ?: return null

        return try {
            Gson().fromJson(jsonString, JsonObject::class.java)
        } catch (e: Exception) {
            null
        }
    }

    private fun putBondCategories(bondCategories: JsonObject?) {
        with(sharedPreferences.edit()) {
            val jsonString = bondCategories?.let {
                Gson().toJson(it)
            }

            putString(KEY_BOND_CATEGORIES, jsonString)
            apply()
        }
    }

    fun getETFProviders(): JsonObject? {
        val jsonString = sharedPreferences.getString(KEY_ETF_PROVIDERS, null) ?: return null

        return try {
            Gson().fromJson(jsonString, JsonObject::class.java)
        } catch (e: Exception) {
            null
        }
    }

    private fun putETFProviders(etfProviders: JsonObject?) {
        with(sharedPreferences.edit()) {
            val jsonString = etfProviders?.let {
                Gson().toJson(it)
            }

            putString(KEY_ETF_PROVIDERS, jsonString)
            apply()
        }
    }

    fun getPricing(): Map<String, Plan>? {
        val jsonString = sharedPreferences.getString(KEY_PRICING, null) ?: return null

        val itemType = object : TypeToken<Map<String, Plan>>() {}.type

        return try {
            Gson().fromJson(jsonString, itemType)
        } catch (e: Exception) {
            null
        }
    }

    private fun putPricing(pricingConfig: Map<String, Plan>?) {
        with(sharedPreferences.edit()) {
            val jsonString = pricingConfig?.let {
                Gson().toJson(it)
            }

            putString(KEY_PRICING, jsonString)
            commit()
        }
    }

    fun getLocales(): Map<String, String>? {
        val jsonString = sharedPreferences.getString(KEY_LOCALES, null) ?: return null

        val itemType = object : TypeToken<Map<String, String>>() {}.type

        return try {
            Gson().fromJson(jsonString, itemType)
        } catch (e: Exception) {
            null
        }
    }

    private fun putLocales(locales: Map<String, String>?) {
        with(sharedPreferences.edit()) {
            val jsonString = locales?.let {
                Gson().toJson(it)
            }

            putString(KEY_LOCALES, jsonString)
            commit()
        }
    }

    fun getLegalDocuments(): LegalLinks? {
        val jsonString =
            sharedPreferences.getString(KEY_LEGAL_DOCUMENTS, null) ?: return null

        return try {
            Gson().fromJson(
                jsonString,
                LegalLinks::class.java
            )
        } catch (e: Exception) {
            null
        }
    }

    private fun putLegalDocuments(legalLinks: LegalLinks?) {
        with(sharedPreferences.edit()) {
            val jsonString = legalLinks?.let {
                Gson().toJson(it)
            }

            putString(KEY_LEGAL_DOCUMENTS, jsonString)
            commit()
        }
    }

    fun getLegalPages(): LegalLinks? {
        val jsonString =
            sharedPreferences.getString(KEY_LEGAL_PAGES, null) ?: return null

        return try {
            Gson().fromJson(
                jsonString,
                LegalLinks::class.java
            )
        } catch (e: Exception) {
            null
        }
    }

    private fun putLegalPages(legalLinks: LegalLinks?) {
        with(sharedPreferences.edit()) {
            val jsonString = legalLinks?.let {
                Gson().toJson(it)
            }

            putString(KEY_LEGAL_PAGES, jsonString)
            commit()
        }
    }

    fun getTaxResidencyConfig(): TaxResidencyConfig? {
        val jsonString =
            sharedPreferences.getString(KEY_TAX_RESIDENCY_CONFIG, null) ?: return null

        return try {
            Gson().fromJson(
                jsonString,
                TaxResidencyConfig::class.java
            )
        } catch (e: Exception) {
            null
        }
    }

    private fun putTaxResidencyConfig(config: TaxResidencyConfig?) {
        with(sharedPreferences.edit()) {
            val jsonString = config?.let {
                Gson().toJson(it)
            }

            putString(KEY_TAX_RESIDENCY_CONFIG, jsonString)
            commit()
        }
    }

    fun getDepositsConfig(): DepositsConfig? {
        val jsonString =
            sharedPreferences.getString(KEY_DEPOSITS_CONFIG, null) ?: return null

        return try {
            Gson().fromJson(
                jsonString,
                DepositsConfig::class.java
            )
        } catch (e: Exception) {
            null
        }
    }

    private fun putDepositsConfig(config: DepositsConfig?) {
        with(sharedPreferences.edit()) {
            val jsonString = config?.let {
                Gson().toJson(it)
            }

            putString(KEY_DEPOSITS_CONFIG, jsonString)
            commit()
        }
    }

    fun getStatementPeriodSectors(): List<StatementPeriodsSector>? {
        val jsonString =
            sharedPreferences.getString(KEY_STATEMENT_PERIOD_SECTORS, null) ?: return null

        val itemType = object : TypeToken<List<StatementPeriodsSector>>() {}.type

        return try {
            Gson().fromJson(
                jsonString,
                itemType
            )
        } catch (e: Exception) {
            null
        }
    }

    private fun putStatementPeriodSectors(sectors: List<StatementPeriodsSector>?) {
        with(sharedPreferences.edit()) {
            val jsonString = sectors?.let {
                Gson().toJson(it)
            }

            putString(KEY_STATEMENT_PERIOD_SECTORS, jsonString)
            commit()
        }
    }

    fun getWhitelistedMails(): GetWhitelistedMailsResponse? {
        val jsonString =
            sharedPreferences.getString(KEY_WHITELISTED_MAILS, null) ?: return null

        return try {
            Gson().fromJson(
                jsonString,
                GetWhitelistedMailsResponse::class.java
            )
        } catch (e: Exception) {
            null
        }
    }

    fun putWhitelistedMails(mails: GetWhitelistedMailsResponse?) {
        with(sharedPreferences.edit()) {
            val jsonString = mails?.let {
                Gson().toJson(it)
            }

            putString(KEY_WHITELISTED_MAILS, jsonString)
            commit()
        }
    }

    fun getDiscoverConfiguration(): GetInvestmentUniverseResponse.DiscoverConfiguration? {
        val jsonString =
            sharedPreferences.getString(KEY_DISCOVER_CONFIGURATION, null) ?: return null

        return try {
            Gson().fromJson(
                jsonString,
                GetInvestmentUniverseResponse.DiscoverConfiguration::class.java
            )
        } catch (e: Exception) {
            null
        }
    }

    private fun putDiscoverConfiguration(discoverConfiguration: GetInvestmentUniverseResponse.DiscoverConfiguration?) {
        with(sharedPreferences.edit()) {
            val jsonString = discoverConfiguration?.let {
                Gson().toJson(it)
            }

            putString(KEY_DISCOVER_CONFIGURATION, jsonString)
            apply()
        }
    }

    fun getSupportedCountries(): List<SupportedCountry>? {
        val jsonString = sharedPreferences.getString(KEY_SUPPORTED_COUNTRIES, null) ?: return null

        val itemType = object : TypeToken<List<SupportedCountry>>() {}.type

        return try {
            Gson().fromJson(jsonString, itemType)
        } catch (e: Exception) {
            null
        }
    }

    private fun putSupportedCountries(countries: List<SupportedCountry>?) {
        with(sharedPreferences.edit()) {
            val jsonString = countries?.let {
                Gson().toJson(it)
            }

            putString(KEY_SUPPORTED_COUNTRIES, jsonString)
            apply()
        }
    }

    fun getBankDetails(): GetInvestmentUniverseResponse.BankDetails? {
        val jsonString = sharedPreferences.getString(KEY_UNIVERSE_BANK_DETAILS, null) ?: return null

        return try {
            Gson().fromJson(jsonString, GetInvestmentUniverseResponse.BankDetails::class.java)
        } catch (e: Exception) {
            null
        }
    }

    private fun putBankDetails(bankDetails: GetInvestmentUniverseResponse.BankDetails?) {
        with(sharedPreferences.edit()) {
            val jsonString = bankDetails?.let {
                Gson().toJson(it)
            }

            putString(KEY_UNIVERSE_BANK_DETAILS, jsonString)
            apply()
        }
    }

    fun getWealthyhoodDividends(): GetInvestmentUniverseResponse.WealthyhoodDividends? {
        val jsonString =
            sharedPreferences.getString(KEY_WEALTHYHOOD_DIVIDENDS, null) ?: return null

        return try {
            Gson().fromJson(
                jsonString,
                GetInvestmentUniverseResponse.WealthyhoodDividends::class.java
            )
        } catch (e: Exception) {
            null
        }
    }

    private fun putWealthyhoodDividends(dividends: GetInvestmentUniverseResponse.WealthyhoodDividends?) {
        with(sharedPreferences.edit()) {
            val jsonString = dividends?.let {
                Gson().toJson(it)
            }

            putString(KEY_WEALTHYHOOD_DIVIDENDS, jsonString)
            commit()
        }
    }

    fun getCashbacks(): GetInvestmentUniverseResponse.Cashbacks? {
        val jsonString =
            sharedPreferences.getString(KEY_CASHBACKS, null) ?: return null

        return try {
            Gson().fromJson(
                jsonString,
                GetInvestmentUniverseResponse.Cashbacks::class.java
            )
        } catch (e: Exception) {
            null
        }
    }

    private fun putCashbacks(dividends: GetInvestmentUniverseResponse.Cashbacks?) {
        with(sharedPreferences.edit()) {
            val jsonString = dividends?.let {
                Gson().toJson(it)
            }

            putString(KEY_CASHBACKS, jsonString)
            commit()
        }
    }

    fun getFees(): GetInvestmentUniverseResponse.Fees? {
        val jsonString =
            sharedPreferences.getString(KEY_FEES, null) ?: return null

        return try {
            Gson().fromJson(
                jsonString,
                GetInvestmentUniverseResponse.Fees::class.java
            )
        } catch (e: Exception) {
            null
        }
    }

    private fun putFees(fees: GetInvestmentUniverseResponse.Fees?) {
        with(sharedPreferences.edit()) {
            val jsonString = fees?.let {
                Gson().toJson(it)
            }

            putString(KEY_FEES, jsonString)
            commit()
        }
    }

    fun getUniversePlans(): GetInvestmentUniverseResponse.Plans? {
        val jsonString = sharedPreferences.getString(KEY_UNIVERSE_PLANS, null) ?: return null

        return try {
            Gson().fromJson(
                jsonString,
                GetInvestmentUniverseResponse.Plans::class.java
            )
        } catch (e: Exception) {
            null
        }
    }

    private fun putUniversePlans(fees: GetInvestmentUniverseResponse.Plans?) {
        with(sharedPreferences.edit()) {
            val jsonString = fees?.let {
                Gson().toJson(it)
            }

            putString(KEY_UNIVERSE_PLANS, jsonString)
            commit()
        }
    }

    fun getRewardSettings(): GetInvestmentUniverseResponse.RewardSettings? {
        val jsonString =
            sharedPreferences.getString(KEY_REWARD_SETTINGS, null) ?: return null

        return try {
            Gson().fromJson(
                jsonString,
                GetInvestmentUniverseResponse.RewardSettings::class.java
            )
        } catch (e: Exception) {
            null
        }
    }

    private fun putRewardSettings(rewardSettings: GetInvestmentUniverseResponse.RewardSettings?) {
        with(sharedPreferences.edit()) {
            val jsonString = rewardSettings?.let {
                Gson().toJson(it)
            }

            putString(KEY_REWARD_SETTINGS, jsonString)
            commit()
        }
    }

    fun getExecutionWindows(): GetInvestmentUniverseResponse.ExecutionWindows? {
        val jsonString =
            sharedPreferences.getString(KEY_EXECUTION_WINDOWS, null) ?: return null

        return try {
            Gson().fromJson(
                jsonString,
                GetInvestmentUniverseResponse.ExecutionWindows::class.java
            )
        } catch (e: Exception) {
            null
        }
    }

    fun getFormattedETFStartExecutionWindow(): String? {
        val fullDate = getExecutionWindows()?.etfStart?.convertToFullDate()

        return fullDate?.convertUTCToLocal(
            utcPattern = "yyyy-MM-dd HH:mm",
            localPattern = "HH:mm"
        )
    }

    private fun putExecutionWindows(executionWindows: GetInvestmentUniverseResponse.ExecutionWindows?) {
        with(sharedPreferences.edit()) {
            val jsonString = executionWindows?.let {
                Gson().toJson(it)
            }

            putString(KEY_EXECUTION_WINDOWS, jsonString)
            commit()
        }
    }

    fun getLastTransactionCreatedAt(): Long {
        return sharedPreferences.getLong(KEY_LAST_TRANSACTION_CREATED_AT, -1L)
    }

    fun putLastTransactionCreatedAt(timestamp: Long) {
        with(sharedPreferences.edit()) {
            putLong(KEY_LAST_TRANSACTION_CREATED_AT, timestamp)
            apply()
        }
    }

    fun getInvestmentProductsUpdatedAt(): Long {
        return sharedPreferences.getLong(KEY_INVESTMENT_PRODUCTS_UPDATED_AT, -1L)
    }

    fun putInvestmentProductsUpdatedAt(timestamp: Long) {
        with(sharedPreferences.edit()) {
            putLong(KEY_INVESTMENT_PRODUCTS_UPDATED_AT, timestamp)
            apply()
        }
    }

    fun getIncomingCashFlowsUpdatedAt(): Long {
        return sharedPreferences.getLong(KEY_INCOMING_CASH_FLOWS_UPDATED_AT, -1L)
    }

    fun putIncomingCashFlowsUpdatedAt(timestamp: Long) {
        with(sharedPreferences.edit()) {
            putLong(KEY_INCOMING_CASH_FLOWS_UPDATED_AT, timestamp)
            apply()
        }
    }

    fun getLastAutomationChangeMadeAt(): Long {
        return sharedPreferences.getLong(KEY_LAST_AUTOMATION_CHANGE_MADE_AT, -1L)
    }

    fun putLastAutomationChangeMadeAt(timestamp: Long) {
        with(sharedPreferences.edit()) {
            putLong(KEY_LAST_AUTOMATION_CHANGE_MADE_AT, timestamp)
            apply()
        }
    }

    fun getTargetAllocationCreatedAt(): Long {
        return sharedPreferences.getLong(KEY_TARGET_ALLOCATION_CREATED_AT, -1L)
    }

    fun putTargetAllocationCreatedAt(timestamp: Long) {
        with(sharedPreferences.edit()) {
            putLong(KEY_TARGET_ALLOCATION_CREATED_AT, timestamp)
            apply()
        }
    }

    fun getLastVerificationStepCompletedAt(): Long {
        return sharedPreferences.getLong(KEY_LAST_VERIFICATION_STEP_COMPLETED_AT, -1L)
    }

    fun putLastVerificationStepCompletedAt(timestamp: Long) {
        with(sharedPreferences.edit()) {
            putLong(KEY_LAST_VERIFICATION_STEP_COMPLETED_AT, timestamp)
            apply()
        }
    }

    fun getDidSeePortfolioBuySchedulingOptions(): Boolean {
        return sharedPreferences.getBoolean(KEY_DID_SEE_PORTFOLIO_BUY_SCHEDULING_OPTIONS, false)
    }

    fun putDidSeePortfolioBuySchedulingOptions(didSee: Boolean) {
        with(sharedPreferences.edit()) {
            putBoolean(KEY_DID_SEE_PORTFOLIO_BUY_SCHEDULING_OPTIONS, didSee)
            apply()
        }
    }

    fun getShouldShowInvestedDashboardWithoutChecking(): Boolean {
        return sharedPreferences.getBoolean(
            KEY_SHOULD_SHOW_INVESTED_DASHBOARD_WITHOUT_CHECKING,
            false
        )
    }

    fun putShouldShowInvestedDashboardWithoutChecking(show: Boolean) {
        with(sharedPreferences.edit()) {
            putBoolean(KEY_SHOULD_SHOW_INVESTED_DASHBOARD_WITHOUT_CHECKING, show)
            apply()
        }
    }

    fun getDidSeeAddMoneySchedulingOptions(): Boolean {
        return sharedPreferences.getBoolean(KEY_DID_SEE_ADD_MONEY_SCHEDULING_OPTIONS, false)
    }

    fun putDidSeeAddMoneySchedulingOptions(didSee: Boolean) {
        with(sharedPreferences.edit()) {
            putBoolean(KEY_DID_SEE_ADD_MONEY_SCHEDULING_OPTIONS, didSee)
            apply()
        }
    }

    fun getDidSeeAutomatedRebalancingDescription(): Boolean {
        return sharedPreferences.getBoolean(KEY_DID_SEE_AUTOMATED_REBALANCING_DESCRIPTION, false)
    }

    fun putDidSeeAutomatedRebalancingDescription(didSee: Boolean) {
        with(sharedPreferences.edit()) {
            putBoolean(KEY_DID_SEE_AUTOMATED_REBALANCING_DESCRIPTION, didSee)
            apply()
        }
    }

    fun getViewedIntercomCarousels(): Set<String>? {
        return sharedPreferences.getStringSet(KEY_VIEWED_INTERCOM_CAROUSELS, null)
    }

    fun putViewedIntercomCarousels(carousels: Set<String>?) {
        with(sharedPreferences.edit()) {
            putStringSet(KEY_VIEWED_INTERCOM_CAROUSELS, carousels)
            apply()
        }
    }

    fun getTransactionsCacheData(): CacheData? {
        val jsonString = sharedPreferences.getString(KEY_TRANSACTIONS_CACHE, null) ?: return null

        return try {
            Gson().fromJson(jsonString, CacheData::class.java)
        } catch (e: Exception) {
            null
        }
    }

    fun putTransactionsCacheData(cacheData: CacheData?) {
        with(sharedPreferences.edit()) {
            val jsonString = cacheData?.let {
                Gson().toJson(it)
            }

            putString(KEY_TRANSACTIONS_CACHE, jsonString)
            apply()
        }
    }

    fun getInvestmentProductsCacheData(): CacheData? {
        val jsonString =
            sharedPreferences.getString(KEY_INVESTMENT_PRODUCTS_CACHE, null) ?: return null

        return try {
            Gson().fromJson(jsonString, CacheData::class.java)
        } catch (e: Exception) {
            null
        }
    }

    fun putInvestmentProductsCacheData(cacheData: CacheData?) {
        with(sharedPreferences.edit()) {
            val jsonString = cacheData?.let {
                Gson().toJson(it)
            }

            putString(KEY_INVESTMENT_PRODUCTS_CACHE, jsonString)
            apply()
        }
    }

    fun getDashboardData(): DashboardData? {
        val jsonString =
            sharedPreferences.getString(KEY_INVESTED_DASHBOARD_DATA, null) ?: return null

        return try {
            Gson().fromJson(jsonString, DashboardData::class.java)
        } catch (e: Exception) {
            null
        }
    }

    fun putDashboardData(dashboardData: DashboardData?) {
        with(sharedPreferences.edit()) {
            val jsonString = dashboardData?.let {
                Gson().toJson(it)
            }

            putString(KEY_INVESTED_DASHBOARD_DATA, jsonString)
            apply()
        }
    }

    fun getInvestmentsData(): InvestmentsData? {
        val jsonString =
            sharedPreferences.getString(KEY_INVESTMENTS_DATA, null) ?: return null

        return try {
            Gson().fromJson(jsonString, InvestmentsData::class.java)
        } catch (e: Exception) {
            null
        }
    }

    fun putInvestmentsData(data: InvestmentsData?) {
        with(sharedPreferences.edit()) {
            val jsonString = data?.let {
                Gson().toJson(it)
            }

            putString(KEY_INVESTMENTS_DATA, jsonString)
            apply()
        }
    }

    fun getDailyBriefData(): DailyBriefData? {
        val jsonString =
            sharedPreferences.getString(KEY_DAILY_BRIEF_DATA, null) ?: return null

        return try {
            Gson().fromJson(jsonString, DailyBriefData::class.java)
        } catch (e: Exception) {
            null
        }
    }

    fun putDailyBriefData(data: DailyBriefData?) {
        with(sharedPreferences.edit()) {
            val jsonString = data?.let {
                Gson().toJson(it)
            }

            putString(KEY_DAILY_BRIEF_DATA, jsonString)
            apply()
        }
    }

    fun getAccountsData(): AccountsData? {
        val jsonString =
            sharedPreferences.getString(KEY_ACCOUNTS_DATA, null) ?: return null

        return try {
            Gson().fromJson(jsonString, AccountsData::class.java)
        } catch (e: Exception) {
            null
        }
    }

    fun putAccountsData(accountsData: AccountsData?) {
        with(sharedPreferences.edit()) {
            val jsonString = accountsData?.let {
                Gson().toJson(it)
            }

            putString(KEY_ACCOUNTS_DATA, jsonString)
            apply()
        }
    }

    fun getWealthyHubData(): WealthyHubData? {
        val jsonString =
            sharedPreferences.getString(KEY_WEALTHY_HUB_DATA, null) ?: return null

        return try {
            Gson().fromJson(jsonString, WealthyHubData::class.java)
        } catch (e: Exception) {
            null
        }
    }

    fun putWealthyHubData(wealthyHubData: WealthyHubData?) {
        with(sharedPreferences.edit()) {
            val jsonString = wealthyHubData?.let {
                Gson().toJson(it)
            }

            putString(KEY_WEALTHY_HUB_DATA, jsonString)
            apply()
        }
    }

    fun getScreenChooserData(): ScreenChooserData? {
        val jsonString =
            sharedPreferences.getString(KEY_SCREEN_CHOOSER_DATA, null) ?: return null

        return try {
            Gson().fromJson(jsonString, ScreenChooserData::class.java)
        } catch (e: Exception) {
            null
        }
    }

    fun putScreenChooserData(data: ScreenChooserData?) {
        with(sharedPreferences.edit()) {
            val jsonString = data?.let {
                Gson().toJson(it)
            }

            putString(KEY_SCREEN_CHOOSER_DATA, jsonString)
            apply()
        }
    }

    fun getPortfoliosCache(): List<CacheData>? {
        val jsonString = sharedPreferences.getString(KEY_PORTFOLIOS_CACHE, null) ?: return null

        val itemType = object : TypeToken<List<CacheData>>() {}.type

        return try {
            Gson().fromJson(jsonString, itemType)
        } catch (e: Exception) {
            null
        }
    }

    fun putPortfoliosCache(cache: List<CacheData>?) {
        with(sharedPreferences.edit()) {
            val jsonString = cache?.let {
                Gson().toJson(it)
            }

            putString(KEY_PORTFOLIOS_CACHE, jsonString)
            apply()
        }
    }

    fun getBannerCloseInfo(): List<BannerCloseInfo>? {
        val jsonString = sharedPreferences.getString(KEY_BANNER_CLOSE_INFO, null) ?: return null

        val itemType = object : TypeToken<List<BannerCloseInfo>>() {}.type

        return try {
            Gson().fromJson(jsonString, itemType)
        } catch (e: Exception) {
            null
        }
    }

    fun putBannerCloseInfo(info: List<BannerCloseInfo>?) {
        with(sharedPreferences.edit()) {
            val jsonString = info?.let {
                Gson().toJson(it)
            }

            putString(KEY_BANNER_CLOSE_INFO, jsonString)
            apply()
        }
    }

    // Asset grouping type on Dashboard
    fun getDashboardAssetGroupingType(): String? {
        return sharedPreferences.getString(KEY_DASHBOARD_ASSET_GROUPING_TYPE, null)
    }

    fun putDashboardAssetGroupingType(groupingTypeOptionID: String?) {
        with(sharedPreferences.edit()) {
            putString(KEY_DASHBOARD_ASSET_GROUPING_TYPE, groupingTypeOptionID)
            apply()
        }
    }

    // Asset grouping type on Target and Portfolio Creation
    fun getTargetAssetGroupingType(): String? {
        return sharedPreferences.getString(KEY_TARGET_ASSET_GROUPING_TYPE, null)
    }

    fun putTargetAssetGroupingType(groupingTypeOptionID: String?) {
        with(sharedPreferences.edit()) {
            putString(KEY_TARGET_ASSET_GROUPING_TYPE, groupingTypeOptionID)
            apply()
        }
    }

    fun getFCMNotificationData(): FCMNotificationData? {
        val jsonString =
            sharedPreferences.getString(KEY_FCM_NOTIFICATION_DATA, null) ?: return null

        return try {
            Gson().fromJson(jsonString, FCMNotificationData::class.java)
        } catch (e: Exception) {
            null
        }
    }

    fun putFCMNotificationData(data: FCMNotificationData?) {
        with(sharedPreferences.edit()) {
            val jsonString = data?.let {
                Gson().toJson(it)
            }

            putString(KEY_FCM_NOTIFICATION_DATA, jsonString)
            apply()
        }
    }

    fun getIfHasQualifyingTradeCompleted(): Boolean {
        return sharedPreferences.getBoolean(KEY_HAS_COMPLETED_A_QUALIFYING_TRADE, false)
    }

    fun putIfHasCompletedAQualifyingTrade(hasCompletedQualifyingTrade: Boolean) {
        with(sharedPreferences.edit()) {
            putBoolean(KEY_HAS_COMPLETED_A_QUALIFYING_TRADE, hasCompletedQualifyingTrade)
            apply()
        }
    }

    fun getDeepLinkBuyAmount(): String? {
        return sharedPreferences.getString(KEY_DEEP_LINK_BUY_AMOUNT, null)
    }

    fun putDeepLinkBuyAmount(buyAmount: String?) {
        with(sharedPreferences.edit()) {
            putString(KEY_DEEP_LINK_BUY_AMOUNT, buyAmount)
            commit()
        }
    }
}
