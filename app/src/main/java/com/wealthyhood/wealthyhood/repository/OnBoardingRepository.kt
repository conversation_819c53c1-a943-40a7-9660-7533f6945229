package com.wealthyhood.wealthyhood.repository

import android.content.Context
import com.wealthyhood.wealthyhood.database.WealthyhoodDatabase
import com.wealthyhood.wealthyhood.service.AssetClass
import com.wealthyhood.wealthyhood.service.BondCategory
import com.wealthyhood.wealthyhood.service.ETFProvider
import com.wealthyhood.wealthyhood.service.Geography
import com.wealthyhood.wealthyhood.service.SavingsProductConfig
import com.wealthyhood.wealthyhood.service.Sector
import kotlinx.coroutines.coroutineScope

class OnBoardingRepository {

    fun getSavingsProductConfigs(preferencesRepository: PreferencesRepository): List<SavingsProductConfig>? {
        val jsonObject = preferencesRepository.getSavingsUniverse() ?: return null

        val finalAnswer = mutableListOf<SavingsProductConfig>()

        jsonObject.keySet()?.forEach outerForEach@{ savingsProductID ->
            val configJsonObject = try {
                jsonObject.get(savingsProductID)?.asJsonObject
            } catch (e: IllegalStateException) {
                e.printStackTrace()
                null
            } ?: return@outerForEach

            SavingsProductConfig.fromJsonObject(
                savingsProductID = savingsProductID,
                jsonObject = configJsonObject
            )?.let { savingsProduct ->
                finalAnswer.add(savingsProduct)
            }
        }

        return finalAnswer
    }

    fun getAssetClasses(preferencesRepository: PreferencesRepository): List<AssetClass>? {
        val jsonObject = preferencesRepository.getAssetClasses() ?: return null

        val finalAnswer = mutableListOf<AssetClass>()

        jsonObject.keySet()?.forEach { assetClassID ->
            val assetClassJsonObject = try {
                jsonObject.get(assetClassID)?.asJsonObject
            } catch (e: IllegalStateException) {
                e.printStackTrace()
                null
            } ?: return@forEach

            AssetClass.fromJsonObject(assetClassID, assetClassJsonObject)?.let { assetClass ->
                finalAnswer.add(assetClass)
            }
        }

        return finalAnswer
    }

    fun getGeographies(preferencesRepository: PreferencesRepository): List<Geography>? {
        val jsonObject = preferencesRepository.getGeographies() ?: return null

        val finalAnswer = mutableListOf<Geography>()

        jsonObject.keySet()?.forEach { geographyID ->
            val geographyJsonObject = try {
                jsonObject.get(geographyID)?.asJsonObject
            } catch (e: IllegalStateException) {
                e.printStackTrace()
                null
            } ?: return@forEach

            Geography.fromJsonObject(geographyID, geographyJsonObject)?.let { geography ->
                finalAnswer.add(geography)
            }
        }

        return finalAnswer
    }

    fun getSectors(preferencesRepository: PreferencesRepository): List<Sector>? {
        val jsonObject = preferencesRepository.getSectors() ?: return null

        val finalAnswer = mutableListOf<Sector>()

        jsonObject.keySet()?.forEach { sectorID ->
            val sectorJsonObject = try {
                jsonObject.get(sectorID)?.asJsonObject
            } catch (e: IllegalStateException) {
                e.printStackTrace()
                null
            } ?: return@forEach

            Sector.fromJsonObject(sectorID, sectorJsonObject)?.let { sector ->
                if (sector.id == "general") return@forEach

                finalAnswer.add(sector)
            }
        }

        return finalAnswer
    }

    fun getETFProviders(preferencesRepository: PreferencesRepository): List<ETFProvider>? {
        val jsonObject = preferencesRepository.getETFProviders() ?: return null

        val finalAnswer = mutableListOf<ETFProvider>()

        jsonObject.keySet()?.forEach { etfProviderID ->
            val etfProviderJsonObject = try {
                jsonObject.get(etfProviderID)?.asJsonObject
            } catch (e: IllegalStateException) {
                e.printStackTrace()
                null
            } ?: return@forEach

            ETFProvider.fromJsonObject(etfProviderID, etfProviderJsonObject)?.let { etfProvider ->
                finalAnswer.add(etfProvider)
            }
        }

        return finalAnswer
    }

    suspend fun getAssetsCoroutines(
        context: Context,
        shouldExcludeDeprecated: Boolean = false
    ): List<com.wealthyhood.wealthyhood.database.Asset> {
        return coroutineScope {
            val database = WealthyhoodDatabase.getInstance(context.applicationContext)

            return@coroutineScope if (shouldExcludeDeprecated) {
                database.assetDAO.getNonDeprecated()
            } else database.assetDAO.getAll()
        }
    }

    suspend fun getAssetsByIDs(
        context: Context,
        assetIDs: List<String>
    ): List<com.wealthyhood.wealthyhood.database.Asset> {
        return coroutineScope {
            val database = WealthyhoodDatabase.getInstance(context.applicationContext)

            return@coroutineScope database.assetDAO.getAssetsByIDs(assetIDs)
        }
    }

    suspend fun getAsset(
        context: Context,
        assetID: String?
    ): com.wealthyhood.wealthyhood.database.Asset? {
        if (assetID == null) return null

        return coroutineScope {
            val database = WealthyhoodDatabase.getInstance(context.applicationContext)

            return@coroutineScope database.assetDAO.getAsset(assetID)
        }
    }

    suspend fun getAssetWithISIN(
        context: Context,
        isin: String?
    ): com.wealthyhood.wealthyhood.database.Asset? {
        if (isin == null) return null

        return coroutineScope {
            val database = WealthyhoodDatabase.getInstance(context.applicationContext)

            return@coroutineScope database.assetDAO.getAssetWithISIN(isin)
        }
    }

    fun getBondCategories(preferencesRepository: PreferencesRepository): List<BondCategory>? {
        val jsonObject = preferencesRepository.getBondCategories() ?: return null

        val finalAnswer = mutableListOf<BondCategory>()

        jsonObject.keySet()?.forEach { bondCategoryID ->
            val bondJsonObject = try {
                jsonObject.get(bondCategoryID)?.asJsonObject
            } catch (e: IllegalStateException) {
                e.printStackTrace()
                null
            } ?: return@forEach

            BondCategory.fromJsonObject(bondCategoryID, bondJsonObject)?.let { bondCategory ->
                if (bondCategory.id == "general") return@forEach

                finalAnswer.add(bondCategory)
            }
        }

        return finalAnswer
    }
}
