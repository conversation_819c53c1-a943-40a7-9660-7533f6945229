package com.wealthyhood.wealthyhood.repository

import android.content.Context
import android.text.Spannable
import android.text.SpannableString
import android.text.SpannableStringBuilder
import android.text.style.BulletSpan
import android.text.style.StrikethroughSpan
import androidx.annotation.ColorRes
import androidx.core.content.ContextCompat
import com.wealthyhood.wealthyhood.R
import com.wealthyhood.wealthyhood.extensions.convertCurrencyCodeToSymbol
import com.wealthyhood.wealthyhood.extensions.findUserLocale
import com.wealthyhood.wealthyhood.extensions.formatAsPercentage
import com.wealthyhood.wealthyhood.extensions.formatStringAsStrikeThrough
import com.wealthyhood.wealthyhood.extensions.formatSubstringsAsBold
import com.wealthyhood.wealthyhood.extensions.formatSubstringsWithColor
import com.wealthyhood.wealthyhood.extensions.generateFormattedCurrency
import com.wealthyhood.wealthyhood.extensions.generateFormattedNumber
import com.wealthyhood.wealthyhood.model.BasePlan
import com.wealthyhood.wealthyhood.model.BasePlanConfig
import com.wealthyhood.wealthyhood.model.PlanV3Feature
import com.wealthyhood.wealthyhood.service.Plan
import com.wealthyhood.wealthyhood.service.SavingsProductFeeDetails
import io.sentry.Sentry
import io.sentry.SentryLevel

class PlansRepository(private val preferencesRepository: PreferencesRepository) {

    private val currencyISOCode = preferencesRepository.getUserCurrency()
    private val userLocale = preferencesRepository.findUserLocale()

    // Το companyEntity το θέλουμε μόνο για τα plan features του Plans screen.
    // Επειδή εκεί δε μπορεί να αλλάξει, το κάνουμε initialize εδώ.

    private val companyEntity = preferencesRepository.getUserCompanyEntity()

    private val isRealtimeETFExecutionEnabled =
        preferencesRepository.getUserIsRealtimeETFExecutionEnabled()

    private val wealthyhoodDividends = preferencesRepository.getWealthyhoodDividends()
    private val cashbacks = preferencesRepository.getCashbacks()
    private val fees = preferencesRepository.getFees()

    companion object {

        private const val EARN_INTEREST_WITH_MMF_FEATURE_ID = "earnInterestWithMMFsFeature"
        private const val FX_RATE_FEATURE_ID = "fxRateFeature"

        fun generateFeesFromLines(
            context: Context,
            lines: List<Pair<String, Boolean>>
        ): SpannableString {
            val spannableStringBuilder = SpannableStringBuilder()

            lines.forEachIndexed { index, line ->
                val text = line.first
                val isStrikethrough = line.second

                if (index != 0) spannableStringBuilder.append("\n")

                spannableStringBuilder.append(
                    generateFeesLine(
                        context = context,
                        text = text,
                        isStrikeThrough = isStrikethrough
                    )
                )
            }

            return SpannableString(spannableStringBuilder)
        }

        private fun generateFeesLine(
            context: Context,
            text: String,
            isStrikeThrough: Boolean
        ): Spannable {
            val resources = context.resources

            val spannableString = SpannableString(text)

            val bulletSpacing = resources.getDimensionPixelSize(R.dimen.spacing_8)
            val bulletColor = ContextCompat.getColor(context, R.color.wealthyhood_dark_blue_60)

            spannableString.setSpan(
                BulletSpan(bulletSpacing, bulletColor),
                0,
                1,
                Spannable.SPAN_EXCLUSIVE_EXCLUSIVE
            )

            if (!isStrikeThrough) {
                return spannableString
            }

            val textStartIndex = 0
            val textEndIndex = text.length

            spannableString.setSpan(
                StrikethroughSpan(),
                textStartIndex,
                textEndIndex,
                Spannable.SPAN_EXCLUSIVE_EXCLUSIVE
            )

            return spannableString
        }
    }

    fun getAllBasePlans(
        context: Context,
        feeDetails: SavingsProductFeeDetails? = null
    ): List<BasePlan> {
        val isUserEU = (companyEntity == "WEALTHYHOOD_EUROPE")

        return mutableListOf(
            generateBasicBasePlan(context, feeDetails, isUserEU),
            generatePlusBasePlan(context, feeDetails, isUserEU),
            generateGoldBasePlan(context, feeDetails, isUserEU)
        )
    }

    fun getAllPlans(): List<Plan> {
        val finalAnswer = mutableListOf<Plan>()

        val prices = preferencesRepository.getPricing()

        prices?.forEach {
            finalAnswer.add(it.value)
        }

        return finalAnswer
    }

    fun getAllBasePlanConfigs(context: Context): List<BasePlanConfig> {
        val finalAnswer = mutableListOf<BasePlanConfig>()

        val resources = context.resources

        val beginnerConfig = BasePlanConfig(
            basePlanID = BasePlan.BASIC_PLAN_ID,
            downgradeDialogTitle = resources.getString(R.string.downgrade_message_beginner_title),
            backgroundDrawableRes = R.drawable.basic_plan_background,
            badgeBackgroundColorRes = null,
            badgeText = null,
            premiumFeatureIconTintColorRes = R.color.accent
        )

        val plusConfig = BasePlanConfig(
            basePlanID = BasePlan.PLUS_PLAN_ID,
            downgradeDialogTitle = resources.getString(R.string.downgrade_message_plus_title),
            backgroundDrawableRes = R.drawable.plus_plan_background,
            badgeBackgroundColorRes = R.color.grey_5,
            badgeText = resources.getString(R.string.plans_plus_badge_text),
            premiumFeatureIconTintColorRes = R.color.primary
        )

        val goldConfig = BasePlanConfig(
            basePlanID = BasePlan.GOLD_PLAN_ID,
            downgradeDialogTitle = null,
            backgroundDrawableRes = R.drawable.gold_plan_background,
            badgeBackgroundColorRes = R.color.gold_plan_billing_banner_background,
            badgeText = resources.getString(R.string.plans_gold_badge_text),
            premiumFeatureIconTintColorRes = R.color.gold_plan_premium_feature_icon_tint
        )

        finalAnswer.add(beginnerConfig)
        finalAnswer.add(plusConfig)
        finalAnswer.add(goldConfig)

        return finalAnswer
    }

    private fun generateBeginnerPlanFees(context: Context): SpannableString {
        val fees = preferencesRepository.getFees()

        val fxFee = fees?.fx?.rates?.free
        val formattedFXFee = fxFee?.formatAsPercentage(
            locale = userLocale
        )

        val fxFeeText = context.resources.getString(
            R.string.plan_fx_fee_text,
            formattedFXFee
        )

        val custodyFee = fees?.custody?.rates?.free
        val formattedCustodyFee = custodyFee?.formatAsPercentage(
            locale = userLocale
        )

        val custodyFeeText = context.resources.getString(
            R.string.plan_custody_fee_text,
            formattedCustodyFee
        )

        val lines = mutableListOf(
            Pair(fxFeeText, false),
            Pair(custodyFeeText, false)
        )

        return generateFeesFromLines(
            context = context,
            lines = lines
        )
    }

    private fun generatePlusPlanFees(context: Context): SpannableString {
        val fees = preferencesRepository.getFees()

        val fxFee = fees?.fx?.rates?.paidLow
        val formattedFXFee = fxFee?.formatAsPercentage(
            locale = userLocale
        )

        val fxFeeText = context.resources.getString(
            R.string.plan_fx_fee_text,
            formattedFXFee
        )

        val lines = mutableListOf(
            Pair(fxFeeText, false)
        )

        return generateFeesFromLines(
            context = context,
            lines = lines
        )
    }

    private fun generateProPlanFees(context: Context): SpannableString {
        val fees = preferencesRepository.getFees()

        val fxFee = fees?.fx?.rates?.paidMid
        val formattedFXFee = fxFee?.formatAsPercentage(
            locale = userLocale
        )

        val fxFeeText = context.resources.getString(
            R.string.plan_fx_fee_text,
            formattedFXFee
        )

        val lines = mutableListOf(
            Pair(fxFeeText, false)
        )

        return generateFeesFromLines(
            context = context,
            lines = lines
        )
    }

    private fun generateFractionalSharesFeature(
        context: Context,
        currencyISOCode: String?
    ): PlanV3Feature {
        val resources = context.resources

        val amountText = 1.0.generateFormattedCurrency(
            currencyISOCode = currencyISOCode,
            maximumFractionDigits = 0,
            locale = userLocale
        )

        val description = resources.getString(
            R.string.fractional_shares_feature_description,
            amountText
        )

        return PlanV3Feature(
            id = "fractionalSharesFeature",
            iconDrawableRes = R.drawable.ic_layers_filled,
            title = resources.getString(R.string.fractional_shares_feature_title),
            titleSuffix = null,
            description = description,
            isEnabled = true,
            isComingSoon = false,
            isPremium = false
        )
    }

    private fun generatePortfolioAutomationFeature(context: Context): PlanV3Feature {
        val resources = context.resources

        return PlanV3Feature(
            id = "portfolioAutomationFeature",
            iconDrawableRes = R.drawable.ic_autopilot,
            title = resources.getString(R.string.portfolio_automation_feature_title),
            titleSuffix = null,
            description = resources.getString(R.string.portfolio_automation_feature_description),
            isEnabled = true,
            isComingSoon = false,
            isPremium = false
        )
    }

    private fun generatePortfolioTemplatesFeature(context: Context): PlanV3Feature {
        val resources = context.resources

        return PlanV3Feature(
            id = "portfolioTemplatesFeature",
            iconDrawableRes = R.drawable.ic_web,
            title = resources.getString(R.string.portfolio_templates_feature_title),
            titleSuffix = null,
            description = resources.getString(R.string.portfolio_templates_feature_description),
            isEnabled = true,
            isComingSoon = false,
            isPremium = false
        )
    }

    private fun generateLearningGuidesFeature(
        context: Context,
        isEnabled: Boolean,
        isPremium: Boolean
    ): PlanV3Feature {
        val resources = context.resources

        return PlanV3Feature(
            id = "learningGuidesFeature",
            iconDrawableRes = R.drawable.ic_school,
            title = resources.getString(R.string.learning_guides_feature_title),
            titleSuffix = null,
            description = resources.getString(R.string.learning_guides_feature_description),
            isEnabled = isEnabled,
            isComingSoon = false,
            isPremium = isPremium
        )
    }

    private fun generateFreeDepositsAndWithdrawalsFeature(context: Context): PlanV3Feature {
        val resources = context.resources

        return PlanV3Feature(
            id = "freeDepositsAndWithdrawalsFeature",
            iconDrawableRes = R.drawable.ic_redeem,
            title = resources.getString(R.string.free_deposits_and_withdrawals_feature_title),
            titleSuffix = null,
            description = resources.getString(R.string.free_deposits_and_withdrawals_feature_description),
            isEnabled = true,
            isComingSoon = false,
            isPremium = false
        )
    }

    private fun generateEarnInterestWithMMFsFeature(
        context: Context,
        feeDetailsItem: SavingsProductFeeDetails.FeeDetailsItem?,
        isPremium: Boolean
    ): PlanV3Feature {
        val resources = context.resources

        val netInterestRate = feeDetailsItem?.netInterestRateValue?.div(100)?.formatAsPercentage(
            locale = userLocale
        )

        val title = resources.getString(
            R.string.earn_interest_with_mmfs_feature_title,
            netInterestRate
        )

        return PlanV3Feature(
            id = EARN_INTEREST_WITH_MMF_FEATURE_ID,
            iconDrawableRes = R.drawable.ic_humidity_percentage,
            title = title,
            titleSuffix = null,
            description = "plans_fee_matrix",
            isEnabled = true,
            isComingSoon = false,
            isPremium = isPremium
        )
    }

    private fun generateCommissionFreeFeature(
        context: Context,
        currencyISOCode: String?,
        isEnabled: Boolean,
        isPremiumFeature: Boolean
    ): PlanV3Feature {
        val resources = context.resources

        val currencySymbol = currencyISOCode?.convertCurrencyCodeToSymbol()

        val title: String
        val description: String

        val formattedNumber = (1000.0).generateFormattedNumber(
            minimumFractionDigits = 0,
            maximumFractionDigits = 0,
            locale = userLocale
        )

        if (isRealtimeETFExecutionEnabled) {
            title = resources.getString(
                R.string.commission_free_feature_title_2,
                formattedNumber
            )

            description = resources.getString(R.string.commission_free_feature_description_2)
        } else {
            title = resources.getString(R.string.commission_free_feature_title)

            description = resources.getString(
                R.string.commission_free_feature_description,
                currencySymbol
            )
        }

        return PlanV3Feature(
            id = "commissionFreeFeature",
            iconDrawableRes = R.drawable.ic_money_off,
            title = title,
            titleSuffix = null,
            description = description,
            isEnabled = isEnabled,
            isComingSoon = false,
            isPremium = isPremiumFeature
        )
    }

    private fun generateDividendFeature(
        context: Context,
        currencyISOCode: String?,
        bonusDividend: String?,
        extra: String?,
        isEnabled: Boolean,
        isPremiumFeature: Boolean
    ): PlanV3Feature {
        val resources = context.resources

        val titleSuffix = extra?.let {
            resources.getString(R.string.feature_title_suffix, it)
        }

        val dividendLimit = wealthyhoodDividends?.maximumPortfolioValue

        val formattedDividendLimit = dividendLimit?.generateFormattedCurrency(
            currencyISOCode = currencyISOCode,
            maximumFractionDigits = 0,
            locale = userLocale
        )

        return PlanV3Feature(
            id = "dividendFeature",
            iconDrawableRes = R.drawable.ic_payments,
            title = resources.getString(
                R.string.dividend_feature_title,
                bonusDividend
            ),
            titleSuffix = titleSuffix,
            description = resources.getString(
                R.string.dividend_feature_description,
                bonusDividend,
                bonusDividend,
                formattedDividendLimit
            ),
            isEnabled = isEnabled,
            isComingSoon = false,
            isPremium = isPremiumFeature
        )
    }

    private fun generateCashbackFeature(
        context: Context,
        currencyISOCode: String?,
        bonusCashback: String?,
        extra: String?,
        isEnabled: Boolean,
        isPremiumFeature: Boolean
    ): PlanV3Feature {
        val resources = context.resources

        val titleSuffix = extra?.let {
            resources.getString(R.string.feature_title_suffix, it)
        }

        val minimumInvestmentAmount =
            cashbacks?.minimumInvestmentAmount?.generateFormattedCurrency(
                currencyISOCode = currencyISOCode,
                maximumFractionDigits = 0,
                locale = userLocale
            )

        return PlanV3Feature(
            id = "cashbackFeature",
            iconDrawableRes = R.drawable.ic_currency_exchange,
            title = resources.getString(
                R.string.cashback_feature_title,
                bonusCashback
            ),
            titleSuffix = titleSuffix,
            description = resources.getString(
                R.string.cashback_feature_description,
                bonusCashback,
                minimumInvestmentAmount
            ),
            isEnabled = isEnabled,
            isComingSoon = false,
            isPremium = isPremiumFeature
        )
    }

    private fun generateZeroCustodyFeesFeature(
        context: Context,
        isEnabled: Boolean,
        isPremiumFeature: Boolean,
        isUserEU: Boolean
    ): PlanV3Feature {
        val resources = context.resources

        val title = resources.getString(R.string.zero_custody_fees_feature_title)

        val description = if (isUserEU) {
            resources.getString(R.string.zero_custody_fees_feature_description_eu)
        } else {
            resources.getString(R.string.zero_custody_fees_feature_description_uk)
        }

        return PlanV3Feature(
            id = "zeroCustodyFeesFeature",
            iconDrawableRes = R.drawable.ic_counter_0,
            title = title,
            titleSuffix = null,
            description = description,
            isEnabled = isEnabled,
            isComingSoon = false,
            isPremium = isPremiumFeature
        )
    }

    private fun generateFXRateFeature(
        title: String,
        description: String,
        isEnabled: Boolean,
        isPremiumFeature: Boolean
    ): PlanV3Feature {
        return PlanV3Feature(
            id = FX_RATE_FEATURE_ID,
            iconDrawableRes = R.drawable.ic_monetization_on,
            title = title,
            titleSuffix = null,
            description = description,
            isEnabled = isEnabled,
            isComingSoon = false,
            isPremium = isPremiumFeature
        )
    }

    private fun generatePriorityCustomerServiceFeature(
        context: Context,
        isEnabled: Boolean,
        isPremiumFeature: Boolean
    ): PlanV3Feature {
        val resources = context.resources

        return PlanV3Feature(
            id = "priorityCustomerServiceFeature",
            iconDrawableRes = R.drawable.ic_support_agent,
            title = resources.getString(R.string.priority_customer_service_feature_title),
            titleSuffix = null,
            description = resources.getString(R.string.priority_customer_service_feature_description),
            isEnabled = isEnabled,
            isComingSoon = false,
            isPremium = isPremiumFeature
        )
    }

    private fun generateInstantExecutionFeature(
        context: Context,
        isEnabled: Boolean,
        isPremiumFeature: Boolean,
        isComingSoon: Boolean
    ): PlanV3Feature {
        val resources = context.resources

        return PlanV3Feature(
            id = "instantExecutionFeature",
            iconDrawableRes = R.drawable.ic_acute,
            title = resources.getString(R.string.instant_execution_feature_title),
            titleSuffix = null,
            description = resources.getString(R.string.instant_execution_feature_description),
            isEnabled = isEnabled,
            isComingSoon = isComingSoon,
            isPremium = isPremiumFeature
        )
    }

    private fun generateDeletedFormattedPrice(
        context: Context,
        formattedPrice: String?,
        @ColorRes colorRes: Int
    ): SpannableString? {
        if (formattedPrice == null) return null

        val formattedPriceSuffix = context.resources.getString(R.string.plans_plan_price_suffix)
        val finalString = "$formattedPrice $formattedPriceSuffix"

        val spannableString = SpannableString(finalString)

        spannableString.formatSubstringsAsBold(
            context = context,
            substrings = listOf(formattedPrice)
        )

        spannableString.formatSubstringsWithColor(
            context = context,
            substrings = listOf(formattedPrice),
            colorRes = colorRes
        )

        spannableString.formatStringAsStrikeThrough(
            string = finalString,
            startSearchingFromIndex = 0
        )

        return spannableString
    }

    private fun generateBasicBasePlan(
        context: Context,
        feeDetails: SavingsProductFeeDetails?,
        isUserEU: Boolean
    ): BasePlan {
        return BasePlan(
            id = BasePlan.BASIC_PLAN_ID,
            weight = 0,
            title = context.resources.getString(R.string.basic_label),
            features = generateBasicPlanFeatures(context, feeDetails, isUserEU),
            feesText = generateBeginnerPlanFees(context)
        )
    }

    private fun generatePlusBasePlan(
        context: Context,
        feeDetails: SavingsProductFeeDetails?,
        isUserEU: Boolean
    ): BasePlan {
        return BasePlan(
            id = BasePlan.PLUS_PLAN_ID,
            weight = 1,
            title = context.resources.getString(R.string.plus_label),
            features = generatePlusPlanFeatures(context, feeDetails, isUserEU),
            feesText = generatePlusPlanFees(context)
        )
    }

    private fun generateGoldBasePlan(
        context: Context,
        feeDetails: SavingsProductFeeDetails?,
        isUserEU: Boolean
    ): BasePlan {
        return BasePlan(
            id = BasePlan.GOLD_PLAN_ID,
            weight = 2,
            title = context.resources.getString(R.string.gold_label),
            features = generateGoldPlanFeatures(context, feeDetails, isUserEU),
            feesText = generateProPlanFees(context)
        )
    }

    private fun generateBasicPlanFeatures(
        context: Context,
        feeDetails: SavingsProductFeeDetails?,
        isUserEU: Boolean
    ): List<PlanV3Feature> {
        val resources = context.resources

        val dividendFeature = if (!isUserEU) {
            // We need to show the Wealthyhood dividend from the Plus plan as deleted.
            val dividend = wealthyhoodDividends?.rates?.paidLow

            val formattedDividend = dividend?.formatAsPercentage(
                minimumFractionDigits = 0,
                maximumFractionDigits = 0,
                locale = userLocale
            )

            generateDividendFeature(
                context = context,
                currencyISOCode = currencyISOCode,
                bonusDividend = formattedDividend,
                extra = null,
                isEnabled = false,
                isPremiumFeature = false
            )
        } else null

        val cashBackFeature = if (!isUserEU) {
            // We need to show the cashback from the Plus plan as deleted.
            val cashback = cashbacks?.rates?.paidLow

            val formattedCashback = cashback?.formatAsPercentage(
                minimumFractionDigits = 0,
                maximumFractionDigits = 2,
                locale = userLocale
            )

            generateCashbackFeature(
                context = context,
                currencyISOCode = currencyISOCode,
                bonusCashback = formattedCashback,
                extra = null,
                isEnabled = false,
                isPremiumFeature = false
            )
        } else null

        val plusFXRate = fees?.fx?.rates?.paidLow
        val formattedPlusFXRate = plusFXRate?.formatAsPercentage(
            minimumFractionDigits = 2,
            maximumFractionDigits = 2,
            locale = userLocale
        )

        val goldFXRate = fees?.fx?.rates?.paidMid
        val formattedGoldFXRate = goldFXRate?.formatAsPercentage(
            minimumFractionDigits = 2,
            maximumFractionDigits = 2,
            locale = userLocale
        )

        val fxRateFeatureTitle = resources.getString(R.string.premium_fx_rates_feature_title)

        val fxRateFeatureDescription = resources.getString(
            R.string.premium_fx_rates_feature_description,
            formattedPlusFXRate,
            formattedGoldFXRate
        )

        val feeDetailsItem = feeDetails?.feeDetails?.find {
            it.planPriceAPIKey == BasePlan.BASIC_PLAN_ID
        }

        val features = listOfNotNull(
            generateCommissionFreeFeature(
                context = context,
                currencyISOCode = currencyISOCode,
                isEnabled = true,
                isPremiumFeature = false
            ),
            generateFractionalSharesFeature(context = context, currencyISOCode = currencyISOCode),
            generatePortfolioAutomationFeature(context),
            generatePortfolioTemplatesFeature(context),
            generateFreeDepositsAndWithdrawalsFeature(context),
            generateEarnInterestWithMMFsFeature(
                context = context,
                feeDetailsItem = feeDetailsItem,
                isPremium = false
            ),
            generateLearningGuidesFeature(
                context = context,
                isEnabled = false,
                isPremium = false
            ),
            dividendFeature,
            cashBackFeature,
            generateFXRateFeature(
                title = fxRateFeatureTitle,
                description = fxRateFeatureDescription,
                isEnabled = false,
                isPremiumFeature = false
            ),
            generatePriorityCustomerServiceFeature(
                context = context,
                isEnabled = false,
                isPremiumFeature = false
            )
        )

        val finalAnswer = generateListByAddingZeroCustodyFeesFeature(
            context = context,
            existingFeatures = features,
            isUserEU = isUserEU,
            isPlanBasic = true
        )

        return finalAnswer
    }

    private fun generatePlusPlanFeatures(
        context: Context,
        feeDetails: SavingsProductFeeDetails?,
        isUserEU: Boolean
    ): List<PlanV3Feature> {
        val resources = context.resources

        val dividendFeature = if (!isUserEU) {
            val dividend = wealthyhoodDividends?.rates?.paidLow

            val formattedDividend = dividend?.formatAsPercentage(
                minimumFractionDigits = 0,
                maximumFractionDigits = 0,
                locale = userLocale
            )

            generateDividendFeature(
                context = context,
                currencyISOCode = currencyISOCode,
                bonusDividend = formattedDividend,
                extra = null,
                isEnabled = true,
                isPremiumFeature = true
            )
        } else null

        val cashBackFeature = if (!isUserEU) {
            val cashback = cashbacks?.rates?.paidLow

            val formattedCashback = cashback?.formatAsPercentage(
                minimumFractionDigits = 0,
                maximumFractionDigits = 2,
                locale = userLocale
            )

            generateCashbackFeature(
                context = context,
                currencyISOCode = currencyISOCode,
                bonusCashback = formattedCashback,
                extra = null,
                isEnabled = true,
                isPremiumFeature = true
            )
        } else null

        val fxRate = fees?.fx?.rates?.paidLow
        val formattedFXRate = fxRate?.formatAsPercentage(
            minimumFractionDigits = 2,
            maximumFractionDigits = 2,
            locale = userLocale
        )

        val fxRateFeatureTitle = resources.getString(
            R.string.plus_fx_rate_feature_title,
            formattedFXRate
        )

        val fxRateFeatureDescription = resources.getString(
            R.string.plus_fx_rate_feature_description,
            formattedFXRate
        )

        val feeDetailsItem = feeDetails?.feeDetails?.find {
            it.planPriceAPIKey == BasePlan.PLUS_PLAN_ID
        }

        val features = listOfNotNull(
            generateCommissionFreeFeature(
                context = context,
                currencyISOCode = currencyISOCode,
                isEnabled = true,
                isPremiumFeature = false
            ),
            generateFractionalSharesFeature(context = context, currencyISOCode = currencyISOCode),
            generatePortfolioAutomationFeature(context),
            generatePortfolioTemplatesFeature(context),
            generateFreeDepositsAndWithdrawalsFeature(context),
            generateEarnInterestWithMMFsFeature(
                context = context,
                feeDetailsItem = feeDetailsItem,
                isPremium = true
            ),
            generateLearningGuidesFeature(
                context = context,
                isEnabled = true,
                isPremium = true
            ),
            dividendFeature,
            cashBackFeature,
            generateFXRateFeature(
                title = fxRateFeatureTitle,
                description = fxRateFeatureDescription,
                isEnabled = true,
                isPremiumFeature = true
            ),
            generatePriorityCustomerServiceFeature(
                context = context,
                isEnabled = false,
                isPremiumFeature = false
            )
        )

        val finalAnswer = generateListByAddingZeroCustodyFeesFeature(
            context = context,
            existingFeatures = features,
            isUserEU = isUserEU,
            isPlanBasic = false
        )

        return finalAnswer
    }

    private fun generateGoldPlanFeatures(
        context: Context,
        feeDetails: SavingsProductFeeDetails?,
        isUserEU: Boolean
    ): List<PlanV3Feature> {
        val resources = context.resources

        val dividendFeature = if (!isUserEU) {
            val dividend = wealthyhoodDividends?.rates?.paidMid

            val formattedDividend = dividend?.formatAsPercentage(
                minimumFractionDigits = 0,
                maximumFractionDigits = 0,
                locale = userLocale
            )

            val previousDividend = wealthyhoodDividends?.rates?.paidLow

            val dividendExtra = if (dividend != null && previousDividend != null) {
                val dividendDiff = dividend - previousDividend
                dividendDiff.formatAsPercentage(
                    minimumFractionDigits = 0,
                    maximumFractionDigits = 0,
                    locale = userLocale
                )
            } else null

            generateDividendFeature(
                context = context,
                currencyISOCode = currencyISOCode,
                bonusDividend = formattedDividend,
                extra = dividendExtra,
                isEnabled = true,
                isPremiumFeature = true
            )
        } else null

        val cashBackFeature = if (!isUserEU) {
            val cashback = cashbacks?.rates?.paidMid

            val formattedCashback = cashback?.formatAsPercentage(
                minimumFractionDigits = 2,
                maximumFractionDigits = 2,
                locale = userLocale
            )

            val previousCashback = cashbacks?.rates?.paidLow

            val cashbackExtra = if (cashback != null && previousCashback != null) {
                val cashbackDiff = cashback - previousCashback
                cashbackDiff.formatAsPercentage(
                    locale = userLocale
                )
            } else null

            generateCashbackFeature(
                context = context,
                currencyISOCode = currencyISOCode,
                bonusCashback = formattedCashback,
                extra = cashbackExtra,
                isEnabled = true,
                isPremiumFeature = true
            )
        } else null

        val fxRate = fees?.fx?.rates?.paidMid
        val formattedFXRate = fxRate?.formatAsPercentage(
            minimumFractionDigits = 2,
            maximumFractionDigits = 2,
            locale = userLocale
        )

        val fxRateFeatureTitle = resources.getString(
            R.string.gold_fx_rate_feature_title,
            formattedFXRate
        )

        val fxRateFeatureDescription = resources.getString(
            R.string.gold_fx_rate_feature_description,
            formattedFXRate
        )

        val feeDetailsItem = feeDetails?.feeDetails?.find {
            it.planPriceAPIKey == BasePlan.GOLD_PLAN_ID
        }

        val features = listOfNotNull(
            generateCommissionFreeFeature(
                context = context,
                currencyISOCode = currencyISOCode,
                isEnabled = true,
                isPremiumFeature = false
            ),
            generateFractionalSharesFeature(context = context, currencyISOCode = currencyISOCode),
            generatePortfolioAutomationFeature(context),
            generatePortfolioTemplatesFeature(context),
            generateFreeDepositsAndWithdrawalsFeature(context),
            generateEarnInterestWithMMFsFeature(
                context = context,
                feeDetailsItem = feeDetailsItem,
                isPremium = true
            ),
            generateLearningGuidesFeature(
                context = context,
                isEnabled = true,
                isPremium = true
            ),
            dividendFeature,
            cashBackFeature,
            generateFXRateFeature(
                title = fxRateFeatureTitle,
                description = fxRateFeatureDescription,
                isEnabled = true,
                isPremiumFeature = true
            ),
            generatePriorityCustomerServiceFeature(
                context = context,
                isEnabled = true,
                isPremiumFeature = true
            )
        )

        val finalAnswer = generateListByAddingZeroCustodyFeesFeature(
            context = context,
            existingFeatures = features,
            isUserEU = isUserEU,
            isPlanBasic = false
        )

        return finalAnswer
    }

    private fun generateListByAddingZeroCustodyFeesFeature(
        context: Context,
        existingFeatures: List<PlanV3Feature>,
        isUserEU: Boolean,
        isPlanBasic: Boolean
    ): List<PlanV3Feature> {
        val finalAnswer = existingFeatures.toMutableList()

        val isEnabled: Boolean
        val isPremiumFeature: Boolean
        val addBeforeFeatureID: String

        if (isUserEU) {
            isEnabled = true
            isPremiumFeature = false
            addBeforeFeatureID = EARN_INTEREST_WITH_MMF_FEATURE_ID
        } else {
            isEnabled = !isPlanBasic
            isPremiumFeature = !isPlanBasic
            addBeforeFeatureID = FX_RATE_FEATURE_ID
        }

        val addBeforeIndex = existingFeatures.indexOfFirst {
            it.id == addBeforeFeatureID
        }

        val indexToAdd = if (addBeforeIndex != -1) (addBeforeIndex) else -1

        val feature = generateZeroCustodyFeesFeature(
            context = context,
            isEnabled = isEnabled,
            isPremiumFeature = isPremiumFeature,
            isUserEU = isUserEU
        )

        try {
            finalAnswer.add(indexToAdd, feature)
        } catch (e: Exception) {
            val message = "Could not show \"Zero custody fees\" feature in plans: ${e.message}"
            Sentry.captureMessage(message, SentryLevel.ERROR)
        }

        return finalAnswer
    }
}
