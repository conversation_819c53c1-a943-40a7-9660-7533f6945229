package com.wealthyhood.wealthyhood.repository

import android.content.Context
import com.google.gson.Gson
import com.wealthyhood.wealthyhood.domain.AppsFlyerHelper
import com.wealthyhood.wealthyhood.model.AppsFlyerConversionData
import com.wealthyhood.wealthyhood.model.NetworkResource
import com.wealthyhood.wealthyhood.model.TrueLayerURIComponents
import com.wealthyhood.wealthyhood.service.ActivateSubscriptionBodyParams
import com.wealthyhood.wealthyhood.service.Allocation
import com.wealthyhood.wealthyhood.service.ApiResponse
import com.wealthyhood.wealthyhood.service.AuthorizeWithTrueLayerParameters
import com.wealthyhood.wealthyhood.service.AuthorizeWithTrueLayerResponse
import com.wealthyhood.wealthyhood.service.Automation
import com.wealthyhood.wealthyhood.service.BuyETFWithBankAccountParameters
import com.wealthyhood.wealthyhood.service.BuyETFWithCashParameters
import com.wealthyhood.wealthyhood.service.BuyPortfolioWithBankAccountParameters
import com.wealthyhood.wealthyhood.service.BuyPortfolioWithCashParameters
import com.wealthyhood.wealthyhood.service.CloseAccountBodyParams
import com.wealthyhood.wealthyhood.service.CreateAutomationBodyParams
import com.wealthyhood.wealthyhood.service.CreateMandateBodyParams
import com.wealthyhood.wealthyhood.service.GenericService
import com.wealthyhood.wealthyhood.service.GetFuturePerformance
import com.wealthyhood.wealthyhood.service.GetFuturePerformanceQueryParams
import com.wealthyhood.wealthyhood.service.GetIntercomAssetsResponse
import com.wealthyhood.wealthyhood.service.GetLifetimePlanDeepLinkBodyParams
import com.wealthyhood.wealthyhood.service.GetLifetimePlanDeepLinkResponse
import com.wealthyhood.wealthyhood.service.GetLinkedBankAccountsResponse
import com.wealthyhood.wealthyhood.service.GetOptimalAllocationQueryParams
import com.wealthyhood.wealthyhood.service.GetPastPerformance
import com.wealthyhood.wealthyhood.service.GetPastPerformanceQueryParams
import com.wealthyhood.wealthyhood.service.GetPortfoliosQueryParams
import com.wealthyhood.wealthyhood.service.GetPromptsResponse
import com.wealthyhood.wealthyhood.service.GetRewardsQueryParams
import com.wealthyhood.wealthyhood.service.GetUserQueryParams
import com.wealthyhood.wealthyhood.service.LinkBankAccountParameters
import com.wealthyhood.wealthyhood.service.MakeDepositParameters
import com.wealthyhood.wealthyhood.service.MakeDepositResponse
import com.wealthyhood.wealthyhood.service.MakeWithdrawalParameters
import com.wealthyhood.wealthyhood.service.Mandate
import com.wealthyhood.wealthyhood.service.MarkPromptsAsSeenBodyParams
import com.wealthyhood.wealthyhood.service.ParticipateBodyParams
import com.wealthyhood.wealthyhood.service.Portfolio
import com.wealthyhood.wealthyhood.service.PreviewPortfolioUpdateBodyParams
import com.wealthyhood.wealthyhood.service.PreviewPortfolioUpdateQueryParams
import com.wealthyhood.wealthyhood.service.RedeemReferralBodyParams
import com.wealthyhood.wealthyhood.service.Reward
import com.wealthyhood.wealthyhood.service.SellETFParameters
import com.wealthyhood.wealthyhood.service.SellPortfolioParameters
import com.wealthyhood.wealthyhood.service.SendCloseAccountFeedbackBodyParams
import com.wealthyhood.wealthyhood.service.SendGiftBodyParams
import com.wealthyhood.wealthyhood.service.SendLogsBodyParams
import com.wealthyhood.wealthyhood.service.SendNewInstallEventBodyParams
import com.wealthyhood.wealthyhood.service.SubmitAddressBodyParams
import com.wealthyhood.wealthyhood.service.SubmitAllocationBodyParams
import com.wealthyhood.wealthyhood.service.SubmitPersonalDetailsBodyParams
import com.wealthyhood.wealthyhood.service.SubmitPersonalisationPreferencesBodyParams
import com.wealthyhood.wealthyhood.service.SubmitTaxResidencyBodyParams
import com.wealthyhood.wealthyhood.service.Subscription
import com.wealthyhood.wealthyhood.service.SyncLifetimePlanResponse
import com.wealthyhood.wealthyhood.service.SyncedTransaction
import com.wealthyhood.wealthyhood.service.Transaction
import com.wealthyhood.wealthyhood.service.TransactionPreview
import com.wealthyhood.wealthyhood.service.TrueLayerAuthService
import com.wealthyhood.wealthyhood.service.UpdateRewardBodyParams
import com.wealthyhood.wealthyhood.service.UpdateSubscriptionBodyParams
import com.wealthyhood.wealthyhood.service.User
import com.wealthyhood.wealthyhood.service.UserDataArray
import com.wealthyhood.wealthyhood.service.VerifyResponse
import com.wealthyhood.wealthyhood.service.WealthyhoodService
import okhttp3.ResponseBody
import retrofit2.Call
import java.util.UUID

class MyAccountRepository {

    private var getPastPerformanceCall: Call<GetPastPerformance>? = null
    private var getFuturePerformanceCall: Call<GetFuturePerformance>? = null
    private var getFuturePerformanceCallMonteCarloCall: Call<GetFuturePerformance>? = null
    private var getMandatesCall: Call<ApiResponse<List<Mandate>>>? = null
    private var getLinkedBankAccountsCall: Call<GetLinkedBankAccountsResponse>? = null
    private var getPortfoliosCall: Call<List<Portfolio>>? = null
    private var getUserCall: Call<User>? = null
    private var getRewardsCall: Call<ApiResponse<List<Reward>>>? = null

    fun cancelPerformanceCalls() {
        getPastPerformanceCall?.cancel()
        getFuturePerformanceCall?.cancel()
        getFuturePerformanceCallMonteCarloCall?.cancel()
    }

    // Instead of getUser use the GetUserUseCase.
    fun getUser(
        accessToken: String,
        idToken: String,
        queryParams: GetUserQueryParams? = null,
        callback: (networkResource: NetworkResource<User>) -> Unit
    ) {
        getUserCall?.cancel()

        getUserCall = WealthyhoodService.instance.getUser(
            authorization = accessToken,
            identity = idToken,
            populate = queryParams?.populate
        )

        getUserCall?.enqueue(RetrofitCallbackFactory.create(callback))
    }

    fun getLinkedBankAccounts(
        accessToken: String,
        idToken: String,
        userID: String?,
        callback: (networkResource: NetworkResource<GetLinkedBankAccountsResponse>) -> Unit
    ) {
        getLinkedBankAccountsCall?.cancel()

        getLinkedBankAccountsCall = WealthyhoodService.instance.getLinkedBankAccounts(
            authorization = accessToken,
            identity = idToken,
            externalUserID = userID
        )

        getLinkedBankAccountsCall?.enqueue(RetrofitCallbackFactory.create(callback))
    }

    fun linkBankAccount(
        code: String?,
        userID: String?,
        callback: (networkResource: NetworkResource<ResponseBody>) -> Unit
    ) {
        val parameters = LinkBankAccountParameters(
            code = code,
            userID = userID
        )

        val call = WealthyhoodService.instance.linkBankAccount(
            parameters = parameters
        )

        call.enqueue(RetrofitCallbackFactory.create(callback))
    }

    fun getPortfolios(
        accessToken: String,
        idToken: String,
        parameters: GetPortfoliosQueryParams? = null,
        callback: (networkResource: NetworkResource<List<Portfolio>>) -> Unit
    ) {
        getPortfoliosCall?.cancel()

        getPortfoliosCall = WealthyhoodService.instance.getPortfolios(
            authorization = accessToken,
            identity = idToken,
            mode = parameters?.mode
        )

        getPortfoliosCall?.enqueue(RetrofitCallbackFactory.create(callback))
    }

    fun authorizeWithTrueLayer(
        trueLayerURIComponents: TrueLayerURIComponents,
        callback: (networkResource: NetworkResource<AuthorizeWithTrueLayerResponse>) -> Unit
    ) {
        val consentID = UUID.randomUUID().toString()

        val parameters = AuthorizeWithTrueLayerParameters(
            responseType = "code",
            clientID = trueLayerURIComponents.clientID,
            redirectURI = trueLayerURIComponents.redirectURI,
            scope = "accounts",
            state = trueLayerURIComponents.state,
            consentID = consentID,
            providerID = trueLayerURIComponents.providerID,
            userMail = trueLayerURIComponents.userMail
        )

        val call = TrueLayerAuthService.instance.authorize(
            parameters = parameters
        )

        call.enqueue(RetrofitCallbackFactory.create(callback))
    }

    fun makeDeposit(
        accessToken: String,
        idToken: String,
        amount: Float,
        bankID: String?,
        bankAccountID: String?,
        depositAndInvest: Boolean,
        callback: (networkResource: NetworkResource<MakeDepositResponse>) -> Unit
    ) {
        val parameters = MakeDepositParameters(
            amount = amount,
            bankID = bankID,
            bankAccountID = bankAccountID,
            depositAndInvest = depositAndInvest
        )

        val call = WealthyhoodService.instance.makeDeposit(
            authorization = accessToken,
            identity = idToken,
            parameters = parameters
        )

        call.enqueue(RetrofitCallbackFactory.create(callback))
    }

    fun syncDeposit(
        accessToken: String,
        idToken: String,
        trueLayerID: String?,
        callback: (networkResource: NetworkResource<SyncedTransaction>) -> Unit
    ) {

        val call = WealthyhoodService.instance.syncDeposit(
            authorization = accessToken,
            identity = idToken,
            trueLayerID = trueLayerID
        )

        call.enqueue(RetrofitCallbackFactory.create(callback))
    }

    fun makeWithdrawal(
        accessToken: String,
        idToken: String,
        portfolioID: String?,
        amount: Float?,
        bankAccountID: String?,
        callback: (networkResource: NetworkResource<ResponseBody>) -> Unit
    ) {
        val parameters = MakeWithdrawalParameters(
            amount = amount,
            bankAccountID = bankAccountID
        )

        val call = WealthyhoodService.instance.makeWithdrawal(
            authorization = accessToken,
            identity = idToken,
            portfolioID = portfolioID,
            parameters = parameters
        )

        call.enqueue(RetrofitCallbackFactory.create(callback))
    }

    fun buyPortfolioWithCash(
        accessToken: String,
        idToken: String,
        portfolioID: String?,
        amount: Float,
        giftID: String?,
        shouldUseDeprecatedURI: Boolean,
        allocationMethod: String?,
        executeETFOrdersInRealtime: Boolean?,
        callback: (networkResource: NetworkResource<Transaction>) -> Unit
    ) {
        val parameters = BuyPortfolioWithCashParameters(
            amount = amount.toString(),
            giftID = giftID
        )

        val action = if (shouldUseDeprecatedURI) "convert" else "buy"
        val paymentMethod = if (giftID != null) "gift" else null

        val call = WealthyhoodService.instance.buyPortfolioWithCash(
            authorization = accessToken,
            identity = idToken,
            portfolioID = portfolioID,
            action = action,
            paymentMethod = paymentMethod,
            allocationMethod = allocationMethod,
            executeETFOrdersInRealtime = executeETFOrdersInRealtime,
            parameters = parameters
        )

        call.enqueue(RetrofitCallbackFactory.create(callback))
    }

    fun buyPortfolioWithBankAccount(
        accessToken: String,
        idToken: String,
        portfolioID: String?,
        amount: Float?,
        pendingDepositID: String?,
        allocationMethod: String?,
        executeETFOrdersInRealtime: Boolean?,
        callback: (networkResource: NetworkResource<Transaction>) -> Unit
    ) {
        val parameters = BuyPortfolioWithBankAccountParameters(
            amount = amount.toString(),
            pendingDepositID = pendingDepositID
        )

        val call = WealthyhoodService.instance.buyPortfolioWithBankAccount(
            authorization = accessToken,
            identity = idToken,
            portfolioID = portfolioID,
            allocationMethod = allocationMethod,
            executeETFOrdersInRealtime = executeETFOrdersInRealtime,
            parameters = parameters
        )

        call.enqueue(RetrofitCallbackFactory.create(callback))
    }

    fun sellPortfolio(
        accessToken: String,
        idToken: String,
        portfolioID: String?,
        amount: Float?,
        callback: (networkResource: NetworkResource<Transaction>) -> Unit
    ) {
        val parameters = SellPortfolioParameters(
            amount = amount.toString()
        )

        val call = WealthyhoodService.instance.sellPortfolio(
            authorization = accessToken,
            identity = idToken,
            portfolioID = portfolioID,
            parameters = parameters
        )

        call.enqueue(RetrofitCallbackFactory.create(callback))
    }

    fun sellAllPortfolio(
        accessToken: String,
        idToken: String,
        portfolioID: String?,
        callback: (networkResource: NetworkResource<Transaction>) -> Unit
    ) {
        val call = WealthyhoodService.instance.sellAllPortfolio(
            authorization = accessToken,
            identity = idToken,
            portfolioID = portfolioID
        )

        call.enqueue(RetrofitCallbackFactory.create(callback))
    }

    fun buyETFWithCash(
        accessToken: String,
        idToken: String,
        portfolioID: String?,
        etfID: String?,
        amount: Float?,
        giftID: String?,
        executeETFOrdersInRealtime: Boolean?,
        callback: (networkResource: NetworkResource<Transaction>) -> Unit
    ) {
        val pendingOrders = etfID?.let {
            mapOf(
                etfID to BuyETFWithCashParameters.PendingOrder("buy", amount)
            )
        }

        val parameters = BuyETFWithCashParameters(
            pendingOrders = pendingOrders,
            giftID = giftID
        )

        val paymentMethod = if (giftID != null) "gift" else null

        val call = WealthyhoodService.instance.buyETFWithCash(
            authorization = accessToken,
            identity = idToken,
            portfolioID = portfolioID,
            paymentMethod = paymentMethod,
            executeETFOrdersInRealtime = executeETFOrdersInRealtime,
            parameters = parameters
        )

        call.enqueue(RetrofitCallbackFactory.create(callback))
    }

    fun buyETFWithBankAccount(
        accessToken: String,
        idToken: String,
        portfolioID: String?,
        etfID: String?,
        amount: Float?,
        pendingDepositID: String?,
        executeETFOrdersInRealtime: Boolean?,
        callback: (networkResource: NetworkResource<Transaction>) -> Unit
    ) {
        val pendingOrders = etfID?.let {
            mapOf(
                etfID to BuyETFWithBankAccountParameters.PendingOrder("buy", amount)
            )
        }

        val parameters = BuyETFWithBankAccountParameters(
            pendingOrders = pendingOrders,
            pendingDepositID = pendingDepositID
        )

        val call = WealthyhoodService.instance.buyETFWithBankAccount(
            authorization = accessToken,
            identity = idToken,
            portfolioID = portfolioID,
            executeETFOrdersInRealtime = executeETFOrdersInRealtime,
            parameters = parameters
        )

        call.enqueue(RetrofitCallbackFactory.create(callback))
    }

    fun sellETF(
        accessToken: String,
        idToken: String,
        portfolioID: String?,
        etfID: String?,
        amount: Double?,
        callback: (networkResource: NetworkResource<Transaction>) -> Unit
    ) {
        val pendingOrders = etfID?.let {
            mapOf(
                etfID to SellETFParameters.PendingOrder("sell", amount)
            )
        }

        val parameters = SellETFParameters(
            pendingOrders = pendingOrders
        )

        val call = WealthyhoodService.instance.sellETF(
            authorization = accessToken,
            identity = idToken,
            portfolioID = portfolioID,
            parameters = parameters
        )

        call.enqueue(RetrofitCallbackFactory.create(callback))
    }

    fun submitPersonalisationPreferences(
        accessToken: String,
        idToken: String,
        portfolioID: String?,
        body: SubmitPersonalisationPreferencesBodyParams,
        callback: (networkResource: NetworkResource<ResponseBody>) -> Unit
    ) {
        val call = WealthyhoodService.instance.submitPersonalisationPreferences(
            authorization = accessToken,
            identity = idToken,
            portfolioID = portfolioID,
            body = body
        )

        call.enqueue(RetrofitCallbackFactory.create(callback))
    }

    fun submitAllocation(
        accessToken: String,
        idToken: String,
        portfolioID: String?,
        allocation: SubmitAllocationBodyParams, // TODO: Rename it to body
        callback: (networkResource: NetworkResource<Portfolio>) -> Unit
    ) {
        val call = WealthyhoodService.instance.submitAllocation(
            authorization = accessToken,
            identity = idToken,
            portfolioID = portfolioID,
            body = allocation
        )

        call.enqueue(RetrofitCallbackFactory.create(callback))
    }

    fun rebalancePortfolio(
        accessToken: String,
        idToken: String,
        portfolioID: String?,
        callback: (networkResource: NetworkResource<Transaction>) -> Unit
    ) {
        val call = WealthyhoodService.instance.rebalancePortfolio(
            authorization = accessToken,
            identity = idToken,
            portfolioID = portfolioID
        )

        call.enqueue(RetrofitCallbackFactory.create(callback))
    }

    fun getOptimalAllocation(
        accessToken: String,
        idToken: String,
        params: GetOptimalAllocationQueryParams,
        callback: (networkResource: NetworkResource<Allocation>) -> Unit
    ) {
        val call = WealthyhoodService.instance.getOptimalAllocation(
            authorization = accessToken,
            identity = idToken,
            risk = params.risk,
            assetAllocationMap = params.assetIds
        )

        call.enqueue(RetrofitCallbackFactory.create(callback))
    }

    fun submitPersonalDetails(
        accessToken: String,
        idToken: String,
        body: SubmitPersonalDetailsBodyParams,
        callback: (networkResource: NetworkResource<UserDataArray>) -> Unit
    ) {
        val call = WealthyhoodService.instance.submitPersonalDetails(
            authorization = accessToken,
            identity = idToken,
            body = body
        )

        call.enqueue(RetrofitCallbackFactory.create(callback))
    }

    fun submitTaxResidency(
        accessToken: String,
        idToken: String,
        body: SubmitTaxResidencyBodyParams,
        callback: (networkResource: NetworkResource<UserDataArray>) -> Unit
    ) {
        val call = WealthyhoodService.instance.submitTaxResidency(
            authorization = accessToken,
            identity = idToken,
            body = body
        )

        call.enqueue(RetrofitCallbackFactory.create(callback))
    }

    fun submitAddress(
        accessToken: String,
        idToken: String,
        body: SubmitAddressBodyParams,
        callback: (networkResource: NetworkResource<ResponseBody>) -> Unit
    ) {
        val call = WealthyhoodService.instance.submitAddress(
            authorization = accessToken,
            identity = idToken,
            body = body
        )

        call.enqueue(RetrofitCallbackFactory.create(callback))
    }

    fun verify(
        accessToken: String,
        idToken: String,
        callback: (networkResource: NetworkResource<VerifyResponse>) -> Unit
    ) {
        val call = WealthyhoodService.instance.verify(
            authorization = accessToken,
            identity = idToken
        )

        call.enqueue(RetrofitCallbackFactory.create(callback))
    }

    fun getPastPerformance(
        accessToken: String,
        idToken: String,
        queryParams: GetPastPerformanceQueryParams? = null,
        callback: (networkResource: NetworkResource<GetPastPerformance>) -> Unit
    ) {
        getPastPerformanceCall?.cancel()

        getPastPerformanceCall = WealthyhoodService.instance.getPastPerformance(
            authorization = accessToken,
            identity = idToken,
            // TODO: statistics wrapper of api does not support this param
            // It may be added in case a performance issue occurs (previously used)
            // weeklyResample = queryParams?.weeklyResample,
            initial = queryParams?.initial,
            options = queryParams?.options
        )

        getPastPerformanceCall?.enqueue(RetrofitCallbackFactory.create(callback))
    }

    fun getFuturePerformance(
        accessToken: String,
        idToken: String,
        queryParams: GetFuturePerformanceQueryParams? = null,
        callback: (networkResource: NetworkResource<GetFuturePerformance>) -> Unit
    ) {
        getFuturePerformanceCall?.cancel()

        getFuturePerformanceCall = WealthyhoodService.instance.getFuturePerformance(
            authorization = accessToken,
            identity = idToken,
            initial = 150, // TODO: may be changed dynamically, It is ok for now
            options = queryParams?.options
        )

        getFuturePerformanceCall?.enqueue(RetrofitCallbackFactory.create(callback))
    }

    fun getFuturePerformanceMonteCarlo(
        accessToken: String,
        idToken: String,
        queryParams: GetFuturePerformanceQueryParams? = null,
        callback: (networkResource: NetworkResource<GetFuturePerformance>) -> Unit
    ) {
        getFuturePerformanceCallMonteCarloCall?.cancel()

        getFuturePerformanceCallMonteCarloCall =
            WealthyhoodService.instance.getFuturePerformanceMonteCarlo(
                authorization = accessToken,
                identity = idToken,
                initial = 150, // TODO: may be changed dynamically, It is ok for now
                options = queryParams?.options
            )

        getFuturePerformanceCallMonteCarloCall?.enqueue(RetrofitCallbackFactory.create(callback))
    }

    fun getRewards(
        accessToken: String,
        idToken: String,
        params: GetRewardsQueryParams?,
        callback: (networkResource: NetworkResource<ApiResponse<List<Reward>>>) -> Unit
    ) {
        getRewardsCall?.cancel()

        getRewardsCall = WealthyhoodService.instance.getRewards(
            authorization = accessToken,
            identity = idToken,
            status = params?.status,
            restrictedOnly = params?.restrictedOnly,
            hasViewedAppModal = params?.hasViewedAppModal
        )

        getRewardsCall?.enqueue(RetrofitCallbackFactory.create(callback))
    }

    fun updateRewards(
        accessToken: String,
        idToken: String,
        rewardID: String,
        bodyParams: UpdateRewardBodyParams?,
        callback: (networkResource: NetworkResource<ResponseBody>) -> Unit
    ) {
        val call = WealthyhoodService.instance.updateReward(
            authorization = accessToken,
            identity = idToken,
            rewardID = rewardID,
            body = bodyParams
        )

        call.enqueue(RetrofitCallbackFactory.create(callback))
    }

    fun previewPortfolioUpdate(
        accessToken: String,
        idToken: String,
        params: PreviewPortfolioUpdateQueryParams,
        body: PreviewPortfolioUpdateBodyParams? = null,
        callback: (networkResource: NetworkResource<TransactionPreview>) -> Unit
    ) {
        val call = WealthyhoodService.instance.previewPortfolioUpdate(
            authorization = accessToken,
            identity = idToken,
            category = params.category,
            portfolioID = params.portfolioID,
            portfolioTransactionType = params.portfolioTransactionType,
            orderAmount = params.orderAmount,
            allocationMethod = params.allocationMethod,
            body = body ?: PreviewPortfolioUpdateBodyParams()
        )

        call.enqueue(RetrofitCallbackFactory.create(callback))
    }

    fun activateSubscription(
        accessToken: String,
        idToken: String,
        bodyParams: ActivateSubscriptionBodyParams,
        callback: (networkResource: NetworkResource<Subscription>) -> Unit
    ) {
        val call = WealthyhoodService.instance.activateSubscription(
            authorization = accessToken,
            identity = idToken,
            body = bodyParams
        )

        call.enqueue(RetrofitCallbackFactory.create(callback))
    }

    fun updateSubscription(
        accessToken: String,
        idToken: String,
        subscriptionID: String?,
        priceAPIKey: String?,
        category: String?,
        callback: (networkResource: NetworkResource<Subscription>) -> Unit
    ) {
        val bodyParams = UpdateSubscriptionBodyParams(
            priceAPIKey = priceAPIKey,
            category = category
        )

        val call = WealthyhoodService.instance.updateSubscription(
            authorization = accessToken,
            identity = idToken,
            subscriptionID = subscriptionID,
            body = bodyParams
        )

        call.enqueue(RetrofitCallbackFactory.create(callback))
    }

    fun sendEvent(
        context: Context,
        eventID: String,
        callback: (networkResource: NetworkResource<ResponseBody>) -> Unit
    ) {
        val bodyParameters = SendNewInstallEventBodyParams(
            eventID = eventID
        )

        val appsFlyerAttributionData = AppsFlyerConversionData.mapForSentry?.let {
            Gson().toJson(it)
        }

        val call = WealthyhoodService.instance.sendEvent(
            appsFlyerID = AppsFlyerHelper.getUID(context),
            appsFlyerAttributionData = appsFlyerAttributionData,
            grsf = AppsFlyerConversionData.grsf,
            wlthd = AppsFlyerConversionData.wlthd,
            sid = AppsFlyerConversionData.sid,
            body = bodyParameters
        )

        call.enqueue(RetrofitCallbackFactory.create(callback))
    }

    fun participate(
        context: Context,
        email: String,
        callback: (networkResource: NetworkResource<ResponseBody>) -> Unit
    ) {
        val appsFlyerAttributionData = AppsFlyerConversionData.mapForSentry?.let {
            Gson().toJson(it)
        }

        val bodyParams = ParticipateBodyParams(
            email = email
        )

        val call = WealthyhoodService.instance.participate(
            appsFlyerID = AppsFlyerHelper.getUID(context),
            appsFlyerAttributionData = appsFlyerAttributionData,
            grsf = AppsFlyerConversionData.grsf,
            wlthd = AppsFlyerConversionData.wlthd,
            sid = AppsFlyerConversionData.sid,
            body = bodyParams
        )

        call.enqueue(RetrofitCallbackFactory.create(callback))
    }

    fun sendLogs(
        context: Context,
        message: String,
        callback: (networkResource: NetworkResource<ResponseBody>) -> Unit
    ) {
        val appsFlyerAttributionData = AppsFlyerConversionData.mapForSentry?.let {
            Gson().toJson(it)
        }

        val bodyParams = SendLogsBodyParams(
            message = message
        )

        val call = WealthyhoodService.instance.sendLogs(
            appsFlyerID = AppsFlyerHelper.getUID(context),
            appsFlyerAttributionData = appsFlyerAttributionData,
            grsf = AppsFlyerConversionData.grsf,
            wlthd = AppsFlyerConversionData.wlthd,
            sid = AppsFlyerConversionData.sid,
            body = bodyParams
        )

        call.enqueue(RetrofitCallbackFactory.create(callback))
    }

    fun closeAccount(
        accessToken: String,
        idToken: String,
        callback: (networkResource: NetworkResource<ResponseBody>) -> Unit
    ) {
        val bodyParameters = CloseAccountBodyParams(
            requestType = "disassociation"
        )

        val call = WealthyhoodService.instance.closeAccount(
            authorization = accessToken,
            identity = idToken,
            body = bodyParameters
        )

        call.enqueue(RetrofitCallbackFactory.create(callback))
    }

    fun sendCloseAccountFeedback(
        accessToken: String,
        idToken: String,
        message: String?,
        callback: (networkResource: NetworkResource<ResponseBody>) -> Unit
    ) {
        val bodyParameters = SendCloseAccountFeedbackBodyParams(
            feedback = message
        )

        val call = WealthyhoodService.instance.sendCloseAccountFeedback(
            authorization = accessToken,
            identity = idToken,
            body = bodyParameters
        )

        call.enqueue(RetrofitCallbackFactory.create(callback))
    }

    fun sendGift(
        accessToken: String,
        idToken: String,
        targetUserMail: String,
        message: String?,
        callback: (networkResource: NetworkResource<ResponseBody>) -> Unit
    ) {
        val bodyParameters = SendGiftBodyParams(
            targetUserMail = targetUserMail,
            message = message
        )

        val call = WealthyhoodService.instance.sendGift(
            authorization = accessToken,
            identity = idToken,
            body = bodyParameters
        )

        call.enqueue(RetrofitCallbackFactory.create(callback))
    }

    fun getAutomations(
        accessToken: String,
        idToken: String,
        category: String?,
        callback: (networkResource: NetworkResource<ApiResponse<List<Automation>>>) -> Unit
    ) {
        val call = WealthyhoodService.instance.getAutomations(
            authorization = accessToken,
            identity = idToken,
            category = category
        )

        call.enqueue(RetrofitCallbackFactory.create(callback))
    }

    fun createAutomation(
        accessToken: String,
        idToken: String,
        category: String,
        mandateID: String?,
        savingsProductID: String?,
        formattedAmount: String?,
        allocationMethod: String?,
        dayOfMonth: Int?,
        callback: (networkResource: NetworkResource<ResponseBody>) -> Unit
    ) {
        val body = CreateAutomationBodyParams(
            category = category,
            frequency = "monthly",
            mandateID = mandateID,
            savingsProductID = savingsProductID,
            formattedAmount = formattedAmount,
            dayOfMonth = dayOfMonth
        )

        val call = WealthyhoodService.instance.createAutomation(
            authorization = accessToken,
            identity = idToken,
            allocationMethod = allocationMethod,
            body = body
        )

        call.enqueue(RetrofitCallbackFactory.create(callback))
    }

    fun cancelAutomation(
        accessToken: String,
        idToken: String,
        automationID: String,
        callback: (networkResource: NetworkResource<ResponseBody>) -> Unit
    ) {
        val call = WealthyhoodService.instance.cancelAutomation(
            authorization = accessToken,
            identity = idToken,
            automationID = automationID
        )

        call.enqueue(RetrofitCallbackFactory.create(callback))
    }

    fun getMandates(
        accessToken: String,
        idToken: String,
        category: String?,
        callback: (networkResource: NetworkResource<ApiResponse<List<Mandate>>>) -> Unit
    ) {
        getMandatesCall?.cancel()

        getMandatesCall = WealthyhoodService.instance.getMandates(
            authorization = accessToken,
            identity = idToken,
            category = category
        )

        getMandatesCall?.enqueue(RetrofitCallbackFactory.create(callback))
    }

    fun createMandate(
        accessToken: String,
        idToken: String,
        category: String?,
        bankAccountID: String?,
        callback: (networkResource: NetworkResource<Mandate>) -> Unit
    ) {
        val bodyParams = CreateMandateBodyParams(
            bankAccountID = bankAccountID,
            category = category
        )
        val call = WealthyhoodService.instance.createMandate(
            authorization = accessToken,
            identity = idToken,
            body = bodyParams
        )

        call.enqueue(RetrofitCallbackFactory.create(callback))
    }

    fun getIntercomAssets(
        callback: (networkResource: NetworkResource<GetIntercomAssetsResponse>) -> Unit
    ) {
        val call = GenericService.instance.getIntercomAssets()

        call.enqueue(RetrofitCallbackFactory.create(callback))
    }

    fun redeemReferralCode(
        accessToken: String,
        idToken: String,
        referralCode: String,
        callback: (networkResource: NetworkResource<ResponseBody>) -> Unit
    ) {
        val bodyParams = RedeemReferralBodyParams(
            referralCode = referralCode
        )

        val call = WealthyhoodService.instance.redeemReferralCode(
            authorization = accessToken,
            identity = idToken,
            body = bodyParams
        )

        call.enqueue(RetrofitCallbackFactory.create(callback))
    }

    fun getPrompts(
        accessToken: String,
        idToken: String,
        type: String?,
        callback: (networkResource: NetworkResource<GetPromptsResponse>) -> Unit
    ) {
        val call = WealthyhoodService.instance.getPrompts(
            authorization = accessToken,
            identity = idToken,
            type = type
        )

        call.enqueue(RetrofitCallbackFactory.create(callback))
    }

    fun markPromptsAsSeen(
        accessToken: String,
        idToken: String,
        promptType: String?,
        modalType: String?,
        ids: List<String>?,
        callback: (networkResource: NetworkResource<ResponseBody>) -> Unit
    ) {
        val body = MarkPromptsAsSeenBodyParams(
            promptType = promptType,
            modalType = modalType,
            ids = ids
        )

        val call = WealthyhoodService.instance.markPromptsAsSeen(
            authorization = accessToken,
            identity = idToken,
            body = body
        )

        call.enqueue(RetrofitCallbackFactory.create(callback))
    }

    fun getLifetimePlanDeepLink(
        accessToken: String,
        idToken: String,
        bankAccountID: String?,
        priceAPIKey: String?,
        callback: (networkResource: NetworkResource<GetLifetimePlanDeepLinkResponse>) -> Unit
    ) {
        val bodyParams = GetLifetimePlanDeepLinkBodyParams(
            bankAccountID = bankAccountID,
            priceAPIKey = priceAPIKey
        )

        val call = WealthyhoodService.instance.getLifetimePlanDeepLink(
            authorization = accessToken,
            identity = idToken,
            body = bodyParams
        )

        call.enqueue(RetrofitCallbackFactory.create(callback))
    }

    fun syncLifetimePlan(
        accessToken: String,
        idToken: String,
        trueLayerID: String?,
        callback: (networkResource: NetworkResource<SyncLifetimePlanResponse>) -> Unit
    ) {
        val call = WealthyhoodService.instance.syncLifetimePlan(
            authorization = accessToken,
            identity = idToken,
            trueLayerID = trueLayerID
        )

        call.enqueue(RetrofitCallbackFactory.create(callback))
    }
}
