package com.wealthyhood.wealthyhood.service

import com.google.gson.annotations.SerializedName

// TODO: Rename it to AssetData
data class EtfData(
    @SerializedName("tags")
    val tags: List<String>?,

    @SerializedName("marketInfo")
    val marketInfo: MarketInfo?,

    @SerializedName("baseCurrency")
    val baseCurrency: String?,

    @SerializedName("topHoldings")
    val topHoldings: List<TopHolding>?,

    @SerializedName("expenseRatio")
    val expenseRatio: String?,

    @SerializedName("indexStats")
    val indexStats: IndexStats,

    @SerializedName("holdingsCount")
    val holdingsCount: Int?,

    @SerializedName("metrics")
    val metrics: Metrics?,

    @SerializedName("analystViews")
    val analystViews: AnalystViews?,

    @SerializedName("geographyDistribution")
    val geographyDistribution: List<GeographyDistributionItem>?,

    @SerializedName("sectorDistribution")
    val sectorDistribution: List<SectorDistributionItem>?,

    @SerializedName("about")
    val about: About?,

    @SerializedName("news")
    val news: List<AssetNewsArticle>?
) {

    data class MarketInfo(
        @SerializedName("isOpen")
        val isOpen: Boolean?,

        @SerializedName("nextMarketOpen")
        val nextMarketOpen: Long?
    )

    data class TopHolding(
        @SerializedName("name")
        val name: String?,

        @SerializedName("weight")
        val weight: String?,

        @SerializedName("logoUrl")
        val logoUrl: String?
    )

    data class IndexStats(
        @SerializedName("expectedReturn")
        val expectedReturn: String?,

        @SerializedName("annualRisk")
        val annualRisk: String?,

        @SerializedName("fpEarnings")
        val fpEarnings: String?,

        @SerializedName("dividendYield")
        val dividendYield: String?,

        @SerializedName("bondYield")
        val bondYield: String?,

        @SerializedName("coupon")
        val coupon: String?
    )

    data class Metrics(
        @SerializedName("marketCap")
        val marketCap: String?,

        @SerializedName("beta")
        val beta: String?,

        @SerializedName("peRatio")
        val peRatio: String?,

        @SerializedName("eps")
        val eps: String?,

        @SerializedName("forwardPE")
        val forwardPE: String?,

        @SerializedName("dividendYield")
        val dividendYield: String?
    )

    data class AnalystViews(
        @SerializedName("averagePriceTarget")
        val averagePriceTarget: String?,

        @SerializedName("priceTargetPercentageDifference")
        val priceTargetPercentageDifference: String?,

        @SerializedName("isPriceTargetPercentageDifferencePositive")
        val isPriceTargetPercentageDifferencePositive: Boolean?,

        @SerializedName("totalAnalysts")
        val totalAnalysts: Int?,

        @SerializedName("percentageBuy")
        val percentageBuy: Int?,

        @SerializedName("percentageSell")
        val percentageSell: Int?,

        @SerializedName("percentageHold")
        val percentageHold: Int?,

        @SerializedName("isMajority")
        val isMajority: String? // buy, sell or hold
    )

    data class GeographyDistributionItem(
        @SerializedName("name")
        val name: String?,

        @SerializedName("percentage")
        val percentage: Double?
    )

    data class SectorDistributionItem(
        @SerializedName("id")
        val id: String?,

        @SerializedName("name")
        val name: String?,

        @SerializedName("percentage")
        val percentage: Double?
    )

    data class About(
        @SerializedName("ticker")
        val ticker: String?,

        @SerializedName("exchange")
        val exchange: String?,

        @SerializedName("isin")
        val isin: String?,

        @SerializedName("sector")
        val sector: String?,

        @SerializedName("assetClass")
        val assetClass: String?,

        @SerializedName("income")
        val income: String?,

        @SerializedName("provider")
        val provider: String?,

        @SerializedName("industry")
        val industry: String?,

        @SerializedName("description")
        val description: String?,

        @SerializedName("employees")
        val employees: String?,

        @SerializedName("website")
        val website: String?,

        @SerializedName("ceo")
        val ceo: String?,

        @SerializedName("headquarters")
        val headquarters: String?,

        @SerializedName("index")
        val index: String?,

        @SerializedName("replication")
        val replication: String?
    )
}
