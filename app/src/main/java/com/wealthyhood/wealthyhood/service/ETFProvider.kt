package com.wealthyhood.wealthyhood.service

import com.google.gson.Gson
import com.google.gson.JsonObject
import com.google.gson.annotations.SerializedName

data class ETFProvider(
    @SerializedName("id")
    val id: String,

    @SerializedName("displayName")
    val displayName: String?,

    @SerializedName("icon")
    val icon: String?,

    @SerializedName("keyName")
    val keyName: String?
) {

    companion object {

        fun fromJsonObject(etfProviderID: String, jsonObject: JsonObject): ETFProvider? {
            jsonObject.addProperty("id", etfProviderID)

            return try {
                val jsonString = Gson().toJson(jsonObject)
                Gson().fromJson(jsonString, ETFProvider::class.java)
            } catch (e: Exception) {
                null
            }
        }
    }
}
