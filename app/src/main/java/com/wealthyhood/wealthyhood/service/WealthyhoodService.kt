package com.wealthyhood.wealthyhood.service

import com.wealthyhood.wealthyhood.BuildConfig
import io.sentry.HttpStatusCodeRange
import io.sentry.okhttp.SentryOkHttpInterceptor
import okhttp3.Interceptor
import okhttp3.OkHttpClient
import okhttp3.Request
import okhttp3.ResponseBody
import okhttp3.logging.HttpLoggingInterceptor
import retrofit2.Call
import retrofit2.Response
import retrofit2.Retrofit
import retrofit2.converter.gson.GsonConverterFactory
import retrofit2.http.Body
import retrofit2.http.GET
import retrofit2.http.Header
import retrofit2.http.POST
import retrofit2.http.Path
import retrofit2.http.Query
import retrofit2.http.QueryMap
import java.util.concurrent.TimeUnit

interface WealthyhoodService {

    companion object {

        val instance: WealthyhoodService by lazy {
            val loggingInterceptor = HttpLoggingInterceptor()

            loggingInterceptor.level = if (BuildConfig.DEBUG) {
                HttpLoggingInterceptor.Level.BODY
            } else {
                HttpLoggingInterceptor.Level.NONE
            }

            val headersInterceptor = Interceptor { chain ->
                val request: Request = chain.request().newBuilder()
                    .addHeader("Platform", "android")
                    .addHeader("version", BuildConfig.VERSION_NAME)
                    .build()

                chain.proceed(request)
            }

            val sentryOkHttpInterceptor = SentryOkHttpInterceptor(
                captureFailedRequests = true,
                failedRequestStatusCodes = listOf(
                    HttpStatusCodeRange(400, 599)
                ),
                failedRequestTargets = listOf(BuildConfig.WEALTHYHOOD_API_BASE_URL)
            )

            val httpClient = OkHttpClient.Builder()
                .connectTimeout(20, TimeUnit.SECONDS)
                .readTimeout(20, TimeUnit.SECONDS)
                .writeTimeout(20, TimeUnit.SECONDS)
                .addInterceptor(headersInterceptor)
                .addInterceptor(loggingInterceptor)
                .addInterceptor(sentryOkHttpInterceptor)
                .build()

            val retrofit = Retrofit.Builder()
                .client(httpClient)
                .baseUrl(BuildConfig.WEALTHYHOOD_API_BASE_URL)
                .addConverterFactory(GsonConverterFactory.create())
                .build()

            retrofit.create(WealthyhoodService::class.java)
        }
    }

    @POST("api/c2m/auth")
    fun auth(
        @Header("Authorization") authorization: String,
        @Header("Identity") identity: String,
        @Header("appsflyer_id") appsFlyerID: String?,
        @Header("appsflyer_attribution_data") appsFlyerAttributionData: String?,
        @Header("grsf") grsf: String?,
        @Header("wlthd") wlthd: String?,
        @Header("sid") sid: String?
    ): Call<AuthResponse>

    @GET("api/c2m/users/me")
    fun getUser(
        @Header("Authorization") authorization: String,
        @Header("Identity") identity: String,
        @Query("populate", encoded = true) populate: String?
    ): Call<User>

    @GET("api/c2m/users/me")
    suspend fun getUserCoroutines(
        @Header("Authorization") authorization: String,
        @Header("Identity") identity: String,
        @Query("populate", encoded = true) populate: String?
    ): Response<User>

    @GET("api/c2m/transactions/pending")
    suspend fun getPendingTransactions(
        @Header("Authorization") authorization: String,
        @Header("Identity") identity: String,
        @Query("portfolio") portfolioID: String?,
        @Query("populate", encoded = true) populate: String?
    ): Response<List<Transaction>>

    @GET("api/c2m/savings-products/activity")
    suspend fun getSavingsProductActivity(
        @Header("Authorization") authorization: String,
        @Header("Identity") identity: String,
        @Query("savingsProductId") savingsProductID: String?,
        @Query("limit") limit: Int?
    ): Response<List<TransactionActivityResponse>>

    @GET("api/c2m/transactions")
    suspend fun getTransactionsCoroutines(
        @Header("Authorization") authorization: String,
        @Header("Identity") identity: String,
        @Query("portfolio") portfolioID: String?,
        @Query("category") category: String?, // TODO: This one will go away soon, it was added for an iOS bug
        @Query("truelayerStatus") trueLayerStatus: List<String>?,
        @Query("sort") sort: String?,
        @Query("status") status: List<String>?,
        @Query("populate", encoded = true) populate: String?
    ): Response<List<Transaction>>

    @GET("api/c2m/portfolios/{portfolioID}/pending-cash-flows")
    suspend fun getIncomingCashFlows(
        @Header("Authorization") authorization: String,
        @Header("Identity") identity: String,
        @Path("portfolioID") portfolioID: String?,
        @Query("populate", encoded = true) populate: String?
    ): Response<List<Transaction>>

    @POST("api/c2m/transactions/{transactionID}/cancel")
    suspend fun cancelTransaction(
        @Header("Authorization") authorization: String,
        @Header("Identity") identity: String,
        @Path("transactionID") transactionID: String?
    ): Response<Void>

    @POST("api/c2m/orders/{orderID}/cancel")
    suspend fun cancelOrder(
        @Header("Authorization") authorization: String,
        @Header("Identity") identity: String,
        @Path("orderID") orderID: String?
    ): Response<Order>

    @POST("api/c2m/portfolios/{portfolioID}/{action}")
    fun buyPortfolioWithCash(
        @Header("Authorization") authorization: String,
        @Header("Identity") identity: String,
        @Path("portfolioID") portfolioID: String?,
        @Path("action") action: String,
        @Query("paymentMethod") paymentMethod: String?,
        @Query("allocationMethod") allocationMethod: String?,
        @Query("executeEtfOrdersInRealtime") executeETFOrdersInRealtime: Boolean?,
        @Body parameters: BuyPortfolioWithCashParameters
    ): Call<Transaction>

    @GET("api/c2m/investment-products")
    suspend fun getInvestmentProducts(
        @Header("Authorization") authorization: String,
        @Header("Identity") identity: String,
        @Query("populateTicker") shouldPopulateTicker: Boolean?
    ): Response<List<InvestmentProduct>>

    @GET("api/c2m/investment-products/investment-details")
    suspend fun getInvestmentDetails(
        @Header("Authorization") authorization: String,
        @Header("Identity") identity: String,
        @Query("assetId") assetID: String?
    ): Response<GetInvestmentDetailsResponse>

    @GET("api/c2m/investment-products/recent-activity")
    suspend fun getAssetRecentActivity(
        @Header("Authorization") authorization: String,
        @Header("Identity") identity: String,
        @Query("assetId") assetID: String?,
        @Query("limit") limit: Int?
    ): Response<List<AssetRecentActivityItem>>

    @GET("api/c2m/investment-products/prices-by-tenor")
    suspend fun getInvestmentProductPricesByTenor(
        @Header("Authorization") authorization: String,
        @Header("Identity") identity: String,
        @Query("assetId") assetID: String?
    ): Response<GetInvestmentProductPriceByTenorResponse>

    @GET("api/c2m/investment-products/etf-data")
    suspend fun getInvestmentProductEtfData(
        @Header("Authorization") authorization: String,
        @Header("Identity") identity: String,
        @Query("assetId") assetID: String?
    ): Response<EtfData>

    @GET("api/c2m/users/me/linked-bank-accounts")
    fun getLinkedBankAccounts(
        @Header("Authorization") authorization: String,
        @Header("Identity") identity: String,
        @Header("external-user-id") externalUserID: String?
    ): Call<GetLinkedBankAccountsResponse>

    @GET("api/c2m/users/me/linked-bank-accounts")
    suspend fun getLinkedBankAccountsCoroutines(
        @Header("Authorization") authorization: String,
        @Header("Identity") identity: String,
        @Header("external-user-id") externalUserID: String?
    ): Response<GetLinkedBankAccountsResponse>

    @POST("api/c2m/bank-accounts/{bankAccountID}/deactivate")
    suspend fun deactivateBankAccount(
        @Header("Authorization") authorization: String,
        @Header("Identity") identity: String,
        @Path("bankAccountID") bankAccountID: String?
    ): Response<DeactivateBankAccountResponse>

    @POST("truelayer/link-account")
    fun linkBankAccount(
        @Body parameters: LinkBankAccountParameters
    ): Call<ResponseBody>

    @GET("api/c2m/portfolios")
    fun getPortfolios(
        @Header("Authorization") authorization: String,
        @Header("Identity") identity: String,
        @Query("mode") mode: PortfolioModeEnum?
    ): Call<List<Portfolio>>

    @GET("api/c2m/portfolios")
    suspend fun getPortfoliosCoroutines(
        @Header("Authorization") authorization: String,
        @Header("Identity") identity: String,
        @Query("mode") mode: PortfolioModeEnum?
    ): Response<List<Portfolio>>

    @POST("api/c2m/transactions/deposits")
    fun makeDeposit(
        @Header("Authorization") authorization: String,
        @Header("Identity") identity: String,
        @Body parameters: MakeDepositParameters
    ): Call<MakeDepositResponse>

    @POST("api/c2m/transactions/deposits/sync-truelayer")
    fun syncDeposit(
        @Header("Authorization") authorization: String,
        @Header("Identity") identity: String,
        @Query("truelayerId") trueLayerID: String?
    ): Call<SyncedTransaction>

    @GET("api/c2m/transactions/deposits")
    suspend fun syncSaltedge(
        @Header("Authorization") authorization: String,
        @Header("Identity") identity: String,
        @Query("saltedgeCustomId") saltedgeCustomID: String?
    ): Response<List<SyncedTransaction>>

    @POST("api/c2m/portfolios/{portfolioID}/withdraw")
    fun makeWithdrawal(
        @Header("Authorization") authorization: String,
        @Header("Identity") identity: String,
        @Path("portfolioID") portfolioID: String?,
        @Body parameters: MakeWithdrawalParameters
    ): Call<ResponseBody>

    @POST("api/c2m/portfolios/{portfolioID}/invest-pending-deposit")
    fun buyPortfolioWithBankAccount(
        @Header("Authorization") authorization: String,
        @Header("Identity") identity: String,
        @Path("portfolioID") portfolioID: String?,
        @Query("allocationMethod") allocationMethod: String?,
        @Query("executeEtfOrdersInRealtime") executeETFOrdersInRealtime: Boolean?,
        @Body parameters: BuyPortfolioWithBankAccountParameters
    ): Call<Transaction>

    @POST("api/c2m/portfolios/{portfolioID}/sell")
    fun sellPortfolio(
        @Header("Authorization") authorization: String,
        @Header("Identity") identity: String,
        @Path("portfolioID") portfolioID: String?,
        @Body parameters: SellPortfolioParameters
    ): Call<Transaction>

    @POST("api/c2m/portfolios/{portfolioID}/sell/all")
    fun sellAllPortfolio(
        @Header("Authorization") authorization: String,
        @Header("Identity") identity: String,
        @Path("portfolioID") portfolioID: String?
    ): Call<Transaction>

    @POST("api/c2m/portfolios/{portfolioID}/submit-orders")
    fun buyETFWithCash(
        @Header("Authorization") authorization: String,
        @Header("Identity") identity: String,
        @Path("portfolioID") portfolioID: String?,
        @Query("paymentMethod") paymentMethod: String?,
        @Query("executeEtfOrdersInRealtime") executeETFOrdersInRealtime: Boolean?,
        @Body parameters: BuyETFWithCashParameters
    ): Call<Transaction>

    @POST("api/c2m/portfolios/{portfolioID}/buy-asset-pending-deposit")
    fun buyETFWithBankAccount(
        @Header("Authorization") authorization: String,
        @Header("Identity") identity: String,
        @Path("portfolioID") portfolioID: String?,
        @Query("executeEtfOrdersInRealtime") executeETFOrdersInRealtime: Boolean?,
        @Body parameters: BuyETFWithBankAccountParameters
    ): Call<Transaction>

    @POST("api/c2m/portfolios/{portfolioID}/submit-orders")
    fun sellETF(
        @Header("Authorization") authorization: String,
        @Header("Identity") identity: String,
        @Path("portfolioID") portfolioID: String?,
        @Body parameters: SellETFParameters
    ): Call<Transaction>

    @POST("api/c2m/portfolios/{portfolioID}/personalisation-preferences")
    fun submitPersonalisationPreferences(
        @Header("Authorization") authorization: String,
        @Header("Identity") identity: String,
        @Path("portfolioID") portfolioID: String?,
        @Body body: SubmitPersonalisationPreferencesBodyParams
    ): Call<ResponseBody>

    @POST("api/c2m/portfolios/{portfolioID}/allocation")
    fun submitAllocation(
        @Header("Authorization") authorization: String,
        @Header("Identity") identity: String,
        @Path("portfolioID") portfolioID: String?,
        @Body body: SubmitAllocationBodyParams
    ): Call<Portfolio>

    @POST("api/c2m/portfolios/{portfolioID}/allocation")
    suspend fun submitAllocationCoroutines(
        @Header("Authorization") authorization: String,
        @Header("Identity") identity: String,
        @Path("portfolioID") portfolioID: String?,
        @Body body: SubmitAllocationBodyParams
    ): Response<ResponseBody>

    @POST("api/c2m/portfolios/{portfolioID}/rebalance")
    fun rebalancePortfolio(
        @Header("Authorization") authorization: String,
        @Header("Identity") identity: String,
        @Path("portfolioID") portfolioID: String?
    ): Call<Transaction>

    @GET("api/c2m/statistics/optimal-allocation")
    fun getOptimalAllocation(
        @Header("Authorization") authorization: String,
        @Header("Identity") identity: String,
        @Query("risk") risk: Float?,
        @Query("asset") assetAllocationMap: List<String>?
    ): Call<Allocation>

    @GET("api/c2m/statistics/optimal-allocation")
    suspend fun getOptimalAllocationCoroutines(
        @Header("Authorization") authorization: String,
        @Header("Identity") identity: String,
        @Query("roboAdvisorRiskLevel") roboAdvisorRiskLevel: String?
    ): Response<Allocation>

    @GET("api/c2m/statistics/past-performance")
    fun getPastPerformance(
        @Header("Authorization") authorization: String,
        @Header("Identity") identity: String,
        @Query("initial") initial: Int?,
        @QueryMap options: Map<String, String>?
    ): Call<GetPastPerformance>

    @GET("api/c2m/statistics/past-performance")
    suspend fun getPastPerformanceCoroutines(
        @Header("Authorization") authorization: String,
        @Header("Identity") identity: String,
        @Query("initial") initial: Int?,
        @QueryMap options: Map<String, String>?
    ): Response<GetPastPerformance>

    @GET("api/c2m/statistics/future-performance")
    fun getFuturePerformance(
        @Header("Authorization") authorization: String,
        @Header("Identity") identity: String,
        @Query("initial") initial: Int?,
        @QueryMap options: Map<String, String>?
    ): Call<GetFuturePerformance>

    @GET("api/c2m/statistics/future-performance-monte-carlo")
    fun getFuturePerformanceMonteCarlo(
        @Header("Authorization") authorization: String,
        @Header("Identity") identity: String,
        @Query("initial") initial: Int?,
        @QueryMap options: Map<String, String>?
    ): Call<GetFuturePerformance>

    @POST("api/c2m/users/me")
    fun submitPersonalDetails(
        @Header("Authorization") authorization: String,
        @Header("Identity") identity: String,
        @Body body: SubmitPersonalDetailsBodyParams
    ): Call<UserDataArray>

    @POST("api/c2m/users/me")
    fun submitTaxResidency(
        @Header("Authorization") authorization: String,
        @Header("Identity") identity: String,
        @Body body: SubmitTaxResidencyBodyParams
    ): Call<UserDataArray>

    @POST("api/c2m/addresses")
    fun submitAddress(
        @Header("Authorization") authorization: String,
        @Header("Identity") identity: String,
        @Body body: SubmitAddressBodyParams
    ): Call<ResponseBody>

    @POST("api/c2m/users/verify")
    fun verify(
        @Header("Authorization") authorization: String,
        @Header("Identity") identity: String
    ): Call<VerifyResponse>

    @GET("api/c2m/portfolios/{portfolioID}/prices-by-tenor")
    suspend fun getDashboardChartDataByTenor(
        @Header("Authorization") authorization: String,
        @Header("Identity") identity: String,
        @Path("portfolioID") portfolioID: String?
    ): Response<Map<String, DashboardChartTenor>>

    @GET("api/c2m/portfolios/{portfolioID}/with-returns-by-tenor")
    suspend fun getPortfolioWithReturnsByTenor(
        @Header("Authorization") authorization: String,
        @Header("Identity") identity: String,
        @Path("portfolioID") portfolioID: String?
    ): Response<Portfolio>

    @POST("api/c2m/users/{userID}")
    suspend fun updateUser(
        @Header("Authorization") authorization: String,
        @Header("Identity") identity: String,
        @Path("userID") userID: String?,
        @Body body: UpdateUserBodyParams?
    ): Response<ResponseBody>

    @GET("api/c2m/rewards")
    fun getRewards(
        @Header("Authorization") authorization: String,
        @Header("Identity") identity: String,
        @Query("status") status: List<String>?,
        @Query("restrictedOnly") restrictedOnly: Boolean?,
        @Query("hasViewedAppModal") hasViewedAppModal: Boolean?
    ): Call<ApiResponse<List<Reward>>>

    @POST("api/c2m/rewards/{rewardID}")
    fun updateReward(
        @Header("Authorization") authorization: String,
        @Header("Identity") identity: String,
        @Path("rewardID") rewardID: String,
        @Body body: UpdateRewardBodyParams?
    ): Call<ResponseBody>

    @POST("api/c2m/transactions/preview")
    fun previewPortfolioUpdate(
        @Header("Authorization") authorization: String,
        @Header("Identity") identity: String,
        @Query("category") category: PreviewPortfolioUpdateQueryParams.PreviewCategory?,
        @Query("portfolioId") portfolioID: String?,
        @Query("portfolioTransactionType") portfolioTransactionType: PreviewPortfolioUpdateQueryParams.TransactionType?,
        @Query("orderAmount") orderAmount: String?,
        @Query("allocationMethod") allocationMethod: String?,
        @Body body: PreviewPortfolioUpdateBodyParams?
    ): Call<TransactionPreview>

    @POST("api/c2m/subscriptions")
    fun activateSubscription(
        @Header("Authorization") authorization: String,
        @Header("Identity") identity: String,
        @Body body: ActivateSubscriptionBodyParams
    ): Call<Subscription>

    @POST("api/c2m/subscriptions/{subscriptionID}")
    fun updateSubscription(
        @Header("Authorization") authorization: String,
        @Header("Identity") identity: String,
        @Path("subscriptionID") subscriptionID: String?,
        @Body body: UpdateSubscriptionBodyParams
    ): Call<Subscription>

    @POST("api/c2m/subscriptions/{subscriptionID}/renew")
    suspend fun renewSubscription(
        @Header("Authorization") authorization: String,
        @Header("Identity") identity: String,
        @Path("subscriptionID") subscriptionID: String?
    ): Response<Subscription>

    @GET("api/c2m/portfolios/{portfolioID}/available-holdings")
    suspend fun getAvailableHoldings(
        @Header("Authorization") authorization: String,
        @Header("Identity") identity: String,
        @Path("portfolioID") portfolioID: String?
    ): Response<List<AvailableHolding>>

    @GET("api/c2m/portfolios/{portfolioID}/asset-restriction")
    suspend fun getAssetRestriction(
        @Header("Authorization") authorization: String,
        @Header("Identity") identity: String,
        @Path("portfolioID") portfolioID: String?,
        @Query("assetId") assetId: String?
    ): Response<GetAssetRestrictionResponse>

    @GET("api/c2m/portfolios/{portfolioID}/restricted-holdings")
    suspend fun getRestrictedHoldings(
        @Header("Authorization") authorization: String,
        @Header("Identity") identity: String,
        @Path("portfolioID") portfolioID: String?,
    ): Response<GetRestrictedHoldingsResponse>

    @POST("events")
    fun sendEvent(
        @Header("appsflyer_id") appsFlyerID: String?,
        @Header("appsflyer_attribution_data") appsFlyerAttributionData: String?,
        @Header("grsf") grsf: String?,
        @Header("wlthd") wlthd: String?,
        @Header("sid") sid: String?,
        @Body body: SendNewInstallEventBodyParams
    ): Call<ResponseBody>

    @POST("participants")
    fun participate(
        @Header("appsflyer_id") appsFlyerID: String?,
        @Header("appsflyer_attribution_data") appsFlyerAttributionData: String?,
        @Header("grsf") grsf: String?,
        @Header("wlthd") wlthd: String?,
        @Header("sid") sid: String?,
        @Body body: ParticipateBodyParams
    ): Call<ResponseBody>

    @POST("logs")
    fun sendLogs(
        @Header("appsflyer_id") appsFlyerID: String?,
        @Header("appsflyer_attribution_data") appsFlyerAttributionData: String?,
        @Header("grsf") grsf: String?,
        @Header("wlthd") wlthd: String?,
        @Header("sid") sid: String?,
        @Body body: SendLogsBodyParams
    ): Call<ResponseBody>

    @POST("api/c2m/user-data-requests")
    fun closeAccount(
        @Header("Authorization") authorization: String,
        @Header("Identity") identity: String,
        @Body body: CloseAccountBodyParams
    ): Call<ResponseBody>

    @POST("api/c2m/users/me/deletion-feedback")
    fun sendCloseAccountFeedback(
        @Header("Authorization") authorization: String,
        @Header("Identity") identity: String,
        @Body body: SendCloseAccountFeedbackBodyParams
    ): Call<ResponseBody>

    @POST("api/c2m/gifts")
    fun sendGift(
        @Header("Authorization") authorization: String,
        @Header("Identity") identity: String,
        @Body body: SendGiftBodyParams
    ): Call<ResponseBody>

    @GET("api/c2m/gifts")
    suspend fun getGiftsCoroutines(
        @Header("Authorization") authorization: String,
        @Header("Identity") identity: String,
        @Query("used") used: Boolean,
        @Query("hasViewedAppModal") hasViewedAppModal: Boolean?
    ): Response<ApiResponse<List<Gift>>>

    @GET("api/c2m/automations")
    fun getAutomations(
        @Header("Authorization") authorization: String,
        @Header("Identity") identity: String,
        @Query("category") category: String?
    ): Call<ApiResponse<List<Automation>>>

    @GET("api/c2m/automations")
    suspend fun getAutomationsCoroutines(
        @Header("Authorization") authorization: String,
        @Header("Identity") identity: String,
        @Query("category") category: String?
    ): Response<ApiResponse<List<Automation>>>

    @POST("api/c2m/automations")
    fun createAutomation(
        @Header("Authorization") authorization: String,
        @Header("Identity") identity: String,
        @Query("allocationMethod") allocationMethod: String?,
        @Body body: CreateAutomationBodyParams?
    ): Call<ResponseBody>

    @POST("api/c2m/automations")
    suspend fun createAutomationCoroutines(
        @Header("Authorization") authorization: String,
        @Header("Identity") identity: String,
        @Query("allocationMethod") allocationMethod: String?,
        @Body body: CreateAutomationBodyParams?
    ): Response<ResponseBody>

    @POST("api/c2m/automations/{automationID}/cancel")
    fun cancelAutomation(
        @Header("Authorization") authorization: String,
        @Header("Identity") identity: String,
        @Path("automationID") automationID: String
    ): Call<ResponseBody>

    @GET("api/c2m/mandates")
    fun getMandates(
        @Header("Authorization") authorization: String,
        @Header("Identity") identity: String,
        @Query("category") category: String?
    ): Call<ApiResponse<List<Mandate>>>

    @GET("api/c2m/mandates")
    suspend fun getMandatesCoroutines(
        @Header("Authorization") authorization: String,
        @Header("Identity") identity: String,
        @Query("category") category: String?
    ): Response<ApiResponse<List<Mandate>>>

    @POST("api/c2m/mandates")
    fun createMandate(
        @Header("Authorization") authorization: String,
        @Header("Identity") identity: String,
        @Body body: CreateMandateBodyParams?
    ): Call<Mandate>

    @POST("api/c2m/mandates")
    suspend fun createMandateCoroutines(
        @Header("Authorization") authorization: String,
        @Header("Identity") identity: String,
        @Body body: CreateMandateBodyParams?
    ): Response<Mandate>

    @POST("api/c2m/reward-invitations")
    suspend fun inviteUser(
        @Header("Authorization") authorization: String,
        @Header("Identity") identity: String,
        @Body body: InviteUserBodyParams?
    ): Response<ResponseBody>

    @POST("api/c2m/users/me/set-referrer")
    fun redeemReferralCode(
        @Header("Authorization") authorization: String,
        @Header("Identity") identity: String,
        @Body body: RedeemReferralBodyParams?
    ): Call<ResponseBody>

    @POST("api/c2m/referral-codes")
    suspend fun generateReferralCode(
        @Header("Authorization") authorization: String,
        @Header("Identity") identity: String
    ): Response<GenerateReferralCodeResponse>

    @GET("api/c2m/transactions/pending/rebalances")
    suspend fun getPendingRebalances(
        @Header("Authorization") authorization: String,
        @Header("Identity") identity: String
    ): Response<List<Transaction>>

    @GET("api/c2m/users/prompts")
    fun getPrompts(
        @Header("Authorization") authorization: String,
        @Header("Identity") identity: String,
        @Query("type") type: String?
    ): Call<GetPromptsResponse>

    @POST("api/c2m/users/prompts/seen")
    fun markPromptsAsSeen(
        @Header("Authorization") authorization: String,
        @Header("Identity") identity: String,
        @Body body: MarkPromptsAsSeenBodyParams?
    ): Call<ResponseBody>

    @GET("api/c2m/users/prompts")
    suspend fun getPromptsCoroutines(
        @Header("Authorization") authorization: String,
        @Header("Identity") identity: String,
        @Query("type") type: String?
    ): Response<GetPromptsResponse>

    @POST("api/c2m/users/me/residency-country")
    suspend fun setSelectedCountry(
        @Header("Authorization") authorization: String,
        @Header("Identity") identity: String,
        @Body body: SetSelectCountryBodyParams?
    ): Response<ResponseBody>

    @GET("api/c2m/wealthyhub/analyst-insights/{id}")
    suspend fun getAnalystInsightArticle(
        @Header("Authorization") authorization: String,
        @Header("Identity") identity: String,
        @Path("id") articleID: String?
    ): Response<GetAnalystInsightArticleResponse>

    @GET("api/c2m/wealthyhub/{type}")
    suspend fun getWealthyHubArticlesCoroutines(
        @Header("Authorization") authorization: String,
        @Header("Identity") identity: String,
        @Path("type") type: String,
        @Query("page") page: Int?,
        @Query("pageSize") pageSize: Int?
    ): Response<GetWealthyHubArticlesResponse>

    @POST("api/c2m/transactions/charges/lifetime")
    fun getLifetimePlanDeepLink(
        @Header("Authorization") authorization: String,
        @Header("Identity") identity: String,
        @Body body: GetLifetimePlanDeepLinkBodyParams?
    ): Call<GetLifetimePlanDeepLinkResponse>

    @POST("api/c2m/transactions/charges/lifetime/sync-truelayer")
    fun syncLifetimePlan(
        @Header("Authorization") authorization: String,
        @Header("Identity") identity: String,
        @Query("truelayerId") trueLayerID: String?
    ): Call<SyncLifetimePlanResponse>

    @POST("api/c2m/users/me")
    suspend fun submitHasAcceptedTerms(
        @Header("Authorization") authorization: String,
        @Header("Identity") identity: String,
        @Body body: SubmitHasAcceptedTermsParams
    ): Response<ResponseBody>

    @GET("api/c2m/users/employment-config")
    suspend fun getEmploymentConfig(
        @Header("Authorization") authorization: String,
        @Header("Identity") identity: String
    ): Response<GetEmploymentConfigResponse>

    @POST("api/c2m/users/me/employment-info")
    suspend fun submitEmploymentInfo(
        @Header("Authorization") authorization: String,
        @Header("Identity") identity: String,
        @Body body: SubmitEmploymentInfoBody
    ): Response<Void>

    @GET("api/c2m/transactions/billing")
    suspend fun getBilling(
        @Header("Authorization") authorization: String,
        @Header("Identity") identity: String,
        @Query("limit") limit: Int?
    ): Response<List<Transaction>>

    @GET("api/c2m/payment-methods")
    suspend fun getPaymentMethods(
        @Header("Authorization") authorization: String,
        @Header("Identity") identity: String
    ): Response<ApiResponse<List<PaymentMethod>>>

    @POST("api/c2m/payment-methods/initiate-stripe")
    suspend fun initiateStripe(
        @Header("Authorization") authorization: String,
        @Header("Identity") identity: String
    ): Response<InitiateStripeResponse>

    @POST("api/c2m/payment-methods/complete-stripe")
    suspend fun completeStripe(
        @Header("Authorization") authorization: String,
        @Header("Identity") identity: String,
        @Body body: CompleteStripeParams
    ): Response<PaymentMethod>

    @POST("api/c2m/subscriptions/initiate-stripe")
    suspend fun initiateStripeForSubscription(
        @Header("Authorization") authorization: String,
        @Header("Identity") identity: String,
        @Body body: InitiateStripeForSubscriptionParams
    ): Response<InitiateStripeForSubscriptionResponse>

    @POST("api/c2m/subscriptions/complete-stripe")
    suspend fun completeStripeForSubscription(
        @Header("Authorization") authorization: String,
        @Header("Identity") identity: String,
        @Body body: CompleteStripeForSubscriptionParams
    ): Response<Subscription>

    @POST("api/c2m/users/me/viewed-referral-code-screen")
    suspend fun markReferralCodeScreenAsViewed(
        @Header("Authorization") authorization: String,
        @Header("Identity") identity: String
    ): Response<ResponseBody>

    @POST("api/c2m/subscriptions/payment-method")
    suspend fun updatePaymentMethodForSubscription(
        @Header("Authorization") authorization: String,
        @Header("Identity") identity: String,
        @Body body: UpdatePaymentMethodForSubscriptionBody
    ): Response<Subscription>

    @GET("api/c2m/orders/pending")
    suspend fun getPendingOrders(
        @Header("Authorization") authorization: String,
        @Header("Identity") identity: String
    ): Response<GetPendingOrdersResponse>

    @POST("api/c2m/transactions/savings/deposit")
    suspend fun makeDepositForSavings(
        @Header("Authorization") authorization: String,
        @Header("Identity") identity: String,
        @Body parameters: MakeDepositParameters
    ): Response<MakeDepositResponse>

    @POST("api/c2m/portfolios/{portfolioID}/topup-savings")
    suspend fun addMoneyWithCash(
        @Header("Authorization") authorization: String,
        @Header("Identity") identity: String,
        @Path("portfolioID") portfolioID: String?,
        @Body parameters: AddMoneyWithCashParameters
    ): Response<Transaction>

    @POST("api/c2m/portfolios/{portfolioID}/withdraw-savings")
    suspend fun withdrawMoney(
        @Header("Authorization") authorization: String,
        @Header("Identity") identity: String,
        @Path("portfolioID") portfolioID: String?,
        @Body parameters: WithdrawMoneyParams
    ): Response<Transaction>

    @GET("api/c2m/savings-products/me")
    suspend fun getSavingsProducts(
        @Header("Authorization") authorization: String,
        @Header("Identity") identity: String
    ): Response<List<SavingsProduct>>

    @GET("api/c2m/savings-products/data")
    suspend fun getSavingsProductDetails(
        @Header("Authorization") authorization: String,
        @Header("Identity") identity: String,
        @Query("savingsProductId") savingsProductID: String?
    ): Response<GetSavingsProductDetailsResponse>

    @GET("api/c2m/savings-products/fee-details")
    suspend fun getSavingsProductFeeDetails(
        @Header("Authorization") authorization: String,
        @Header("Identity") identity: String,
        @Query("savingsProductId") savingsProductID: String?
    ): Response<SavingsProductFeeDetails>

    @GET("api/c2m/asset-news")
    suspend fun getAssetNews(
        @Header("Authorization") authorization: String,
        @Header("Identity") identity: String,
        @Query("assetId") assetID: String?,
        @Query("limit") limit: Int?
    ): Response<List<AssetNewsSection>>

    @POST("api/c2m/users/me/subscribe-wealthybites")
    suspend fun subscribeToWealthyBites(
        @Header("Authorization") authorization: String,
        @Header("Identity") identity: String,
        @Query("didSubscribe") didSubscribe: Boolean
    ): Response<ResponseBody>

    @GET("api/c2m/bank-accounts/banks")
    suspend fun getEuropeProviders(
        @Header("Authorization") authorization: String,
        @Header("Identity") identity: String,
        @Query("scope") scope: String?
    ): Response<List<EuropeProvider>>

    @POST("api/c2m/bank-accounts/initiate-bank-linking")
    suspend fun initiateEuropeBankLinking(
        @Header("Authorization") authorization: String,
        @Header("Identity") identity: String,
        @Body parameters: InitiateEuropeBankLinkingParams
    ): Response<InitiateEuropeBankLinkingResponse>

    @POST("gocardless/link-account")
    suspend fun linkEuropeBank(
        @Header("Authorization") authorization: String,
        @Header("Identity") identity: String,
        @Body parameters: LinkEuropeBankParams
    ): Response<ResponseBody>

    @POST("api/c2m/app-ratings/{appRatingID}")
    suspend fun rateApp(
        @Header("Authorization") authorization: String,
        @Header("Identity") identity: String,
        @Path("appRatingID") appRatingID: String?,
        @Body parameters: RateAppParams
    ): Response<ResponseBody>

    @POST("api/c2m/users/me/account-statements/generate")
    suspend fun generateAccountStatement(
        @Header("Authorization") authorization: String,
        @Header("Identity") identity: String,
        @Query("start") startDate: String?,
        @Query("end") endDate: String?
    ): Response<GenerateAccountStatementResponse>

    @GET("api/c2m/wealthyhub/me/help-centre")
    suspend fun getHelpCenterContent(
        @Header("Authorization") authorization: String,
        @Header("Identity") identity: String
    ): Response<GetHelpCenterContentResponse>

    @POST("api/c2m/kyc-operations/initiate")
    suspend fun initiateKYC(
        @Header("Authorization") authorization: String,
        @Header("Identity") identity: String
    ): Response<InitiateKYCResponse>

    @GET("api/c2m/kyc-operations/me")
    suspend fun syncKYC(
        @Header("Authorization") authorization: String,
        @Header("Identity") identity: String
    ): Response<SyncKYCResponse>

    @GET("api/c2m/notification-settings/me")
    suspend fun getNotificationSettings(
        @Header("Authorization") authorization: String,
        @Header("Identity") identity: String
    ): Response<NotificationSettings>

    @POST("api/c2m/notification-settings/me")
    suspend fun updateNotificationSetting(
        @Header("Authorization") authorization: String,
        @Header("Identity") identity: String,
        @Body parameters: UpdateNotificationSettingParams
    ): Response<NotificationSettings>

    @POST("api/c2m/notification-settings/me/device-settings")
    suspend fun updateDeviceNotificationSettings(
        @Header("Authorization") authorization: String,
        @Header("Identity") identity: String,
        @Body parameters: UpdateDeviceNotificationSettingsParams
    ): Response<ResponseBody>

    @GET("api/c2m/wealthyhub/learning-guides")
    suspend fun getLearningGuides(
        @Header("Authorization") authorization: String,
        @Header("Identity") identity: String
    ): Response<GetLearningGuidesResponse>

    @GET("api/c2m/wealthyhub/learning-guides/slug/{slug}")
    suspend fun getLearningGuideBySlug(
        @Header("Authorization") authorization: String,
        @Header("Identity") identity: String,
        @Path("slug") guideSlug: String?
    ): Response<GetLearningGuideResponse>

    @GET("api/c2m/wealthyhub/learning-guides/{id}")
    suspend fun getLearningGuideByID(
        @Header("Authorization") authorization: String,
        @Header("Identity") identity: String,
        @Path("id") guideID: String?
    ): Response<GetLearningGuideResponse>

    @GET("api/c2m/orders/matched/latest")
    suspend fun getLatestMatchedOrder(
        @Header("Authorization") authorization: String,
        @Header("Identity") identity: String
    ): Response<GetLatestMatchedOrderResponse>

    @POST("api/c2m/users/me/device-token")
    suspend fun registerDeviceToken(
        @Header("Authorization") authorization: String,
        @Header("Identity") identity: String,
        @Body parameters: RegisterDeviceTokenParams
    ): Response<ResponseBody>

    @GET("api/c2m/users/me/daily-summaries")
    suspend fun getDailySummaries(
        @Header("Authorization") authorization: String,
        @Header("Identity") identity: String
    ): Response<GetDailySummariesResponse>

    @GET("api/c2m/transactions/me/cash-activity")
    suspend fun getCashActivity(
        @Header("Authorization") authorization: String,
        @Header("Identity") identity: String,
        @Query("limit") limit: Int?
    ): Response<List<TransactionActivityResponse>>

    @GET("api/c2m/users/me/investment-activity")
    suspend fun getInvestmentActivity(
        @Header("Authorization") authorization: String,
        @Header("Identity") identity: String,
        @Query("limit") limit: Int?
    ): Response<List<TransactionActivityResponse>>

    @POST("api/c2m/users/me/join-waiting-list")
    suspend fun joinWaitingList(
        @Header("Authorization") authorization: String,
        @Header("Identity") identity: String,
        @Body body: JoinWaitingListParams?
    ): Response<ResponseBody>

    @POST("api/c2m/orders/{orderID}/trade-confirmations/generate")
    suspend fun generateOrderTradeConfirmation(
        @Header("Authorization") authorization: String,
        @Header("Identity") identity: String,
        @Path("orderID") orderID: String?
    ): Response<GenerateTradeConfirmationResponse>

    @POST("api/c2m/rewards/{orderID}/trade-confirmations/generate")
    suspend fun generateRewardTradeConfirmation(
        @Header("Authorization") authorization: String,
        @Header("Identity") identity: String,
        @Path("orderID") orderID: String?
    ): Response<GenerateTradeConfirmationResponse>
}
