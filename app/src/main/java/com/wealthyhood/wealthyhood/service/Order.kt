package com.wealthyhood.wealthyhood.service

import com.google.gson.Gson
import com.google.gson.annotations.SerializedName
import com.wealthyhood.wealthyhood.extensions.convertToTransaction
import com.wealthyhood.wealthyhood.extensions.generateFormattedCurrency
import com.wealthyhood.wealthyhood.ui.myaccount.transactions.TransactionsHelper
import java.util.Locale

data class Order(
    @SerializedName("_id")
    val id: String?,

    @SerializedName("displayUserFriendlyId")
    val displayUserFriendlyID: String?,

    @SerializedName("transaction")
    val transaction: Any?,

    @SerializedName("executionWindow")
    val executionWindow: ExecutionWindow.Data?,

    @SerializedName("displayExchangeRate")
    val displayExchangeRate: ExchangeRate?,

    // AssetCategoryEnum
    @SerializedName("assetCategory")
    val assetCategory: String?,

    @SerializedName("status")
    val status: String?,

    @SerializedName("isMatched")
    val isMatched: Boolean?,

    @SerializedName("isin")
    val isin: String?,

    @SerializedName("side")
    val side: String?,

    @SerializedName("quantity")
    val quantity: Float?, // TODO: Float?

    @SerializedName("displayQuantity")
    val displayQuantity: Double?,

    @SerializedName("consideration")
    val consideration: Consideration?,

    @SerializedName("displayDate")
    val displayDate: String?,

    @SerializedName("displayAmount")
    val displayAmount: Double?,

    @SerializedName("displayUnitPrice")
    val unitPrice: UnitPrice?,

    @SerializedName("isCancellable")
    val isCancellable: Boolean?,

    @SerializedName("hasExecutionStarted")
    val hasExecutionStarted: Boolean?,

    @SerializedName("fees")
    val fees: Fees?,

    @SerializedName("estimatedRealTimeCommission")
    val estimatedRealTimeCommission: Double?
) {

    fun generateFormattedUnitPrice(userLocale: Locale): String? {
        val amount = unitPrice?.amount ?: return null
        val currency = unitPrice.currency ?: return null

        return amount.generateFormattedCurrency(
            currencyISOCode = currency,
            locale = userLocale
        )
    }

    fun getTransactionAsString(): String? {
        return transaction as? String
    }

    fun getTransactionAsObject(): Transaction? {
        return Gson().toJson(transaction).convertToTransaction()
    }

    data class WealthKernel(
        @SerializedName("id")
        val id: String?,

        @SerializedName("status")
        val status: TransactionsHelper.OrderStatus? // TODO: Is this working?
    )
}
