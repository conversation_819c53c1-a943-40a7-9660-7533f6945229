package com.wealthyhood.wealthyhood.service

import com.google.gson.annotations.SerializedName
import com.wealthyhood.wealthyhood.ui.myaccount.transactions.TransactionsHelper

data class Reward(
    @SerializedName("_id")
    val id: String?,

    @SerializedName("displayUserFriendlyId")
    val displayUserFriendlyID: String?,

    @SerializedName("isin")
    val isin: String?,

    @SerializedName("consideration")
    val consideration: Consideration?,

    @SerializedName("displayAmount")
    val displayAmount: Double?,

    @SerializedName("quantity")
    val quantity: Double?,

    @SerializedName("referrer")
    val referrer: String?,

    @SerializedName("referral")
    val referral: String?,

    @SerializedName("createdAt")
    val createdAt: String?,

    @SerializedName("updatedAt")
    val updatedAt: String?,

    @SerializedName("displayDate")
    val displayDate: String?,

    @SerializedName("status")
    val status: TransactionsHelper.TransactionStatus?,

    @SerializedName("accepted")
    val accepted: Boolean?,

    @SerializedName("displayUnitPrice")
    val unitPrice: UnitPrice?
)
