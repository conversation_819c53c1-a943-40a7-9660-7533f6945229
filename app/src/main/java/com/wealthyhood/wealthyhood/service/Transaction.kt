package com.wealthyhood.wealthyhood.service

import com.google.gson.Gson
import com.google.gson.JsonObject
import com.google.gson.annotations.SerializedName
import com.wealthyhood.wealthyhood.domain.ExecutableInterface
import com.wealthyhood.wealthyhood.extensions.convertToAutomation
import com.wealthyhood.wealthyhood.extensions.convertToBankAccount
import com.wealthyhood.wealthyhood.extensions.convertToPaymentMethod
import com.wealthyhood.wealthyhood.extensions.convertToPendingDeposit

data class Transaction(
    @SerializedName("_id")
    val id: String?,

    @SerializedName("linkedAutomation")
    val linkedAutomation: Any?,

    @SerializedName("displayTitle")
    val displayTitle: String?,

    @SerializedName("category")
    val category: String?,

    @SerializedName("displayStatus")
    val displayStatus: String?,

    @SerializedName("chargeMonth")
    val chargeMonth: String?,

    @SerializedName("bankAccount")
    val bankAccount: BankAccount?,

    @SerializedName("consideration")
    val consideration: Consideration?,

    @SerializedName("portfolioTransactionCategory")
    val portfolioTransactionCategory: String?,

    @SerializedName("orders")
    val orders: List<Order>?,

    @SerializedName("executionWindow")
    val executionWindow: ExecutionWindow?,

    @SerializedName("sellExecutionWindow")
    override val sellExecutionWindow: ExecutionWindow.Data?,

    @SerializedName("buyExecutionWindow")
    override val buyExecutionWindow: ExecutionWindow.Data?,

    @SerializedName("executionProgress")
    val executionProgress: ExecutionProgress?,

    @SerializedName("pendingDeposit")
    val pendingDeposit: Any?,

    @SerializedName("isin")
    val isin: String?,

    @SerializedName("fees")
    val fees: Fees?,

    @SerializedName("foreignCurrencyRates")
    val foreignCurrencyRates: JsonObject?,

    @SerializedName("displayAmount")
    val displayAmount: Double?,

    @SerializedName("displayDate")
    val displayDate: String?,

    @SerializedName("chargeMethod")
    val chargeMethod: String?,

    @SerializedName("chargeType")
    val chargeType: String?,

    @SerializedName("price")
    val price: String?,

    @SerializedName("paymentMethod")
    val paymentMethod: Any?,

    @SerializedName("isCancellable")
    val isCancellable: Boolean?,

    @SerializedName("hasSellExecutionStarted")
    val hasSellExecutionStarted: Boolean?,

    @SerializedName("hasBuyExecutionStarted")
    val hasBuyExecutionStarted: Boolean?,

    @SerializedName("displayTag")
    val displayTag: String?,

    @SerializedName("shouldPromptToMonthly")
    val shouldPromptToMonthly: Boolean?
) : ExecutableInterface {

    override fun getExecutionWindow(
        shouldAllowSmartExecutionOption: Boolean,
        isSmartExecutionSwitchOn: Boolean
    ): ExecutionWindow? {
        return executionWindow
    }

    override fun hasExecutionStarted(type: Int): Boolean {
        return when (type) {
            ExecutableInterface.ASSET_EXECUTION_TYPE -> orders?.firstOrNull()?.hasExecutionStarted == true
            ExecutableInterface.SELL_EXECUTION_TYPE -> hasSellExecutionStarted == true
            ExecutableInterface.BUY_EXECUTION_TYPE -> hasBuyExecutionStarted == true
            else -> false
        }
    }

    fun getPaymentMethodAsObject(): PaymentMethod? {
        return Gson().toJson(paymentMethod).convertToPaymentMethod()
    }

    fun getPendingDepositAsObject(): PendingDeposit? {
        return Gson().toJson(pendingDeposit).convertToPendingDeposit()
    }

    fun getLinkedAutomationAsObject(): Automation? {
        return Gson().toJson(linkedAutomation).convertToAutomation()
    }

    data class PendingDeposit(
        @SerializedName("_id")
        val id: String?,

        @SerializedName("displayStatus")
        val displayStatus: String?,

        @SerializedName("truelayer")
        val trueLayer: SyncedTransaction.TrueLayer?,

        @SerializedName("bankAccount")
        val bankAccount: Any?,

        @SerializedName("shouldIncludeMandateStep")
        val shouldIncludeMandateStep: Boolean?,

        @SerializedName("isDirectDebitPaymentCollected")
        val isDirectDebitPaymentCollected: Boolean?,

        @SerializedName("directDebitProgressPercentage")
        val directDebitProgressPercentage: Float?,

        @SerializedName("isMoneyReceived")
        val moneyReceived: Boolean?
    ) {
        fun getBankAccountAsObject(): BankAccount? {
            return Gson().toJson(bankAccount).convertToBankAccount()
        }
    }
}
