package com.wealthyhood.wealthyhood.service

import android.content.Context
import android.net.Uri
import androidx.annotation.DrawableRes
import com.google.gson.Gson
import com.google.gson.JsonObject
import com.google.gson.annotations.SerializedName
import com.wealthyhood.wealthyhood.extensions.getDrawableRes
import com.wealthyhood.wealthyhood.model.DescriptionTypeEnum

data class AssetClass(
    @SerializedName("id")
    val id: String,

    @SerializedName("fieldName")
    val fieldName: String?,

    @SerializedName("keyName")
    val keyName: String?,

    @SerializedName("colorClass")
    val colorClass: String?,

    @SerializedName("unselectedColorClass")
    val unselectedColorClass: String?,

    @SerializedName("icon")
    val icon: String?,

    @SerializedName("coreAssets")
    val coreAssets: Map<String, List<String>>?,

    @SerializedName("assets")
    val assets: List<String>?,

    @SerializedName("sorting")
    val order: Int?,

    @SerializedName("selectable")
    val selectable: Boolean?
) {

    companion object {

        fun fromJsonObject(assetClassID: String, jsonObject: JsonObject): AssetClass? {
            jsonObject.addProperty("id", assetClassID)

            return try {
                val jsonString = Gson().toJson(jsonObject)
                Gson().fromJson(jsonString, AssetClass::class.java)
            } catch (e: Exception) {
                null
            }
        }
    }

    fun getDescriptionType(): DescriptionTypeEnum {
        return when (id) {
            "equities" -> DescriptionTypeEnum.STOCKS
            "bonds" -> DescriptionTypeEnum.BONDS
            "commodities" -> DescriptionTypeEnum.COMMODITIES
            else -> DescriptionTypeEnum.REAL_ESTATE
        }
    }

    @DrawableRes
    fun getIconDrawableRes(context: Context): Int? {
        return getFileName(icon)?.let {
            context.getDrawableRes(it)
        }
    }

    private fun getFileName(icon: String?): String? {
        if (icon == null) return null
        return Uri.parse(icon).lastPathSegment?.substringBefore('.')
    }
}
