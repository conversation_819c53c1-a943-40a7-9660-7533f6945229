package com.wealthyhood.wealthyhood.service

import com.google.gson.Gson
import com.google.gson.annotations.SerializedName
import com.wealthyhood.wealthyhood.extensions.convertToMandate

data class Automation(

    @SerializedName("_id")
    val id: String?,

    @SerializedName("savingsProduct")
    val savingsProductID: String?,

    @SerializedName("status")
    val status: String?,

    @SerializedName("category")
    val category: String?,

    @SerializedName("mandate")
    val mandate: Any?,

    @SerializedName("consideration")
    val consideration: Consideration?,

    @SerializedName("dayOfMonth")
    val dayOfMonth: Int?,

    @SerializedName("allocationMethod")
    val allocationMethod: String?
) {

    fun getMandateAsString(): String? {
        return mandate as? String
    }

    fun getMandateAsObject(): Mandate? {
        return Gson().toJson(mandate).convertToMandate()
    }
}
