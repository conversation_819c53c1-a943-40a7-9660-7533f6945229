package com.wealthyhood.wealthyhood.service

import com.google.gson.Gson
import com.google.gson.annotations.SerializedName
import com.wealthyhood.wealthyhood.extensions.convertToListOfPortfolios
import com.wealthyhood.wealthyhood.extensions.convertToWallet

data class User(
    @SerializedName("_id")
    val id: String?,

    @SerializedName("isCashbackFeatureEnabled")
    val isCashbackFeatureEnabled: Boolean?,

    @SerializedName("androidDeviceToken")
    val androidDeviceToken: String?,

    @SerializedName("bankReference")
    val bankReference: Any?,

    @SerializedName("wallet")
    val wallet: Any?,

    @SerializedName("companyEntity")
    val companyEntity: String?,

    @SerializedName("currency")
    val currency: String?,

    @SerializedName("isNew")
    val isUserNew: Boolean?,

    @SerializedName("hasAcceptedTerms")
    val hasAcceptedTerms: Boolean?,

    @SerializedName("hasIncompleteKycJourney")
    val hasIncompleteKYCJourney: Boolean?,

    @SerializedName("hasCompletedKycJourney")
    val hasCompletedKYCJourney: Boolean?,

    @SerializedName("hasJoinedWaitingList")
    val hasJoinedWaitingList: Boolean?,

    @SerializedName("isEuWhitelisted")
    val isEUWhitelisted: Boolean?,

    @SerializedName("kycOperation")
    val kycOperation: KYCOperation?,

    @SerializedName("isSweatcoinReferred")
    val isSweatCoinReferred: Boolean?,

    @SerializedName("intercomUserIdHashAndroid")
    val intercomUserIDHash: String?,

    // Possible values: uk, non-uk
    @SerializedName("UKResidentStatus")
    val ukResidentStatus: String?,

    @SerializedName("firstName")
    val firstName: String?,

    @SerializedName("lastName")
    val lastName: String?,

    @SerializedName("email")
    val email: String?,

    @SerializedName("referredByEmail")
    val referredByEmail: String?,

    @SerializedName("oneTimeReferralCode")
    val oneTimeReferralCode: OneTimeReferralCode?,

    @SerializedName("canUnlockFreeShare")
    val canUnlockFreeShare: Boolean?,

    @SerializedName("canReceiveCashback")
    val canReceiveCashback: Boolean?,

    @SerializedName("viewedWelcomePage")
    val hasViewedWelcomeScreen: Boolean?,

    // Determines if we should show the VerificationCompleted screen
    // if the user has passed the KYC.
    @SerializedName("shouldShowKYCSuccessPage")
    val shouldShowVerificationCompletedScreen: Boolean?,

    @SerializedName("portfolioConversionStatus")
    val portfolioConversionStatus: String?,

    @SerializedName("isConvertingPortfolio")
    val isConvertingPortfolio: Boolean?,

    @SerializedName("hasConvertedPortfolio")
    val hasConvertedPortfolio: Boolean?,

    @SerializedName("hasDisassociationRequest")
    val hasDisassociationRequest: Boolean?,

    // This field heavily depends on the possible values: what if the API decides to change them?
    // For this reason we created the hasFailedKYC, hasPassedKYC and isVerified fields.
    @SerializedName("kycStatus") // pending, failed, passed
    val kycStatus: String?,

    @SerializedName("hasFailedKyc")
    val hasFailedKYC: Boolean?,

    @SerializedName("hasPassedKyc")
    val hasPassedKYC: Boolean?,

    // isVerified, unlike hasPassedKYC, takes into account if the user has
    // a WealthKernel portfolio.
    // It needs the `portfolios,addresses` populations in order to work.
    @SerializedName("isVerified")
    val isVerified: Boolean?,

    @SerializedName("submittedRequiredInfo")
    val hasSubmittedRequiredInfo: Boolean?,

    @SerializedName("skippedVerification")
    val hasSkippedVerification: Boolean?,

    @SerializedName("shouldDisplayReferralCodeScreen")
    val shouldDisplayReferralCodeScreen: Boolean?,

    @SerializedName("dateOfBirth")
    val dateOfBirth: String?,

    @SerializedName("nationalities")
    val nationalities: List<String>?,

    @SerializedName("addresses")
    val addresses: List<Address>?,

    @SerializedName("hasSubscription")
    val hasSubscription: Boolean?,

    @SerializedName("subscription")
    val subscription: Subscription?,

    @SerializedName("participant")
    val participant: Participant?,

    @SerializedName("taxResidency")
    val taxResidency: TaxResidency?,

    @SerializedName("canSendGiftUntil")
    val canSendGiftUntil: String?,

    @SerializedName("residencyCountry")
    val residencyCountry: String?,

    @SerializedName("employmentInfo")
    val employmentInfo: EmploymentInfo?,

    @SerializedName("employmentInfoSubmitted")
    val employmentInfoSubmitted: Boolean?,

    @SerializedName("providers")
    val providers: Providers?,

    @SerializedName("shouldDisplayWealthybitesScreen")
    val shouldDisplayWealthybitesScreen: Boolean?,

    @SerializedName("skippedPortfolioCreation")
    val hasSkippedPortfolioCreation: Boolean?,

    @SerializedName("portfolios")
    val portfolios: Any?,

    @SerializedName("shouldShowStatements")
    val shouldShowStatements: Boolean?,

    @SerializedName("isPriceMomentumSentimentEnabled")
    val isPriceMomentumSentimentEnabled: Boolean?,

    @SerializedName("isRealtimeETFExecutionEnabled")
    val isRealtimeETFExecutionEnabled: Boolean?,

    @SerializedName("isRoboAdvisorEnabled")
    val isRoboAdvisorEnabled: Boolean?
) {

    fun getWalletAsObject(): Wallet? {
        return Gson().toJson(wallet).convertToWallet()
    }

    fun getPortfoliosAsListOfObjects(): List<Portfolio>? {
        return Gson().toJson(portfolios).convertToListOfPortfolios()
    }

    data class Address(
        @SerializedName("_id")
        val id: String?,

        @SerializedName("line1")
        val addressOne: String?,

        @SerializedName("line2")
        val addressTwo: String?,

        @SerializedName("city")
        val city: String?,

        @SerializedName("postalCode")
        val postCode: String?,

        @SerializedName("countryCode")
        val countryCode: String?
    )

    data class Participant(
        @SerializedName("_id")
        val id: String?,

        @SerializedName("anonymousId")
        val anonymousID: String?
    )

    data class TaxResidency(
        @SerializedName("value")
        val value: String?
    )

    data class EmploymentInfo(
        @SerializedName("incomeRangeId")
        val annualIncome: String?,

        @SerializedName("sourcesOfWealth")
        val sourcesOfWealth: List<String>?,

        @SerializedName("employmentStatus")
        val employmentStatus: String?,

        @SerializedName("industry")
        val industry: String?
    )
}
