package com.wealthyhood.wealthyhood.service

import com.google.gson.Gson
import com.google.gson.annotations.SerializedName
import com.wealthyhood.wealthyhood.extensions.convertToMandate

data class BankAccount(
    @SerializedName("id")
    val id: String?,

    // The BankAccount of the Mandate class has _id instead of id.
    @SerializedName("_id")
    val alternativeID: String?,

    @SerializedName("sortCode")
    val sortCode: String?,

    @SerializedName("number")
    val number: String?,

    @SerializedName("name")
    val name: String?,

    @SerializedName("provider")
    val provider: TrueLayerProvider?,

    @SerializedName("truelayerProviderId")
    val truelayerProviderId: String?,

    @SerializedName("mandate")
    val mandate: Any?,

    // This is used for the EU bank accounts
    @SerializedName("displayBankName")
    val displayBankName: String?,

    // This is used for the EU bank accounts
    @SerializedName("bankIconURL")
    val bankIconURL: String?,

    @SerializedName("displayAccountIdentifier")
    val displayAccountIdentifier: String?,

    @SerializedName("supportsEasyTransfer")
    val supportsEasyTransfer: Boolean?,

    @SerializedName("isAvailableForDirectDebit")
    val isAvailableForDirectDebit: Boolean?
) {

    fun getMandateAsObject(): Mandate? {
        return Gson().toJson(mandate).convertToMandate()
    }
}
