package com.wealthyhood.wealthyhood.service

import com.google.gson.JsonObject
import com.google.gson.annotations.SerializedName
import com.wealthyhood.wealthyhood.model.StatementPeriodsSector

data class GetInvestmentUniverseResponse(
    @SerializedName("country")
    val country: String?,

    @SerializedName("investmentUniverse")
    val investmentUniverse: InvestmentUniverse?,

    @SerializedName("plans")
    val plans: Plans?,

    @SerializedName("savingsUniverse")
    val savingsUniverse: JsonObject?,

    @SerializedName("savingsConfig")
    val savingsConfig: SavingsConfig?,

    @SerializedName("pricing")
    val pricing: Map<String, Plan>?,

    @SerializedName("wealthyhoodDividends")
    val wealthyhoodDividends: WealthyhoodDividends?,

    @SerializedName("cashbacks")
    val cashbacks: Cashbacks?,

    @SerializedName("discovery")
    val discoverConfiguration: DiscoverConfiguration?,

    @SerializedName("fees")
    val fees: Fees?,

    @SerializedName("rewards")
    val rewardSettings: RewardSettings?,

    @SerializedName("executionWindows")
    val executionWindows: ExecutionWindows?,

    @SerializedName("countries")
    val supportedCountries: List<SupportedCountry>?,

    @SerializedName("locales")
    val locales: Map<String, String>?,

    @SerializedName("bankDetails")
    val bankDetails: BankDetails?,

    @SerializedName("legalDocuments")
    val legalDocuments: LegalLinks?,

    @SerializedName("legalPages")
    val legalPages: LegalLinks?,

    @SerializedName("taxResidency")
    val taxResidency: TaxResidencyConfig?,

    @SerializedName("deposits")
    val deposits: DepositsConfig?,

    @SerializedName("statementPeriods")
    val statementPeriods: List<StatementPeriodsSector>?
) {

    data class InvestmentUniverse(
        @SerializedName("assetClasses")
        val assetClasses: JsonObject?,

        @SerializedName("geographies")
        val geographies: JsonObject?,

        @SerializedName("sectors")
        val sectors: JsonObject?,

        @SerializedName("bondCategories")
        val bondCategories: JsonObject?,

        @SerializedName("assets")
        val assets: JsonObject?,

        @SerializedName("providers")
        val providers: JsonObject?
    )

    data class Plans(
        @SerializedName("free")
        val free: PlanProperties?,

        @SerializedName("paid_low")
        val paidLow: PlanProperties?,

        @SerializedName("paid_mid")
        val paidMid: PlanProperties?
    ) {

        data class PlanProperties(
            @SerializedName("colorGradients")
            val colorGradients: List<String>?,

            @SerializedName("color")
            val color: String?,

            @SerializedName("colorLight")
            val colorLight: String?,

            @SerializedName("downgradeMessage")
            val downgradeMessage: String?
        )
    }

    data class SavingsConfig(
        @SerializedName("promotionalSavingsProduct")
        val promotionalSavingsProductID: String?,

        @SerializedName("minimumSavingsTopupAmount")
        val minimumSavingsTopUpAmount: Double?,

        @SerializedName("minimumSavingsWithdrawalAmount")
        val minimumSavingsWithdrawalAmount: Double?
    )

    data class WealthyhoodDividends(
        @SerializedName("rates")
        val rates: Rates?,

        @SerializedName("maximumPortfolioValue")
        val maximumPortfolioValue: Double?
    )

    data class Cashbacks(
        @SerializedName("rates")
        val rates: Rates?,

        @SerializedName("minimumInvestmentAmount")
        val minimumInvestmentAmount: Double?
    )

    data class Rates(
        @SerializedName("free")
        val free: Double?,

        @SerializedName("paid_low")
        val paidLow: Double?,

        @SerializedName("paid_mid")
        val paidMid: Double?
    )

    data class DiscoverConfiguration(
        @SerializedName("topMovers")
        val topMovers: TopMovers?,

        @SerializedName("etfSection")
        val etfSection: ETFSection?,

        @SerializedName("readyMadePortfolios")
        val readyMadePortfolios: List<String>?,

        @SerializedName("popularAssetsSection")
        val popularAssetsSection: List<String>?,

        @SerializedName("collections")
        val collections: Map<String, CollectionItem>?
    ) {

        data class TopMovers(
            @SerializedName("best")
            val best: List<TopMover>?,

            @SerializedName("worst")
            val worst: List<TopMover>?
        ) {

            data class TopMover(
                @SerializedName("asset")
                val assetID: String?,

                @SerializedName("returnPercentage")
                val returnPercentage: Double?
            )
        }

        data class ETFSection(
            @SerializedName("top")
            val top: List<String>?,

            @SerializedName("popularIndex")
            val popularIndex: List<String>?
        )

        data class CollectionItem(
            @SerializedName("label")
            val label: String?,

            @SerializedName("emoji")
            val emoji: String?,

            @SerializedName("assets")
            val assets: List<String>?
        )
    }

    data class Fees(
        @SerializedName("fx")
        val fx: Fee?,

        @SerializedName("custody")
        val custody: Fee?,

        @SerializedName("commission")
        val commission: Fee?
    ) {

        data class Fee(
            @SerializedName("rates")
            val rates: Rates?,

            @SerializedName("minimumFee")
            val minimumFee: Double?
        )
    }

    data class RewardSettings(
        @SerializedName("minInvestmentAmountEligibility")
        val minInvestmentAmountEligibility: Double?,

        @SerializedName("maxRewardAmountCopy")
        val maxRewardAmountCopy: Double?,

        @SerializedName("minRewardAmountCopy")
        val minRewardAmountCopy: Double?
    )

    data class ExecutionWindows(
        @SerializedName("etfStart")
        val etfStart: String?
    )

    data class BankDetails(
        @SerializedName("accountName")
        val accountName: String?,

        @SerializedName("bank")
        val bank: Bank?,

        @SerializedName("beneficiaryFullAddress")
        val beneficiaryFullAddress: String?
    ) {

        data class Bank(
            @SerializedName("bic")
            val bic: String?,

            @SerializedName("name")
            val name: String?,

            @SerializedName("fullAddress")
            val fullAddress: String?
        )
    }
}
