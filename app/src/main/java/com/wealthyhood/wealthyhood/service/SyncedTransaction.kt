package com.wealthyhood.wealthyhood.service

import com.google.gson.annotations.SerializedName

data class SyncedTransaction(
    @SerializedName("_id")
    val id: String?,

    @SerializedName("displayStatus")
    val displayStatus: String?,

    @SerializedName("consideration")
    val consideration: Consideration,

    @SerializedName("truelayer")
    val trueLayer: TrueLayer,

    @SerializedName("providers")
    val providers: Providers
) {

    data class TrueLayer(
        @SerializedName("status")
        val status: String?,

        @SerializedName("failureReason")
        val failureReason: String?
    )
}
