package com.wealthyhood.wealthyhood.service

import android.content.Context
import androidx.core.content.ContextCompat
import com.wealthyhood.wealthyhood.R
import com.wealthyhood.wealthyhood.domain.InfoRowEnum
import com.wealthyhood.wealthyhood.domain.formatAsAssetSellAvailableShares
import com.wealthyhood.wealthyhood.domain.generateBuyExecutionWindowInfoRow
import com.wealthyhood.wealthyhood.domain.generateExecutionWindowInfoRows
import com.wealthyhood.wealthyhood.domain.generateSellExecutionWindowInfoRow
import com.wealthyhood.wealthyhood.extensions.formatAsPercentage
import com.wealthyhood.wealthyhood.extensions.generateCommissionCurrencyCode
import com.wealthyhood.wealthyhood.extensions.generateFXRate
import com.wealthyhood.wealthyhood.extensions.generateFormattedCurrency
import com.wealthyhood.wealthyhood.extensions.generateFormattedTotalCommission
import com.wealthyhood.wealthyhood.extensions.generateFormattedTradedPrice
import com.wealthyhood.wealthyhood.model.InfoRow
import com.wealthyhood.wealthyhood.service.TransactionPreview.OrderPreview
import java.math.BigDecimal
import java.util.Locale

fun TransactionPreview.generatePortfolioBuyInfoRows(
    context: Context,
    currencyISOCode: String?,
    userLocale: Locale,
    isCashbackFeatureEnabled: Boolean,
    shouldAllowSmartExecutionOption: Boolean,
    isSmartExecutionSwitchOn: Boolean,
    switchUpdateTimestamp: Long?
): List<InfoRow> {
    val resources = context.resources
    val defaultColor = ContextCompat.getColor(context, R.color.wealthyhood_dark_blue)
    val infoRows = mutableListOf<InfoRow>()

    val isRepeating = (repeatingText != null)

    val totalInfoRow = InfoRow(
        id = "totalInfoRow",
        label = resources.getString(R.string.total),
        shouldShowInfoButton = false,
        value = orderAmount?.generateFormattedCurrency(
            currencyISOCode = currencyISOCode,
            locale = userLocale
        ),
        secondaryValue = null,
        shouldShowSwitch = false,
        isSwitchOn = false,
        switchUpdateTimestamp = null,
        valueTextColor = defaultColor,
        hasSeparator = true
    )

    infoRows.add(totalInfoRow)

    if (isCashbackFeatureEnabled) {
        cashback?.let {
            val value = it.generateFormattedCurrency(
                currencyISOCode = currencyISOCode,
                locale = userLocale
            )

            val cashbackInfoRow = InfoRow(
                id = InfoRowEnum.CASHBACK.rawValue,
                label = resources.getString(R.string.cashback_label),
                shouldShowInfoButton = true,
                value = value,
                secondaryValue = null,
                shouldShowSwitch = false,
                isSwitchOn = false,
                switchUpdateTimestamp = null,
                valueTextColor = defaultColor,
                hasSeparator = true
            )

            infoRows.add(cashbackInfoRow)
        }
    }

    if (isRepeating) {
        val repeatingInfoRow = InfoRow(
            id = "repeatingInfoRow",
            label = resources.getString(R.string.repeating_label),
            shouldShowInfoButton = false,
            value = repeatingText,
            secondaryValue = null,
            shouldShowSwitch = false,
            isSwitchOn = false,
            switchUpdateTimestamp = null,
            valueTextColor = defaultColor,
            hasSeparator = true
        )

        infoRows.add(repeatingInfoRow)
    }

    generateFormattedFee(
        userLocale = userLocale,
        shouldAllowSmartExecutionOption = shouldAllowSmartExecutionOption,
        isSmartExecutionSwitchOn = isSmartExecutionSwitchOn
    )?.let {
        val secondaryValue = generateDeletedFormattedFee(
            userLocale = userLocale,
            shouldAllowSmartExecutionOption = shouldAllowSmartExecutionOption,
            isSmartExecutionSwitchOn = isSmartExecutionSwitchOn
        )

        val commissionInfoRowID = if (isRepeating) {
            InfoRowEnum.REPEATING_PORTFOLIO_COMMISSION.rawValue
        } else {
            if (hasETFOrders == true) {
                InfoRowEnum.PORTFOLIO_COMMISSION.rawValue
            } else {
                InfoRowEnum.PORTFOLIO_WITHOUT_ETF_ORDERS_COMMISSION.rawValue
            }
        }

        val feeInfoRow = InfoRow(
            id = commissionInfoRowID,
            label = resources.getString(R.string.commission_info_row_label),
            shouldShowInfoButton = true,
            value = it,
            secondaryValue = secondaryValue,
            shouldShowSwitch = false,
            isSwitchOn = false,
            switchUpdateTimestamp = null,
            valueTextColor = defaultColor,
            hasSeparator = true
        )

        infoRows.add(feeInfoRow)
    }

    foreignCurrencyRates?.generateFXRate(
        userCurrencyISOCode = currencyISOCode,
        userLocale = userLocale
    )?.let {
        val fxRateInfoRow = InfoRow(
            id = InfoRowEnum.FX_RATE.rawValue,
            label = resources.getString(R.string.fx_rate_info_row_label),
            shouldShowInfoButton = true,
            value = it,
            secondaryValue = null,
            shouldShowSwitch = false,
            isSwitchOn = false,
            switchUpdateTimestamp = null,
            valueTextColor = defaultColor,
            hasSeparator = true
        )

        infoRows.add(fxRateInfoRow)
    }

    if (repeatingText == null) {
        val executionWindowInfoRows = generateExecutionWindowInfoRows(
            context = context,
            valueTextColor = defaultColor,
            shouldAllowSmartExecutionOption = shouldAllowSmartExecutionOption,
            isSmartExecutionSwitchOn = isSmartExecutionSwitchOn
        )

        infoRows.addAll(executionWindowInfoRows)
    }

    if (shouldAllowSmartExecutionOption) {
        val infoRow = InfoRow(
            id = InfoRowEnum.PORTFOLIO_SMART_EXECUTION.rawValue,
            label = resources.getString(R.string.smart_execution_label),
            shouldShowInfoButton = true,
            value = null,
            secondaryValue = null,
            shouldShowSwitch = true,
            isSwitchOn = isSmartExecutionSwitchOn,
            switchUpdateTimestamp = switchUpdateTimestamp,
            valueTextColor = defaultColor,
            hasSeparator = true
        )

        infoRows.add(infoRow)
    }

    return infoRows
}

fun TransactionPreview.generatePortfolioSellInfoRows(
    context: Context,
    currencyISOCode: String?,
    userLocale: Locale
): List<InfoRow> {
    val resources = context.resources
    val defaultColor = ContextCompat.getColor(context, R.color.wealthyhood_dark_blue)
    val infoRows = mutableListOf<InfoRow>()

    val totalInfoRow = InfoRow(
        id = "totalInfoRow",
        label = resources.getString(R.string.total_est),
        shouldShowInfoButton = false,
        value = orderAmount?.generateFormattedCurrency(
            currencyISOCode = currencyISOCode,
            locale = userLocale
        ),
        secondaryValue = null,
        shouldShowSwitch = false,
        isSwitchOn = false,
        switchUpdateTimestamp = null,
        valueTextColor = defaultColor,
        hasSeparator = true
    )

    infoRows.add(totalInfoRow)

    val portfolioPercentageRow = InfoRow(
        id = "portfolioPercentageRow",
        label = resources.getString(R.string.portfolio_percentage),
        shouldShowInfoButton = false,
        value = portfolioPercentage?.formatAsPercentage(locale = userLocale),
        secondaryValue = null,
        shouldShowSwitch = false,
        isSwitchOn = false,
        switchUpdateTimestamp = null,
        valueTextColor = defaultColor,
        hasSeparator = true
    )

    infoRows.add(portfolioPercentageRow)

    generateFormattedFee(
        userLocale = userLocale,
        shouldAllowSmartExecutionOption = false,
        isSmartExecutionSwitchOn = false
    )?.let {
        val feeInfoRow = InfoRow(
            id = InfoRowEnum.PORTFOLIO_COMMISSION.rawValue,
            label = resources.getString(R.string.commission_info_row_label),
            shouldShowInfoButton = true,
            value = it,
            secondaryValue = null,
            shouldShowSwitch = false,
            isSwitchOn = false,
            switchUpdateTimestamp = null,
            valueTextColor = defaultColor,
            hasSeparator = true
        )

        infoRows.add(feeInfoRow)
    }

    foreignCurrencyRates?.generateFXRate(
        userCurrencyISOCode = currencyISOCode,
        userLocale = userLocale
    )?.let {
        val fxRateInfoRow = InfoRow(
            id = InfoRowEnum.FX_RATE.rawValue,
            label = resources.getString(R.string.fx_rate_info_row_label),
            shouldShowInfoButton = true,
            value = it,
            secondaryValue = null,
            shouldShowSwitch = false,
            isSwitchOn = false,
            switchUpdateTimestamp = null,
            valueTextColor = defaultColor,
            hasSeparator = true
        )

        infoRows.add(fxRateInfoRow)
    }

    val executionWindowInfoRows = generateExecutionWindowInfoRows(
        context = context,
        valueTextColor = defaultColor,
        shouldAllowSmartExecutionOption = false,
        isSmartExecutionSwitchOn = false
    )

    infoRows.addAll(executionWindowInfoRows)

    return infoRows
}

fun TransactionPreview.generateSellOrderInfoRows(
    context: Context,
    currencyISOCode: String?,
    userLocale: Locale,
    isStock: Boolean
): List<InfoRow> {
    val assetID = investmentProduct?.commonID
    val resources = context.resources
    val defaultColor = ContextCompat.getColor(context, R.color.wealthyhood_dark_blue)
    val infoRows = mutableListOf<InfoRow>()

    val ordersMap = ordersMap.smart ?: ordersMap.express

    val quantity = BigDecimal.valueOf(ordersMap?.get(assetID)?.quantity?.toDouble() ?: 0.0)

    val sharesInfoRow = InfoRow(
        id = "sharesInfoRow",
        label = resources.getString(R.string.shares),
        shouldShowInfoButton = false,
        value = quantity?.formatAsAssetSellAvailableShares(userLocale),
        secondaryValue = null,
        shouldShowSwitch = false,
        isSwitchOn = false,
        switchUpdateTimestamp = null,
        valueTextColor = defaultColor,
        hasSeparator = true
    )

    infoRows.add(sharesInfoRow)

    val amount = ordersMap?.get(assetID)?.quantity?.times(
        investmentProduct?.currentTickerPrice ?: 0f
    )

    val totalInfoRow = InfoRow(
        id = "amountInfoRow",
        label = resources.getString(R.string.amount_est),
        shouldShowInfoButton = false,
        value =
            amount?.generateFormattedCurrency(
                currencyISOCode = currencyISOCode,
                locale = userLocale
            ),
        secondaryValue = null,
        shouldShowSwitch = false,
        isSwitchOn = false,
        switchUpdateTimestamp = null,
        valueTextColor = defaultColor,
        hasSeparator = true
    )

    infoRows.add(totalInfoRow)

    val latestPriceInfoRow = InfoRow(
        id = "latestPriceInfoRow",
        label = resources.getString(R.string.latest_price),
        shouldShowInfoButton = false,
        value = investmentProduct?.generateFormattedTradedPrice(userLocale = userLocale),
        secondaryValue = null,
        shouldShowSwitch = false,
        isSwitchOn = false,
        switchUpdateTimestamp = null,
        valueTextColor = defaultColor,
        hasSeparator = true
    )

    infoRows.add(latestPriceInfoRow)

    generateFormattedFee(
        userLocale = userLocale,
        shouldAllowSmartExecutionOption = false,
        isSmartExecutionSwitchOn = false
    )?.let {
        val infoRowID = if (isStock) {
            InfoRowEnum.STOCK_COMMISSION.rawValue
        } else {
            InfoRowEnum.ETF_SELL_COMMISSION.rawValue
        }

        val feeInfoRow = InfoRow(
            id = infoRowID,
            label = resources.getString(R.string.commission_info_row_label),
            shouldShowInfoButton = true,
            value = it,
            secondaryValue = null,
            shouldShowSwitch = false,
            isSwitchOn = false,
            switchUpdateTimestamp = null,
            valueTextColor = defaultColor,
            hasSeparator = true
        )

        infoRows.add(feeInfoRow)
    }

    foreignCurrencyRates?.generateFXRate(
        userCurrencyISOCode = currencyISOCode,
        userLocale = userLocale
    )?.let {
        val fxRateInfoRow = InfoRow(
            id = InfoRowEnum.FX_RATE.rawValue,
            label = resources.getString(R.string.fx_rate_info_row_label),
            shouldShowInfoButton = true,
            value = it,
            secondaryValue = null,
            shouldShowSwitch = false,
            isSwitchOn = false,
            switchUpdateTimestamp = null,
            valueTextColor = defaultColor,
            hasSeparator = true
        )

        infoRows.add(fxRateInfoRow)
    }

    val executionWindowInfoRows = generateExecutionWindowInfoRows(
        context = context,
        valueTextColor = defaultColor,
        shouldAllowSmartExecutionOption = false,
        isSmartExecutionSwitchOn = false
    )

    infoRows.addAll(executionWindowInfoRows)

    return infoRows
}

fun TransactionPreview.generateBuyOrderInfoRows(
    context: Context,
    currencyISOCode: String?,
    userLocale: Locale,
    isStock: Boolean,
    isCashbackFeatureEnabled: Boolean,
    shouldAllowSmartExecutionOption: Boolean,
    isSmartExecutionSwitchOn: Boolean,
    switchUpdateTimestamp: Long?
): List<InfoRow> {
    val resources = context.resources
    val defaultColor = ContextCompat.getColor(context, R.color.wealthyhood_dark_blue)
    val infoRows = mutableListOf<InfoRow>()

    val totalInfoRow = InfoRow(
        id = "amountInfoRow",
        label = resources.getString(R.string.amount),
        shouldShowInfoButton = false,
        value = orderAmount?.generateFormattedCurrency(
            currencyISOCode = currencyISOCode,
            locale = userLocale
        ),
        secondaryValue = null,
        shouldShowSwitch = false,
        isSwitchOn = false,
        switchUpdateTimestamp = null,
        valueTextColor = defaultColor,
        hasSeparator = true
    )

    infoRows.add(totalInfoRow)

    if (isCashbackFeatureEnabled) {
        cashback?.let {
            val value = it.generateFormattedCurrency(
                currencyISOCode = currencyISOCode,
                locale = userLocale
            )

            val cashbackInfoRow = InfoRow(
                id = InfoRowEnum.CASHBACK.rawValue,
                label = resources.getString(R.string.cashback_label),
                shouldShowInfoButton = true,
                value = value,
                secondaryValue = null,
                shouldShowSwitch = false,
                isSwitchOn = false,
                switchUpdateTimestamp = null,
                valueTextColor = defaultColor,
                hasSeparator = true
            )

            infoRows.add(cashbackInfoRow)
        }
    }

    investmentProduct?.currentTickerPrice?.let {
        val assetID = investmentProduct?.commonID

        val ordersMap = getOrdersMap(
            shouldAllowSmartExecutionOption = shouldAllowSmartExecutionOption,
            isSmartExecutionSwitchOn = isSmartExecutionSwitchOn
        )

        val quantity = BigDecimal.valueOf(ordersMap?.get(assetID)?.quantity?.toDouble() ?: 0.0)
        val formattedQuantity = quantity?.formatAsAssetSellAvailableShares(userLocale)

        val sharesInfoRow = InfoRow(
            id = "sharesInfoRow",
            label = resources.getString(R.string.shares_est),
            shouldShowInfoButton = false,
            value = formattedQuantity,
            secondaryValue = null,
            shouldShowSwitch = false,
            isSwitchOn = false,
            switchUpdateTimestamp = null,
            valueTextColor = defaultColor,
            hasSeparator = true
        )

        infoRows.add(sharesInfoRow)
    }

    val latestPriceInfoRow = InfoRow(
        id = "latestPriceInfoRow",
        label = resources.getString(R.string.latest_price),
        shouldShowInfoButton = false,
        value = investmentProduct?.generateFormattedTradedPrice(userLocale = userLocale),
        secondaryValue = null,
        shouldShowSwitch = false,
        isSwitchOn = false,
        switchUpdateTimestamp = null,
        valueTextColor = defaultColor,
        hasSeparator = true
    )

    infoRows.add(latestPriceInfoRow)

    generateFormattedFee(
        userLocale = userLocale,
        shouldAllowSmartExecutionOption = shouldAllowSmartExecutionOption,
        isSmartExecutionSwitchOn = isSmartExecutionSwitchOn
    )?.let {
        val infoRowID = if (isStock) {
            InfoRowEnum.STOCK_COMMISSION.rawValue
        } else {
            InfoRowEnum.ETF_BUY_COMMISSION.rawValue
        }

        val secondaryValue = generateDeletedFormattedFee(
            userLocale = userLocale,
            shouldAllowSmartExecutionOption = shouldAllowSmartExecutionOption,
            isSmartExecutionSwitchOn = isSmartExecutionSwitchOn
        )

        val feeInfoRow = InfoRow(
            id = infoRowID,
            label = resources.getString(R.string.commission_info_row_label),
            shouldShowInfoButton = true,
            value = it,
            secondaryValue = secondaryValue,
            shouldShowSwitch = false,
            isSwitchOn = false,
            switchUpdateTimestamp = null,
            valueTextColor = defaultColor,
            hasSeparator = true
        )

        infoRows.add(feeInfoRow)
    }

    foreignCurrencyRates?.generateFXRate(
        userCurrencyISOCode = currencyISOCode,
        userLocale = userLocale
    )?.let {
        val fxRateInfoRow = InfoRow(
            id = InfoRowEnum.FX_RATE.rawValue,
            label = resources.getString(R.string.fx_rate_info_row_label),
            shouldShowInfoButton = true,
            value = it,
            secondaryValue = null,
            shouldShowSwitch = false,
            isSwitchOn = false,
            switchUpdateTimestamp = null,
            valueTextColor = defaultColor,
            hasSeparator = true
        )

        infoRows.add(fxRateInfoRow)
    }

    val executionWindowInfoRows = generateExecutionWindowInfoRows(
        context = context,
        valueTextColor = defaultColor,
        shouldAllowSmartExecutionOption = shouldAllowSmartExecutionOption,
        isSmartExecutionSwitchOn = isSmartExecutionSwitchOn
    )

    infoRows.addAll(executionWindowInfoRows)

    if (shouldAllowSmartExecutionOption) {
        val infoRow = InfoRow(
            id = InfoRowEnum.ASSET_SMART_EXECUTION.rawValue,
            label = resources.getString(R.string.smart_execution_label),
            shouldShowInfoButton = true,
            value = null,
            secondaryValue = null,
            shouldShowSwitch = true,
            isSwitchOn = isSmartExecutionSwitchOn,
            switchUpdateTimestamp = switchUpdateTimestamp,
            valueTextColor = defaultColor,
            hasSeparator = true
        )

        infoRows.add(infoRow)
    }

    return infoRows
}

fun TransactionPreview.generateRebalanceInfoRows(
    context: Context,
    currencyISOCode: String?,
    userLocale: Locale
): List<InfoRow> {
    val resources = context.resources
    val defaultColor = ContextCompat.getColor(context, R.color.wealthyhood_dark_blue)
    val infoRows = mutableListOf<InfoRow>()

    generateFormattedFee(
        userLocale = userLocale,
        shouldAllowSmartExecutionOption = false,
        isSmartExecutionSwitchOn = false
    )?.let {
        val feeInfoRow = InfoRow(
            id = InfoRowEnum.PORTFOLIO_COMMISSION.rawValue,
            label = resources.getString(R.string.commission_info_row_label),
            shouldShowInfoButton = true,
            value = it,
            secondaryValue = null,
            shouldShowSwitch = false,
            isSwitchOn = false,
            switchUpdateTimestamp = null,
            valueTextColor = defaultColor,
            hasSeparator = true
        )

        infoRows.add(feeInfoRow)
    }

    foreignCurrencyRates?.generateFXRate(
        userCurrencyISOCode = currencyISOCode,
        userLocale = userLocale
    )?.let {
        val fxRateInfoRow = InfoRow(
            id = InfoRowEnum.FX_RATE.rawValue,
            label = resources.getString(R.string.fx_rate_info_row_label),
            shouldShowInfoButton = true,
            value = it,
            secondaryValue = null,
            shouldShowSwitch = false,
            isSwitchOn = false,
            switchUpdateTimestamp = null,
            valueTextColor = defaultColor,
            hasSeparator = true
        )

        infoRows.add(fxRateInfoRow)
    }

    generateSellExecutionWindowInfoRow(
        context = context,
        valueTextColor = defaultColor
    )?.let {
        infoRows.add(it)
    }

    generateBuyExecutionWindowInfoRow(
        context = context,
        valueTextColor = defaultColor
    )?.let {
        infoRows.add(it)
    }

    return infoRows
}


// TODO: The following is no longer needed. It uses the LocalDateTime class which needs
//  a higher minSDK version.
//private fun generateFormattedExecutionWindow2(
//    context: Context,
//    stringPattern: String,
//    dateStr: String?
//): String? {
//    if (dateStr == null) return null
//
//    val zonedDateTime =
//        LocalDateTime.parse(dateStr, DateTimeFormatter.ISO_DATE_TIME).atZone(ZoneId.of("UTC"))
//
//    val dayDescription = if (zonedDateTime.toLocalDate().equals(LocalDate.now())) {
//        context.resources.getString(R.string.today)
//    } else if (zonedDateTime.toLocalDate().equals(LocalDate.now().plusDays(1))) {
//        context.resources.getString(R.string.tomorrow)
//    } else null
//
//    if (dayDescription == null) return zonedDateTime.format(
//        DateTimeFormatter.ofPattern(stringPattern)
//            .withZone(ZoneId.of("Europe/London"))
//    )
//
//    return "$dayDescription - ${
//        zonedDateTime.format(
//            DateTimeFormatter.ofPattern("HH:mm")
//                .withZone(ZoneId.of("Europe/London"))
//        )
//    }"
//}

private fun TransactionPreview.getOrdersMap(
    shouldAllowSmartExecutionOption: Boolean,
    isSmartExecutionSwitchOn: Boolean
): Map<String, OrderPreview>? {
    val smartOrdersMap = ordersMap.smart
    val expressOrdersMap = ordersMap.express

    val ordersMap = if (!shouldAllowSmartExecutionOption) {
        smartOrdersMap ?: expressOrdersMap
    } else {
        if (isSmartExecutionSwitchOn) smartOrdersMap else expressOrdersMap
    }

    return ordersMap
}


fun TransactionPreview.generateFormattedFee(
    userLocale: Locale,
    shouldAllowSmartExecutionOption: Boolean,
    isSmartExecutionSwitchOn: Boolean
): String? {
    val smartFees = fees?.smart
    val expressFees = fees?.express

    val fees = if (!shouldAllowSmartExecutionOption) {
        smartFees ?: expressFees
    } else {
        if (isSmartExecutionSwitchOn) smartFees else expressFees
    }

    val currencyISOCode = fees?.generateCommissionCurrencyCode()

    return fees?.generateFormattedTotalCommission(
        userLocale = userLocale,
        currencyISOCode = currencyISOCode
    )
}

fun TransactionPreview.generateDeletedFormattedFee(
    userLocale: Locale,
    shouldAllowSmartExecutionOption: Boolean,
    isSmartExecutionSwitchOn: Boolean
): String? {
    if (!shouldAllowSmartExecutionOption) return null
    if (!isSmartExecutionSwitchOn) return null

    val expressFees = fees?.express

    val currencyISOCode = expressFees?.generateCommissionCurrencyCode()

    return expressFees?.generateFormattedTotalCommission(
        userLocale = userLocale,
        currencyISOCode = currencyISOCode
    )
}
