package com.wealthyhood.wealthyhood.service

import com.wealthyhood.wealthyhood.BuildConfig
import io.sentry.HttpStatusCodeRange
import io.sentry.okhttp.SentryOkHttpInterceptor
import okhttp3.OkHttpClient
import okhttp3.logging.HttpLoggingInterceptor
import retrofit2.Call
import retrofit2.Response
import retrofit2.Retrofit
import retrofit2.converter.gson.GsonConverterFactory
import retrofit2.http.GET
import retrofit2.http.Query
import java.util.concurrent.TimeUnit

interface GenericService {

    companion object {

        val instance: GenericService by lazy {
            val loggingInterceptor = HttpLoggingInterceptor()

            loggingInterceptor.level = if (BuildConfig.DEBUG) {
                HttpLoggingInterceptor.Level.BODY
            } else {
                HttpLoggingInterceptor.Level.NONE
            }

            val sentryOkHttpInterceptor = SentryOkHttpInterceptor(
                captureFailedRequests = true,
                failedRequestStatusCodes = listOf(
                    HttpStatusCodeRange(400, 599)
                ),
                failedRequestTargets = listOf(BuildConfig.WEALTHYHOOD_API_BASE_URL)
            )

            val httpClient = OkHttpClient.Builder()
                .connectTimeout(20, TimeUnit.SECONDS)
                .readTimeout(20, TimeUnit.SECONDS)
                .writeTimeout(20, TimeUnit.SECONDS)
                .addInterceptor(loggingInterceptor)
                .addInterceptor(sentryOkHttpInterceptor)
                .build()

            val retrofit = Retrofit.Builder()
                .client(httpClient)
                .addConverterFactory(GsonConverterFactory.create())
                .baseUrl("https://pub-ed7b04fb4f54412db49742990e70ae3f.r2.dev/") // TODO: Create a constant for this
                .build()

            retrofit.create(GenericService::class.java)
        }
    }

    @GET("https://mobile-assets.wealthyhood.dev/versions.json")
    suspend fun getForceUpdateData(
    ): Response<GetForceUpdateDataResponse>

    /**
     * If url is a relative URL, base is required, and will be used as the base URL.
     * If url is an absolute URL, a given base will be ignored.
     * A string representing the base URL to use in cases where url is a relative URL.
     * https://developer.mozilla.org/en-US/docs/Web/API/URL/URL#
     */
    @GET("https://mobile-config-edge-server.wealthyhood.workers.dev/app-config")
    suspend fun getInvestmentUniverse(
        @Query("companyEntity") companyEntity: String?,
        @Query("country") country: String?,
        @Query("email") email: String?,
        @Query("code") referralCode: String?
    ): Response<GetInvestmentUniverseResponse>

    @GET("https://mobile-config-edge-server.wealthyhood.workers.dev/whitelist")
    suspend fun getWhitelistedMails(): Response<GetWhitelistedMailsResponse>

    @GET("intercomAssets.json")
    fun getIntercomAssets(
    ): Call<GetIntercomAssetsResponse>
}
