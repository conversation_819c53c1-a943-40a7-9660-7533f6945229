package com.wealthyhood.wealthyhood.service

import com.google.gson.JsonObject
import com.google.gson.annotations.SerializedName
import com.wealthyhood.wealthyhood.database.InvestmentProduct
import com.wealthyhood.wealthyhood.domain.ExecutableInterface

data class TransactionPreview(
    @SerializedName("executionWindow")
    val executionWindow: TransactionPreviewExecutionWindow?,

    @SerializedName("sellExecutionWindow")
    override val sellExecutionWindow: ExecutionWindow.Data?,

    @SerializedName("buyExecutionWindow")
    override val buyExecutionWindow: ExecutionWindow.Data?,

    @SerializedName("orders")
    val ordersMap: TransactionPreviewOrders,

    @SerializedName("fees")
    val fees: TransactionPreviewFees?,

    @SerializedName("foreignCurrencyRates")
    val foreignCurrencyRates: JsonObject?,

    @SerializedName("willSkipOrders")
    val willSkipOrders: Boolean?,

    @SerializedName("willResultInLowQuantityHolding")
    val willResultInLowQuantityHolding: Boolean?,

    @SerializedName("cashback")
    val cashback: Double?,

    @SerializedName("hasETFOrders")
    val hasETFOrders: Boolean?,

    // Optional calculated from view model fields
    var repeatingText: String?,
    var disclaimerText: String?,
    var orderAmount: Float?, // TODO: Remove them from here
    var investmentProduct: InvestmentProduct?, // TODO: Remove them from here
    var portfolioPercentage: Double? // TODO: Remove them from here
) : ExecutableInterface {

    data class TransactionPreviewFees(
        @SerializedName("smart")
        val smart: Fees?,

        @SerializedName("express")
        val express: Fees?
    )

    data class TransactionPreviewOrders(
        @SerializedName("smart")
        val smart: Map<String, OrderPreview>?,

        @SerializedName("express")
        val express: Map<String, OrderPreview>?
    )

    data class TransactionPreviewExecutionWindow(
        @SerializedName("smart")
        val smart: ExecutionWindow?,

        @SerializedName("express")
        val express: ExecutionWindow?
    )

    override fun getExecutionWindow(
        shouldAllowSmartExecutionOption: Boolean,
        isSmartExecutionSwitchOn: Boolean
    ): ExecutionWindow? {
        val smartExecutionWindow = executionWindow?.smart
        val expressExecutionWindow = executionWindow?.express

        return if (!shouldAllowSmartExecutionOption) {
            smartExecutionWindow ?: expressExecutionWindow
        } else {
            if (isSmartExecutionSwitchOn) smartExecutionWindow else expressExecutionWindow
        }
    }

    override fun hasExecutionStarted(type: Int): Boolean {
        return false
    }

    data class OrderPreview(
        val side: String?,
        val quantity: Float?,
        val money: Float?
    )
}
