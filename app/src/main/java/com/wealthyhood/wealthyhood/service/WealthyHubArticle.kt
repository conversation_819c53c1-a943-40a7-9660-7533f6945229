package com.wealthyhood.wealthyhood.service

import com.google.gson.annotations.SerializedName

data class WealthyHubArticle(
    @SerializedName("id")
    val id: String?,

    @SerializedName("key")
    val key: String?,

    @SerializedName("analystInsightType")
    val analystInsightType: String?,

    @SerializedName("contentType")
    val contentType: String?,

    @SerializedName("createdAt")
    val createdAt: String?,

    @SerializedName("publishedAt")
    val publishedAt: String?,

    @SerializedName("fullImageURL")
    val fullImageURL: String?,

    @SerializedName("previewImageURL")
    val previewImageURL: String?,

    @SerializedName("storyImageURL")
    val storyImageURL: String?,

    @SerializedName("title")
    val title: String?,

    @SerializedName("readingTime")
    val readingTime: String?,

    @SerializedName("contentHTML")
    val contentHTML: String?,

    @SerializedName("definitionHTML")
    val definitionHTML: String?
) {

    companion object {

        const val NEWS_CONTENT_TYPE = "newsWealthyhub"
        const val ANALYST_INSIGHTS_CONTENT_TYPE = "analystInsights"
    }
}
